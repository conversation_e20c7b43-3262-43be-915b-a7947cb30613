
  * {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, "Courier New", monospace;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.sidebar-holder {
  width: 100%;
  min-width: 15rem;
  max-width: 15rem;
  position: relative;
  background: #151515;
  color: #fff;
  z-index: 2;
  transition: all 0.3s;
  min-height: 100vh;
  overflow: hidden;
  transition: 0.2s;
}

.open-nav {
  min-width: 0rem !important;
  max-width: 0rem !important;
  width: 0 !important;
  transition: 0.2s;
  opacity: 0;
}

.sidebar-list ul li a {
  padding: .625rem;
  display: block;
  width: 100%;
  font-size: 1.125rem;
  font-weight: 600;
  transition: 0.2s ease-in;
  text-transform: capitalize;
}

.sidebar-list ul li a:hover {
  color: #151515;
  background: white;
}

.page-header {
  width: 100%;
  padding: 1.25rem;
  background: white;
}

.page-header span {
  cursor: pointer;
  display: block;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  font-size: 1.25rem;
}

.center-svg {
  aspect-ratio: 1/1;
  align-items: center;
  justify-content: center;
  line-height: 1.2em !important;
}

.uppy-Dashboard-inner {
  width: 100% !important;
}

/*
! tailwindcss v3.0.24 | MIT License | https://tailwindcss.com
*/

/*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
  box-sizing: border-box;
  /* 1 */
  border-width: 0;
  /* 2 */
  border-style: solid;
  /* 2 */
  border-color: #e5e7eb;
  /* 2 */
}

::before,
::after {
  --tw-content: "";
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
*/

html {
  line-height: 1.5;
  /* 1 */
  -webkit-text-size-adjust: 100%;
  /* 2 */
  -moz-tab-size: 4;
  /* 3 */
  -o-tab-size: 4;
  tab-size: 4;
  /* 3 */
  font-family: Inter, sans-serif;
  /* 4 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
  margin: 0;
  /* 1 */
  line-height: inherit;
  /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
  height: 0;
  /* 1 */
  color: inherit;
  /* 2 */
  border-top-width: .0625rem;
  /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
  text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
  color: inherit;
  text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
  font-weight: bolder;
}

/*
1. Use the user's configured `mono` font family by default.
2. Correct the odd `em` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
  font-family: ui-monospace, monospace;
  /* 1 */
  font-size: 1em;
  /* 2 */
}

/*
Add the correct font size in all browsers.
*/

small {
  font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
  text-indent: 0;
  /* 1 */
  border-color: inherit;
  /* 2 */
  border-collapse: collapse;
  /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
  font-family: inherit;
  /* 1 */
  font-size: 100%;
  /* 1 */
  line-height: inherit;
  /* 1 */
  color: inherit;
  /* 1 */
  margin: 0;
  /* 2 */
  padding: 0;
  /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
  text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
[type="button"],
[type="reset"],
[type="submit"] {
  -webkit-appearance: button;
  /* 1 */
  background-color: transparent;
  /* 2 */
  background-image: none;
  /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
  outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
  box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
  vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type="search"] {
  -webkit-appearance: textfield;
  /* 1 */
  outline-offset: -0.125rem;
  /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

::-webkit-file-upload-button {
  -webkit-appearance: button;
  /* 1 */
  font: inherit;
  /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
  display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
  resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::-moz-placeholder,
textarea::-moz-placeholder {
  opacity: 1;
  /* 1 */
  color: #9ca3af;
  /* 2 */
}

input:-ms-input-placeholder,
textarea:-ms-input-placeholder {
  opacity: 1;
  /* 1 */
  color: #9ca3af;
  /* 2 */
}

input::placeholder,
textarea::placeholder {
  opacity: 1;
  /* 1 */
  color: #9ca3af;
  /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role="button"] {
  cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/

:disabled {
  cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block;
  /* 1 */
  vertical-align: middle;
  /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}

/*
Ensure the default browser behavior of the `hidden` attribute.
*/

[hidden] {
  display: none;
}

*,
::before,
::after {
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x: ;
  --tw-pan-y: ;
  --tw-pinch-zoom: ;
  --tw-scroll-snap-strictness: proximity;
  --tw-ordinal: ;
  --tw-slashed-zero: ;
  --tw-numeric-figure: ;
  --tw-numeric-spacing: ;
  --tw-numeric-fraction: ;
  --tw-ring-inset: ;
  --tw-ring-offset-width: 0rem;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur: ;
  --tw-brightness: ;
  --tw-contrast: ;
  --tw-grayscale: ;
  --tw-hue-rotate: ;
  --tw-invert: ;
  --tw-saturate: ;
  --tw-sepia: ;
  --tw-drop-shadow: ;
  --tw-backdrop-blur: ;
  --tw-backdrop-brightness: ;
  --tw-backdrop-contrast: ;
  --tw-backdrop-grayscale: ;
  --tw-backdrop-hue-rotate: ;
  --tw-backdrop-invert: ;
  --tw-backdrop-opacity: ;
  --tw-backdrop-saturate: ;
  --tw-backdrop-sepia: ;
}

.container {
  width: 100%;
}

@media (min-width: 40rem) {
  .container {
    max-width: 40rem;
  }
}

@media (min-width: 48rem) {
  .container {
    max-width: 48rem;
  }
}

@media (min-width: 64rem) {
  .container {
    max-width: 64rem;
  }
}

@media (min-width: 80rem) {
  .container {
    max-width: 80rem;
  }
}

@media (min-width: 96rem) {
  .container {
    max-width: 96rem;
  }
}

:root {
  --bs-blue: #0d6efd;
  --bs-indigo: #6610f2;
  --bs-purple: #6f42c1;
  --bs-pink: #d63384;
  --bs-red: #dc3545;
  --bs-orange: #fd7e14;
  --bs-yellow: #ffc107;
  --bs-green: #198754;
  --bs-teal: #20c997;
  --bs-cyan: #0dcaf0;
  --bs-white: #fff;
  --bs-gray: #6c757d;
  --bs-gray-dark: #343a40;
  --bs-gray-100: #f8f9fa;
  --bs-gray-200: #e9ecef;
  --bs-gray-300: #dee2e6;
  --bs-gray-400: #ced4da;
  --bs-gray-500: #adb5bd;
  --bs-gray-600: #6c757d;
  --bs-gray-700: #495057;
  --bs-gray-800: #343a40;
  --bs-gray-900: #212529;
  --bs-primary: #0d6efd;
  --bs-secondary: #6c757d;
  --bs-success: #198754;
  --bs-info: #0dcaf0;
  --bs-warning: #ffc107;
  --bs-danger: #dc3545;
  --bs-light: #f8f9fa;
  --bs-dark: #212529;
  --bs-primary-rgb: 13, 110, 253;
  --bs-secondary-rgb: 108, 117, 125;
  --bs-success-rgb: 25, 135, 84;
  --bs-info-rgb: 13, 202, 240;
  --bs-warning-rgb: 255, 193, 7;
  --bs-danger-rgb: 220, 53, 69;
  --bs-light-rgb: 248, 249, 250;
  --bs-dark-rgb: 33, 37, 41;
  --bs-white-rgb: 255, 255, 255;
  --bs-black-rgb: 0, 0, 0;
  --bs-body-color-rgb: 33, 37, 41;
  --bs-body-bg-rgb: 255, 255, 255;
  --bs-font-sans-serif: system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", "Liberation Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol",
    "Noto Color Emoji";
  --bs-font-monospace: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  --bs-gradient: linear-gradient(180deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0));
  --bs-body-font-family: var(--bs-font-sans-serif);
  --bs-body-font-size: 16px;
  --bs-body-font-weight: 400;
  --bs-body-line-height: 1.5;
  --bs-body-color: #212529;
  --bs-body-bg: #fff;
}

.input-group:not(.has-validation) > .dropdown-toggle:nth-last-child(n + 3) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.input-group.has-validation > .dropdown-toggle:nth-last-child(n + 4) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.is-invalid ~ .invalid-feedback {
  display: block;
}

.is-invalid ~ .invalid-tooltip {
  display: block;
}

.form-control.is-invalid {
  border-color: #dc3545;
  padding-right: calc(1.5em + 12px);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right calc(0.375em + 3px) center;
  background-size: calc(0.75em + 6px) calc(0.75em + 6px);
}

.form-control.is-invalid:focus {
  border-color: #dc3545;
  box-shadow: 0 0 0 4px rgba(220, 53, 69, 0.25);
}

textarea.form-control.is-invalid {
  padding-right: calc(1.5em + 12px);
  background-position: top calc(0.375em + 3px) right calc(0.375em + 3px);
}

.form-select.is-invalid {
  border-color: #dc3545;
}

.form-select.is-invalid:not([multiple]):not([size]) {
  padding-right: 66px;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e"),
    url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
  background-position: right 12px center, center right 36px;
  background-size: 1rem .75rem, calc(0.75em + 6px) calc(0.75em + 6px);
}

.form-select.is-invalid:not([multiple])[size="1"] {
  padding-right: 66px;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e"),
    url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
  background-position: right 12px center, center right 36px;
  background-size: 1rem .75rem, calc(0.75em + 6px) calc(0.75em + 6px);
}

.form-select.is-invalid:focus {
  border-color: #dc3545;
  box-shadow: 0 0 0 4px rgba(220, 53, 69, 0.25);
}

.form-check-input.is-invalid {
  border-color: #dc3545;
}

.form-check-input.is-invalid:checked {
  background-color: #dc3545;
}

.form-check-input.is-invalid:focus {
  box-shadow: 0 0 0 4px rgba(220, 53, 69, 0.25);
}

.form-check-input.is-invalid ~ .form-check-label {
  color: #dc3545;
}

.input-group .form-control.is-invalid {
  z-index: 2;
}

.input-group .form-select.is-invalid {
  z-index: 2;
}

.input-group .form-control.is-invalid:focus {
  z-index: 3;
}

.input-group .form-select.is-invalid:focus {
  z-index: 3;
}

.btn.active {
  box-shadow: none;
}

.btn.active:focus {
  box-shadow: none;
}

.fade {
  transition: opacity 0.15s linear;
}

.fade:not(.show) {
  opacity: 0;
}

.collapse:not(.show) {
  display: none;
}

.collapsing {
  height: 0;
  overflow: hidden;
  transition: height 0.35s ease;
}

.collapsing.collapse-horizontal {
  width: 0;
  height: auto;
  transition: width 0.35s ease;
}

.dropdown-menu {
  z-index: 1000;
}

.dropdown-item.active {
  color: #1f2937;
  -webkit-text-decoration: none;
  text-decoration: none;
  background-color: #0d6efd;
}

.dropdown-item:active {
  color: #1f2937;
  -webkit-text-decoration: none;
  text-decoration: none;
  background-color: #0d6efd;
}

.dropdown-item:disabled {
  color: #adb5bd;
  pointer-events: none;
  background-color: transparent;
}

.dropdown-menu.show {
  display: block;
}

.dropdown-menu-dark .dropdown-item.active {
  color: #fff;
  background-color: #0d6efd;
}

.dropdown-menu-dark .dropdown-item:active {
  color: #fff;
  background-color: #0d6efd;
}

.dropdown-menu-dark .dropdown-item.disabled {
  color: #adb5bd;
}

.dropdown-menu-dark .dropdown-item:disabled {
  color: #adb5bd;
}

.nav-tabs .nav-link {
  color: #4b5563;
}

.nav-tabs .nav-link:hover {
  isolation: isolate;
}

.nav-tabs .nav-link:focus {
  isolation: isolate;
}

.nav-tabs .nav-link.disabled {
  color: #9ca3af;
  background-color: transparent;
  border-color: transparent;
}

.nav-tabs .nav-link.active {
  color: #2563eb;
  border-color: #2563eb;
}

.nav-tabs .nav-item.show .nav-link {
  color: #2563eb;
  border-color: #2563eb;
}

.nav-tabs .dropdown-menu {
  margin-top: -0.0625rem;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.nav-pills .nav-link {
  background: #f3f4f6;
  color: #4b5563;
  box-shadow: none;
}

.nav-pills .nav-link.active {
  background: #2563eb;
  color: #fff;
  box-shadow: 0 .25rem .375rem -0.0625rem rgba(0, 0, 0, 0.1), 0 .125rem .25rem -0.0625rem rgba(0, 0, 0, 0.06);
}

.nav-pills .show > .nav-link {
  background: #2563eb;
  color: #fff;
  box-shadow: 0 .25rem .375rem -0.0625rem rgba(0, 0, 0, 0.1), 0 .125rem .25rem -0.0625rem rgba(0, 0, 0, 0.06);
}

.nav-pills .disabled {
  color: #9ca3af;
  background-color: rgba(243, 244, 246, 0.5);
}

.nav-pills.menu-sidebar .nav-link {
  background-color: transparent;
  box-shadow: none;
  padding: 0 .3125rem;
  border-radius: 0;
}

.nav-pills.menu-sidebar .nav-link.active {
  color: #1266f1;
  font-weight: 600;
  border-left: 2px solid #1266f1;
}

.nav-justified > .nav-link {
  -ms-flex-basis: 0;
  flex-basis: 0;
}

.nav-justified .nav-item {
  -ms-flex-basis: 0;
  flex-basis: 0;
}

.tab-content > .active {
  display: block;
}

.navbar-expand .navbar-nav {
  flex-direction: row;
}

.navbar-expand .navbar-nav .dropdown-menu {
  position: absolute;
}

.navbar-expand .navbar-nav .nav-link {
  padding-right: 8px;
  padding-left: 8px;
}

.navbar-expand .offcanvas {
  position: inherit;
  bottom: 0;
  z-index: 1000;
  -ms-flex-grow: 1;
  flex-grow: 1;
  visibility: visible !important;
  background-color: transparent;
  border-right: 0;
  border-left: 0;
  transition: none;
  transform: none;
}

.navbar-light .navbar-nav .nav-link.disabled {
  color: rgba(0, 0, 0, 0.3);
}

.navbar-light .navbar-nav .show > .nav-link {
  color: rgba(0, 0, 0, 0.9);
}

.navbar-light .navbar-nav .nav-link.active {
  color: rgba(0, 0, 0, 0.9);
}

.navbar-dark .navbar-nav .nav-link.disabled {
  color: rgba(255, 255, 255, 0.25);
}

.navbar-dark .navbar-nav .show > .nav-link {
  color: #fff;
}

.navbar-dark .navbar-nav .nav-link.active {
  color: #fff;
}

.accordion-item:last-of-type .accordion-button.collapsed {
  border-bottom-right-radius: calc(8px - .0625rem);
  border-bottom-left-radius: calc(8px - .0625rem);
}

.btn-close.disabled {
  pointer-events: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  opacity: 0.25;
}

.modal {
  z-index: 1055;
}

.modal-dialog {
  margin: 8px;
}

.modal.fade .modal-dialog {
  transition: transform 0.3s ease-out;
  transform: translate(0, -3.125rem);
}

.modal.show .modal-dialog {
  transform: none;
}

.modal.modal-static .modal-dialog {
  transform: scale(1.02);
}

.modal-dialog-scrollable .modal-body {
  overflow-y: auto;
}

.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1050;
  width: 100vw;
  height: 100vh;
  background-color: #000;
}

.modal-backdrop.fade {
  opacity: 0;
}

.modal-backdrop.show {
  opacity: 0.5;
}

.modal-body {
  flex: 1 1 auto;
}

.modal-fullscreen .modal-body {
  overflow-y: auto;
}

.tooltip {
  position: absolute;
  z-index: 1080;
  display: block;
  margin: 0;
  font-family: var(--bs-font-sans-serif);
  font-style: normal;
  font-weight: 400;
  line-height: 1.5;
  -webkit-text-align: start;
  text-align: start;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-text-shadow: none;
  text-shadow: none;
  -webkit-text-transform: none;
  text-transform: none;
  letter-spacing: normal;
  word-break: normal;
  word-spacing: normal;
  white-space: normal;
  line-break: auto;
  font-size: 14px;
  word-wrap: break-word;
  opacity: 0;
}

.tooltip.show {
  opacity: 1;
}

.bs-tooltip-top .tooltip-arrow {
  bottom: 0;
}

.bs-tooltip-auto[data-popper-placement^="top"] .tooltip-arrow {
  bottom: 0;
}

.bs-tooltip-top .tooltip-arrow::before {
  top: -0.0625rem;
  border-width: 6.4px 6.4px 0;
  border-top-color: #000;
}

.bs-tooltip-auto[data-popper-placement^="top"] .tooltip-arrow::before {
  top: -0.0625rem;
  border-width: 6.4px 6.4px 0;
  border-top-color: #000;
}

.bs-tooltip-end .tooltip-arrow {
  left: 0;
  width: 6.4px;
  height: 12.8px;
}

.bs-tooltip-auto[data-popper-placement^="right"] .tooltip-arrow {
  left: 0;
  width: 6.4px;
  height: 12.8px;
}

.bs-tooltip-end .tooltip-arrow::before {
  right: -0.0625rem;
  border-width: 6.4px 6.4px 6.4px 0;
  border-right-color: #000;
}

.bs-tooltip-auto[data-popper-placement^="right"] .tooltip-arrow::before {
  right: -0.0625rem;
  border-width: 6.4px 6.4px 6.4px 0;
  border-right-color: #000;
}

.bs-tooltip-bottom .tooltip-arrow {
  top: 0;
}

.bs-tooltip-auto[data-popper-placement^="bottom"] .tooltip-arrow {
  top: 0;
}

.bs-tooltip-bottom .tooltip-arrow::before {
  bottom: -0.0625rem;
  border-width: 0 6.4px 6.4px;
  border-bottom-color: #000;
}

.bs-tooltip-auto[data-popper-placement^="bottom"] .tooltip-arrow::before {
  bottom: -0.0625rem;
  border-width: 0 6.4px 6.4px;
  border-bottom-color: #000;
}

.bs-tooltip-start .tooltip-arrow {
  right: 0;
  width: 6.4px;
  height: 12.8px;
}

.bs-tooltip-auto[data-popper-placement^="left"] .tooltip-arrow {
  right: 0;
  width: 6.4px;
  height: 12.8px;
}

.bs-tooltip-start .tooltip-arrow::before {
  left: -0.0625rem;
  border-width: 6.4px 0 6.4px 6.4px;
  border-left-color: #000;
}

.bs-tooltip-auto[data-popper-placement^="left"] .tooltip-arrow::before {
  left: -0.0625rem;
  border-width: 6.4px 0 6.4px 6.4px;
  border-left-color: #000;
}

.tooltip-inner {
  max-width: 12.5rem;
  font-size: .875rem;
  padding: .375rem 1rem;
  color: #fff;
  -webkit-text-align: center;
  text-align: center;
  background-color: #6d6d6d;
  border-radius: 4px;
}

.popover {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1070;
  display: block;
  max-width: 17.25rem;
  font-family: var(--bs-font-sans-serif);
  font-style: normal;
  font-weight: 400;
  line-height: 1.5;
  -webkit-text-align: start;
  text-align: start;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-text-shadow: none;
  text-shadow: none;
  -webkit-text-transform: none;
  text-transform: none;
  letter-spacing: normal;
  word-break: normal;
  word-spacing: normal;
  white-space: normal;
  line-break: auto;
  font-size: 14px;
  word-wrap: break-word;
  background-color: #fff;
  background-clip: padding-box;
  border: 0;
  border-radius: 8px;
  box-shadow: 0 .625rem .9375rem -0.1875rem rgba(0, 0, 0, 0.1), 0 .25rem .375rem -0.125rem rgba(0, 0, 0, 0.05);
}

.bs-popover-top > .popover-arrow {
  bottom: calc(-8px - .0625rem);
}

.bs-popover-auto[data-popper-placement^="top"] > .popover-arrow {
  bottom: calc(-8px - .0625rem);
}

.bs-popover-top > .popover-arrow::before {
  bottom: 0;
  border-width: 8px 8px 0;
  border-top-color: rgba(0, 0, 0, 0.25);
}

.bs-popover-auto[data-popper-placement^="top"] > .popover-arrow::before {
  bottom: 0;
  border-width: 8px 8px 0;
  border-top-color: rgba(0, 0, 0, 0.25);
}

.bs-popover-top > .popover-arrow::after {
  bottom: .0625rem;
  border-width: 8px 8px 0;
  border-top-color: #fff;
}

.bs-popover-auto[data-popper-placement^="top"] > .popover-arrow::after {
  bottom: .0625rem;
  border-width: 8px 8px 0;
  border-top-color: #fff;
}

.bs-popover-end > .popover-arrow {
  left: calc(-8px - .0625rem);
  width: 8px;
  height: 16px;
}

.bs-popover-auto[data-popper-placement^="right"] > .popover-arrow {
  left: calc(-8px - .0625rem);
  width: 8px;
  height: 16px;
}

.bs-popover-end > .popover-arrow::before {
  left: 0;
  border-width: 8px 8px 8px 0;
  border-right-color: rgba(0, 0, 0, 0.25);
}

.bs-popover-auto[data-popper-placement^="right"] > .popover-arrow::before {
  left: 0;
  border-width: 8px 8px 8px 0;
  border-right-color: rgba(0, 0, 0, 0.25);
}

.bs-popover-end > .popover-arrow::after {
  left: .0625rem;
  border-width: 8px 8px 8px 0;
  border-right-color: #fff;
}

.bs-popover-auto[data-popper-placement^="right"] > .popover-arrow::after {
  left: .0625rem;
  border-width: 8px 8px 8px 0;
  border-right-color: #fff;
}

.bs-popover-bottom > .popover-arrow {
  top: calc(-8px - .0625rem);
}

.bs-popover-auto[data-popper-placement^="bottom"] > .popover-arrow {
  top: calc(-8px - .0625rem);
}

.bs-popover-bottom > .popover-arrow::before {
  top: 0;
  border-width: 0 8px 8px 8px;
  border-bottom-color: rgba(0, 0, 0, 0.25);
}

.bs-popover-auto[data-popper-placement^="bottom"] > .popover-arrow::before {
  top: 0;
  border-width: 0 8px 8px 8px;
  border-bottom-color: rgba(0, 0, 0, 0.25);
}

.bs-popover-bottom > .popover-arrow::after {
  top: .0625rem;
  border-width: 0 8px 8px 8px;
  border-bottom-color: #fff;
}

.bs-popover-auto[data-popper-placement^="bottom"] > .popover-arrow::after {
  top: .0625rem;
  border-width: 0 8px 8px 8px;
  border-bottom-color: #fff;
}

.bs-popover-bottom .popover-header::before {
  position: absolute;
  top: 0;
  left: 50%;
  display: block;
  width: 16px;
  margin-left: -8px;
  content: "";
  border-bottom: .0625rem solid #f0f0f0;
}

.bs-popover-auto[data-popper-placement^="bottom"] .popover-header::before {
  position: absolute;
  top: 0;
  left: 50%;
  display: block;
  width: 16px;
  margin-left: -8px;
  content: "";
  border-bottom: .0625rem solid #f0f0f0;
}

.bs-popover-start > .popover-arrow {
  right: calc(-8px - .0625rem);
  width: 8px;
  height: 16px;
}

.bs-popover-auto[data-popper-placement^="left"] > .popover-arrow {
  right: calc(-8px - .0625rem);
  width: 8px;
  height: 16px;
}

.bs-popover-start > .popover-arrow::before {
  right: 0;
  border-width: 8px 0 8px 8px;
  border-left-color: rgba(0, 0, 0, 0.25);
}

.bs-popover-auto[data-popper-placement^="left"] > .popover-arrow::before {
  right: 0;
  border-width: 8px 0 8px 8px;
  border-left-color: rgba(0, 0, 0, 0.25);
}

.bs-popover-start > .popover-arrow::after {
  right: .0625rem;
  border-width: 8px 0 8px 8px;
  border-left-color: #fff;
}

.bs-popover-auto[data-popper-placement^="left"] > .popover-arrow::after {
  right: .0625rem;
  border-width: 8px 0 8px 8px;
  border-left-color: #fff;
}

.popover-header {
  padding: 8px 16px;
  margin-bottom: 0;
  font-size: 16px;
  background-color: #fff;
  border-bottom: .0625rem solid rgba(0, 0, 0, 0.2);
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  font-weight: 500;
}

.popover-header:empty {
  display: none;
}

.popover-body {
  padding: 16px 16px;
  color: #212529;
}

.carousel.pointer-event {
  touch-action: pan-y;
}

.carousel-item {
  display: none;
  margin-right: -100%;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  transition: transform 0.6s ease-in-out;
}

.carousel-item.active {
  display: block;
}

.carousel-item-next {
  display: block;
}

.carousel-item-prev {
  display: block;
}

.carousel-item-next:not(.carousel-item-start) {
  transform: translateX(100%);
}

.active.carousel-item-end {
  transform: translateX(100%);
}

.carousel-item-prev:not(.carousel-item-end) {
  transform: translateX(-100%);
}

.active.carousel-item-start {
  transform: translateX(-100%);
}

.carousel-fade .carousel-item {
  opacity: 0;
  transition-property: opacity;
  transform: none;
}

.carousel-fade .carousel-item.active {
  z-index: 1;
  opacity: 1;
}

.carousel-fade .carousel-item-next.carousel-item-start {
  z-index: 1;
  opacity: 1;
}

.carousel-fade .carousel-item-prev.carousel-item-end {
  z-index: 1;
  opacity: 1;
}

.carousel-fade .active.carousel-item-start {
  z-index: 0;
  opacity: 0;
  transition: opacity 0s 0.6s;
}

.carousel-fade .active.carousel-item-end {
  z-index: 0;
  opacity: 0;
  transition: opacity 0s 0.6s;
}

.carousel-indicators {
  z-index: 2;
  margin-right: 15%;
  margin-left: 15%;
  list-style: none;
}

.carousel-indicators [data-bs-target] {
  box-sizing: content-box;
  flex: 0 1 auto;
  width: 1.875rem;
  height: .1875rem;
  padding: 0;
  margin-right: .1875rem;
  margin-left: .1875rem;
  -webkit-text-indent: -62.4375rem;
  text-indent: -62.4375rem;
  cursor: pointer;
  background-color: #fff;
  background-clip: padding-box;
  border: 0;
  border-top: .625rem solid transparent;
  border-bottom: .625rem solid transparent;
  opacity: 0.5;
  transition: opacity 0.6s ease;
}

.carousel-indicators .active {
  opacity: 1;
}

.carousel-dark .carousel-indicators [data-bs-target] {
  background-color: #000;
}

.offcanvas {
  z-index: 1045;
}

.offcanvas-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1040;
  width: 100vw;
  height: 100vh;
  background-color: #000;
}

.offcanvas-backdrop.fade {
  opacity: 0;
}

.offcanvas-backdrop.show {
  opacity: 0.5;
}

.offcanvas.show {
  transform: none;
}

.sticky-top {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 1020;
}

.vr {
  display: inline-block;
  align-self: stretch;
  width: .0625rem;
  min-height: 1em;
  background-color: currentColor;
  opacity: 0.25;
}

.animation {
  -webkit-animation-duration: 1s;
  animation-duration: 1s;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
  padding: auto;
}

.fade-in {
  -webkit-animation-name: _fade-in;
  animation-name: _fade-in;
}

.fade-out {
  -webkit-animation-name: _fade-out;
  animation-name: _fade-out;
}

.animation.infinite {
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
}

.animation.delay-1s {
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}

.animation.delay-2s {
  -webkit-animation-delay: 2s;
  animation-delay: 2s;
}

.animation.delay-3s {
  -webkit-animation-delay: 3s;
  animation-delay: 3s;
}

.animation.delay-4s {
  -webkit-animation-delay: 4s;
  animation-delay: 4s;
}

.animation.delay-5s {
  -webkit-animation-delay: 5s;
  animation-delay: 5s;
}

.animation.fast {
  -webkit-animation-duration: 800ms;
  animation-duration: 800ms;
}

.animation.faster {
  -webkit-animation-duration: 500ms;
  animation-duration: 500ms;
}

.animation.slow {
  -webkit-animation-duration: 2s;
  animation-duration: 2s;
}

.animation.slower {
  -webkit-animation-duration: 3s;
  animation-duration: 3s;
}

.slide-in-left {
  -webkit-animation-name: _slide-in-left;
  animation-name: _slide-in-left;
}

.slide-in-right {
  -webkit-animation-name: _slide-in-right;
  animation-name: _slide-in-right;
}

.slide-out-left {
  -webkit-animation-name: _slide-out-left;
  animation-name: _slide-out-left;
}

.slide-out-right {
  -webkit-animation-name: _slide-out-right;
  animation-name: _slide-out-right;
}

.ripple-surface {
  position: relative;
  overflow: hidden;
  display: inline-block;
  vertical-align: bottom;
}

.ripple-surface-unbound {
  overflow: visible;
}

.ripple-wave {
  background-image: radial-gradient(circle, rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, transparent 70%);
  border-radius: 50%;
  opacity: 0.5;
  pointer-events: none;
  position: absolute;
  touch-action: none;
  transform: scale(0);
  transition-property: transform, opacity;
  transition-timing-function: cubic-bezier(0, 0, 0.15, 1), cubic-bezier(0, 0, 0.15, 1);
  z-index: 999;
}

.ripple-wave.active {
  transform: scale(1);
  opacity: 0;
}

.btn .ripple-wave {
  background-image: radial-gradient(circle, rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);
}

.ripple-surface-primary .ripple-wave {
  background-image: radial-gradient(circle, rgba(18, 102, 241, 0.2) 0, rgba(18, 102, 241, 0.3) 40%, rgba(18, 102, 241, 0.4) 50%, rgba(18, 102, 241, 0.5) 60%, rgba(18, 102, 241, 0) 70%);
}

.ripple-surface-secondary .ripple-wave {
  background-image: radial-gradient(circle, rgba(178, 60, 253, 0.2) 0, rgba(178, 60, 253, 0.3) 40%, rgba(178, 60, 253, 0.4) 50%, rgba(178, 60, 253, 0.5) 60%, rgba(178, 60, 253, 0) 70%);
}

.ripple-surface-success .ripple-wave {
  background-image: radial-gradient(circle, rgba(0, 183, 74, 0.2) 0, rgba(0, 183, 74, 0.3) 40%, rgba(0, 183, 74, 0.4) 50%, rgba(0, 183, 74, 0.5) 60%, rgba(0, 183, 74, 0) 70%);
}

.ripple-surface-info .ripple-wave {
  background-image: radial-gradient(circle, rgba(57, 192, 237, 0.2) 0, rgba(57, 192, 237, 0.3) 40%, rgba(57, 192, 237, 0.4) 50%, rgba(57, 192, 237, 0.5) 60%, rgba(57, 192, 237, 0) 70%);
}

.ripple-surface-warning .ripple-wave {
  background-image: radial-gradient(circle, rgba(255, 169, 0, 0.2) 0, rgba(255, 169, 0, 0.3) 40%, rgba(255, 169, 0, 0.4) 50%, rgba(255, 169, 0, 0.5) 60%, rgba(255, 169, 0, 0) 70%);
}

.ripple-surface-danger .ripple-wave {
  background-image: radial-gradient(circle, rgba(249, 49, 84, 0.2) 0, rgba(249, 49, 84, 0.3) 40%, rgba(249, 49, 84, 0.4) 50%, rgba(249, 49, 84, 0.5) 60%, rgba(249, 49, 84, 0) 70%);
}

.ripple-surface-light .ripple-wave {
  background-image: radial-gradient(circle, rgba(251, 251, 251, 0.2) 0, rgba(251, 251, 251, 0.3) 40%, rgba(251, 251, 251, 0.4) 50%, rgba(251, 251, 251, 0.5) 60%, rgba(251, 251, 251, 0) 70%);
}

.ripple-surface-dark .ripple-wave {
  background-image: radial-gradient(circle, rgba(38, 38, 38, 0.2) 0, rgba(38, 38, 38, 0.3) 40%, rgba(38, 38, 38, 0.4) 50%, rgba(38, 38, 38, 0.5) 60%, rgba(38, 38, 38, 0) 70%);
}

.ripple-surface-white .ripple-wave {
  background-image: radial-gradient(circle, rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);
}

.ripple-surface-black .ripple-wave {
  background-image: radial-gradient(circle, rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, transparent 70%);
}

.datepicker-toggle-button {
  position: absolute;
  outline: none;
  border: none;
  background-color: transparent;
  right: .625rem;
  top: 50%;
  transform: translate(-50%, -50%);
}

.datepicker-toggle-button:focus {
  color: #2979ff;
}

.datepicker-toggle-button:hover {
  color: #2979ff;
}

.datepicker-backdrop {
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.4);
  z-index: 1065;
}

.datepicker-dropdown-container {
  width: 20.5rem;
  height: 23.75rem;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 .625rem .9375rem -0.1875rem rgba(0, 0, 0, 0.07), 0 .25rem .375rem -0.125rem rgba(0, 0, 0, 0.05);
  z-index: 1066;
}

.datepicker-modal-container {
  display: flex;
  flex-direction: column;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20.5rem;
  height: 32rem;
  background-color: #fff;
  border-radius: 9.6px 9.6px 8px 8px;
  box-shadow: 0 .625rem .9375rem -0.1875rem rgba(0, 0, 0, 0.07), 0 .25rem .375rem -0.125rem rgba(0, 0, 0, 0.05);
  z-index: 1066;
}

.datepicker-header {
  height: 7.5rem;
  padding-right: 1.5rem;
  padding-left: 1.5rem;
  background-color: #2979ff;
  display: flex;
  flex-direction: column;
  border-radius: 8px 8px 0 0;
}

.datepicker-title {
  height: 2rem;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}

.datepicker-title-text {
  font-size: .625rem;
  font-weight: 400;
  -webkit-text-transform: uppercase;
  text-transform: uppercase;
  letter-spacing: .1062rem;
  color: #fff;
}

.datepicker-date {
  height: 4.5rem;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}

.datepicker-date-text {
  font-size: 2.125rem;
  font-weight: 400;
  color: #fff;
}

.datepicker-main {
  position: relative;
  height: 100%;
}

.datepicker-date-controls {
  padding: .625rem .75rem 0 .75rem;
  display: flex;
  justify-content: space-between;
  color: rgba(0, 0, 0, 0.64);
}

.datepicker-view-change-button {
  padding: .625rem;
  color: #666;
  font-weight: 500;
  font-size: 14.4px;
  border-radius: .625rem;
  box-shadow: none;
  background-color: transparent;
  margin: 0;
  border: none;
}

.datepicker-view-change-button:hover {
  background-color: #eee;
}

.datepicker-view-change-button:focus {
  background-color: #eee;
}

.datepicker-view-change-button:after {
  content: "";
  display: inline-block;
  width: 0;
  height: 0;
  border-left: .3125rem solid transparent;
  border-right: .3125rem solid transparent;
  border-top-width: .3125rem;
  border-top-style: solid;
  margin: 0 0 0 .3125rem;
  vertical-align: middle;
}

.datepicker-arrow-controls {
  margin-top: .625rem;
}

.datepicker-previous-button {
  position: relative;
  padding: 0;
  width: 2.5rem;
  height: 2.5rem;
  line-height: 2.5rem;
  border: none;
  outline: none;
  margin: 0;
  color: rgba(0, 0, 0, 0.64);
  background-color: transparent;
  margin-right: 1.5rem;
}

.datepicker-previous-button:hover {
  background-color: #eee;
  border-radius: 50%;
}

.datepicker-previous-button:focus {
  background-color: #eee;
  border-radius: 50%;
}

.datepicker-previous-button::after {
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  position: absolute;
  content: "";
  margin: .9688rem;
  border: 0 solid currentColor;
  border-top-width: .125rem;
  border-left-width: .125rem;
  transform: translateX(.125rem) rotate(-45deg);
}

.datepicker-next-button {
  position: relative;
  padding: 0;
  width: 2.5rem;
  height: 2.5rem;
  line-height: 2.5rem;
  border: none;
  outline: none;
  margin: 0;
  color: rgba(0, 0, 0, 0.64);
  background-color: transparent;
}

.datepicker-next-button:hover {
  background-color: #eee;
  border-radius: 50%;
}

.datepicker-next-button:focus {
  background-color: #eee;
  border-radius: 50%;
}

.datepicker-next-button::after {
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  position: absolute;
  content: "";
  margin: .9688rem;
  border: 0 solid currentColor;
  border-top-width: .125rem;
  border-right-width: .125rem;
  transform: translateX(-0.125rem) rotate(45deg);
}

.datepicker-view {
  padding-left: .75rem;
  padding-right: .75rem;
  outline: none;
}

.datepicker-table {
  margin-right: auto;
  margin-left: auto;
  width: 19rem;
}

.datepicker-day-heading {
  width: 2.5rem;
  height: 2.5rem;
  -webkit-text-align: center;
  text-align: center;
  font-size: .75rem;
  font-weight: 400;
}

.datepicker-cell {
  -webkit-text-align: center;
  text-align: center;
}

.datepicker-cell.disabled {
  color: #ccc;
  cursor: default;
  pointer-events: none;
}

.datepicker-cell.disabled:hover {
  cursor: default;
}

.datepicker-cell:hover {
  cursor: pointer;
}

.datepicker-cell:not(.disabled):not(.selected):hover .datepicker-cell-content {
  background-color: #d3d3d3;
}

.datepicker-cell.selected .datepicker-cell-content {
  background-color: #2979ff;
  color: #fff;
}

.datepicker-cell:not(.selected).focused .datepicker-cell-content {
  background-color: #eee;
}

.datepicker-cell.focused .datepicker-cell-content.selected {
  background-color: #2979ff;
}

.datepicker-cell.current .datepicker-cell-content {
  border: .0625rem solid #000;
}

.datepicker-small-cell {
  width: 2.5rem;
  height: 2.5rem;
}

.datepicker-small-cell-content {
  width: 2.25rem;
  height: 2.25rem;
  line-height: 2.25rem;
  border-radius: 50%;
  font-size: .8125rem;
}

.datepicker-large-cell {
  width: 4.75rem;
  height: 2.625rem;
}

.datepicker-large-cell-content {
  width: 4.5rem;
  height: 2.5rem;
  line-height: 2.5rem;
  padding: .0625rem .125rem;
  border-radius: 62.4375rem;
}

.datepicker-footer {
  height: 3.5rem;
  display: flex;
  position: absolute;
  width: 100%;
  bottom: 0;
  justify-content: flex-end;
  align-items: center;
  padding-left: .75rem;
  padding-right: .75rem;
}

.datepicker-footer-btn {
  background-color: #fff;
  color: #2979ff;
  border: none;
  cursor: pointer;
  padding: 0 .625rem;
  -webkit-text-transform: uppercase;
  text-transform: uppercase;
  font-size: 12.8px;
  font-weight: 500;
  height: 2.5rem;
  line-height: 2.5rem;
  letter-spacing: 1.6px;
  border-radius: .625rem;
  margin-bottom: .625rem;
}

.datepicker-footer-btn:hover {
  background-color: #eee;
}

.datepicker-footer-btn:focus {
  background-color: #eee;
}

.datepicker-clear-btn {
  margin-right: auto;
}

.timepicker-wrapper {
  touch-action: none;
  z-index: 1065;
  opacity: 0;
  right: 0;
  bottom: 0;
  top: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.4);
}

.timepicker-elements {
  min-width: 19.375rem;
  min-height: 20.3125rem;
  background: #fff;
  border-top-right-radius: 9.6px;
  border-top-left-radius: 9.6px;
}

.timepicker-head {
  background-color: #2979ff;
  height: 6.25rem;
  border-top-right-radius: 8px;
  border-top-left-radius: 8px;
  padding: .625rem 1.5rem .625rem 3.125rem;
}

.timepicker-button {
  font-size: 12.8px;
  min-width: 4rem;
  box-sizing: border-box;
  font-weight: 500;
  line-height: 2.5rem;
  border-radius: .625rem;
  letter-spacing: 1.6px;
  -webkit-text-transform: uppercase;
  text-transform: uppercase;
  color: #2979ff;
  border: none;
  background-color: transparent;
  transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, box-shadow 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, border 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  outline: none;
  padding: 0 .625rem;
  height: 2.5rem;
  margin-bottom: .625rem;
}

.timepicker-button:hover {
  background-color: rgba(0, 0, 0, 0.08);
}

.timepicker-button:focus {
  outline: none;
  background-color: rgba(0, 0, 0, 0.08);
}

.timepicker-current {
  font-size: 60px;
  font-weight: 300;
  line-height: 1.2;
  letter-spacing: -0.00833em;
  color: #fff;
  opacity: 0.54;
  border: none;
  background: transparent;
  padding: 0;
}

.timepicker-current.active {
  opacity: 1;
}

.timepicker-current-wrapper {
  direction: ltr;
}

.timepicker-mode-wrapper {
  margin-left: 1.25rem;
  font-size: 1.125rem;
  color: rgba(255, 255, 255, 0.54);
}

.timepicker-mode-wrapper.active {
  opacity: 1;
}

.timepicker-clock-wrapper {
  min-width: 19.375rem;
  max-width: 20.3125rem;
  min-height: 19.0625rem;
  overflow-x: hidden;
  height: 100%;
}

.timepicker-clock {
  position: relative;
  border-radius: 100%;
  width: 16.25rem;
  height: 16.25rem;
  cursor: default;
  margin: 0 auto;
  background-color: rgba(0, 0, 0, 0.07);
}

.timepicker-time-tips-minutes.active {
  color: #fff;
  background-color: #2979ff;
  font-weight: 400;
}

.timepicker-time-tips-inner.active {
  color: #fff;
  background-color: #2979ff;
  font-weight: 400;
}

.timepicker-time-tips-hours.active {
  color: #fff;
  background-color: #2979ff;
  font-weight: 400;
}

.timepicker-time-tips-minutes.disabled {
  color: #b3afaf;
  pointer-events: none;
  background-color: transparent;
}

.timepicker-time-tips-inner.disabled {
  color: #b3afaf;
  pointer-events: none;
  background-color: transparent;
}

.timepicker-time-tips-hours.disabled {
  color: #b3afaf;
  pointer-events: none;
  background-color: transparent;
}

.timepicker-dot {
  font-weight: 300;
  line-height: 1.2;
  letter-spacing: -0.00833em;
  color: #fff;
  font-size: 60px;
  opacity: 0.54;
  border: none;
  background: transparent;
  padding: 0;
}

.timepicker-middle-dot {
  top: 50%;
  left: 50%;
  width: .375rem;
  height: .375rem;
  transform: translate(-50%, -50%);
  border-radius: 50%;
  background-color: #2979ff;
}

.timepicker-hand-pointer {
  background-color: #2979ff;
  bottom: 50%;
  height: 40%;
  left: calc(50% - .0625rem);
  transform-origin: center bottom 0;
  width: .125rem;
}

.timepicker-time-tips.active {
  color: #fff;
}

.timepicker-circle {
  top: -1.3125rem;
  left: -0.9375rem;
  width: .25rem;
  border: .875rem solid #2979ff;
  height: .25rem;
  box-sizing: content-box;
  border-radius: 100%;
}

.timepicker-hour-mode {
  padding: 0;
  background-color: transparent;
  border: none;
  color: #fff;
  opacity: 0.54;
  cursor: pointer;
}

.timepicker-hour {
  cursor: pointer;
}

.timepicker-minute {
  cursor: pointer;
}

.timepicker-hour-mode:hover {
  background-color: rgba(0, 0, 0, 0.15);
  outline: none;
}

.timepicker-hour-mode:focus {
  background-color: rgba(0, 0, 0, 0.15);
  outline: none;
}

.timepicker-hour:hover {
  background-color: rgba(0, 0, 0, 0.15);
  outline: none;
}

.timepicker-hour:focus {
  background-color: rgba(0, 0, 0, 0.15);
  outline: none;
}

.timepicker-minute:hover {
  background-color: rgba(0, 0, 0, 0.15);
  outline: none;
}

.timepicker-minute:focus {
  background-color: rgba(0, 0, 0, 0.15);
  outline: none;
}

.timepicker-hour-mode.active {
  color: #fff;
  opacity: 1;
}

.timepicker-hour.active {
  color: #fff;
  opacity: 1;
}

.timepicker-minute.active {
  color: #fff;
  opacity: 1;
}

.timepicker-footer {
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 3.5rem;
  padding-left: .75rem;
  padding-right: .75rem;
  background-color: #fff;
}

.timepicker-container {
  max-height: calc(100% - 4rem);
  overflow-y: auto;
  box-shadow: 0 .625rem .9375rem -0.1875rem rgba(0, 0, 0, 0.07), 0 .25rem .375rem -0.125rem rgba(0, 0, 0, 0.05);
}

.timepicker-icon-up.active {
  opacity: 1;
}

.timepicker-icon-down.active {
  opacity: 1;
}

.timepicker-toggle-button {
  position: absolute;
  outline: none;
  border: none;
  background-color: transparent;
  right: .625rem;
  top: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.3s ease;
  cursor: pointer;
}

.timepicker-toggle-button:hover {
  color: #2979ff;
}

.timepicker-toggle-button:focus {
  color: #2979ff;
}

.timepicker-input:focus + .timepicker-toggle-button {
  color: #2979ff;
}

.timepicker-input:focus + .timepicker-toggle-button i {
  color: #2979ff;
}

.timepicker a.timepicker-toggle-button {
  right: .0625rem;
}

.timepicker-toggle-button.timepicker-icon {
  right: .0625rem;
}

.timepicker-modal .fade.show {
  opacity: 1;
}

.stepper {
  position: relative;
  padding: 0;
  margin: 0;
  width: 100%;
  list-style: none;
  overflow: hidden;
  transition: height 0.2s ease-in-out;
}

.stepper:not(.stepper-vertical) {
  display: flex;
  justify-content: space-between;
}

.stepper:not(.stepper-vertical) .stepper-content {
  position: absolute;
  width: 100%;
  padding: 16px;
}

.stepper:not(.stepper-vertical) .stepper-step {
  flex: auto;
  height: 72px;
}

.stepper:not(.stepper-vertical) .stepper-step:first-child .stepper-head {
  padding-left: 24px;
}

.stepper:not(.stepper-vertical) .stepper-step:last-child .stepper-head {
  padding-right: 24px;
}

.stepper:not(.stepper-vertical) .stepper-step:not(:first-child) .stepper-head:before {
  flex: 1;
  height: .0625rem;
  width: 100%;
  margin-right: 8px;
  content: "";
  background-color: rgba(0, 0, 0, 0.1);
}

.stepper:not(.stepper-vertical) .stepper-step:not(:last-child) .stepper-head:after {
  flex: 1;
  height: .0625rem;
  width: 100%;
  margin-left: 8px;
  content: "";
  background-color: rgba(0, 0, 0, 0.1);
}

.stepper:not(.stepper-vertical) .stepper-head-icon {
  margin: 24px 8px 24px 0;
}

.stepper.stepper-mobile {
  justify-content: center;
  align-items: flex-end;
}

.stepper.stepper-mobile.stepper-progress-bar .stepper-head-icon {
  display: none;
}

.stepper.stepper-mobile .stepper-step {
  flex: unset;
  height: -webkit-fit-content;
  height: -moz-fit-content;
  height: fit-content;
  margin: 16px 0 16px 0;
}

.stepper.stepper-mobile .stepper-step:not(:last-child) .stepper-head:after {
  margin-left: 0;
}

.stepper.stepper-mobile .stepper-step:not(:first-child) .stepper-head:before {
  margin-right: 0;
}

.stepper.stepper-mobile .stepper-step:not(:last-child):not(:first-child) .stepper-head {
  padding-left: 4px;
  padding-right: 4px;
}

.stepper.stepper-mobile .stepper-head-icon {
  font-size: 0;
  margin: 0;
  height: 8px;
  width: 8px;
  z-index: 1;
}

.stepper.stepper-mobile .stepper-head-text {
  display: none;
}

.stepper.stepper-mobile .stepper-content {
  top: 40.96px;
}

@media (prefers-reduced-motion: reduce) {
  .form-control::-webkit-file-upload-button {
    -webkit-transition: none;
    transition: none;
  }
  .form-control::file-selector-button {
    transition: none;
  }

  .form-control::-webkit-file-upload-button {
    -webkit-transition: none;
    transition: none;
  }

  .form-switch .form-check-input {
    transition: none;
  }

  .form-range::-webkit-slider-thumb {
    -webkit-transition: none;
    transition: none;
  }

  .form-range::-moz-range-thumb {
    -moz-transition: none;
    transition: none;
  }

  .form-floating > label {
    transition: none;
  }

  .fade {
    transition: none;
  }

  .collapsing {
    transition: none;
  }

  .collapsing.collapse-horizontal {
    transition: none;
  }

  .accordion-button::after {
    transition: none;
  }

  .modal.fade .modal-dialog {
    transition: none;
  }

  .carousel-item {
    transition: none;
  }

  .carousel-fade .active.carousel-item-start {
    transition: none;
  }

  .carousel-fade .active.carousel-item-end {
    transition: none;
  }

  .carousel-control-prev {
    transition: none;
  }

  .carousel-control-next {
    transition: none;
  }

  .carousel-indicators [data-bs-target] {
    transition: none;
  }

  .spinner-border {
    -webkit-animation-duration: 1.5s;
    animation-duration: 1.5s;
  }

  .spinner-grow {
    -webkit-animation-duration: 1.5s;
    animation-duration: 1.5s;
  }
}

@media (min-width: 36rem) {
  .navbar-expand-sm {
    flex-wrap: nowrap;
    justify-content: flex-start;
  }

  .navbar-expand-sm .navbar-nav {
    flex-direction: row;
  }

  .navbar-expand-sm .navbar-nav .dropdown-menu {
    position: absolute;
  }

  .navbar-expand-sm .navbar-nav .nav-link {
    padding-right: 8px;
    padding-left: 8px;
  }

  .navbar-expand-sm .navbar-nav-scroll {
    overflow: visible;
  }

  .navbar-expand-sm .navbar-collapse {
    display: flex !important;
    -ms-flex-basis: auto;
    flex-basis: auto;
  }

  .navbar-expand-sm .navbar-toggler {
    display: none;
  }

  .navbar-expand-sm .offcanvas-header {
    display: none;
  }

  .navbar-expand-sm .offcanvas {
    position: inherit;
    bottom: 0;
    z-index: 1000;
    -ms-flex-grow: 1;
    flex-grow: 1;
    visibility: visible !important;
    background-color: transparent;
    border-right: 0;
    border-left: 0;
    transition: none;
    transform: none;
  }

  .navbar-expand-sm .offcanvas-top {
    height: auto;
    border-top: 0;
    border-bottom: 0;
  }

  .navbar-expand-sm .offcanvas-bottom {
    height: auto;
    border-top: 0;
    border-bottom: 0;
  }

  .navbar-expand-sm .offcanvas-body {
    display: flex;
    -ms-flex-grow: 0;
    flex-grow: 0;
    padding: 0;
    overflow-y: visible;
  }

  .modal-dialog {
    max-width: 31.25rem;
    margin: 28px auto;
  }

  .modal-dialog-scrollable {
    height: calc(100% - 56px);
  }

  .modal-dialog-centered {
    min-height: calc(100% - 56px);
  }

  .modal-sm {
    max-width: 18.75rem;
  }

  .sticky-sm-top {
    position: -webkit-sticky;
    position: sticky;
    top: 0;
    z-index: 1020;
  }
}

@media (min-width: 48rem) {
  .navbar-expand-md {
    flex-wrap: nowrap;
    justify-content: flex-start;
  }

  .navbar-expand-md .navbar-nav {
    flex-direction: row;
  }

  .navbar-expand-md .navbar-nav .dropdown-menu {
    position: absolute;
  }

  .navbar-expand-md .navbar-nav .nav-link {
    padding-right: 8px;
    padding-left: 8px;
  }

  .navbar-expand-md .navbar-nav-scroll {
    overflow: visible;
  }

  .navbar-expand-md .navbar-collapse {
    display: flex !important;
    -ms-flex-basis: auto;
    flex-basis: auto;
  }

  .navbar-expand-md .navbar-toggler {
    display: none;
  }

  .navbar-expand-md .offcanvas-header {
    display: none;
  }

  .navbar-expand-md .offcanvas {
    position: inherit;
    bottom: 0;
    z-index: 1000;
    -ms-flex-grow: 1;
    flex-grow: 1;
    visibility: visible !important;
    background-color: transparent;
    border-right: 0;
    border-left: 0;
    transition: none;
    transform: none;
  }

  .navbar-expand-md .offcanvas-top {
    height: auto;
    border-top: 0;
    border-bottom: 0;
  }

  .navbar-expand-md .offcanvas-bottom {
    height: auto;
    border-top: 0;
    border-bottom: 0;
  }

  .navbar-expand-md .offcanvas-body {
    display: flex;
    -ms-flex-grow: 0;
    flex-grow: 0;
    padding: 0;
    overflow-y: visible;
  }

  .sticky-md-top {
    position: -webkit-sticky;
    position: sticky;
    top: 0;
    z-index: 1020;
  }
}

@media (min-width: 62rem) {
  .navbar-expand-lg {
    flex-wrap: nowrap;
    justify-content: flex-start;
  }

  .navbar-expand-lg .navbar-nav {
    flex-direction: row;
  }

  .navbar-expand-lg .navbar-nav .dropdown-menu {
    position: absolute;
  }

  .navbar-expand-lg .navbar-nav .nav-link {
    padding-right: 8px;
    padding-left: 8px;
  }

  .navbar-expand-lg .navbar-nav-scroll {
    overflow: visible;
  }

  .navbar-expand-lg .navbar-collapse {
    display: flex !important;
    -ms-flex-basis: auto;
    flex-basis: auto;
  }

  .navbar-expand-lg .navbar-toggler {
    display: none;
  }

  .navbar-expand-lg .offcanvas-header {
    display: none;
  }

  .navbar-expand-lg .offcanvas {
    position: inherit;
    bottom: 0;
    z-index: 1000;
    -ms-flex-grow: 1;
    flex-grow: 1;
    visibility: visible !important;
    background-color: transparent;
    border-right: 0;
    border-left: 0;
    transition: none;
    transform: none;
  }

  .navbar-expand-lg .offcanvas-top {
    height: auto;
    border-top: 0;
    border-bottom: 0;
  }

  .navbar-expand-lg .offcanvas-bottom {
    height: auto;
    border-top: 0;
    border-bottom: 0;
  }

  .navbar-expand-lg .offcanvas-body {
    display: flex;
    -ms-flex-grow: 0;
    flex-grow: 0;
    padding: 0;
    overflow-y: visible;
  }

  .modal-lg {
    max-width: 50rem;
  }

  .modal-xl {
    max-width: 50rem;
  }

  .sticky-lg-top {
    position: -webkit-sticky;
    position: sticky;
    top: 0;
    z-index: 1020;
  }
}

@media (min-width: 75rem) {
  .navbar-expand-xl {
    flex-wrap: nowrap;
    justify-content: flex-start;
  }

  .navbar-expand-xl .navbar-nav {
    flex-direction: row;
  }

  .navbar-expand-xl .navbar-nav .dropdown-menu {
    position: absolute;
  }

  .navbar-expand-xl .navbar-nav .nav-link {
    padding-right: 8px;
    padding-left: 8px;
  }

  .navbar-expand-xl .navbar-nav-scroll {
    overflow: visible;
  }

  .navbar-expand-xl .navbar-collapse {
    display: flex !important;
    -ms-flex-basis: auto;
    flex-basis: auto;
  }

  .navbar-expand-xl .navbar-toggler {
    display: none;
  }

  .navbar-expand-xl .offcanvas-header {
    display: none;
  }

  .navbar-expand-xl .offcanvas {
    position: inherit;
    bottom: 0;
    z-index: 1000;
    -ms-flex-grow: 1;
    flex-grow: 1;
    visibility: visible !important;
    background-color: transparent;
    border-right: 0;
    border-left: 0;
    transition: none;
    transform: none;
  }

  .navbar-expand-xl .offcanvas-top {
    height: auto;
    border-top: 0;
    border-bottom: 0;
  }

  .navbar-expand-xl .offcanvas-bottom {
    height: auto;
    border-top: 0;
    border-bottom: 0;
  }

  .navbar-expand-xl .offcanvas-body {
    display: flex;
    -ms-flex-grow: 0;
    flex-grow: 0;
    padding: 0;
    overflow-y: visible;
  }

  .modal-xl {
    max-width: 71.25rem;
  }

  .sticky-xl-top {
    position: -webkit-sticky;
    position: sticky;
    top: 0;
    z-index: 1020;
  }
}

@media (min-width: 87.5rem) {
  .navbar-expand-xxl {
    flex-wrap: nowrap;
    justify-content: flex-start;
  }

  .navbar-expand-xxl .navbar-nav {
    flex-direction: row;
  }

  .navbar-expand-xxl .navbar-nav .dropdown-menu {
    position: absolute;
  }

  .navbar-expand-xxl .navbar-nav .nav-link {
    padding-right: 8px;
    padding-left: 8px;
  }

  .navbar-expand-xxl .navbar-nav-scroll {
    overflow: visible;
  }

  .navbar-expand-xxl .navbar-collapse {
    display: flex !important;
    -ms-flex-basis: auto;
    flex-basis: auto;
  }

  .navbar-expand-xxl .navbar-toggler {
    display: none;
  }

  .navbar-expand-xxl .offcanvas-header {
    display: none;
  }

  .navbar-expand-xxl .offcanvas {
    position: inherit;
    bottom: 0;
    z-index: 1000;
    -ms-flex-grow: 1;
    flex-grow: 1;
    visibility: visible !important;
    background-color: transparent;
    border-right: 0;
    border-left: 0;
    transition: none;
    transform: none;
  }

  .navbar-expand-xxl .offcanvas-top {
    height: auto;
    border-top: 0;
    border-bottom: 0;
  }

  .navbar-expand-xxl .offcanvas-bottom {
    height: auto;
    border-top: 0;
    border-bottom: 0;
  }

  .navbar-expand-xxl .offcanvas-body {
    display: flex;
    -ms-flex-grow: 0;
    flex-grow: 0;
    padding: 0;
    overflow-y: visible;
  }

  .sticky-xxl-top {
    position: -webkit-sticky;
    position: sticky;
    top: 0;
    z-index: 1020;
  }
}

@media (max-width: 35.9988rem) {
  .modal-fullscreen-sm-down {
    width: 100vw;
    max-width: none;
    height: 100%;
    margin: 0;
  }

  .modal-fullscreen-sm-down .modal-content {
    height: 100%;
    border: 0;
    border-radius: 0;
  }

  .modal-fullscreen-sm-down .modal-header {
    border-radius: 0;
  }

  .modal-fullscreen-sm-down .modal-body {
    overflow-y: auto;
  }

  .modal-fullscreen-sm-down .modal-footer {
    border-radius: 0;
  }
}

@media (max-width: 47.9988rem) {
  .modal-fullscreen-md-down {
    width: 100vw;
    max-width: none;
    height: 100%;
    margin: 0;
  }

  .modal-fullscreen-md-down .modal-content {
    height: 100%;
    border: 0;
    border-radius: 0;
  }

  .modal-fullscreen-md-down .modal-header {
    border-radius: 0;
  }

  .modal-fullscreen-md-down .modal-body {
    overflow-y: auto;
  }

  .modal-fullscreen-md-down .modal-footer {
    border-radius: 0;
  }
}

@media (max-width: 61.9988rem) {
  .modal-fullscreen-lg-down {
    width: 100vw;
    max-width: none;
    height: 100%;
    margin: 0;
  }

  .modal-fullscreen-lg-down .modal-content {
    height: 100%;
    border: 0;
    border-radius: 0;
  }

  .modal-fullscreen-lg-down .modal-header {
    border-radius: 0;
  }

  .modal-fullscreen-lg-down .modal-body {
    overflow-y: auto;
  }

  .modal-fullscreen-lg-down .modal-footer {
    border-radius: 0;
  }
}

@media (max-width: 74.9988rem) {
  .modal-fullscreen-xl-down {
    width: 100vw;
    max-width: none;
    height: 100%;
    margin: 0;
  }

  .modal-fullscreen-xl-down .modal-content {
    height: 100%;
    border: 0;
    border-radius: 0;
  }

  .modal-fullscreen-xl-down .modal-header {
    border-radius: 0;
  }

  .modal-fullscreen-xl-down .modal-body {
    overflow-y: auto;
  }

  .modal-fullscreen-xl-down .modal-footer {
    border-radius: 0;
  }
}

@media (max-width: 87.4988rem) {
  .modal-fullscreen-xxl-down {
    width: 100vw;
    max-width: none;
    height: 100%;
    margin: 0;
  }

  .modal-fullscreen-xxl-down .modal-content {
    height: 100%;
    border: 0;
    border-radius: 0;
  }

  .modal-fullscreen-xxl-down .modal-header {
    border-radius: 0;
  }

  .modal-fullscreen-xxl-down .modal-body {
    overflow-y: auto;
  }

  .modal-fullscreen-xxl-down .modal-footer {
    border-radius: 0;
  }
}

@media (prefers-reduced-motion) {
  .animation {
    transition: none !important;
    -webkit-animation: unset !important;
    animation: unset !important;
  }
}

@media screen and (min-width: 20rem) and (max-width: 51.25rem) and (orientation: landscape) {
  .datepicker-modal-container .datepicker-header {
    height: 100%;
  }

  .datepicker-modal-container .datepicker-date {
    margin-top: 6.25rem;
  }

  .datepicker-modal-container .datepicker-day-cell {
    width: 32x;
    height: 32x;
  }

  .datepicker-modal-container {
    flex-direction: row;
    width: 29.6875rem;
    height: 22.5rem;
  }

  .datepicker-modal-container.datepicker-day-cell {
    width: 2.25rem;
    height: 2.25rem;
  }
}

@media screen and (min-width: 20rem) and (max-width: 51.5625rem) and (orientation: landscape) {
  .timepicker-elements {
    flex-direction: row !important;
    border-bottom-left-radius: 8px;
    min-width: auto;
    min-height: auto;
    overflow-y: auto;
  }

  .timepicker-head {
    border-top-right-radius: 0;
    border-bottom-left-radius: 0;
    padding: .625rem;
    padding-right: .625rem !important;
    height: auto;
    min-height: 19.0625rem;
  }

  .timepicker-head-content {
    flex-direction: column;
  }

  .timepicker-mode-wrapper {
    justify-content: space-around !important;
    flex-direction: row !important;
  }

  .timepicker-current {
    font-size: 48px;
    font-weight: 400;
  }

  .timepicker-dot {
    font-size: 48px;
    font-weight: 400;
  }
}

@-webkit-keyframes _spinner-grow {
  0% {
    transform: scale(0);
  }

  50% {
    opacity: 1;
    transform: none;
  }
}

@keyframes _spinner-grow {
  0% {
    transform: scale(0);
  }

  50% {
    opacity: 1;
    transform: none;
  }
}

@-webkit-keyframes _fade-in {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes _fade-in {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@-webkit-keyframes _fade-out {
  from {
    opacity: 1;
  }

  to {
    opacity: 0;
  }
}

@keyframes _fade-out {
  from {
    opacity: 1;
  }

  to {
    opacity: 0;
  }
}

@-webkit-keyframes _fade-in-down {
  from {
    opacity: 0;
    transform: translate3d(0, -100%, 0);
  }

  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

@keyframes _fade-in-down {
  from {
    opacity: 0;
    transform: translate3d(0, -100%, 0);
  }

  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

@-webkit-keyframes _fade-in-left {
  from {
    opacity: 0;
    transform: translate3d(-100%, 0, 0);
  }

  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

@keyframes _fade-in-left {
  from {
    opacity: 0;
    transform: translate3d(-100%, 0, 0);
  }

  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

@-webkit-keyframes _fade-in-right {
  from {
    opacity: 0;
    transform: translate3d(100%, 0, 0);
  }

  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

@keyframes _fade-in-right {
  from {
    opacity: 0;
    transform: translate3d(100%, 0, 0);
  }

  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

@-webkit-keyframes _fade-in-up {
  from {
    opacity: 0;
    transform: translate3d(0, 100%, 0);
  }

  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

@keyframes _fade-in-up {
  from {
    opacity: 0;
    transform: translate3d(0, 100%, 0);
  }

  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

@-webkit-keyframes _fade-out-down {
  from {
    opacity: 1;
  }

  to {
    opacity: 0;
    transform: translate3d(0, 100%, 0);
  }
}

@keyframes _fade-out-down {
  from {
    opacity: 1;
  }

  to {
    opacity: 0;
    transform: translate3d(0, 100%, 0);
  }
}

@-webkit-keyframes _fade-out-left {
  from {
    opacity: 1;
  }

  to {
    opacity: 0;
    transform: translate3d(-100%, 0, 0);
  }
}

@keyframes _fade-out-left {
  from {
    opacity: 1;
  }

  to {
    opacity: 0;
    transform: translate3d(-100%, 0, 0);
  }
}

@-webkit-keyframes _fade-out-right {
  from {
    opacity: 1;
  }

  to {
    opacity: 0;
    transform: translate3d(100%, 0, 0);
  }
}

@keyframes _fade-out-right {
  from {
    opacity: 1;
  }

  to {
    opacity: 0;
    transform: translate3d(100%, 0, 0);
  }
}

@-webkit-keyframes _fade-out-up {
  from {
    opacity: 1;
  }

  to {
    opacity: 0;
    transform: translate3d(0, -100%, 0);
  }
}

@keyframes _fade-out-up {
  from {
    opacity: 1;
  }

  to {
    opacity: 0;
    transform: translate3d(0, -100%, 0);
  }
}

@-webkit-keyframes _slide-in-down {
  from {
    visibility: visible;
    transform: translate3d(0, -100%, 0);
  }

  to {
    transform: translate3d(0, 0, 0);
  }
}

@keyframes _slide-in-down {
  from {
    visibility: visible;
    transform: translate3d(0, -100%, 0);
  }

  to {
    transform: translate3d(0, 0, 0);
  }
}

@-webkit-keyframes _slide-in-left {
  from {
    visibility: visible;
    transform: translate3d(-100%, 0, 0);
  }

  to {
    transform: translate3d(0, 0, 0);
  }
}

@keyframes _slide-in-left {
  from {
    visibility: visible;
    transform: translate3d(-100%, 0, 0);
  }

  to {
    transform: translate3d(0, 0, 0);
  }
}

@-webkit-keyframes _slide-in-right {
  from {
    visibility: visible;
    transform: translate3d(100%, 0, 0);
  }

  to {
    transform: translate3d(0, 0, 0);
  }
}

@keyframes _slide-in-right {
  from {
    visibility: visible;
    transform: translate3d(100%, 0, 0);
  }

  to {
    transform: translate3d(0, 0, 0);
  }
}

@-webkit-keyframes _slide-in-up {
  from {
    visibility: visible;
    transform: translate3d(0, 100%, 0);
  }

  to {
    transform: translate3d(0, 0, 0);
  }
}

@keyframes _slide-in-up {
  from {
    visibility: visible;
    transform: translate3d(0, 100%, 0);
  }

  to {
    transform: translate3d(0, 0, 0);
  }
}

@-webkit-keyframes _slide-out-down {
  from {
    transform: translate3d(0, 0, 0);
  }

  to {
    visibility: hidden;
    transform: translate3d(0, 100%, 0);
  }
}

@keyframes _slide-out-down {
  from {
    transform: translate3d(0, 0, 0);
  }

  to {
    visibility: hidden;
    transform: translate3d(0, 100%, 0);
  }
}

@-webkit-keyframes _slide-out-left {
  from {
    transform: translate3d(0, 0, 0);
  }

  to {
    visibility: hidden;
    transform: translate3d(-100%, 0, 0);
  }
}

@keyframes _slide-out-left {
  from {
    transform: translate3d(0, 0, 0);
  }

  to {
    visibility: hidden;
    transform: translate3d(-100%, 0, 0);
  }
}

@-webkit-keyframes _slide-out-right {
  from {
    transform: translate3d(0, 0, 0);
  }

  to {
    visibility: hidden;
    transform: translate3d(100%, 0, 0);
  }
}

@keyframes _slide-out-right {
  from {
    transform: translate3d(0, 0, 0);
  }

  to {
    visibility: hidden;
    transform: translate3d(100%, 0, 0);
  }
}

@-webkit-keyframes _slide-out-up {
  from {
    transform: translate3d(0, 0, 0);
  }

  to {
    visibility: hidden;
    transform: translate3d(0, -100%, 0);
  }
}

@keyframes _slide-out-up {
  from {
    transform: translate3d(0, 0, 0);
  }

  to {
    visibility: hidden;
    transform: translate3d(0, -100%, 0);
  }
}

@-webkit-keyframes _slide-down {
  from {
    transform: translate3d(0, 0, 0);
  }

  to {
    transform: translate3d(0, 100%, 0);
  }
}

@keyframes _slide-down {
  from {
    transform: translate3d(0, 0, 0);
  }

  to {
    transform: translate3d(0, 100%, 0);
  }
}

@-webkit-keyframes _slide-left {
  from {
    transform: translate3d(0, 0, 0);
  }

  to {
    transform: translate3d(-100%, 0, 0);
  }
}

@keyframes _slide-left {
  from {
    transform: translate3d(0, 0, 0);
  }

  to {
    transform: translate3d(-100%, 0, 0);
  }
}

@-webkit-keyframes _slide-right {
  from {
    transform: translate3d(0, 0, 0);
  }

  to {
    transform: translate3d(100%, 0, 0);
  }
}

@keyframes _slide-right {
  from {
    transform: translate3d(0, 0, 0);
  }

  to {
    transform: translate3d(100%, 0, 0);
  }
}

@-webkit-keyframes _slide-up {
  from {
    transform: translate3d(0, 0, 0);
  }

  to {
    transform: translate3d(0, -100%, 0);
  }
}

@keyframes _slide-up {
  from {
    transform: translate3d(0, 0, 0);
  }

  to {
    transform: translate3d(0, -100%, 0);
  }
}

@-webkit-keyframes _zoom-in {
  from {
    opacity: 0;
    transform: scale3d(0.3, 0.3, 0.3);
  }

  50% {
    opacity: 1;
  }
}

@keyframes _zoom-in {
  from {
    opacity: 0;
    transform: scale3d(0.3, 0.3, 0.3);
  }

  50% {
    opacity: 1;
  }
}

@-webkit-keyframes _zoom-out {
  from {
    opacity: 1;
  }

  50% {
    opacity: 0;
    transform: scale3d(0.3, 0.3, 0.3);
  }

  to {
    opacity: 0;
  }
}

@keyframes _zoom-out {
  from {
    opacity: 1;
  }

  50% {
    opacity: 0;
    transform: scale3d(0.3, 0.3, 0.3);
  }

  to {
    opacity: 0;
  }
}

@-webkit-keyframes _tada {
  from {
    transform: scale3d(1, 1, 1);
  }

  10% {
    transform: scale3d(0.9, 0.9, 0.9) rotate3d(0, 0, 1, -3deg);
  }

  20% {
    transform: scale3d(0.9, 0.9, 0.9) rotate3d(0, 0, 1, -3deg);
  }

  30% {
    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
  }

  50% {
    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
  }

  70% {
    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
  }

  90% {
    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
  }

  40% {
    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
  }

  60% {
    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
  }

  80% {
    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
  }

  to {
    transform: scale3d(1, 1, 1);
  }
}

@keyframes _tada {
  from {
    transform: scale3d(1, 1, 1);
  }

  10% {
    transform: scale3d(0.9, 0.9, 0.9) rotate3d(0, 0, 1, -3deg);
  }

  20% {
    transform: scale3d(0.9, 0.9, 0.9) rotate3d(0, 0, 1, -3deg);
  }

  30% {
    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
  }

  50% {
    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
  }

  70% {
    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
  }

  90% {
    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
  }

  40% {
    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
  }

  60% {
    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
  }

  80% {
    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
  }

  to {
    transform: scale3d(1, 1, 1);
  }
}

@-webkit-keyframes _pulse {
  from {
    transform: scale3d(1, 1, 1);
  }

  50% {
    transform: scale3d(1.05, 1.05, 1.05);
  }

  to {
    transform: scale3d(1, 1, 1);
  }
}

@keyframes _pulse {
  from {
    transform: scale3d(1, 1, 1);
  }

  50% {
    transform: scale3d(1.05, 1.05, 1.05);
  }

  to {
    transform: scale3d(1, 1, 1);
  }
}

@-webkit-keyframes _show-up-clock {
  0% {
    opacity: 0;
    transform: scale(0.7);
  }

  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes _show-up-clock {
  0% {
    opacity: 0;
    transform: scale(0.7);
  }

  to {
    opacity: 1;
    transform: scale(1);
  }
}

.sr-only {
  position: absolute;
  width: .0625rem;
  height: .0625rem;
  padding: 0;
  margin: -0.0625rem;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

.visible {
  visibility: visible;
}

.static {
  position: static;
}

.fixed {
  position: fixed;
}

.absolute {
  position: absolute;
}

.relative {
  position: relative;
}

.sticky {
  position: -webkit-sticky;
  position: sticky;
}

.inset-0 {
  top: 0rem;
  right: 0rem;
  bottom: 0rem;
  left: 0rem;
}

.top-0 {
  top: 0rem;
}

.right-0 {
  right: 0rem;
}

.bottom-0 {
  bottom: 0rem;
}

.top-5 {
  top: 20px;
}

.right-5 {
  right: 20px;
}

.z-10 {
  z-index: 10;
}

.m-auto {
  margin: auto;
}

.m-2 {
  margin: 8px;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.my-auto {
  margin-top: auto;
  margin-bottom: auto;
}

.my-2 {
  margin-top: 8px;
  margin-bottom: 8px;
}

.my-4 {
  margin-top: 16px;
  margin-bottom: 16px;
}

.mx-1 {
  margin-left: 4px;
  margin-right: 4px;
}

.mb-4 {
  margin-bottom: 16px;
}

.mb-8 {
  margin-bottom: 32px;
}

.mr-4 {
  margin-right: 16px;
}

.ml-2 {
  margin-left: 8px;
}

.mt-3 {
  margin-top: 12px;
}

.mt-2 {
  margin-top: 8px;
}

.mt-4 {
  margin-top: 16px;
}

.ml-auto {
  margin-left: auto;
}

.mt-0 {
  margin-top: 0rem;
}

.mb-3 {
  margin-bottom: 12px;
}

.mb-2 {
  margin-bottom: 8px;
}

.mb-5 {
  margin-bottom: 20px;
}

.mb-10 {
  margin-bottom: 40px;
}

.mt-10 {
  margin-top: 40px;
}

.mt-8 {
  margin-top: 32px;
}

.mb-6 {
  margin-bottom: 24px;
}

.mr-2 {
  margin-right: 8px;
}

.ml-5 {
  margin-left: 20px;
}

.ml-6 {
  margin-left: 24px;
}

.block {
  display: block;
}

.inline-block {
  display: inline-block;
}

.flex {
  display: flex;
}

.inline-flex {
  display: inline-flex;
}

.table {
  display: table;
}

.hidden {
  display: none;
}

.h-full {
  height: 100%;
}

.h-7 {
  height: 28px;
}

.h-fit {
  height: -webkit-fit-content;
  height: -moz-fit-content;
  height: fit-content;
}

.h-10 {
  height: 40px;
}

.h-12 {
  height: 48px;
}

.h-6 {
  height: 24px;
}

.h-14 {
  height: 56px;
}

.h-4 {
  height: 16px;
}

.h-40 {
  height: 160px;
}

.h-[70vh] {
  height: 70vh;
}

.h-[50vh] {
  height: 50vh;
}

.h-8 {
  height: 32px;
}

.h-5 {
  height: 20px;
}

.h-screen {
  height: 100vh;
}

.h-16 {
  height: 64px;
}

.max-h-[70vh] {
  max-height: 70vh;
}

.max-h-[80vh] {
  max-height: 80vh;
}

.max-h-60 {
  max-height: 240px;
}

.min-h-[60vh] {
  min-height: 60vh;
}

.min-h-screen {
  min-height: 100vh;
}

.w-full {
  width: 100%;
}

.w-7 {
  width: 28px;
}

.w-11/12 {
  width: 91.666667%;
}

.w-10 {
  width: 40px;
}

.w-12 {
  width: 48px;
}

.w-32 {
  width: 128px;
}

.w-6 {
  width: 24px;
}

.w-14 {
  width: 56px;
}

.w-4 {
  width: 16px;
}

.w-8 {
  width: 32px;
}

.w-5 {
  width: 20px;
}

.w-16 {
  width: 64px;
}

.min-w-full {
  min-width: 100%;
}

.max-w-lg {
  max-width: 512px;
}

.max-w-xs {
  max-width: 320px;
}

.max-w-sm {
  max-width: 384px;
}

.flex-1 {
  flex: 1 1 0%;
}

.flex-auto {
  flex: 1 1 auto;
}

.transform {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.cursor-pointer {
  cursor: pointer;
}

.resize {
  resize: both;
}

.list-none {
  list-style-type: none;
}

.appearance-none {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

.flex-row {
  flex-direction: row;
}

.flex-col {
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.content-center {
  align-content: center;
}

.items-end {
  align-items: flex-end;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-around {
  justify-content: space-around;
}

.justify-evenly {
  justify-content: space-evenly;
}

.gap-2 {
  gap: 8px;
}

.space-x-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(8px * var(--tw-space-x-reverse));
  margin-left: calc(8px * calc(1 - var(--tw-space-x-reverse)));
}

.divide-y > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-y-reverse: 0;
  border-top-width: calc(.0625rem * calc(1 - var(--tw-divide-y-reverse)));
  border-bottom-width: calc(.0625rem * var(--tw-divide-y-reverse));
}

.divide-gray-200 > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-divide-opacity));
}

.overflow-hidden {
  overflow: hidden;
}

.overflow-x-auto {
  overflow-x: auto;
}

.overflow-y-auto {
  overflow-y: auto;
}

.overflow-x-hidden {
  overflow-x: hidden;
}

.overflow-y-scroll {
  overflow-y: scroll;
}

.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.whitespace-nowrap {
  white-space: nowrap;
}

.whitespace-pre-line {
  white-space: pre-line;
}

.rounded-full {
  border-radius: 624.9375rem;
}

.rounded {
  border-radius: 4px;
}

.rounded-xl {
  border-radius: 12px;
}

.rounded-lg {
  border-radius: 8px;
}

.rounded-md {
  border-radius: 6px;
}

.rounded-t-lg {
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}

.border-2 {
  border-width: .125rem;
}

.border {
  border-width: .0625rem;
}

.border-b-2 {
  border-bottom-width: .125rem;
}

.border-b {
  border-bottom-width: .0625rem;
}

.border-t {
  border-top-width: .0625rem;
}

.border-gray-200 {
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity));
}

.border-white {
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity));
}

.border-gray-300 {
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity));
}

.border-red-500 {
  --tw-border-opacity: 1;
  border-color: rgb(239 68 68 / var(--tw-border-opacity));
}

.bg-blue-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity));
}

.bg-blue-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(30 58 138 / var(--tw-bg-opacity));
}

.bg-gray-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity));
}

.bg-blue-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(96 165 250 / var(--tw-bg-opacity));
}

.bg-transparent {
  background-color: transparent;
}

.bg-white {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}

.bg-green-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(34 197 94 / var(--tw-bg-opacity));
}

.bg-gray-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(156 163 175 / var(--tw-bg-opacity));
}

.bg-gray-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(209 213 219 / var(--tw-bg-opacity));
}

.bg-blue-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity));
}

.bg-black {
  --tw-bg-opacity: 1;
  background-color: rgb(0 0 0 / var(--tw-bg-opacity));
}

.bg-slate-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(241 245 249 / var(--tw-bg-opacity));
}

.bg-gray-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity));
}

.bg-red-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(220 38 38 / var(--tw-bg-opacity));
}

.bg-slate-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(226 232 240 / var(--tw-bg-opacity));
}

.bg-gray-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity));
}

.object-cover {
  -o-object-fit: cover;
  object-fit: cover;
}

.p-4 {
  padding: 16px;
}

.p-2 {
  padding: 8px;
}

.p-2.5 {
  padding: 10px;
}

.p-1.5 {
  padding: 6px;
}

.p-1 {
  padding: 4px;
}

.p-5 {
  padding: 20px;
}

.p-6 {
  padding: 24px;
}

.py-10 {
  padding-top: 40px;
  padding-bottom: 40px;
}

NaNrem-5 {
  padding-left: 20px;
  padding-right: 20px;
}

NaNrem-2 {
  padding-left: 8px;
  padding-right: 8px;
}

.py-2 {
  padding-top: 8px;
  padding-bottom: 8px;
}

.py-1 {
  padding-top: 4px;
  padding-bottom: 4px;
}

NaNrem-6 {
  padding-left: 24px;
  padding-right: 24px;
}

.py-4 {
  padding-top: 16px;
  padding-bottom: 16px;
}

NaNrem-4 {
  padding-left: 16px;
  padding-right: 16px;
}

.py-8 {
  padding-top: 32px;
  padding-bottom: 32px;
}

.py-2.5 {
  padding-top: 10px;
  padding-bottom: 10px;
}

.py-3 {
  padding-top: 12px;
  padding-bottom: 12px;
}

.py-5 {
  padding-top: 20px;
  padding-bottom: 20px;
}

NaNrem-3 {
  padding-left: 12px;
  padding-right: 12px;
}

NaNrem-8 {
  padding-left: 32px;
  padding-right: 32px;
}

NaNrem-1 {
  padding-left: 4px;
  padding-right: 4px;
}

.pt-4 {
  padding-top: 16px;
}

.pr-6 {
  padding-right: 24px;
}

.pb-6 {
  padding-bottom: 24px;
}

.pl-4 {
  padding-left: 16px;
}

.pb-10 {
  padding-bottom: 40px;
}

.pr-0 {
  padding-right: 0rem;
}

.pr-2 {
  padding-right: 8px;
}

.pl-2 {
  padding-left: 8px;
}

.pt-6 {
  padding-top: 24px;
}

.pb-8 {
  padding-bottom: 32px;
}

.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.align-baseline {
  vertical-align: baseline;
}

.align-text-bottom {
  vertical-align: text-bottom;
}

.text-2xl {
  font-size: 24px;
  line-height: 32px;
}

.text-3xl {
  font-size: 30px;
  line-height: 36px;
}

.text-xs {
  font-size: 12px;
  line-height: 16px;
}

.text-xl {
  font-size: 20px;
  line-height: 28px;
}

.text-7xl {
  font-size: 72px;
  line-height: 1;
}

.text-sm {
  font-size: 14px;
  line-height: 20px;
}

.text-lg {
  font-size: 18px;
  line-height: 28px;
}

.text-base {
  font-size: 16px;
  line-height: 24px;
}

.font-bold {
  font-weight: 700;
}

.font-medium {
  font-weight: 500;
}

.font-normal {
  font-weight: 400;
}

.uppercase {
  text-transform: uppercase;
}

.italic {
  font-style: italic;
}

.leading-6 {
  line-height: 24px;
}

.leading-tight {
  line-height: 1.25;
}

.tracking-wider {
  letter-spacing: 0.05em;
}

.text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.text-black {
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity));
}

.text-gray-700 {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity));
}

.text-gray-800 {
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity));
}

.text-gray-500 {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity));
}

.text-gray-400 {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity));
}

.text-gray-900 {
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity));
}

.text-slate-500 {
  --tw-text-opacity: 1;
  color: rgb(100 116 139 / var(--tw-text-opacity));
}

.text-gray-600 {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity));
}

.text-red-500 {
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity));
}

.text-blue-500 {
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity));
}

.text-red-400 {
  --tw-text-opacity: 1;
  color: rgb(248 113 113 / var(--tw-text-opacity));
}

.opacity-40 {
  opacity: 0.4;
}

.shadow-md {
  --tw-shadow: 0 .25rem .375rem -0.0625rem rgb(0 0 0 / 0.1), 0 .125rem .25rem -0.125rem rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 .25rem .375rem -0.0625rem var(--tw-shadow-color), 0 .125rem .25rem -0.125rem var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow {
  --tw-shadow: 0 .0625rem .1875rem 0 rgb(0 0 0 / 0.1), 0 .0625rem .125rem -0.0625rem rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 .0625rem .1875rem 0 var(--tw-shadow-color), 0 .0625rem .125rem -0.0625rem var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-lg {
  --tw-shadow: 0 .625rem .9375rem -0.1875rem rgb(0 0 0 / 0.1), 0 .25rem .375rem -0.25rem rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 .625rem .9375rem -0.1875rem var(--tw-shadow-color), 0 .25rem .375rem -0.25rem var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-inner {
  --tw-shadow: inset 0 .125rem .25rem 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: inset 0 .125rem .25rem 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.outline-none {
  outline: .125rem solid transparent;
  outline-offset: .125rem;
}

.outline {
  outline-style: solid;
}

.ring-blue-600 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(37 99 235 / var(--tw-ring-opacity));
}

.ring-indigo-600 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(79 70 229 / var(--tw-ring-opacity));
}

.ring-offset-2 {
  --tw-ring-offset-width: .125rem;
}

.blur {
  --tw-blur: blur(.5rem);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.transition-transform {
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition {
  transition-property: color, background-color, border-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-text-decoration-color, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-text-decoration-color,
    -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.duration-300 {
  transition-duration: 300ms;
}

.duration-150 {
  transition-duration: 150ms;
}

.ease-in-out {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

@media screen and (max-width: 47.9375rem) {
  .sidebar-holder {
    width: 100%;
    min-width: 12.5rem;
    max-width: 12.5rem;
    position: fixed;
    top: 0;
    left: 0;
  }

  .page-header span {
    margin-left: auto;
  }
}

.hover:scale-105:hover {
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover:bg-blue-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(29 78 216 / var(--tw-bg-opacity));
}

.hover:bg-gray-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity));
}

.hover:bg-gray-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity));
}

.hover:bg-green-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(22 163 74 / var(--tw-bg-opacity));
}

.hover:bg-red-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(185 28 28 / var(--tw-bg-opacity));
}

.hover:bg-gray-900:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(17 24 39 / var(--tw-bg-opacity));
}

.hover:text-gray-800:hover {
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity));
}

.hover:text-gray-900:hover {
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity));
}

.hover:text-blue-800:hover {
  --tw-text-opacity: 1;
  color: rgb(30 64 175 / var(--tw-text-opacity));
}

.hover:shadow-lg:hover {
  --tw-shadow: 0 .625rem .9375rem -0.1875rem rgb(0 0 0 / 0.1), 0 .25rem .375rem -0.25rem rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 .625rem .9375rem -0.1875rem var(--tw-shadow-color), 0 .25rem .375rem -0.25rem var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.focus:bg-blue-700:focus {
  --tw-bg-opacity: 1;
  background-color: rgb(29 78 216 / var(--tw-bg-opacity));
}

.focus:bg-green-600:focus {
  --tw-bg-opacity: 1;
  background-color: rgb(22 163 74 / var(--tw-bg-opacity));
}

.focus:bg-red-700:focus {
  --tw-bg-opacity: 1;
  background-color: rgb(185 28 28 / var(--tw-bg-opacity));
}

.focus:bg-gray-900:focus {
  --tw-bg-opacity: 1;
  background-color: rgb(17 24 39 / var(--tw-bg-opacity));
}

.focus:shadow-lg:focus {
  --tw-shadow: 0 .625rem .9375rem -0.1875rem rgb(0 0 0 / 0.1), 0 .25rem .375rem -0.25rem rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 .625rem .9375rem -0.1875rem var(--tw-shadow-color), 0 .25rem .375rem -0.25rem var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.focus:outline-none:focus {
  outline: .125rem solid transparent;
  outline-offset: .125rem;
}

.focus:ring-2:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(.125rem + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus:ring-0:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0rem + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus:ring-gray-300:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(209 213 219 / var(--tw-ring-opacity));
}

.active:bg-blue-800:active {
  --tw-bg-opacity: 1;
  background-color: rgb(30 64 175 / var(--tw-bg-opacity));
}

.active:bg-green-700:active {
  --tw-bg-opacity: 1;
  background-color: rgb(21 128 61 / var(--tw-bg-opacity));
}

.active:bg-red-800:active {
  --tw-bg-opacity: 1;
  background-color: rgb(153 27 27 / var(--tw-bg-opacity));
}

.active:bg-gray-900:active {
  --tw-bg-opacity: 1;
  background-color: rgb(17 24 39 / var(--tw-bg-opacity));
}

.active:shadow-lg:active {
  --tw-shadow: 0 .625rem .9375rem -0.1875rem rgb(0 0 0 / 0.1), 0 .25rem .375rem -0.25rem rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 .625rem .9375rem -0.1875rem var(--tw-shadow-color), 0 .25rem .375rem -0.25rem var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.disabled:cursor-not-allowed:disabled {
  cursor: not-allowed;
}

@media (prefers-color-scheme: dark) {
  .dark:text-gray-400 {
    --tw-text-opacity: 1;
    color: rgb(156 163 175 / var(--tw-text-opacity));
  }

  .dark:text-gray-500 {
    --tw-text-opacity: 1;
    color: rgb(107 114 128 / var(--tw-text-opacity));
  }

  .dark:hover:bg-gray-700:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(55 65 81 / var(--tw-bg-opacity));
  }

  .dark:hover:text-white:hover {
    --tw-text-opacity: 1;
    color: rgb(255 255 255 / var(--tw-text-opacity));
  }
}

.property-grid {
  display: grid;
  grid-template-columns: repeat(2, 15.625rem);
  -moz-column-gap: 1.9375rem;
  column-gap: 1.9375rem;
  row-gap: 5rem;
  padding-inline: 16px;
  min-width: 37.5rem;
}

@media (min-width: 40rem) {
  .property-grid {
    grid-template-columns: repeat(2, minmax(18.25rem, 1fr));
    padding-inline: 0;
  }

  .sm:ml-4 {
    margin-left: 16px;
  }

  .sm:flex {
    display: flex;
  }

  .sm:w-1/2 {
    width: 50%;
  }

  .sm:px-6 {
    padding-left: 24px;
    padding-right: 24px;
  }

  .sm:text-left {
    text-align: left;
  }
}

@media (min-width: 48rem) {
  .md:h-52 {
    height: 208px;
  }

  .md:w-1/2 {
    width: 50%;
  }
}

@media (min-width: 64rem) {
  .property-grid {
    grid-template-columns: repeat(3, minmax(18.25rem, 1fr));
  }

  .lg:block {
    display: block;
  }

  .lg:flex {
    display: flex;
  }

  .lg:hidden {
    display: none;
  }

  .lg:h-80 {
    height: 320px;
  }

  .lg:max-h-[60vh] {
    max-height: 60vh;
  }

  .lg:w-1/3 {
    width: 33.333333%;
  }

  .lg:w-1/2 {
    width: 50%;
  }

  .lg:px-6 {
    padding-left: 24px;
    padding-right: 24px;
  }
}

@media (min-width: 80rem) {
  .property-grid {
    grid-template-columns: repeat(4, minmax(18.25rem, 1fr));
  }

  .xl:w-1/4 {
    width: 25%;
  }

  .xl:w-1/2 {
    width: 50%;
  }
}


  
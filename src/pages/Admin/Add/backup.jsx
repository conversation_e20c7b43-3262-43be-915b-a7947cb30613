import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { useNavigate } from "react-router-dom";
import { LazyLoad } from "Components/LazyLoad";
import { InteractiveButton } from "Components/InteractiveButton";
import { AuthContext, tokenExpireError } from "Context/Auth";
import { GlobalContext, setLoading, showToast } from "Context/Global";
import MkdSDK from "Utils/MkdSDK";

const AdminStripePricesAddPage = ({ onSuccess }) => {
  let sdk = new MkdSDK();
  const { dispatch } = React.useContext(AuthContext);
  const {
    dispatch: globalDispatch,
    state: { addStripePrice },
  } = React.useContext(GlobalContext);

  const [priceType, setPriceType] = useState("one_time");
  const [selectProduct, setSelectProduct] = useState([]);

  const navigate = useNavigate();

  const schema = yup
    .object({
      product_id: yup.string().required(),
      name: yup.string().required(),
      amount: yup.string().required(),
      type: yup.string().required(),
      interval: yup.string().when("type", {
        is: "recurring",
        then: (schema) => schema.required(),
        otherwise: (schema) => schema.notRequired(),
      }),
      interval_count: yup.string(),
      usage_type: yup.string().when("type", {
        is: "recurring",
        then: (schema) => schema.required(),
        otherwise: (schema) => schema.notRequired(),
      }),
      usage_limit: yup.string(),
      trial_days: yup.string(),
    })
    .required();

  const {
    register,
    handleSubmit,
    setError,
    setValue,
    trigger,
    resetField,
    getValues,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });

  const selectType = [
    { key: 0, value: "", display: "Nothing Selected" },
    { key: 1, value: "one_time", display: "One Time" },
    { key: 2, value: "recurring", display: "Recurring" },
  ];

  const selectUsageType = [
    { key: 0, value: "", display: "Nothing Selected" },
    { key: 1, value: "licenced", display: "Upfront" },
    { key: 2, value: "metered", display: "Metered" },
  ];

  const selectInterval = [
    { key: 0, value: "", display: "Nothing Selected" },
    { key: 1, value: "day", display: "Day" },
    { key: 2, value: "week", display: "Week" },
    { key: 3, value: "month", display: "Month" },
    { key: 4, value: "year", display: "Year" },
    { key: 5, value: "lifetime", display: "Lifetime" },
  ];

  const onSubmit = async (data) => {
    console.log(data);
    try {
      setLoading(globalDispatch, true, "addStripePrice");
      const result = await sdk.addStripePrice(data);
      if (!result.error) {
        showToast(globalDispatch, "Price Added", 5000, "success");
        if (onSuccess) {
          onSuccess();
        }
      } else {
        if (result.validation) {
          const keys = Object.keys(result.validation);
          for (let i = 0; i < keys.length; i++) {
            const field = keys[i];
            console.log(field);
            setError(field, {
              type: "manual",
              message: result.validation[field],
            });
          }
        }
      }
    } catch (error) {
      console.log("Error", error);

      showToast(globalDispatch, error.message, 5000, "error");
      tokenExpireError(dispatch, error.message);
    } finally {
      setLoading(globalDispatch, false, "addStripePrice");
    }
  };

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "pricing",
      },
    });

    (async () => {
      const { list, error } = await sdk.getStripeProducts({ limit: "all" });
      if (error) {
        showToast(
          globalDispatch,
          "Something went wrong while fetching products list",
          5000,
          "error"
        );
        return;
      }
      setSelectProduct(list);
    })();
  }, []);

  return (
    <div className=" mx-auto h-full max-h-full  min-h-full rounded bg-brown-main-bg  p-5 shadow-md">
      <form
        className="grid h-full max-h-full min-h-full w-full max-w-lg grid-cols-1 grid-rows-[1fr_auto]"
        onSubmit={handleSubmit(onSubmit)}
      >
        <div className="flex h-full max-h-full min-h-full flex-col gap-4 overflow-y-auto">
          <div className="mb-4 ">
            <label
              className="mb-2 block text-sm font-bold text-gray-700"
              htmlFor="product_id"
            >
              Product
            </label>
            <select
              className="focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none"
              {...register("product_id")}
            >
              <option value="" key={`prod_default`}>
                Nothing selected
              </option>
              {selectProduct.map((option, index) => (
                <option value={option.id} key={`prod_${option.id}`}>
                  {option.name}
                </option>
              ))}
            </select>
            <p className="text-xs italic text-red-500">
              {errors.product_id?.message}
            </p>
          </div>

          <div className="mb-4 ">
            <label
              className="mb-2 block text-sm font-bold text-gray-700"
              htmlFor="name"
            >
              Name
            </label>
            <input
              type="text"
              placeholder="Name"
              {...register("name")}
              className={`"shadow focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 focus:outline-none ${
                errors.name?.message ? "border-red-500" : ""
              }`}
            />
            <p className="text-xs italic text-red-500">
              {errors.name?.message}
            </p>
          </div>

          <div className="mb-5">
            <label
              className="mb-2 block text-sm font-bold text-gray-700"
              htmlFor="amount"
            >
              Amount
            </label>
            <input
              type="number"
              min={0.1}
              step="any"
              placeholder="Amount"
              {...register("amount")}
              className={`focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${
                errors.amount?.message ? "border-red-500" : ""
              }`}
            />
            <p className="text-xs italic text-red-500">
              {errors.amount?.message}
            </p>
          </div>

          <div className="mb-5">
            <label
              className="mb-2 block text-sm font-bold text-gray-700"
              htmlFor="type"
            >
              Type
            </label>
            <select
              className="focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none"
              {...register("type")}
              onChange={(e) => {
                const currentTypeSelected = e.target.value;
                setPriceType(currentTypeSelected);
                setValue("type", currentTypeSelected);
                trigger("type");
              }}
            >
              {selectType.map((option) => (
                <option
                  value={option.value.toLowerCase()}
                  key={`interval_${option.key}`}
                >
                  {option.display}
                </option>
              ))}
            </select>
            <p className="text-xs italic text-red-500">
              {errors.type?.message}
            </p>
          </div>
          {priceType === "recurring" ? (
            <div className="ml-6">
              <div className="mb-5">
                <label
                  className="mb-2 block text-sm font-bold text-gray-700"
                  htmlFor="interval"
                >
                  Interval
                </label>
                <select
                  className="focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none"
                  {...register("interval")}
                  placeholder="Select"
                >
                  {selectInterval.map((option) => (
                    <option
                      value={option.value.toLowerCase()}
                      key={`interval_${option.key}`}
                    >
                      {option.display}
                    </option>
                  ))}
                </select>
                <p className="text-xs italic text-red-500">
                  {errors.interval?.message}
                </p>
              </div>
              <div className="mb-5">
                <label
                  className="mb-2 block text-sm font-bold text-gray-700"
                  htmlFor="interval_count"
                >
                  Interval Count
                </label>
                <input
                  type="number"
                  step="1"
                  placeholder="Interval Count"
                  {...register("interval_count")}
                  {...setValue("interval_count", 1)}
                  className={`focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${
                    errors.interval_count?.message ? "border-red-500" : ""
                  }`}
                />
                <p className="text-xs italic text-red-500">
                  {errors.interval_count?.message}
                </p>
              </div>

              <div className="mb-5">
                <label
                  className="mb-2 block text-sm font-bold text-gray-700"
                  htmlFor="usage_type"
                >
                  Usage Type
                </label>
                <select
                  className="focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none"
                  {...register("usage_type")}
                  placeholder="Select"
                >
                  {selectUsageType.map((option) => (
                    <option
                      value={option.value.toLowerCase()}
                      key={`interval_${option.key}`}
                    >
                      {option.display}
                    </option>
                  ))}
                </select>
                <p className="text-xs italic text-red-500">
                  {errors.usage_type?.message}
                </p>
              </div>
              <div className="mb-5">
                <label
                  className="mb-2 block text-sm font-bold text-gray-700"
                  htmlFor="trial_days"
                >
                  Trial Days
                </label>
                <input
                  type="number"
                  step="1"
                  placeholder="0"
                  {...register("trial_days")}
                  className={`focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${
                    errors.trial_days?.message ? "border-red-500" : ""
                  }`}
                />
                <p className="text-xs italic text-red-500">
                  {errors.trial_days?.message}
                </p>
              </div>
              <div className="mb-5">
                <label
                  className="mb-2 block text-sm font-bold text-gray-700"
                  htmlFor="trial_days"
                >
                  Usage Limit
                </label>
                <input
                  type="number"
                  step="1"
                  placeholder="1000"
                  {...register("usage_limit")}
                  className={`focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${
                    errors.usage_limit?.message ? "border-red-500" : ""
                  }`}
                />
                <p className="text-xs italic text-red-500">
                  {errors.usage_limit?.message}
                </p>
              </div>
            </div>
          ) : (
            ""
          )}
        </div>

        <LazyLoad>
          <InteractiveButton
            type="submit"
            color="white"
            loading={addStripePrice?.loading}
            disabled={addStripePrice?.loading}
            className=" !text-white"
          >
            Submit
          </InteractiveButton>
        </LazyLoad>
      </form>
    </div>
  );
};

export default AdminStripePricesAddPage;
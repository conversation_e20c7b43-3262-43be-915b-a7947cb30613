import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { useNavigate } from "react-router-dom";
import { AuthContext, tokenExpireError } from "Src/authContext";
import { GlobalContext } from "Src/globalContext";
import MkdSDK from "Utils/MkdSDK";
import { InteractiveButton } from "Components/InteractiveButton";
import { LazyLoad } from "Components/LazyLoad";

const AddAdminStripeProductPage = ({ onSuccess = null }) => {
  let sdk = new MkdSDK();
  const [isLoading, setIsLoading] = useState(false);

  const { dispatch } = React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);

  const navigate = useNavigate();

  const schema = yup
    .object({
      name: yup.string().required(),
      description: yup.string().nullable(),
    })
    .required();

  const {
    register,
    handleSubmit,
    setError,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });

  // const selectShippable = [
  //   { key: "0", value: false },
  //   { key: "1", value: true },
  // ];

  const onSubmit = async (data) => {
    try {
      setIsLoading(true);
      const result = await sdk.addStripeProduct({
        name: data.name,
        description: data.description,
      });
      if (!result?.error) {
        if (onSuccess) {
          onSuccess();
        }
      } else {
        if (result.validation) {
          const keys = Object.keys(result.validation);
          for (let i = 0; i < keys.length; i++) {
            const field = keys[i];
            setError(field, {
              type: "manual",
              message: result.validation[field],
            });
          }
        }
      }
    } catch (error) {
      console.log("Error", error);
      tokenExpireError(dispatch, error.message);
    } finally {
      setIsLoading(false);
    }
  };

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "plans",
      },
    });
  }, []);

  return (
    <div className="bg-brown-main-bg mx-auto h-full p-5 shadow-md">
      {/* <h4 className="text-2xl font-medium">Add a Plan</h4> */}
      <form className="w-full max-w-lg" onSubmit={handleSubmit(onSubmit)}>
        <div className="mb-4">
          <label
            className="mb-2 block text-sm font-bold text-gray-700"
            htmlFor="name"
          >
            Name
          </label>
          <input
            type="text"
            placeholder="Name"
            {...register("name")}
            className={`"shadow focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 focus:outline-none ${
              errors.name?.message ? "border-red-500" : ""
            }`}
          />
          <p className="text-xs italic text-red-500">{errors.name?.message}</p>
        </div>
        <div className="mb-5">
          <label
            className="mb-2 block text-sm font-bold text-gray-700"
            htmlFor="description"
          >
            Description
          </label>
          <input
            type="text"
            placeholder="Description"
            {...register("description")}
            className={`focus:shadow-outline mb-3 w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${
              errors.description?.message ? "border-red-500" : ""
            }`}
          />
          <p className="text-xs italic text-red-500">
            {errors.description?.message}
          </p>
        </div>

        {/* <div className="mb-5">
          <label className="block mb-2 text-sm font-bold text-gray-700" htmlFor="interval">
            Shippable
          </label>
          <select
            className="px-3 py-2 mb-3 w-full leading-tight text-gray-700 rounded border shadow appearance-none focus:outline-none focus:shadow-outline"
            {...register("shippable")}
          >
            {selectShippable.map((option) => (
              <option value={option.value} key={`shippable_${option.key}`}>
                {option.value}
              </option>
            ))}
          </select>
          <p className="text-xs italic text-red-500">{errors.shippable?.message}</p>
        </div> */}

        <LazyLoad>
          <InteractiveButton
            type="submit"
            color="white"
            loading={isLoading}
            disabled={isLoading}
          >
            Submit
          </InteractiveButton>
        </LazyLoad>
      </form>
    </div>
  );
};

export default AddAdminStripeProductPage;
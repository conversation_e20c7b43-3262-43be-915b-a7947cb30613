
   import React, { useState, useContext } from "react";
   import { LazyLoad } from "Components/LazyLoad";
   import { useParams, Link } from "react-router-dom";
   import { tokenExpireError, AuthContext } from "Context/Auth";
   import { GlobalContext, showToast } from "Context/Global";
   import MkdSDK from "Utils/MkdSDK";

    
     

    
    
    const sdk = new MkdSDK();
    
    
    
    
    
    
    
      const DashboardPage = () => {
      const params = useParams()
      
      
      const {state, dispatch} = useContext(AuthContext);
    const {state:globalState, dispatch:globalDispatch} = useContext(GlobalContext);

      
      
      
      
      


      
      React.useEffect(() => {
        globalDispatch({
          type: "SETPATH",
          payload: {
            path: "dashboard",
          },
        });
      
    }, []);
  
      
      return (
        <div className=" shadow-md rounded  mx-auto p-5">
          <div className="mt-10">
          <div className="mb-10">
            <h3 className="text-[18px]">Engagement Overview:</h3>
            <div className="flex gap-[10%] flex-wrap md:flex-nowrap py-7">
              <div className="w-[300px] p-3 h-[120px] border border-[black]">
                <h5 className="text-[16px]">Total active users</h5>
                <h5 className="text-[18px] mt-5 text-center">158</h5>
              </div>

              <div className="w-[300px] p-3 h-[120px] border border-[black]">
                <h5 className="text-[16px]">Total active subscriptions </h5>
                <h5 className="text-[18px] mt-5 text-center">250</h5>
              </div>

              <div className="w-[300px] p-3 h-[120px] border border-[black]">
                <h5 className="text-[16px]">Total pdfs generated</h5>
                <h5 className="text-[18px] mt-5 text-center">30</h5>
              </div>
              
            </div>
            <div className="w-[300px] p-3 h-[120px] border border-[black]">
                <h5 className="text-[16px]">Total quizzes generated</h5>
                <h5 className="text-[18px] mt-5 text-center">30</h5>
              </div>
          </div>

          <div className="mb-10">
            <h3 className="text-[18px]">Total revenue:</h3>
            <div className="flex gap-[10%] flex-wrap md:flex-nowrap py-7">
              <div className="w-[300px] p-3 h-[120px] border border-[black]">
                <h5 className="text-[16px]">Monthly Recurring Revenue (MRR): </h5>
                <h5 className="text-[18px] mt-5 text-center">$50,000</h5>
              </div>

              <div className="w-[300px] p-3 h-[120px] border border-[black]">
                <h5 className="text-[16px]">Annual Recurring Revenue (ARR):</h5>
                <h5 className="text-[18px] mt-5 text-center">$150,000</h5>
              </div>

             
            </div>
          </div>

          <div className="mb-10">
            <h3 className="text-[18px]">Recurring Subscriptions</h3>
            <div className="flex gap-[10%] flex-wrap md:flex-nowrap py-7">
              <div className="w-[300px] p-3 h-[120px] border border-[black]">
                <h5 className="text-[16px]">Active Subscriptions: </h5>
                <h5 className="text-[18px] mt-5 text-center">500</h5>
              </div>

              <div className="w-[300px] p-3 h-[120px] border border-[black]">
                <h5 className="text-[16px]">Revenue from Active Subscriptions: </h5>
                <h5 className="text-[18px] mt-5 text-center">$150,000</h5>
              </div>
            </div>
          </div>
        </div>
        </div>
      );
    };
    
    export default DashboardPage;
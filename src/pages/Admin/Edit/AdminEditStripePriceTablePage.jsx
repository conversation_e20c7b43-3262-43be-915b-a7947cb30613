
import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import MkdSDK from "Utils/MkdSDK";
import { useNavigate, useParams } from "react-router-dom";
import { AuthContext, tokenExpireError } from "Context/Auth";
import { GlobalContext, showToast } from "Context/Global";
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import { isImage, empty, isVideo, isPdf } from "Utils/utils";
import {MkdInput} from "Components/MkdInput";
import {InteractiveButton} from "Components/InteractiveButton"
import { SkeletonLoader } from "Components/Skeleton";


let sdk = new MkdSDK();

const EditAdminStripePricePage = (props) => {
const { dispatch } = React.useContext(AuthContext);
const schema = yup
    .object({

        name: yup.string(),
        product_id: yup.string(),
        stripe_id: yup.string(),
        is_usage_metered: yup.string(),
        usage_limit: yup.string(),
        object: yup.string(),
        amount: yup.string(),
        trial_days: yup.string(),
        type: yup.string(),
        status: yup.string(),
    })
    .required();
const { dispatch: globalDispatch } = React.useContext(GlobalContext);
const [fileObj, setFileObj] = React.useState({});
const [isLoading, setIsLoading] = React.useState(false)
const [loading, setLoading] = React.useState(false)

const navigate = useNavigate();

          const [name, setName] = useState('');
          const [product_id, setProductId] = useState(0);
          const [stripe_id, setStripeId] = useState('');
          const [is_usage_metered, setIsUsageMetered] = useState('');
          const [usage_limit, setUsageLimit] = useState(0);
          const [object, setObject] = useState('');
          const [amount, setAmount] = useState(0);
          const [trial_days, setTrialDays] = useState(0);
          const [type, setType] = useState('');
          const [status, setStatus] = useState('');
// const [id, setId] = useState(0);
const {
    register,
    handleSubmit,
    setError,
    setValue,
    formState: { errors },
} = useForm({
    resolver: yupResolver(schema),
});

const params = useParams();

useEffect(function () {
    (async function () {
    try {
        setLoading(true)

        sdk.setTable("stripe_price");
   const result = await sdk.callRestAPI(
      { id: props.activeId ? props.activeId : Number(params?.id) },
      "GET"
    );
        if (!result.error) {

         setValue('name', result.model.name);
         setValue('product_id', result.model.product_id);
         setValue('stripe_id', result.model.stripe_id);
         setValue('is_usage_metered', result.model.is_usage_metered);
         setValue('usage_limit', result.model.usage_limit);
         setValue('object', result.model.object);
         setValue('amount', result.model.amount);
         setValue('trial_days', result.model.trial_days);
         setValue('type', result.model.type);
         setValue('status', result.model.status);


       setName(result.model.name);
       setProductId(result.model.product_id);
       setStripeId(result.model.stripe_id);
       setIsUsageMetered(result.model.is_usage_metered);
       setUsageLimit(result.model.usage_limit);
       setObject(result.model.object);
       setAmount(result.model.amount);
       setTrialDays(result.model.trial_days);
       setType(result.model.type);
       setStatus(result.model.status);

            setLoading(false)

        }
    } catch (error) {
        setLoading(false)

        console.log("error", error);
        tokenExpireError(dispatch, error.message);
    }
    })();
}, []);

const previewImage = (field, target) => {
    let tempFileObj = fileObj;
    tempFileObj[field] = {
    file: target.files[0],
    tempURL: URL.createObjectURL(target.files[0]),
    };
    setFileObj({ ...tempFileObj });
};


const onSubmit = async (_data) => {
    setIsLoading(true)
    try {
        sdk.setTable("stripe_price");
    for (let item in fileObj) {
        let formData = new FormData();
        formData.append('file', fileObj[item].file);
        let uploadResult = await sdk.uploadImage(formData);
        _data[item] = uploadResult.url;
        
    }
    const result = await sdk.callRestAPI(
        {
       id: props.activeId ? props.activeId : Number(params?.id) ,
        
            name: _data.name,
            product_id: _data.product_id,
            stripe_id: _data.stripe_id,
            is_usage_metered: _data.is_usage_metered,
            usage_limit: _data.usage_limit,
            object: _data.object,
            amount: _data.amount,
            trial_days: _data.trial_days,
            type: _data.type,
            status: _data.status,

   
        },
        "PUT"
    );

    if (!result.error) {
        showToast(globalDispatch, "Updated");
        navigate("/admin/stripe_price");
        globalDispatch({
        type: "REFRESH_DATA",
        payload: {
          refreshData: true,
        },
      });
      props.setSidebar(false)
    } else {
        if (result.validation) {
        const keys = Object.keys(result.validation);
        for (let i = 0; i < keys.length; i++) {
            const field = keys[i];
            setError(field, {
            type: "manual",
            message: result.validation[field],
            });
        }
        }
    }
    setIsLoading(false)
    } catch (error) {
        setIsLoading(false)
    console.log("Error", error);
    setError("name", {
        type: "manual",
        message: error.message,
    });
    }
};
React.useEffect(() => {
    globalDispatch({
    type: "SETPATH",
    payload: {
        path: "stripe_price",
    },
    });
}, []);

return (
    <div className=" shadow-md rounded   mx-auto p-5">
    <h4 className="text-2xl font-medium">Edit Stripe Price</h4>
   {loading? (<SkeletonLoader/>) :  (<form className=" w-full max-w-lg" onSubmit={handleSubmit(onSubmit)}>
        
        
  <MkdInput
  type={"text"}
  page={"edit"}
  name={"name"}
  errors={errors}
  label={"Name"}
  placeholder={"Name"}
  register={register}
  className={""}
/>
    
        
  <MkdInput
  type={"number"}
  page={"edit"}
  name={"product_id"}
  errors={errors}
  label={"Product Id"}
  placeholder={"Product Id"}
  register={register}
  className={""}
/>
      
    
        
  <MkdInput
  type={"text"}
  page={"edit"}
  name={"stripe_id"}
  errors={errors}
  label={"Stripe Id"}
  placeholder={"Stripe Id"}
  register={register}
  className={""}
/>
    
        
  <MkdInput
  type={"number"}
  page={"edit"}
  name={"is_usage_metered"}
  errors={errors}
  label={"Is Usage Metered"}
  placeholder={"Is Usage Metered"}
  register={register}
  className={""}
/>
      
    
        
  <MkdInput
  type={"number"}
  page={"edit"}
  name={"usage_limit"}
  errors={errors}
  label={"Usage Limit"}
  placeholder={"Usage Limit"}
  register={register}
  className={""}
/>
      
    
        
    <div className="mb-4  ">
    <label
      className="block text-gray-700 text-sm font-bold mb-2"
      htmlFor="object"
    >
      Object
    </label>
    <textarea
      placeholder="Object"
      {...register("object")}
      className={`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${
        errors.object?.message ? "border-red-500" : ""
      }`}
      row={50}
    ></textarea>
    <p className="text-red-500 text-xs italic">
      {errors.object?.message}
    </p>
  </div>
    
        
  <MkdInput
  type={"number"}
  page={"edit"}
  name={"amount"}
  errors={errors}
  label={"Amount"}
  placeholder={"Amount"}
  register={register}
  className={""}
/>
      
    
        
  <MkdInput
  type={"number"}
  page={"edit"}
  name={"trial_days"}
  errors={errors}
  label={"Trial Days"}
  placeholder={"Trial Days"}
  register={register}
  className={""}
/>
      
    
        
  <MkdInput
  type={"text"}
  page={"edit"}
  name={"type"}
  errors={errors}
  label={"Type"}
  placeholder={"Type"}
  register={register}
  className={""}
/>
    
        
  <MkdInput
  type={"number"}
  page={"edit"}
  name={"status"}
  errors={errors}
  label={"Status"}
  placeholder={"Status"}
  register={register}
  className={""}
/>
      
    
        <InteractiveButton
        type="submit"
        className="bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
        loading={isLoading}
        disable={isLoading}
        >
        Submit
        </InteractiveButton>
    </form>)}
    </div>
);
};

export default EditAdminStripePricePage;


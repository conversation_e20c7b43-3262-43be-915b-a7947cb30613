
  
  import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import MkdSDK from "Utils/MkdSDK";
import { useNavigate, useParams } from "react-router-dom";
import { AuthContext, tokenExpireError } from "Context/Auth";
import { GlobalContext, showToast } from "Context/Global";
import { DynamicContentType } from "Components/DynamicContentType";

let sdk = new MkdSDK();

const EditAdminUserPage = ({ activeId, setSidebar }) => {
  const schema = yup
    .object({
      page: yup.string().required(),
      key: yup.string().required(),
      type: yup.string().required(),
      value: yup.string(),
    })
    .required();

  const selectType = [
    { key: "text", value: "Text" },
    { key: "image", value: "Image" },
    { key: "number", value: "Number" },
    { key: "kvp", value: "Key-Value Pair" },
    { key: "image-list", value: "Image List" },
    { key: "captioned-image-list", value: "Captioned Image List" },
    { key: "team-list", value: "Team List" },
  ];

  const { dispatch } = React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const [contentType, setContentType] = React.useState(selectType[0]?.key);
  const [contentValue, setContentValue] = React.useState('');
  const [cmsId, setCmsId] = React.useState("");
  const [isUpdating, setIsUpdating] = React.useState(false);
  const navigate = useNavigate();
  const {
    register,
    handleSubmit,
    setError,
    setValue,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });

  const selectRole = ["admin", "employee"];
  const selectStatus = [
    { key: "0", value: "Inactive" },
    { key: "2", value: "Suspend" },
    { key: "1", value: "Active" },
  ];

  const onSubmit = async (data) => {
    setIsUpdating(true);
    let sdk = new MkdSDK();
    setIsUpdating(true);
    try {
      sdk.setTable("cms");

      const result = await sdk.cmsEdit(
        cmsId,
        data.page,
        data.key,
        data.type,
        contentValue
      );

      if (!result.error) {
        navigate("/admin/cms");
        showToast(globalDispatch, "Updated");
      } else {
        if (result.validation) {
          const keys = Object.keys(result.validation);
          for (let i = 0; i < keys.length; i++) {
            const field = keys[i];
            setError(field, {
              type: "manual",
              message: result.validation[field],
            });
          }
        }
      }
    } catch (error) {
      console.log("Error", error);
      setError("page", {
        type: "manual",
        message: error.message,
      });
      tokenExpireError(dispatch, error.message);
    }
    setIsUpdating(false);
  };

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "cms",
      },
    });

    (async function () {
      try {
        sdk.setTable("cms");
        const result = await sdk.callRestAPI({ id: activeId }, "GET");
        console.log("result: ", result)
        if (!result.error) {
          setCmsId(result.model.id);
        //   data.page,
        // data.key,
        // data.type,
        setValue("page", result.model.page);
          setValue("type", result.model.content_type);
          setValue("key", result.model.content_key);
          setContentValue(result.model.content_value)
        }
      } catch (error) {
        console.log("Error", error);
        tokenExpireError(dispatch, error.message);
      }
    })();
  }, [activeId]);

  return (
    <div className="mx-auto rounded">
      <div
        className={`flex items-center justify-between gap-4 border-b border-b-[#E0E0E0] p-3`}
      >
        <div className="flex items-center gap-3">
          {/* <svg
            onClick={() => setSidebar(false)}
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
          >
            <path
              d="M14.3322 5.83203L19.8751 11.3749C20.2656 11.7654 20.2656 12.3986 19.8751 12.7891L14.3322 18.332M19.3322 12.082H3.83218"
              stroke="#A8A8A8"
              stroke-width="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg> */}
          <span className="text-lg font-semibold">Edit User</span>
        </div>
        <div className="flex items-center gap-4">
          <button
            className="flex items-center rounded-md border border-[#C6C6C6] px-3 py-2 shadow-sm hover:bg-[#f4f4f4]"
            onClick={() => setSidebar(false)}
          >
            Cancel
          </button>
          <button
            className="flex items-center rounded-md bg-[#4F46E5] px-3 py-2 text-white shadow-sm"
            onClick={async () => {
              await handleSubmit(onSubmit)();
              setSidebar(false);
            }}
            disabled={isUpdating}
          >
            {isUpdating ? "Saving..." : "Save"}
          </button>
        </div>
      </div>
      <form className="w-full p-4 text-left" onSubmit={handleSubmit(onSubmit)}>
        <div className="mb-4">
          <label
            className="mb-2 block text-sm font-bold text-gray-700"
            htmlFor="page"
          >
            Page
          </label>
          <input
            type="text"
            placeholder="Page"
            {...register("page")}
            className={`shadow appearance-none border rounded w-full mb-3 py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline}`}
          />
        </div>
        <div className="mb-4">
          <label
            className="mb-2 block text-sm font-bold text-gray-700"
            htmlFor="key"
          >
            Content Identifier
          </label>
          <input
            type="text"
            placeholder="Content Identifier"
            {...register("key")}
            className={`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${errors.key?.message ? "border-red-500" : ""}`}
          />
          <p className="text-xs italic text-red-500">{errors.key?.message}</p>
        </div>
        <div className="mb-4">
          <label className="mb-2 block text-sm font-bold text-gray-700">
            Content Type
          </label>
          <select
            name="type"
            id="type"
            className="shadow border  rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline"
            {...register("type", { onChange: e => setContentType(e.target.value) })}
          >
            {selectType.map((option) => (
              <option name={option.name} value={option.key} key={option.key}>
                {option.value}
              </option>
            ))}
          </select>
        </div>
        <DynamicContentType contentValue={contentValue} contentType={contentType} setContentValue={setContentValue} />
      </form>
    </div>
  );
};

export default EditAdminUserPage;

  
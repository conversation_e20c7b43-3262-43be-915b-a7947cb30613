
import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import MkdSDK from "Utils/MkdSDK";
import { useNavigate, useParams } from "react-router-dom";
import { AuthContext, tokenExpireError } from "Context/Auth";
import { GlobalContext, showToast } from "Context/Global";
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import { isImage, empty, isVideo, isPdf } from "Utils/utils";
import {MkdInput} from "Components/MkdInput";
import {InteractiveButton} from "Components/InteractiveButton"
import { SkeletonLoader } from "Components/Skeleton";


let sdk = new MkdSDK();

const EditAdminPagesTablePage = (props) => {
const { dispatch } = React.useContext(AuthContext);
const schema = yup
    .object({

        name: yup.string(),
        features: yup.string(),
    })
    .required();
const { dispatch: globalDispatch } = React.useContext(GlobalContext);
const [fileObj, setFileObj] = React.useState({});
const [isLoading, setIsLoading] = React.useState(false)
const [loading, setLoading] = React.useState(false)

const navigate = useNavigate();

          const [name, setName] = useState('');
          const [features, setFeatures] = useState('');
// const [id, setId] = useState(0);
const {
    register,
    handleSubmit,
    setError,
    setValue,
    formState: { errors },
} = useForm({
    resolver: yupResolver(schema),
});

const params = useParams();

useEffect(function () {
    (async function () {
    try {
        setLoading(true)

        sdk.setTable("packages");
   const result = await sdk.callRestAPI(
      { id: props.activeId ? props.activeId : Number(params?.id) },
      "GET"
    );
        if (!result.error) {

         setValue('name', result.model.name);
         setValue('features', result.model.features);


       setName(result.model.name);
       setFeatures(result.model.features);

            setLoading(false)

        }
    } catch (error) {
        setLoading(false)

        console.log("error", error);
        tokenExpireError(dispatch, error.message);
    }
    })();
}, []);

const previewImage = (field, target) => {
    let tempFileObj = fileObj;
    tempFileObj[field] = {
    file: target.files[0],
    tempURL: URL.createObjectURL(target.files[0]),
    };
    setFileObj({ ...tempFileObj });
};


const onSubmit = async (_data) => {
    setIsLoading(true)
    try {
        sdk.setTable("packages");
    for (let item in fileObj) {
        let formData = new FormData();
        formData.append('file', fileObj[item].file);
        let uploadResult = await sdk.uploadImage(formData);
        _data[item] = uploadResult.url;
        
    }
    const result = await sdk.callRestAPI(
        {
       id: props.activeId ? props.activeId : Number(params?.id) ,
        
            name: _data.name,
            features: _data.features,

   
        },
        "PUT"
    );

    if (!result.error) {
        showToast(globalDispatch, "Updated");
        navigate("/admin/packages");
        globalDispatch({
        type: "REFRESH_DATA",
        payload: {
          refreshData: true,
        },
      });
      props.setSidebar(false)
    } else {
        if (result.validation) {
        const keys = Object.keys(result.validation);
        for (let i = 0; i < keys.length; i++) {
            const field = keys[i];
            setError(field, {
            type: "manual",
            message: result.validation[field],
            });
        }
        }
    }
    setIsLoading(false)
    } catch (error) {
        setIsLoading(false)
    console.log("Error", error);
    setError("name", {
        type: "manual",
        message: error.message,
    });
    }
};
React.useEffect(() => {
    globalDispatch({
    type: "SETPATH",
    payload: {
        path: "packages",
    },
    });
}, []);

return (
    <div className=" shadow-md rounded   mx-auto p-5">
    <h4 className="text-2xl font-medium">Edit Packages</h4>
   {loading? (<SkeletonLoader/>) :  (<form className=" w-full max-w-lg" onSubmit={handleSubmit(onSubmit)}>
        
        
  <MkdInput
  type={"text"}
  page={"edit"}
  name={"name"}
  errors={errors}
  label={"Name"}
  placeholder={"Name"}
  register={register}
  className={""}
/>
    
        
    <div className="mb-4  ">
    <label
      className="block text-gray-700 text-sm font-bold mb-2"
      htmlFor="features"
    >
      Features
    </label>
    <textarea
      placeholder="Features"
      {...register("features")}
      className={`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${
        errors.features?.message ? "border-red-500" : ""
      }`}
      row={50}
    ></textarea>
    <p className="text-red-500 text-xs italic">
      {errors.features?.message}
    </p>
  </div>
    
        <InteractiveButton
        type="submit"
        className="bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
        loading={isLoading}
        disable={isLoading}
        >
        Submit
        </InteractiveButton>
    </form>)}
    </div>
);
};

export default EditAdminPagesTablePage;




  import React from "react";
import { AuthContext, tokenExpireError } from "Context/Auth";
import MkdSDK from "Utils/MkdSDK";
import { useNavigate } from "react-router-dom";
import { GlobalContext } from "Context/Global";
import Skeleton from "react-loading-skeleton";
import { ModalSidebar } from "Components/ModalSidebar";
import * as yup from "yup";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { BiFilterAlt, BiSearch } from "react-icons/bi";
import { AiOutlineClose, AiOutlinePlus } from "react-icons/ai";
import { RiDeleteBin5Line } from "react-icons/ri";
import AddAdminEmailPage from "../Add/AddAdminEmailPage";
import EditAdminEmailPage from "../Edit/EditAdminEmailPage";
import { AddButton } from "Components/AddButton";
import { PaginationBar } from "Components/PaginationBar";
import SkeletonLoader from "Components/Skeleton/Skeleton";

let sdk = new MkdSDK();

const columns = [
  {
    header: "ID",
    accessor: "id",
  },
  {
    header: "Email Type",
    accessor: "slug",
  },
  {
    header: "Subject",
    accessor: "subject",
  },
  {
    header: "Tags",
    accessor: "tag",
  },
  {
    header: "Action",
    accessor: "",
  },

];

const AdminEmailListPage = () => {
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const { dispatch } = React.useContext(AuthContext);
  const [data, setCurrentTableData] = React.useState([]);
  const [pageSize, setPageSize] = React.useState(10);
  const [pageCount, setPageCount] = React.useState(0);
  const [dataTotal, setDataTotal] = React.useState(0);
  const [currentPage, setPage] = React.useState(0);
  const [canPreviousPage, setCanPreviousPage] = React.useState(false);
  const [canNextPage, setCanNextPage] = React.useState(false);
  const [openFilter, setOpenFilter] = React.useState(false);
  const [showFilterOptions, setShowFilterOptions] = React.useState(false);
  const [selectedOptions, setSelectedOptions] = React.useState([]);
  const [filterConditions, setFilterConditions] = React.useState([]);
  const [optionValue, setOptionValue] = React.useState("eq");
  const [loading, setLoading] = React.useState(true);
  const [showEditSidebar, setShowEditSidebar] = React.useState(false);
  const [showAddSidebar, setShowAddSidebar] = React.useState(false);
  const [activeEditId, setActiveEditId] = React.useState();
  const navigate = useNavigate();
  const dropdownFilterRef = React.useRef(null);

  const schema = yup.object({
    id: yup.string(),
    email: yup.string(),
    role: yup.string(),
    status: yup.string(),
  });

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });

  const selectRole = ["", "admin", "employee"];
  const selectStatus = [
    { key: "", value: "All" },
    { key: "0", value: "Inactive" },
    { key: "1", value: "Active" },
    { key: "2", value: "Suspend" },
  ];

  function previousPage() {
    getData(currentPage - 1, pageSize);
  }

  function nextPage() {
    getData(currentPage + 1, pageSize);
  }

  const addFilterCondition = (option, selectedValue, inputValue) => {
    const input =
      selectedValue === "eq" && isNaN(inputValue)
    ? `${inputValue}`
    : inputValue;
const condition = `${option},${selectedValue},${input}`
    setFilterConditions((prevConditions) => {
      const newConditions = prevConditions.filter(
        (condition) => !condition.includes(option)
      );
      return [...newConditions, condition];
    });
  };

  const handleFilter = () => {
    getData(0, pageSize, {}, filterConditions);
  };
  const deleteFilter = (deleted) => {
    getData(0, pageSize, {}, deleted);
  }

  async function getData(pageNum, limitNum, data = {}, filters = []) {
   setLoading(showAddSidebar || showEditSidebar ? false : true);
    try {
      sdk.setTable("email");

      const result = await sdk.callRestAPI(
        {
          payload: {
            ...data,
          },
          page: pageNum,
          limit: limitNum,
          filter: filters,
        },
        "PAGINATE"
      );

      if (result) {
        setLoading(false);
      }
      const { list, total, limit, num_pages, page } = result;

      setCurrentTableData(list);
      setPageSize(limit);
      setPageCount(num_pages);
      setPage(page);
      setDataTotal(total);
      setCanPreviousPage(page > 1);
      setCanNextPage(page + 1 <= num_pages);
    } catch (error) {
      setLoading(false);
      console.log("ERROR", error);
      tokenExpireError(dispatch, error.message);
    }
  }

  const onSubmit = (data) => {
    const filters = columns
      .filter((col) => col.accessor)
      .map((col) => {
        const value = getNonNullValue(data[col.accessor]);
        return value ? `${col.accessor},cs,${value}` : null;
      })
      .filter(Boolean);
    getData(0, pageSize, {}, filters);
  };

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "email",
      },
    });

    const delay = 700;
    const timeoutId = setTimeout(async () => {
      await getData(1, pageSize);
    }, delay);

    return () => {
      clearTimeout(timeoutId);
    };
  }, []);

  const handleClickOutside = (event) => {
    if (dropdownFilterRef.current && !dropdownFilterRef.current.contains(event.target)) {
      setOpenFilter(false);
    }
  };

  React.useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const clearAllFilters = () => {
    setSelectedOptions([]);
    setFilterConditions([]);
    getData(1, pageSize);
  };

  return (
    <div className="px-8">
      <div className="flex items-center justify-between py-3">
        <form
          className="relative rounded bg-white"
          onSubmit={handleSubmit(onSubmit)}
        >
          <div className="flex items-center gap-4 text-gray-700 text-nowrap" >
            <div className="relative" ref={dropdownFilterRef}>
              <div
                className="flex cursor-pointer items-center justify-between gap-3 rounded-md border border-gray-200 px-3 py-1"
                onClick={() => setOpenFilter(!openFilter)}
              >
                <BiFilterAlt />
                <span>Filters</span>
                {selectedOptions.length > 0 && (
                  <span className="flex h-6 w-6 items-center justify-center rounded-full bg-gray-800 text-start text-white">
                    {selectedOptions.length}
                  </span>
                )}
              </div>
              {openFilter && (
                <div className="absolute z-10 mt-4 w-[500px] min-w-[90%] top-fill left-0 filter-form-holder bg-white border border-gray-200 rounded-md shadow-lg">
                  <div className="p-4">
                    {selectedOptions?.map((option, index) => (
                      <div key={index} className="mb-2 flex w-full items-center justify-between gap-3 text-gray-600">
                        <div className=" mb-3 w-1/3 rounded-md border border-gray-300 px-3 py-2 leading-tight text-gray-700 outline-none">
                          {option}
                        </div>
                        <select
                          className="mb-3 w-[40%] rounded-md border px-3 py-2 leading-tight text-gray-700 outline-none"
                          onChange={(e) => {
                            setOptionValue(e.target.value);
                          }}
                        >
                          <option value="eq" selected>equals</option>
                          <option value="cs">contains</option>
                          <option value="sw">start with</option>
                          <option value="ew">ends with</option>
                          <option value="lt">lower than</option>
                          <option value="le">lower or equal</option>
                          <option value="ge">greater or equal</option>
                          <option value="gt">greater than</option>
                          <option value="bt">between</option>
                          <option value="in">in</option>
                          <option value="is">is null</option>
                        </select>

                        <input
                          type="text"
                          placeholder="Enter value..."
                          className=" mb-3 w-1/3 rounded-md border px-3 py-2 leading-tight text-gray-700 outline-none"
                          onChange={(e) =>
                            addFilterCondition(
                              option,
                              optionValue,
                              e.target.value
                            )
                          }
                        />

                        <div className="w-1/12 mt-[-10px]">
                          <RiDeleteBin5Line
                            className=" cursor-pointer text-xl"
                            onClick={() => {
                              setSelectedOptions((prevOptions) =>
                                prevOptions.filter((op) => op !== option)
                              );
                              setFilterConditions((prevConditions) => {
                                const newConditions = prevConditions.filter(
                                  (condition) => !condition.includes(option)
                                );
                                deleteFilter(newConditions); // Re-apply filters after deletion
                                return newConditions;
                              });
                            }}
                          />
                        </div>
                      </div>
                    ))}

                    <div className="search-buttons relative flex items-center justify-between font-semibold">
                      <div
                        className="mr-2 flex w-auto cursor-pointer items-center gap-2 rounded bg-gray-100 px-4 py-2.5 font-medium leading-tight text-gray-600 transition duration-150 ease-in-out "
                        onClick={() => {
                          setShowFilterOptions(!showFilterOptions);
                        }}
                      >
                        <AiOutlinePlus />
                        Add filter
                      </div>

                      {showFilterOptions && (
                        <div className="absolute top-11 z-10 bg-white px-5 py-3 text-gray-600 shadow-md">
                          <ul className="flex flex-col gap-2 text-gray-500">
                            {columns.slice(0, -1).map((column) => (
                              <li
                                key={column.header}
                                   className={`${selectedOptions.includes(column.accessor)
                                    ? "cursor-not-allowed text-gray-400"
                                    : "cursor-pointer"
                                    }`}
                                onClick={() => {
                                  if (!selectedOptions.includes(column.header)) {
                                    setSelectedOptions((prev) => [
                                      ...prev,
                                      column.accessor,
                                    ]);
                                  }
                                  setShowFilterOptions(false);
                                }}
                              >
                                {column.header}
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                      {selectedOptions.length > 0 && (
                        <div
                          onClick={clearAllFilters}
                          className="inline-block cursor-pointer  rounded px-6  py-2.5 font-medium leading-tight text-gray-600  transition duration-150 ease-in-out"
                        >
                          Clear all filter
                        </div>
                      )}
                    </div>
                    <button
                      type="button"
                      onClick={handleFilter}
                      className="mt-4 inline-block cursor-pointer rounded bg-blue-500 px-6 py-2.5 font-medium leading-tight text-white transition duration-150 ease-in-out"
                    >
                      Apply Filters
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </form>
        <AddButton onClick={() => setShowAddSidebar(true)} />
      </div>
      {loading ? (
        <SkeletonLoader />
      ) : (
        <div className="overflow-x-auto border-b border-gray-200 shadow ">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                {columns.map((column, index) => (
                  <th
                    key={index}
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                  >
                    {column.header}
                    <span>
                      {column.isSorted
                        ? column.isSortedDesc
                          ? " ▼"
                          : " ▲"
                        : ""}
                    </span>
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 bg-white">
              {data.map((row, i) => {
                return (
                  <tr key={i}>
                    {columns.map((cell, index) => {
                      if (cell.accessor == "") {
                        return (
                          <td
                            key={index}
                            className="whitespace-nowrap px-6 py-4"
                          >
                            <button
                              className="text-[#4F46E5]"
                              onClick={() => {
                                setActiveEditId(row.id);
                                setShowEditSidebar(true);
                              }}
                            >
                              {" "}
                              Edit
                            </button>
                          </td>
                        );
                      }
                      if (cell.mapping && cell.accessor === "status") {
                        return (
                          <td key={index} className="px-6 py-5 whitespace-nowrap inline-block text-sm">
                            {row[cell.accessor] === 1 ? (<span className="bg-[#D1FAE5] rounded-md py-1 px-3 text-[#065F46]">{cell.mapping[row[cell.accessor]]}</span>) : (<span className="bg-[#F4F4F4] rounded-md py-1 px-3 text-[#393939]">{cell.mapping[row[cell.accessor]]}</span>)}
                          </td>
                        );
                      }
                      if (cell.mapping) {
                        return (
                          <td
                            key={index}
                            className="whitespace-nowrap px-6 py-4"
                          >
                            {cell.mapping[row[cell.accessor]]}
                          </td>
                        );
                      }
                      return (
                        <td key={index} className="whitespace-nowrap px-6 py-4">
                          {row[cell.accessor]}
                        </td>
                      );
                    })}
                  </tr>
                );
              })}
            </tbody>
          </table>
          {loading && (
            <>
              <p className=" px-10 py-3 text-xl capitalize ">Loading...</p>
            </>
          )}
          {!loading && data.length === 0 && (
            <>
              <p className=" px-10 py-3 text-xl capitalize ">
                You Don't have any Data
              </p>
            </>
          )}
        </div>
      )}
      <PaginationBar
        currentPage={currentPage}
        pageCount={pageCount}
        pageSize={pageSize}
        canPreviousPage={canPreviousPage}
        canNextPage={canNextPage}
        updatePageSize={(newPageSize) => {
          setPageSize(newPageSize);
          getData(1, newPageSize);
        }}
        previousPage={previousPage}
        nextPage={nextPage}
      />
      <ModalSidebar
        isModalActive={showAddSidebar}
        closeModalFn={() => setShowAddSidebar(false)}
      >
        <AddAdminEmailPage setSidebar={setShowAddSidebar}  getData={getData} />
      </ModalSidebar>
      {showEditSidebar && (
        <ModalSidebar
          isModalActive={showEditSidebar}
          closeModalFn={() => setShowEditSidebar(false)}
        >
          <EditAdminEmailPage activeId={activeEditId} setSidebar={setShowEditSidebar} />
        </ModalSidebar>
      )}
    </div>
  );
};

export default AdminEmailListPage;

    
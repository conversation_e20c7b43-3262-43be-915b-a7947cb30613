
  import React, { useRef } from "react";
  import MkdSDK from "Utils/MkdSDK";
  import { AuthContext } from "Context/Auth";
  import { GlobalContext } from "Context/Global";
  import { useNavigate } from "react-router-dom";
  import { LazyLoad } from "Components/LazyLoad";
  import { ModalSidebar } from "Components/ModalSidebar";
  import { MkdListTableV2 } from "Components/MkdListTable";;
  import { AdminEditUserTablePage, AdminAddUserTablePage } from "Src/routes/LazyLoad"
    let sdk = new MkdSDK();

    const columns = [
  
    
        
        {
            header: 'Id',
            accessor: 'id',
            isSorted: false,
            isSortedDesc: false,
            mappingExist : false,
            mappings: {  }
        },
        
        {
            header: 'Oauth',
            accessor: 'oauth',
            isSorted: false,
            isSortedDesc: false,
            mappingExist : false,
            mappings: {  }
        },
        
        {
            header: 'Role',
            accessor: 'role',
            isSorted: false,
            isSortedDesc: false,
            mappingExist : false,
            mappings: {  }
        },
        
        {
            header: 'First Name',
            accessor: 'first_name',
            isSorted: false,
            isSortedDesc: false,
            mappingExist : false,
            mappings: {  }
        },
        
        {
            header: 'Last Name',
            accessor: 'last_name',
            isSorted: false,
            isSortedDesc: false,
            mappingExist : false,
            mappings: {  }
        },
        
        {
            header: 'Email',
            accessor: 'email',
            isSorted: false,
            isSortedDesc: false,
            mappingExist : false,
            mappings: {  }
        },
        
        {
            header: 'Password',
            accessor: 'password',
            isSorted: false,
            isSortedDesc: false,
            mappingExist : false,
            mappings: {  }
        },
        
        {
            header: 'Type',
            accessor: 'type',
            isSorted: false,
            isSortedDesc: false,
            mappingExist : false,
            mappings: {  }
        },
        
        {
            header: 'Verify',
            accessor: 'verify',
            isSorted: false,
            isSortedDesc: false,
            mappingExist : false,
            mappings: {  }
        },
        
        {
            header: 'Packages Id',
            accessor: 'packages_id',
            isSorted: false,
            isSortedDesc: false,
            mappingExist : false,
            mappings: {  }
        },
        
        {
            header: 'Phone',
            accessor: 'phone',
            isSorted: false,
            isSortedDesc: false,
            mappingExist : false,
            mappings: {  }
        },
        
        {
            header: 'Photo',
            accessor: 'photo',
            isSorted: false,
            isSortedDesc: false,
            mappingExist : false,
            mappings: {  }
        },
        
        {
            header: 'Refer',
            accessor: 'refer',
            isSorted: false,
            isSortedDesc: false,
            mappingExist : false,
            mappings: {  }
        },
        
        {
            header: 'Stripe Uid',
            accessor: 'stripe_uid',
            isSorted: false,
            isSortedDesc: false,
            mappingExist : false,
            mappings: {  }
        },
        
        {
            header: 'Paypal Uid',
            accessor: 'paypal_uid',
            isSorted: false,
            isSortedDesc: false,
            mappingExist : false,
            mappings: {  }
        },
        
        {
            header: 'Two Factor Authentication',
            accessor: 'two_factor_authentication',
            isSorted: false,
            isSortedDesc: false,
            mappingExist : false,
            mappings: {  }
        },
        
        {
            header: 'Status',
            accessor: 'status',
            isSorted: false,
            isSortedDesc: false,
            mappingExist : false,
            mappings: {  }
        },
        
        {
            header: 'Create At',
            accessor: 'create_at',
            isSorted: false,
            isSortedDesc: false,
            mappingExist : false,
            mappings: {  }
        },
        
        {
            header: 'Update At',
            accessor: 'update_at',
            isSorted: false,
            isSortedDesc: false,
            mappingExist : false,
            mappings: {  }
        },
    {
        header: "Action",
        accessor: "",
    },
    ];

    const AdminUserListPage = () => {
      const { dispatch } = React.useContext(AuthContext);
      const { dispatch: globalDispatch } = React.useContext(GlobalContext);
      const navigate = useNavigate();
    
      const [showAddSidebar, setShowAddSidebar] = React.useState(false);
      const [showEditSidebar, setShowEditSidebar] = React.useState(false);
      const [activeEditId, setActiveEditId] = React.useState();
      const refreshRef = useRef(null);
    
      const [selectedItems, setSelectedItems] = React.useState([]);
    
      const onToggleModal = (modal, toggle, ids = []) => {
        switch (modal) {
          case "add":
            setShowAddSidebar(toggle);
            // setSelectedItems(ids);
            break;
          case "edit":
            setShowEditSidebar(toggle);
            setSelectedItems(ids);
            setActiveEditId(ids[0]);
            break;
        }
      };
    
      return (
        <>
          <>
            <div className="overflow-x-auto  rounded bg-white p-5 shadow">
              <LazyLoad>
                <MkdListTableV2
                  columns={columns}
                  tableRole={"admin"}
                  table={"user"}
                  actionId={"id"}
                 actions={{
                    view: { show: true, action: null, multiple: false },
                    edit: {
                      show: true,
                      multiple: false,
                      action: (ids) => onToggleModal("edit", true, ids),
                    },
                    delete: { show: true, action: null, multiple: false },
                    select: { show: true, action: null, multiple: false },
                    add: {
                      show: true,
                      action: () => onToggleModal("add", true),
                      multiple: false,
                      children: "Add New",
                      showChildren: true,
                    },
                    export: { show: false, action: null, multiple: true },
                  }}
                  actionPostion={`ontable`}
                  refreshRef={refreshRef}
                />
              </LazyLoad>
            </div>
          </>
    
<LazyLoad>
            <ModalSidebar
              isModalActive={showAddSidebar}
              closeModalFn={() => setShowAddSidebar(false)}
            >
              <AdminAddUserTablePage setSidebar={setShowAddSidebar} />
            </ModalSidebar>
          </LazyLoad>
    {showEditSidebar &&   
          <LazyLoad>
            <ModalSidebar
              isModalActive={showEditSidebar}
              closeModalFn={() => setShowEditSidebar(false)}
            >
              <AdminEditUserTablePage
                activeId={activeEditId}
                setSidebar={setShowEditSidebar}
              />
            </ModalSidebar>
          </LazyLoad>} 
        </>
      );
    };

    export default AdminUserListPage;

    
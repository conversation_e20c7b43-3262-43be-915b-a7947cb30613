

  import React from "react";
import { AuthContext, tokenExpireError } from "Context/Auth";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { useForm } from "react-hook-form";
import { useNavigate } from "react-router-dom";
import MkdSDK from "Utils/MkdSDK";
import { GlobalContext, showToast } from "Context/Global";
import { getNonNullValue } from "Utils/utils";
import { PaginationBar } from "Components/PaginationBar";
import Skeleton from "react-loading-skeleton";
import { ModalSidebar } from "Components/ModalSidebar";
import { BiFilterAlt, BiSearch } from "react-icons/bi";
import { AiOutlineClose, AiOutlinePlus } from "react-icons/ai";
import { RiDeleteBin5Line } from "react-icons/ri";
import AddAdminPhotoPage from "../Add/AddAdminPhotoPage";
import { AddButton } from "Components/AddButton";
import { SkeletonLoader } from "Components/Skeleton";

let sdk = new MkdSDK();

const columns = [
  {
    header: "Photos",
    accessor: "url",
  },
  {
    header: "Create at",
    accessor: "create_at",
  },
  {
    header: "Action",
    accessor: "",
  },

];

const filterColumns = [
  {
    header: "Date",
    accessor: "create_at",
  },
  {
    header: "ID",
    accessor: "id",
  },
  {
    header: "User ID",
    accessor: "user_id",
  },
  {
    header: "Action",
    accessor: "",
  },
]

const AdminPhotoListPage = () => {
  const { dispatch } = React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const [query, setQuery] = React.useState("");
  const [data, setCurrentTableData] = React.useState([]);
  const [pageSize, setPageSize] = React.useState(3);
  const [pageCount, setPageCount] = React.useState(0);
  const [dataTotal, setDataTotal] = React.useState(0);
  const [currentPage, setPage] = React.useState(0);
  const [canPreviousPage, setCanPreviousPage] = React.useState(false);
  const [canNextPage, setCanNextPage] = React.useState(false);
  const [loadingData, setLoadingData] = React.useState(false);
  const [showAddSidebar, setShowAddSidebar] = React.useState(false);
  const [openFilter, setOpenFilter] = React.useState(false);
  const [showFilterOptions, setShowFilterOptions] = React.useState(false);
  const [selectedOptions, setSelectedOptions] = React.useState([]);
  const [filterConditions, setFilterConditions] = React.useState([]);
  const [searchValue, setSearchValue] = React.useState("");
  const [optionValue, setOptionValue] = React.useState("eq");
  const navigate = useNavigate();
  const globalContext = React.useContext(GlobalContext);
  const dropdownFilterRef = React.useRef(null);
  const [loading, setLoading] = React.useState(true);
  const schema = yup.object({
    date: yup.string(),
    id: yup.string(),
    user_id: yup.string(),
  });
  const {
    register,
    handleSubmit,
    setError,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });

  function updatePageSize(limit) {
    (async function () {
      setPageSize(limit);
      await getData(0, limit);
    })();
  }
  function previousPage() {
    (async function () {
      await getData(currentPage - 1 > 0 ? currentPage - 1 : 0, pageSize);
    })();
  }
  function nextPage() {
    (async function () {
      await getData(
        currentPage + 1 <= pageCount ? currentPage + 1 : 0,
        pageSize
      );
    })();
  }

  async function getData(pageNum, limitNum, data) {
    try {
      sdk.setTable("photo");
      const result = await sdk.callRestAPI(
        {
          payload: { ...data },
          page: pageNum,
          limit: limitNum,
        },
        "PAGINATE"
      );

      const { list, total, limit, num_pages, page } = result;

      setCurrentTableData(list);
      setPageSize(limit);
      setPageCount(num_pages);
      setPage(page);
      setDataTotal(total);
      setCanPreviousPage(page > 1);
      setCanNextPage(page + 1 <= num_pages);
      setLoading(false)
    } catch (error) {
      console.log("ERROR", error);
      tokenExpireError(dispatch, error.message);
    }
  };

  const addFilterCondition = (option, selectedValue, inputValue) => {
    const input =
      selectedValue === "eq" && isNaN(inputValue)
    ? `${inputValue}`
    : inputValue;
const condition = `${option},${selectedValue},${input}` 
    setFilterConditions((prevConditions) => {
      const newConditions = prevConditions.filter(
        (condition) => !condition.includes(option)
      );
      return [...newConditions, condition];
    });
    setSearchValue(inputValue);
  };

  const onSubmit = (data) => {
    let create_at = getNonNullValue(data.date);
    let id = getNonNullValue(data.id);
    let user_id = getNonNullValue(data.user_id);
    let filter = { create_at, id, user_id };
    getData(0, 50, filter);
  };

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "photo",
      },
    });

    (async function () {
      setLoadingData(true);
      await getData(0, 50);
      setLoadingData(false);
    })();
  }, []);

  async function deleteImage(id) {
    sdk.setTable("photo");
    const result = await sdk.callRestAPI({ id }, "DELETE");
    showToast(globalDispatch, "Deleted");
    await getData(0, 50);
  }

  const handleClickOutside = (event) => {
    if (dropdownFilterRef.current && !dropdownFilterRef.current.contains(event.target)) {
      setOpenFilter(false);
    }
  };

  React.useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div className="px-8">
      <div className="flex items-center justify-between py-3">
 
        {/* <h4 className="text-2xl font-medium">Photos </h4> */}
        <AddButton onClick={() => setShowAddSidebar(true)} />
      </div>

      <div>
      {loading ? (
        <SkeletonLoader />
      ) : (
        <div className="shadow overflow-x-auto border-b border-gray-200 ">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                {columns.map((column, index) => (
                  <th
                    key={index}
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                  >
                    {column.header}
                    <span>
                      {column.isSorted
                        ? column.isSortedDesc
                          ? " ▼"
                          : " ▲"
                        : ""}
                    </span>
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 bg-white">
             
              {data.map((row, i) => {
                return (
                  <tr key={i}>
                    {columns.map((cell, index) => {
                      if (cell.accessor == "") {
                        return (
                          <td
                            key={index}
                            className="whitespace-nowrap px-6 py-4"
                          >
                            <button
                              className="text-xs text-red-400"
                              onClick={() => {
                                deleteImage(row.id);
                              }}
                            >
                              {" "}
                              Delete
                            </button>
                          </td>
                        );
                      }
                      if (cell.mapping) {
                        return (
                          <td
                            key={index}
                            className="whitespace-nowrap px-6 py-4"
                          >
                            {cell.mapping[row[cell.accessor]]}
                          </td>
                        );
                      }
                      return (
                        <td key={index} className="px-6 py-4 whitespace-nowrap">
                          {cell.accessor == "url" ? <img width={200} height={200}    src={row?.url.includes("http") ? row?.url : `https://mkdlabs.com${row?.url}`}  alt={row?.caption ? row?.caption : "broken image link"}/> : row[cell.accessor]}
                        </td>
                      )
                    })}
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
            )}
      </div>

      <PaginationBar
        currentPage={currentPage}
        pageCount={pageCount}
        pageSize={pageSize}
        canPreviousPage={canPreviousPage}
        canNextPage={canNextPage}
        updatePageSize={updatePageSize}
        previousPage={previousPage}
        nextPage={nextPage}
      />
      <ModalSidebar
        isModalActive={showAddSidebar}
        closeModalFn={() => setShowAddSidebar(false)}
      >
        <AddAdminPhotoPage setSidebar={setShowAddSidebar} />
      </ModalSidebar>
    </div>
  );
};

export default AdminPhotoListPage;

  

  
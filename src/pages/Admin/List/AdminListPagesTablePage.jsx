
  import React, { useRef } from "react";
  import MkdSDK from "Utils/MkdSDK";
  import { AuthContext } from "Context/Auth";
  import { GlobalContext } from "Context/Global";
  import { useNavigate } from "react-router-dom";
  import { LazyLoad } from "Components/LazyLoad";
  import { ModalSidebar } from "Components/ModalSidebar";
  import { MkdListTableV2 } from "Components/MkdListTable";
  import { AdminEditPagesTablePage, AdminAddPagesTablePage } from "Src/routes/LazyLoad"
    let sdk = new MkdSDK();

    const columns = [
  
    
        
        {
            header: 'Header',
            accessor: 'header',
            isSorted: false,
            isSortedDesc: false,
            mappingExist : false,
            mappings: {  }
        },
    {
        header: "Action",
        accessor: "",
    },
    ];

    const AdminPagesListPage = () => {
      const { dispatch } = React.useContext(AuthContext);
      const { dispatch: globalDispatch } = React.useContext(GlobalContext);
      const navigate = useNavigate();
    
      const [showAddSidebar, setShowAddSidebar] = React.useState(false);
      const [showEditSidebar, setShowEditSidebar] = React.useState(false);
      const [activeEditId, setActiveEditId] = React.useState();
      const refreshRef = useRef(null);
    
      const [selectedItems, setSelectedItems] = React.useState([]);
    
      const onToggleModal = (modal, toggle, ids = []) => {
        switch (modal) {
          case "add":
            setShowAddSidebar(toggle);
            // setSelectedItems(ids);
            break;
          case "edit":
            setShowEditSidebar(toggle);
            setSelectedItems(ids);
            setActiveEditId(ids[0]);
            break;
        }
      };
    
      return (
        <>
          <>
          <div className="overflow-x-auto  rounded bg-white p-5 shadow">
              <LazyLoad>
                <MkdListTableV2
                  columns={columns}
                  tableRole={"admin"}
                  table={"pages"}
                  actionId={"id"}
                 actions={{
                    view: { show: true, action: null, multiple: false },
                    edit: {
                      show: true,
                      multiple: false,
                      action: (ids) => onToggleModal("edit", true, ids),
                    },
                    delete: { show: true, action: null, multiple: false },
                    select: { show: true, action: null, multiple: false },
                    add: {
                      show: true,
                      action: () => onToggleModal("add", true),
                      multiple: false,
                      children: "Add New",
                      showChildren: true,
                    },
                    export: { show: false, action: null, multiple: true },
                  }}
                  actionPostion={`ontable`}
                  refreshRef={refreshRef}
                />
              </LazyLoad>
            </div>
          </>
    
          <LazyLoad>
            <ModalSidebar
              isModalActive={showAddSidebar}
              closeModalFn={() => setShowAddSidebar(false)}
            >
              <AdminAddPagesTablePage setSidebar={setShowAddSidebar} />
            </ModalSidebar>
          </LazyLoad>
    {showEditSidebar &&   
          <LazyLoad>
            <ModalSidebar
              isModalActive={showEditSidebar}
              closeModalFn={() => setShowEditSidebar(false)}
            >
              <AdminEditPagesTablePage
                activeId={activeEditId}
                setSidebar={setShowEditSidebar}
              />
            </ModalSidebar>
          </LazyLoad>} 
 
        </>
      );
    };

    export default AdminPagesListPage;

    
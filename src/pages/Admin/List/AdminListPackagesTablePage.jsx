import React, { useRef, useState } from "react";
import MkdSDK from "Utils/MkdSDK";
import { AuthContext } from "Context/Auth";
import { GlobalContext } from "Context/Global";
import { useNavigate } from "react-router-dom";
import { LazyLoad } from "Components/LazyLoad";
import { ModalSidebar } from "Components/ModalSidebar";
import { ClipLoader } from "react-spinners";
import { AdminEditPackagesTablePage, AdminAddPackagesTablePage } from "Src/routes/LazyLoad";

const columns = [
  {
    header: 'Id',
    accessor: 'id',
  },
  {
    header: 'Name',
    accessor: 'name',
  },
  {
    header: 'Features',
    accessor: 'features',
  },
  {
    header: 'Stripe ID',
    accessor: 'stripe_id',
  },
  {
    header: 'Status',
    accessor: 'status',
    mappingExist: true,
    mappings: {
      0: { text: "Inactive", bg: "#F6A13C", color: "black" },
      1: { text: "Active", bg: "#9DD321", color: "black" },
    },
  },
  {
    header: 'Created At',
    accessor: 'create_at',
  },
  {
    header: 'Updated At',
    accessor: 'update_at',
  }
];

const AdminPackagesListPage = () => {
  const { dispatch } = React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const navigate = useNavigate();

  const [currentTableData, setCurrentTableData] = useState([]);
  const [pageSize, setPageSize] = useState(10);
  const [pageCount, setPageCount] = useState(0);
  const [dataTotal, setDataTotal] = useState(0);
  const [currentPage, setPage] = useState(1);
  const [canPreviousPage, setCanPreviousPage] = useState(false);
  const [canNextPage, setCanNextPage] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [showAddSidebar, setShowAddSidebar] = useState(false);
  const [showEditSidebar, setShowEditSidebar] = useState(false);
  const [activeEditId, setActiveEditId] = useState();

  const getData = async (pageNum, limitNum) => {
    setIsLoading(true);
    try {
      let sdk = new MkdSDK();
      const result = await sdk.callRawAPI(
        `/v4/api/records/stripe_product?page=${pageNum}&limit=${limitNum}`,
        [],
        "GET"
      );

      const { list, total, limit, num_pages, page } = result;
      setCurrentTableData(list);
      setPageSize(limit);
      setPageCount(num_pages);
      setPage(page);
      setDataTotal(total);
      setCanPreviousPage(page > 1);
      setCanNextPage(page + 1 <= num_pages);
    } catch (error) {
      console.log("Error", error);
    } finally {
      setIsLoading(false);
    }
  };

  function updatePageSize(limit) {
    setPageSize(limit);
    getData(1, limit);
  }

  function previousPage() {
    getData(currentPage - 1, pageSize);
  }

  function nextPage() {
    getData(currentPage + 1, pageSize);
  }

  const onToggleModal = (modal, toggle, ids = []) => {
    switch (modal) {
      case "add":
        setShowAddSidebar(toggle);
        break;
      case "edit":
        setShowEditSidebar(toggle);
        setActiveEditId(ids[0]);
        break;
    }
  };

  React.useEffect(() => {
    getData(1, pageSize);
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "packages",
      },
    });
  }, []);

  return (
    <>
      <div className="overflow-x-auto rounded bg-white p-5 shadow">
        <div className="flex justify-between mb-5">
          <h4 className="text-2xl font-medium">Packages</h4>
          <button
            onClick={() => onToggleModal("add", true)}
            className="bg-primary px-4 py-2 text-white rounded-md"
          >
            Add New
          </button>
        </div>

        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              {columns.map((column, index) => (
                <th
                  key={index}
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  {column.header}
                </th>
              ))}
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>

          {!isLoading && currentTableData.length > 0 ? (
            <tbody className="bg-white divide-y divide-gray-200">
              {currentTableData.map((row, i) => (
                <tr key={i}>
                  {columns.map((column, j) => {
                    if (column.mappingExist) {
                      const mapping = column.mappings[row[column.accessor]];
                      return (
                        <td key={j} className="px-6 py-4 whitespace-nowrap">
                          <span
                            className="px-2 py-1 rounded"
                            style={{
                              backgroundColor: mapping?.bg,
                              color: mapping?.color,
                            }}
                          >
                            {mapping?.text}
                          </span>
                        </td>
                      );
                    }
                    return (
                      <td key={j} className="px-6 py-4 whitespace-nowrap">
                        {row[column.accessor]}
                      </td>
                    );
                  })}
                  <td className="px-6 py-4 whitespace-nowrap">
                    <button
                      onClick={() => onToggleModal("edit", true, [row.id])}
                      className="text-indigo-600 hover:text-indigo-900 mr-3"
                    >
                      Edit
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          ) : isLoading ? (
            <tbody>
              <tr>
                <td colSpan={columns.length + 1} className="text-center py-4">
                  <ClipLoader color="#000" size={20} className="mr-3" />
                  Loading...
                </td>
              </tr>
            </tbody>
          ) : (
            <tbody>
              <tr>
                <td colSpan={columns.length + 1} className="text-center py-4">
                  No data found
                </td>
              </tr>
            </tbody>
          )}
        </table>

        {currentTableData.length > 0 && !isLoading && (
          <div className="flex items-center justify-between mt-4">
            <button
              onClick={previousPage}
              disabled={!canPreviousPage}
              className="px-4 py-2 border rounded"
            >
              Previous
            </button>
            <span>
              Page {currentPage} of {pageCount}
            </span>
            <button
              onClick={nextPage}
              disabled={!canNextPage}
              className="px-4 py-2 border rounded"
            >
              Next
            </button>
          </div>
        )}
      </div>

      <LazyLoad>
        <ModalSidebar
          isModalActive={showAddSidebar}
          closeModalFn={() => setShowAddSidebar(false)}
        >
          <AdminAddPackagesTablePage 
            setSidebar={setShowAddSidebar}
            onSuccess={() => {
              setShowAddSidebar(false);
              getData(currentPage, pageSize);
            }}
          />
        </ModalSidebar>
      </LazyLoad>

      {showEditSidebar && (
        <LazyLoad>
          <ModalSidebar
            isModalActive={showEditSidebar}
            closeModalFn={() => setShowEditSidebar(false)}
          >
            <AdminEditPackagesTablePage
              activeId={activeEditId}
              setSidebar={setShowEditSidebar}
              onSuccess={() => {
                setShowEditSidebar(false);
                getData(currentPage, pageSize);
              }}
            />
          </ModalSidebar>
        </LazyLoad>
      )}
    </>
  );
};

export default AdminPackagesListPage;

    
import React, { useState, useEffect } from "react";
import { AuthContext } from "Context/Auth";
import { GlobalContext, showToast } from "Context/Global";
import { BiSolidFileExport } from "react-icons/bi";
import { Document } from "Assets/images";
import { Link } from "react-router-dom";
import MkdSDK from "Utils/MkdSDK";
import TreeSDK from "Utils/TreeSDK";
import DeleteModal from "../Onboarding/DeleteModal";

const DocumentLibrary = () => {
  const { dispatch } = React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const [documents, setDocuments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [deleteId, setDeleteId] = useState(null);
  const [openDeleteModal, setOpenDeleteModal] = useState(false);

  const sdk = new MkdSDK()
  const userId = localStorage.getItem("user")
  console.log("userid", userId)
  useEffect(() => {
    const fetchDocuments = async () => {
      try {
        const result = await sdk.callRawAPI(
          '/v3/api/custom/jordan/user/document-library',
          { page: 1, limit: 100 } ,
          'GET',
           // Adjust limit as needed
        );

        console.log("Documents Response:", result);

        if (result && result.list) {
          setDocuments(result.list);
        }
      } catch (error) {
        console.error('Error fetching documents:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchDocuments();
  }, []);

  const truncateUrl = (url) => {
    if (!url) return '';
    if (url.length <= 50) return url;
    return url.substring(0, 47) + '...';
  };

  const handleDownload = async (url) => {
    try {
      const response = await fetch(url);
      const blob = await response.blob();
      const downloadUrl = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = 'document.pdf'; // You can customize the download filename
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(downloadUrl);
    } catch (error) {
      console.error('Error downloading file:', error);
    }
  };

  const handleDelete = async () => {
    console.log("deleteid",deleteId)
    if (deleteId == null) return;
    try {
      sdk.setTable("document_library");
      await sdk.callRestAPI({ id: deleteId }, "DELETE");
      setDocuments(documents.filter(doc => doc.id !== deleteId));
      showToast(globalDispatch, "Document Deleted");
      setOpenDeleteModal(false);
      setDeleteId(null);
    } catch (err) {
      console.error(err);
    }
  };

  console.log("documents",documents)

  return (
    <>
      {openDeleteModal && (
        <DeleteModal 
          isOpen={openDeleteModal}
          onClose={() => {
            setOpenDeleteModal(false);
            setDeleteId(null);
          }}
          onConfirm={handleDelete}
          itemName="document"
        />
      )}
      
      <div className="md:pl-[3rem] w-full text-7xl min-h-screen h-full text-gray-700 bg-[#E2E6EB] p-[1rem]">
      <div className="w-[95%] md:w-[90%] mx-auto md:mx-0">
          <h1 className="text-[20px] text-[#111928] font-semibold mb-6">Documents Library</h1>
        
          <div className="bg-white border border-gray-200 rounded-lg overflow-hidden mt-[1rem] p-[2rem] pb-[4rem]">
            <div className="overflow-x-auto" style={{ 
              msOverflowStyle: 'none',
              scrollbarWidth: 'none',
              WebkitOverflowScrolling: 'touch'
            }}>
              <div style={{ 
                width: '100%', 
                minWidth: '600px'  // Ensures table doesn't get too squished on mobile
              }}>
                <table className="w-full">
                  <thead>
                    <tr className="bg-[#E4E4E4]">
                      <th className="text-left py-3 px-4 text-sm font-semibold text-gray-700">Document name</th>
                      <th className="text-left py-3 px-4 text-sm font-semibold text-gray-700">Document pdf</th>
                      <th className="text-left py-3 px-4 text-sm font-semibold text-gray-700">Date generated</th>
                      <th className="text-left py-3 px-4 text-sm font-semibold text-gray-700">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {documents.map((doc, index) => (
                      <tr 
                        key={doc.id}
                        className={`${
                          index % 2 === 0 
                            ? 'bg-white hover:bg-[#efefefb8]' 
                            : 'bg-[#EFEFEF]'
                        }`}
                      >
                        <td className="py-3 px-4 text-sm text-gray-900">
                          {doc.title || 'Untitled'}
                        </td>
                        <td className="py-3 px-4 text-sm text-blue-600">
                          <a 
                            href={doc.merged_theme} 
                            target="_blank" 
                            rel="noopener noreferrer"
                            className="hover:text-blue-800 underline"
                          >
                            {truncateUrl(doc.merged_theme)}
                          </a>
                        </td>
                        <td className="py-3 px-4 text-sm text-gray-900">
                          {new Date(doc.create_at).toLocaleDateString()}
                        </td>
                        <td className="py-3 px-4 text-sm">
                          <div className="flex gap-[3rem]">
                            <button 
                              onClick={() => handleDownload(doc.merged_theme)}
                              className="text-blue-600 hover:text-blue-800 underline"
                            >
                              Download
                            </button>
                            <button 
                              onClick={() => {
                                setDeleteId(doc.id);
                                setOpenDeleteModal(true);
                              }}
                              className="text-blue-600 hover:text-blue-800 underline"
                            >
                              Delete
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default DocumentLibrary;
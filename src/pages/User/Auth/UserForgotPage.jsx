import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { Link } from "react-router-dom";
import MkdSDK from "Utils/MkdSDK";
import { tokenExpireError } from "Context/Auth";
import { GlobalContext, showToast } from "Context/Global";
import { InteractiveButton } from "Components/InteractiveButton";
import { Logo, LoginImage } from "Assets/images";

const UserForgotPage = () => {
  const [submitLoading, setSubmitLoading] = useState(false);

  const schema = yup
    .object({
      email: yup.string().email().required(),
    })
    .required();

  const {
    register,
    handleSubmit,
    setError,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });

  const { dispatch } = React.useContext(GlobalContext);

  const onSubmit = async (data) => {
    let sdk = new MkdSDK();
    try {
      setSubmitLoading(true);
      const result = await sdk.forgot(data.email);

      if (!result.error) {
        showToast(dispatch, "Reset Code Sent");
      } else {
        if (result.validation) {
          const keys = Object.keys(result.validation);
          for (let i = 0; i < keys.length; i++) {
            const field = keys[i];
            setError(field, {
              type: "manual",
              message: result.validation[field],
            });
          }
        }
      }
      setSubmitLoading(false);
    } catch (error) {
      setSubmitLoading(false);
      console.log("Error", error);
      setError("email", {
        type: "manual",
        message: error?.response?.data?.message ? error?.response?.data?.message : error?.message,
      });
      tokenExpireError(dispatch, error?.response?.data?.message ? error?.response?.data?.message : error?.message);
    }
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-[#FAFAFA] md:p-4">
      <div className='flex flex-row w-full md:w-[90%] h-screen md:h-[80vh] lg:h-[70vh] xl:h-[80vh]'>
        <div className="w-full lg:w-[50%] flex items-center justify-center bg-white h-screen md:h-[80vh] lg:h-[70vh] xl:h-[80vh] rounded-lg shadow px-[2rem] md:px-[7rem] lg:px-[4rem] xl:px-[7rem] py-[2rem] rounded-[24px]]">
          <div className='w-full max-w-md lg:max-w-none'>
            <div className="flex justify-center mb-6">
              <img src={Logo} alt="Logo" className="w-[55px] h-[73px]" />
            </div>

            <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
              <div>
                <label className="block text-sm mb-1">Email</label>
                <input
                  type="email"
                  placeholder="Email"
                  {...register("email")}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none"
                />
                {errors?.email && (
                  <p className="mt-1 text-xs text-red-500">{errors.email.message}</p>
                )}
              </div>

              <InteractiveButton
                type="submit"
                loading={submitLoading}
                disabled={submitLoading}
                className="w-full py-2 px-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 mt-6"
              >
                Reset Password
              </InteractiveButton>

              <div className="text-center mt-4">
                <Link to="/user/login" className="text-blue-600 text-sm">
                  Remember your password? Login
                </Link>
              </div>
            </form>

            
          </div>
        </div>
        <img src={LoginImage} alt='login image' className='w-[50%] hidden lg:flex'/>
      </div>
    </div>
  );
};

export default UserForgotPage;

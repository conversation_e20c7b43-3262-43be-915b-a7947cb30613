import React, { useState, useContext } from 'react';
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { AuthContext } from "Context/Auth";
import { GlobalContext, showToast } from "Context/Global";
import MkdSDK from "Utils/MkdSDK";
import { InteractiveButton } from "Components/InteractiveButton";
import { Logo, LoginImage } from "Assets/images";
const LoginUI = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [submitLoading, setSubmitLoading] = useState(false);
  const { dispatch } = useContext(AuthContext);
  const { dispatch: GlobalDispatch } = useContext(GlobalContext);
  const location = useLocation();
  const navigate = useNavigate();
  const sdk = new MkdSDK();

  const schema = yup.object({
    email: yup.string().email().required(),
    password: yup.string().required(),
  }).required();

  const { register, handleSubmit, setError, formState: { errors } } = useForm({
    resolver: yupResolver(schema),
  });

  const onSubmit = async (data) => {
    try {
      setSubmitLoading(true);
      const result = await sdk.login(data.email, data.password, "user");
      if (!result.error) {
        dispatch({
          type: "LOGIN",
          payload: result,
        });
        showToast(GlobalDispatch, "Successfully Logged In", 4000, "success");
        navigate("/user/dashboard");
      } else {
        setSubmitLoading(false);
        if (result.validation) {
          Object.keys(result.validation).forEach(field => {
            setError(field, {
              type: "manual",
              message: result.validation[field],
            });
          });
        }
      }
    } catch (error) {
      setSubmitLoading(false);
      showToast(GlobalDispatch, error?.response?.data?.message || error?.message, 4000, "error");
      setError("email", {
        type: "manual",
        message: error?.response?.data?.message || error?.message,
      });
    }
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-[#FAFAFA] md:p-4">
      <div className='flex flex-row w-full md:w-[90%] h-screen md:h-[80vh]'>
        <div className="w-full lg:w-[50%] flex items-center justify-center bg-white h-screen md:h-[80vh] rounded-lg shadow px-[2rem] md:px-[7rem] py-[2rem] rounded-[24px]]">
          <div className='w-full max-w-md lg:max-w-none'>
            <div className="flex justify-center mb-6">
              <img src={Logo} alt="Logo" className="w-[55px] h-[73px]" />
            </div>

            <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
              <div>
                <label className="block text-sm mb-1">Email</label>
                <input
                  type="email"
                  placeholder="Placeholder"
                  {...register("email")}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none"
                />
                {errors?.email && (
                  <p className="mt-1 text-xs text-red-500">{errors.email.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm mb-1">Password</label>
                <div className="relative">
                  <input
                    type={showPassword ? "text" : "password"}
                    placeholder="Password"
                    {...register("password")}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none pr-10"
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-1/2 -translate-y-1/2"
                  >
                    {showPassword ? (
                      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                        <path d="M2.5 10C2.5 10 5 5 10 5C15 5 17.5 10 17.5 10C17.5 10 15 15 10 15C5 15 2.5 10 2.5 10Z" stroke="#A8A8A8" strokeWidth="1.5"/>
                        <path d="M10 12.5C11.3807 12.5 12.5 11.3807 12.5 10C12.5 8.61929 11.3807 7.5 10 7.5C8.61929 7.5 7.5 8.61929 7.5 10C7.5 11.3807 8.61929 12.5 10 12.5Z" stroke="#A8A8A8" strokeWidth="1.5"/>
                      </svg>
                    ) : (
                      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                        <path d="M2.5 10C2.5 10 5 5 10 5C15 5 17.5 10 17.5 10C17.5 10 15 15 10 15C5 15 2.5 10 2.5 10Z" stroke="#A8A8A8" strokeWidth="1.5"/>
                        <path d="M10 12.5C11.3807 12.5 12.5 11.3807 12.5 10C12.5 8.61929 11.3807 7.5 10 7.5C8.61929 7.5 7.5 8.61929 7.5 10C7.5 11.3807 8.61929 12.5 10 12.5Z" stroke="#A8A8A8" strokeWidth="1.5"/>
                        <path d="M4 4L16 16" stroke="#A8A8A8" strokeWidth="1.5"/>
                      </svg>
                    )}
                  </button>
                  {errors?.password && (
                    <p className="mt-1 text-xs text-red-500">{errors.password.message}</p>
                  )}
                </div>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="remember"
                  className="w-4 h-4 rounded border-gray-300"
                />
                <label htmlFor="remember" className="ml-2 text-sm text-gray-600">
                  Remember me for 30 days
                </label>
              </div>

              <InteractiveButton
                type="submit"
                loading={submitLoading}
                disabled={submitLoading}
                className="w-full py-2 px-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 mt-6"
              >
                LOGIN
              </InteractiveButton>

              <div className="text-center mt-4">
                <Link to="/user/forgot" className="text-blue-600 text-sm">
                  Forgot password
                </Link>
                <div className="mt-2">
                  <Link to="/user/signup" className="text-blue-600 text-sm">
                    Don't have an account? Sign up
                  </Link>
                </div>
              </div>
            </form>
          </div>
        </div>
        <img src={LoginImage} alt='login image' className='w-[50%] hidden lg:flex'/>
      </div>
    </div>
  );
};

export default LoginUI;
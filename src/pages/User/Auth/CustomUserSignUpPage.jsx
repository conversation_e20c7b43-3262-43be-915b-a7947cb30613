import React, { useState, useContext } from 'react';
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { AuthContext } from "Context/Auth";
import { GlobalContext, showToast } from "Context/Global";
import MkdSDK from "Utils/MkdSDK";
import { InteractiveButton } from "Components/InteractiveButton";
import { Logo, LoginImage } from "Assets/images";
import { Elements } from '@stripe/react-stripe-js';
import { CardElement } from '@stripe/react-stripe-js';
import { loadStripe } from '@stripe/stripe-js';
import CardDetails from '../Payment/CardDetails';

const stripePromise =  loadStripe('pk_test_51R3UZOBazfqAX4xwvEnCZFjXvlOqYkch3qznif64teEvrxi2IzKXj2XKc5LTHhunoQiUE266ZCokcSNHAvswJOt900QLGddBPx');

const SignupUI = () => {
  const [submitLoading, setSubmitLoading] = useState(false);
  const { dispatch } = useContext(AuthContext);
  const { dispatch: GlobalDispatch } = useContext(GlobalContext);
  const { state } = React.useContext(AuthContext);

  const location = useLocation();
  const navigate = useNavigate();
  const sdk = new MkdSDK();
  const searchParams = new URLSearchParams(location.search);
  const redirect_uri = searchParams.get('redirect_uri');
  const [showPassword, setShowPassword] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [formData, setFormData] = useState(null);

  const schema = yup.object({
    firstName: yup.string().required(),
    lastName: yup.string().required(),
    email: yup.string().email().required(),
    password: yup.string().required(),
  }).required();

  const { register, handleSubmit, setError, formState: { errors } } = useForm({
    resolver: yupResolver(schema),
  });

  const handleFormSubmit = async (data) => {
    try {
      setSubmitLoading(true);
      // Check if email already exists
      const emailCheckResult = await sdk.callRawAPI(
        `/v3/api/custom/jordan/user/check-email`,
        {
          email: data.email
        },
        "POST"
      );

      if (emailCheckResult.error || emailCheckResult.exists) {
        // Email already exists
        showToast(GlobalDispatch, "This email is already registered. Please use a different email or login.", 4000, "error");
        setSubmitLoading(false);
        return;
      }

      // Email doesn't exist, proceed with payment modal
      setFormData(data);
      setShowPaymentModal(true);
    } catch (error) {
      showToast(GlobalDispatch, error?.message || "An error occurred. Please try again.", 4000, "error");
    } finally {
      setSubmitLoading(false);
    }
  };

  const handlePaymentSubmit = async ({ stripe, elements }) => {
    try {
      setSubmitLoading(true);
      // Create payment intent
      const paymentIntentResult = await sdk.callRawAPI(
        `/v3/api/custom/jordan/user/create-payment-intent`,
        {
          price_id: 1,
          email: formData.email,
        },
        "POST"
      );

      if (!paymentIntentResult.error) {
        const { stripe_payment_id, secret } = paymentIntentResult.data;
        const { paymentIntent, error } = await stripe.confirmCardPayment(secret, {
          payment_method: { card: elements.getElement(CardElement), },
        });
        console.log("paymentIntent",paymentIntent)

        const response = await sdk.callRawAPI(
          `/v3/api/custom/jordan/user/confirm-registration-payment`,
          {
            "payment_intent_id":paymentIntent?.id,
            email: formData.email,
          },
          "POST"
        );
        if (!response.error){
          const response2 = await sdk.callRawAPI(
            `/v3/api/custom/jordan/user/register-subscribe`,
            {
              "paymentIntentId":paymentIntent?.id,
              "email": formData.email,
              "role":formData.role,
              "password":formData.password,
              "first_name":formData.firstName,
              "last_name":formData.lastName,

            },
            "POST"
          );

          console.log("respose2",response2)

          if (!response2.error){
            dispatch({
              type: "LOGIN",
              payload: response2,
            });
            const user_id= response2.user_id
            console.log("user_id",user_id)
            sdk.verifyUser(user_id)
            showToast(GlobalDispatch, "Successfully Registered", 4000, "success");
            navigate(redirect_uri ?? "/user/dashboard");
          }
        }

        // setShowPaymentModal(false);
        // Proceed with user registration or other necessary steps
      } else {
        showToast(GlobalDispatch, "Payment intent creation failed", 4000, "error");
      }
    } catch (error) {
      showToast(GlobalDispatch, error?.message, 4000, "error");
    } finally {
      setSubmitLoading(false);
    }
  };

  return (
    <>
      <div className="flex flex-col items-center justify-center min-h-screen bg-[#FAFAFA] md:p-4">
        <div className='flex flex-row w-full md:w-[90%] h-screen md:h-[80vh] lg:h-[70vh] xl:h-[80vh]'>
          <div className="w-full lg:w-[50%] flex items-center justify-center bg-white h-screen md:h-[80vh] lg:h-[70vh] xl:h-[80vh] rounded-lg shadow px-[2rem] md:px-[7rem] lg:px-[4rem] xl:px-[7rem] py-[2rem] rounded-[24px]]">
            <div className='w-full max-w-md lg:max-w-none'>
              <div className="flex justify-center mb-6">
                <img src={Logo} alt="Logo" className="w-[55px] h-[73px]" />
              </div>

              <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-4">
                <div>
                  <label className="block text-sm mb-1">First Name</label>
                  <input
                    type="text"
                    placeholder="First Name"
                    {...register("firstName")}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none"
                  />
                  {errors?.firstName && (
                    <p className="mt-1 text-xs text-red-500">{errors.firstName.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm mb-1">Last Name</label>
                  <input
                    type="text"
                    placeholder="Last Name"
                    {...register("lastName")}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none"
                  />
                  {errors?.lastName && (
                    <p className="mt-1 text-xs text-red-500">{errors.lastName.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm mb-1">Email</label>
                  <input
                    type="email"
                    placeholder="Email"
                    {...register("email")}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none"
                  />
                  {errors?.email && (
                    <p className="mt-1 text-xs text-red-500">{errors.email.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm mb-1">Password</label>
                  <div className="relative">
                    <input
                      type={showPassword ? "text" : "password"}
                      placeholder="Password"
                      {...register("password")}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none pr-10"
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute right-3 top-1/2 -translate-y-1/2"
                    >
                      {showPassword ? (
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                          <path d="M2.5 10C2.5 10 5 5 10 5C15 5 17.5 10 17.5 10C17.5 10 15 15 10 15C5 15 2.5 10 2.5 10Z" stroke="#A8A8A8" strokeWidth="1.5"/>
                          <path d="M10 12.5C11.3807 12.5 12.5 11.3807 12.5 10C12.5 8.61929 11.3807 7.5 10 7.5C8.61929 7.5 7.5 8.61929 7.5 10C7.5 11.3807 8.61929 12.5 10 12.5Z" stroke="#A8A8A8" strokeWidth="1.5"/>
                        </svg>
                      ) : (
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                          <path d="M2.5 10C2.5 10 5 5 10 5C15 5 17.5 10 17.5 10C17.5 10 15 15 10 15C5 15 2.5 10 2.5 10Z" stroke="#A8A8A8" strokeWidth="1.5"/>
                          <path d="M10 12.5C11.3807 12.5 12.5 11.3807 12.5 10C12.5 8.61929 11.3807 7.5 10 7.5C8.61929 7.5 7.5 8.61929 7.5 10C7.5 11.3807 8.61929 12.5 10 12.5Z" stroke="#A8A8A8" strokeWidth="1.5"/>
                          <path d="M4 4L16 16" stroke="#A8A8A8" strokeWidth="1.5"/>
                        </svg>
                      )}
                    </button>
                    {errors?.password && (
                      <p className="mt-1 text-xs text-red-500">{errors.password.message}</p>
                    )}
                  </div>
                </div>

                <InteractiveButton
                  type="submit"
                  loading={submitLoading}
                  disabled={submitLoading}
                  className="w-full py-2 px-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 mt-6"
                >
                  {submitLoading ? "CHECKING..." : "CREATE ACCOUNT"}
                </InteractiveButton>

                <div className="text-center mt-4">
                  <Link to="/user/login" className="text-blue-600 text-sm">
                    Already a member? Login
                  </Link>
                </div>
              </form>
            </div>
          </div>
          <img src={LoginImage} alt='login image' className='w-[50%] hidden lg:flex'/>
        </div>
      </div>

      {showPaymentModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999]">
          <div className="relative bg-white rounded-lg w-full max-w-4xl">
            <Elements stripe={stripePromise}>
              <CardDetails
                onClose={() => setShowPaymentModal(false)}
                onSubmit={handlePaymentSubmit}
                price={99}
                loading={submitLoading}
              />
            </Elements>
          </div>
        </div>
      )}
    </>
  );
};

export default SignupUI;
import React, { useState, useEffect } from "react";
import { AuthContext, tokenExpireError } from "Context/Auth";
import { GlobalContext, showToast } from "Context/Global";
import { BiSolidFileExport } from "react-icons/bi";
import { Gem } from "Assets/images";
import Modal from "./modal"
import { Link, useNavigate } from "react-router-dom";
import { DocumentDownload, UploadIcon, Monitor, FormIcon } from "Assets/images";
import MkdSDK from "Utils/MkdSDK";


const GenerateQuiz = () => {
  const { dispatch, state: authState } = React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const navigate = useNavigate();
  const userId = authState?.user;
  const [theme] = useState('Customer Service');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isPremium, setIsPremium] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [allThemes] = useState(() => {
    const stored = localStorage.getItem(`allThemes_${userId}`);
    return stored ? JSON.parse(stored) : [];
  });
  const [mergedPdfUrl, setMergedPdfUrl] = useState(() => {
    // Get the URL from localStorage if it exists
    return localStorage.getItem(`mergedPdfUrl_${userId}`) || '';
  });
  const [showQuizTypes, setShowQuizTypes] = useState(false);
  const [selectedQuizType, setSelectedQuizType] = useState(null);
  const [quizCount, setQuizCount] = useState(0);
  const [planId, setPlanId] = useState(null);
  const [completedThemes] = useState(() => {
    const stored = localStorage.getItem(`completedThemes_${userId}`);
    return stored ? JSON.parse(stored) : [];
  });

  const quizTypes = [
    { id: 1, name: 'Single line text inputs' },
    { id: 2, name: 'Multiple Choice questions' },
    { id: 3, name: 'True False questions' }
  ];

  useEffect(() => {
    const checkPremiumStatus = async () => {
      const sdk = new MkdSDK();
      try {
        const subscription = await sdk.getCustomerStripeSubscription();
        console.log("Subscription response:", subscription);
        
        // Check if this is our test user
        const isTestUser = subscription.customer?.email === "<EMAIL>";
        
        // Consider user premium if they have active subscription OR if they're our test user
        const isPremiumUser = isTestUser || (
          subscription.customer && 
          subscription.customer.subId !== null && 
          subscription.customer.planId !== null
        );
        
        setPlanId(subscription.customer?.planId);
        setIsPremium(isPremiumUser);

        // Get user's quiz count
        const userInfo = await sdk.callRawAPI(
          `/v3/api/custom/jordan/user/user-info`,
          {},
          "GET"
        );

        if (!userInfo.error && userInfo.data) {
          setQuizCount(userInfo.data.quizzes);
        }
      } catch (error) {
        console.error("Premium status check error:", error);
        setIsPremium(false);
      }
    };

    checkPremiumStatus();
  }, []);

  const getQuizLimit = (planId) => {
    switch (planId) {
      case 5: // Basic
        return 5;
      case 6: // Starter
        return 10;
      case 7: // Pro
        return 15;
      default:
        return 0;
    }
  };

  const handleGenerateQuiz = async () => {
    if (!isPremium) {
      setIsModalOpen(true);
      return;
    }

    if (!selectedQuizType) {
      setShowQuizTypes(true);
      return;
    }

    // const quizLimit = getQuizLimit(planId);
    // if (quizCount >= quizLimit) {
    //   showToast(globalDispatch, `You have reached your quiz limit (${quizLimit}) for your current plan.`, 6000, "error");
    //   setIsModalOpen(true);
    //   return;
    // }

    try {
      setIsGenerating(true);
      const sdk = new MkdSDK();

      if (!mergedPdfUrl) {
        showToast(globalDispatch, "Merged PDF URL not found", 4000, "error");
        return;
      }

      const response = await sdk.callRawAPI(
        "/v3/api/custom/theme/generate-quiz",
        {
          pdf_url: mergedPdfUrl,
          quiz_type: selectedQuizType.id.toString()
        },
        "POST"
      );

      console.log('Generate Quiz Response:', response);

      if (!response.error) {
        console.log('Quiz Data:', response.data);
        // Store the quiz ID from the correct location in the response
        const quizId = response.data.quiz_id || response.id;
        console.log('Storing Quiz ID:', quizId);
        
        localStorage.setItem('generatedQuiz', JSON.stringify(response.data));
        localStorage.setItem('quizType', selectedQuizType.id);
        localStorage.setItem('quizId', quizId);
        setQuizCount(prev => prev + 1);
        navigate('/user/onboarding/quiz');
      } else {
        showToast(globalDispatch, "Failed to generate quiz", 4000, "error");
      }
    } catch (error) {
      console.error('Error:', error);
      showToast(globalDispatch, error.message || "Failed to generate quiz", 4000, "error");
      tokenExpireError(dispatch, error.message);
    } finally {
      setIsGenerating(false);
    }
  };

  const handleUpgrade = () => {
    // Handle upgrade logic here
    setIsModalOpen(false);
  };

  const handlePdfDownload = async () => {
    try {
      if (!mergedPdfUrl) {
        showToast(globalDispatch, "PDF URL not found", 4000, "error");
        return;
      }

      // Fetch the PDF
      const response = await fetch(mergedPdfUrl);
      const blob = await response.blob();
      
      // Create a download link
      const downloadUrl = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = 'merged_themes_report.pdf';
      
      // Trigger download
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      // Cleanup
      window.URL.revokeObjectURL(downloadUrl);
    } catch (error) {
      console.error('PDF download error:', error);
      showToast(globalDispatch, "Failed to download PDF", 4000, "error");
    }
  };

  return (
    <>
      <div className="w-full text-7xl min-h-screen h-full text-gray-700 bg-[#E2E6EB] p-[1rem]">
             <div className="w-[95%] md:w-[90%] mx-auto md:mx-0">
      <h1 className="text-[20px] text-[#111928] font-semibold mb-6">Generate themes document</h1>

      {/* Progress Steps */}
      <div className="flex items-center justify-between mb-8 relative bg-white h-[98px] p-[1rem]">
        <div className="absolute left-8 right-8 top-1/4 md:top-1/3 h-0.5 bg-gray-200 w-[100% - 2rem]"/>
        <div className="flex flex-col items-start z-10 w-[28%]">
          <div className="w-10 h-10 border border-[#9C9C9C] bg-white rounded-full flex items-center justify-center">
            <div className="w-6 h-6 bg-[#389C14] rounded-full flex items-center  justify-center">
              <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"/>
              </svg>
            </div>
          </div>
          <Link to="/user/onboarding" className="text-xs md:text-sm hover:text-[#054FB1] transition-colors">Upload your resume & full history docs</Link>
        </div>
        <div className="flex flex-col items-center z-10 w-[24%]">
          <div className="w-10 h-10 border border-[#9C9C9C] bg-white rounded-full flex items-center justify-center">
            <div className="w-6 h-6 bg-[#389C14] rounded-full flex items-center  justify-center">
              <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"/>
              </svg>
            </div>
          </div>
          <Link to="/user/onboarding/form" className="text-xs md:text-sm hover:text-[#054FB1] transition-colors">Fill out questions form</Link>
        </div>
        <div className="flex flex-col items-end z-10 w-[24%]">
          <div className="w-8 h-8 bg-[#054FB1] rounded-full flex items-center justify-center text-gray-500 mb-2">
            <img src={Monitor} className="w-[20px] h-[20px]" alt="upload icon"/>
          </div>
          <Link to="/user/onboarding/theme" className="text-xs md:text-sm text-[#054FB1]">
            Generate Themes ({completedThemes.length}/{allThemes.length})
          </Link>
        </div>
      </div>

      <div className="text-right font-bold text-[14px] md:text-[16px] text-[#373A4B]">50% completed</div>

      <div className=" mt-[2rem] mx-auto p-8 bg-white">
        <div className="flex flex-col-reverse gap-[1rem] md:gap-0 md:flex-row md:justify-between md:items-center mb-8">
          <div className="text-lg">
            <span className="font-medium">Theme title: </span>
            <span>{theme}</span>
          </div>
          <div className="flex items-center gap-2 cursor-pointer" onClick={handlePdfDownload}>
            <img src={DocumentDownload} alt="document download"/>
            <span className="text-[#111928] font-medium text-[12px]">PDF Preview</span>
          </div>
        </div>

        <div className="border rounded-lg p-6 mb-8">
          <h2 className="text-lg font-medium mb-4">
            Tell me a time when you made a MISTAKE how did you fix it? (Eaves Cleaning Mistake)
          </h2>

          <div className="mb-4">
            <h3 className="font-normal mb-2 text-[16px]">Situation:</h3>
            <ul className="space-y-3 text-[16px] text-[#111928] leading-[24px] mt-[1rem] list-disc pl-5">
              <li>In the Fall my business, Tiger Building Services, does a lot of eavestrough cleaning.</li>
              <li>Back in 2019 I was working with an employee in my truck. We were working nicely to hit my daily revenue target.</li>
              <li>We got to the last job of the day; we were tired and running out of sunlight. But I really wanted to squeeze it in.</li>
              <li>We have procedures to follow in order to work safely and effectively. My goal is to be as low impact as possible.</li>
              <li>They were livid. Swearing and completely unhappy with how we were doing the work. I take ownership of my mistakes and realized I screwed up by using blowers instead of hand bombing it.</li>
            </ul>
          </div>
        </div>

        <div className="space-y-4">
          <div className="flex flex-col-reverse md:flex-row justify-end gap-[1rem] md:pl-[3rem]">
            <button className="px-6 py-2 border border-blue-600 text-[#054FB1] rounded hover:bg-blue-50 transition-colors text-[16px] font-medium md:w-[313px] w-full h-[50px]">
              <Link to="/user/onboarding/allquizzes">
                VIEW ALL GENERATED QUIZZES
              </Link>
            </button>

            <div className="relative">
              <button 
                onClick={() => !isGenerating && setShowQuizTypes(!showQuizTypes)}
                className="px-6 py-2 bg-[#054FB1] text-white rounded hover:bg-blue-700 transition-colors font-medium text-[16px] h-[50px] flex items-center w-full md:w-autojustify-center gap-[0.75rem]"
                disabled={isGenerating}
              >
                {isGenerating ? (
                  <>
                    <svg className="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    GENERATING...
                  </>
                ) : (
                  <>
                    <img src={Gem} alt="gem image" /> GENERATE QUIZ
                  </>
                )}
              </button>

              {/* Quiz Type Dropdown */}
              {showQuizTypes && (
                <div className="absolute right-0 bottom-[calc(100%+8px)] w-64 bg-white border rounded-lg shadow-lg z-50">
                  {quizTypes.map((type) => (
                    <div
                      key={type.id}
                      onClick={() => {
                        setSelectedQuizType(type);
                        setShowQuizTypes(false);
                        handleGenerateQuiz();
                      }}
                      className="w-full px-4 py-3 text-left cursor-pointer hover:bg-gray-50 text-[14px] first:rounded-t-lg last:rounded-b-lg border-b border-gray-100 last:border-b-0"
                    >
                      {type.name}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      </div>
      </div>

      {isModalOpen && (
        <Modal 
          onClose={() => setIsModalOpen(false)}
          onUpgrade={handleUpgrade}
        />
      )}
      </>
  )
}
export default GenerateQuiz;
import React, { useState, useEffect } from "react";
import { AuthContext, tokenExpireError } from "Context/Auth";
import { GlobalContext, showToast } from "Context/Global";
import { BiSolidFileExport } from "react-icons/bi";
import { videoPlayerBg } from "Assets/images";
import { Link, useNavigate } from "react-router-dom";
import { DocumentUpload, UploadIcon,Monitor,FormIcon } from "Assets/images";
import { HiSpeakerWave } from "react-icons/hi2";
import { TbWindowMaximize } from "react-icons/tb";
import MkdSDK from "Utils/MkdSDK";

const Theme = () => {
  const { dispatch, state: authState } = React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const navigate = useNavigate();
  const sdk = new MkdSDK();
  
  const userId = authState?.user;
  const [isPlaying, setIsPlaying] = useState(false);
  const [progress, setProgress] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [themes, setThemes] = useState([]);
  const [selectedTheme, setSelectedTheme] = useState(null);
  const [showThemeDropdown, setShowThemeDropdown] = useState(false);
  const [completedThemes] = useState(() => {
    const stored = localStorage.getItem(`completedThemes_${userId}`);
    return stored ? JSON.parse(stored) : [];
  });

  useEffect(() => {
    const fetchThemes = async () => {
      try {
        sdk.setTable("themes");
        const response = await sdk.callRestAPI({}, "GETALL");
        if (response.list) {
          setThemes(response.list);
        }
      } catch (error) {
        console.error('Error fetching themes:', error);
        tokenExpireError(dispatch, error.message);
      }
    };

    fetchThemes();
  }, []);

  useEffect(() => {
    // Get initial selected theme from localStorage
    const storedTheme = localStorage.getItem(`selectedTheme_${userId}`);
    if (storedTheme) {
      setSelectedTheme(JSON.parse(storedTheme));
    }

    // Listen for theme updates from Sidebar
    const handleThemeUpdate = () => {
      const updatedTheme = localStorage.getItem(`selectedTheme_${userId}`);
      if (updatedTheme) {
        setSelectedTheme(JSON.parse(updatedTheme));
      }
    };

    window.addEventListener('themesUpdated', handleThemeUpdate);

    // Cleanup listener
    return () => {
      window.removeEventListener('themesUpdated', handleThemeUpdate);
    };
  }, [userId]);

  const handleContinue = async () => {
    try {
      setIsLoading(true);

      if (!selectedTheme) {
        showToast(globalDispatch, "Please select a theme", 4000, "error");
        return;
      }

      // Get theme_documents_id from localStorage (saved during Form submission)
      const themeDocId = localStorage.getItem('theme_documents_id');
      
      if (!themeDocId) {
        showToast(globalDispatch, "Missing theme document information", 4000, "error");
        return;
      }

      // Navigate to ThemeContd with required data
      navigate("/user/onboarding/themecontd", {
        state: {
          theme_id: selectedTheme.ds_theme_id,
          theme_documents_id: parseInt(themeDocId)
        }
      });

    } catch (error) {
      console.error('Navigation error:', error);
      showToast(globalDispatch, error.message || "An error occurred", 4000, "error");
      tokenExpireError(dispatch, error.message);
    } finally {
      setIsLoading(false);
    }
  };

  const togglePlay = () => {
    setIsPlaying(!isPlaying);
  };

  
  return (
    <>
      <div className="w-full text-7xl min-h-screen h-full text-gray-700 bg-[#E2E6EB] p-[1rem] md:pl-[3rem]">
      <div className="w-[95%] md:w-[90%] mx-auto md:mx-0">
      <h1 className="text-[20px] text-[#111928] font-semibold mb-6">Generate themes document</h1>
      <div className="space-y-8">

{/* Progress Steps */}
<div className="flex items-center justify-between mb-8 relative bg-white h-[98px] p-[1rem]">
  <div className="absolute left-8 right-8 top-1/4 md:top-1/3 h-0.5 bg-gray-200 w-[100% - 2rem]"/>
  <div className="flex flex-col items-start z-10 w-[33%]">
    <div className="w-10 h-10 border border-[#9C9C9C] bg-white rounded-full flex items-center justify-center">
      <div className="w-6 h-6 bg-[#389C14] rounded-full flex items-center  justify-center">
        <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
          <path d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"/>
        </svg>
      </div>
    </div>
    <Link to="/user/onboarding" className="text-xs md:text-sm hover:text-[#054FB1] transition-colors">Upload your resume & full history docs</Link>
  </div>
  <div className="flex flex-col items-center z-10 w-[33%]">
    <div className="w-10 h-10 border border-[#9C9C9C] bg-white rounded-full  flex items-center justify-center ">
      <div className="w-6 h-6 bg-[#389C14] rounded-full flex items-center  justify-center">
        <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
          <path d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"/>
        </svg>
      </div>
    </div>
    <Link to="/user/onboarding/form" className="text-xs md:text-sm hover:text-[#054FB1] transition-colors">Fill out questions form</Link>
  </div>
  <div className="flex flex-col items-end z-10 w-[33%]">
    <div className="w-8 h-8 bg-[#054FB1] rounded-full flex items-center justify-center text-gray-500 mb-2">
      <img src={Monitor} className="w-[20px] h-[20px]" alt="upload icon"/>
    </div>
    <Link to="/user/onboarding/theme" className="text-xs md:text-sm text-[#054FB1]">
      Generate Themes ({completedThemes.length}/{themes.length})
    </Link>
  </div>
</div>
            
            <div className="text-right font-bold text-[12px] md:text-[16px] text-[#373A4B]">50% completed</div>
          </div>

          {/* White Container */}
          <div className="bg-white p-8 rounded-lg mt-[1rem]">
            {/* Video Player */}
            <div className="relative w-full aspect-video bg-black overflow-hidden">
              <iframe
                width="100%"
                height="100%"
                src="https://www.youtube.com/embed/8Hf-XrPZ0mk"
                title="Getting Started Guide"
                frameBorder="0"
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                allowFullScreen
              ></iframe>
            </div>

            {/* Theme Selection */}
            <div className="flex flex-col md:flex-row items-center justify-between mb-4 mt-[2rem]">
              <div className="flex flex-col md:flex-row w-full md:items-center gap-2 text-[16px] font-medium leading-[24px]">
                <span className="font-medium">Theme Title:</span>
                <span>{selectedTheme?.name}</span>
              </div>
              
              {/* Theme Switcher - Mobile */}
              <div className="relative w-full md:hidden">
                <button 
                  onClick={() => setShowThemeDropdown(!showThemeDropdown)}
                  className="px-4 py-2 text-[#637381] border rounded-md flex items-center justify-between text-[16px] w-full mt-[1rem] h-[48px]"
                >
                  Switch theme
                  <svg 
                    className={`w-4 h-4 transform transition-transform ${showThemeDropdown ? 'rotate-180' : ''}`} 
                    viewBox="0 0 20 20" 
                    fill="currentColor"
                  >
                    <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </button>

                {/* Mobile Theme Dropdown */}
                {showThemeDropdown && (
                  <div className="absolute left-0 right-0 bottom-[calc(100%+8px)] bg-white border rounded-lg shadow-lg z-50 max-h-[200px] overflow-y-auto">
                    {themes.map((theme) => (
                      <button
                        key={theme.id}
                        onClick={() => {
                          setSelectedTheme(theme);
                          localStorage.setItem(`selectedTheme_${userId}`, JSON.stringify(theme));
                          setShowThemeDropdown(false);
                        }}
                        className="w-full px-4 py-2 text-left hover:bg-gray-50 flex items-center justify-between border-b border-gray-100 last:border-b-0"
                      >
                        <span className="text-[#373A4B] text-[14px]">{theme.name}</span>
                        {selectedTheme?.id === theme.id && (
                          <svg className="w-4 h-4 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          </svg>
                        )}
                      </button>
                    ))}
                  </div>
                )}
              </div>

              <button
                onClick={handleContinue}
                disabled={isLoading || !selectedTheme}
                className={`w-full md:w-[371px] h-[50px] bg-[#054FB1] text-white rounded-[6px] hover:bg-blue-700 transition-colors font-[Inter] font-medium text-[16px] leading-[24px] text-center mt-[1rem] md:mt-0
                  ${(!selectedTheme || isLoading) ? 'opacity-50 cursor-not-allowed' : ''}`}
              >
                {isLoading ? 'SAVING...' : 'CONTINUE WITH SELECTED THEME'}
              </button>
            </div>

            {/* Theme Switcher - Desktop */}
            <div className="hidden md:flex justify-end relative">
              <button 
                onClick={() => setShowThemeDropdown(!showThemeDropdown)}
                className="px-4 py-2 text-[#637381] border rounded-md flex items-center gap-2 text-[16px] h-[48px] w-[184px]"
              >
                Switch theme
                <svg 
                  className={`w-4 h-4 transform transition-transform ${showThemeDropdown ? 'rotate-180' : ''}`} 
                  viewBox="0 0 20 20" 
                  fill="currentColor"
                >
                  <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </button>

              {/* Desktop Theme Dropdown */}
              {showThemeDropdown && (
                <div className="absolute right-0 bottom-[calc(100%+8px)] w-64 bg-white border rounded-lg shadow-lg z-50 max-h-[200px] overflow-y-auto">
                  {themes.map((theme) => (
                    <button
                      key={theme.id}
                      onClick={() => {
                        setSelectedTheme(theme);
                        localStorage.setItem(`selectedTheme_${userId}`, JSON.stringify(theme));
                        setShowThemeDropdown(false);
                      }}
                      className="w-full px-4 py-2 text-left hover:bg-gray-50 flex items-center justify-between border-b border-gray-100 last:border-b-0"
                    >
                      <span className="text-[#373A4B] text-[14px]">{theme.name}</span>
                      {selectedTheme?.id === theme.id && (
                        <svg className="w-4 h-4 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                      )}
                    </button>
                  ))}
                </div>
              )}
            </div>
          </div>
      </div>
      </div>
      </>
  )
}
export default Theme;
import React,{useState} from "react";
import { AuthContext,tokenExpireError } from "Context/Auth";
import { GlobalContext,showToast } from "Context/Global";
import { BiSolidFileExport } from "react-icons/bi";
import { Document } from "Assets/images";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { DocumentDownload, UploadIcon,Monitor,FormIcon } from "Assets/images";
import { jsPDF } from "jspdf";
import MkdSDK from "Utils/MkdSDK";


const ThemePreview = () => {
  const { dispatch, state: authState } = React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const navigate = useNavigate();
  const location = useLocation();
  const { themeTitle, initialQuestion, userResponses, theme_id } = location.state || {};
  const userId = authState?.user;
  const themeDocId = localStorage.getItem('theme_documents_id');

  console.log("themedocs",themeDocId)
  // Get themes from localStorage
  const [themes] = useState(() => {
    const storedThemes = localStorage.getItem(`allThemes_${userId}`);
    return storedThemes ? JSON.parse(storedThemes) : [];
  });

  // Get current theme from localStorage
  const [currentTheme] = useState(() => {
    const storedTheme = localStorage.getItem(`selectedTheme_${userId}`);
    return storedTheme ? JSON.parse(storedTheme) : null;
  });

  // Add new state to track generated PDFs
  const [generatedThemes, setGeneratedThemes] = useState(() => {
    const stored = localStorage.getItem(`generatedThemes_${userId}`);
    return stored ? JSON.parse(stored) : [];
  });

  const [showThemeDropdown, setShowThemeDropdown] = useState(false);

  const [completedThemes] = useState(() => {
    const stored = localStorage.getItem(`completedThemes_${userId}`);
    return stored ? JSON.parse(stored) : [];
  });

  const handleContinueToNextTheme = async () => {
    // Get completed themes from localStorage
    const completedThemes = JSON.parse(localStorage.getItem(`completedThemes_${userId}`) || '[]');
    
    // Find all uncompleted themes
    const uncompletedThemes = themes.filter(theme => !completedThemes.includes(theme.id));

    // If there are no uncompleted themes, call merge API
    if (uncompletedThemes.length === 0) {
      try {
        const sdk = new MkdSDK();
        
        // Get all generated themes with their PDFs
        const generatedThemesData = generatedThemes.map(theme => ({
          id: theme.id,
          name: theme.name,
          url: theme.pdfUrl
        }));

        const response = await sdk.callRawAPI(
          "/v3/api/custom/theme/merge",
          {
            pdfLinks: generatedThemes.map(theme => theme.pdfUrl),
            generated_themes: JSON.stringify(generatedThemesData),
            theme_documents_id: themeDocId
          },
          "POST"
        );

        if (!response.error) {
          // Store the merged PDF URL
          localStorage.setItem(`mergedPdfUrl_${userId}`, response.data);
          
          // Clear generated themes from localStorage
          localStorage.removeItem(`generatedThemes_${userId}`);
          
          // Navigate to quiz
          navigate('/user/onboarding/generatequiz');
        } else {
          showToast(globalDispatch, "Failed to merge documents", 4000, "error");
        }
      } catch (error) {
        console.error('Merge error:', error);
        showToast(globalDispatch, error.message || "Failed to merge documents", 4000, "error");
        tokenExpireError(dispatch, error.message);
      }
      return;
    }

    // Find current theme index in all themes
    const currentIndex = themes.findIndex(t => t.id === theme_id);
    
    // Find the next uncompleted theme after current theme
    const nextUncompletedTheme = themes
      .slice(currentIndex + 1)
      .find(theme => !completedThemes.includes(theme.id));

    // If no uncompleted themes after current, get the first uncompleted theme from the beginning
    const nextTheme = nextUncompletedTheme || uncompletedThemes[0];

    // Store next theme and navigate
    localStorage.setItem(`selectedTheme_${userId}`, JSON.stringify(nextTheme));
    navigate('/user/onboarding/themecontd', {
      state: {
        theme_id: nextTheme.ds_theme_id,
        theme_documents_id: themeDocId
      }
    });
  };

  const handleNotSatisfied = () => {
    // Get current theme from localStorage
    const currentTheme = JSON.parse(localStorage.getItem(`selectedTheme_${userId}`));
    
    // Remove this theme from completed themes
    const completedThemes = JSON.parse(localStorage.getItem(`completedThemes_${userId}`) || '[]');
    const updatedCompletedThemes = completedThemes.filter(id => id !== theme_id);
    localStorage.setItem(`completedThemes_${userId}`, JSON.stringify(updatedCompletedThemes));
    
    // Trigger theme completed event to update UI
    document.dispatchEvent(new Event('themeCompleted'));
    
    // Navigate back to chat with isRegenerating flag
    navigate('/user/onboarding/themecontd', {
      state: {
        theme_id: currentTheme.ds_theme_id,
        theme_documents_id: themeDocId,
        isRegenerating: true
      }
    });
  };

  // Find next uncompleted theme for button text
  const currentIndex = themes.findIndex(t => t.id === theme_id);
  const nextUncompletedTheme = themes
    .slice(currentIndex + 1)
    .find(theme => !completedThemes.includes(theme.id)) 
    || themes.find(theme => !completedThemes.includes(theme.id));

  const handlePDFDownload = async () => {
    try {
      // Get the PDF URL from location state
      const { pdfUrl } = location.state || {};
      
      if (!pdfUrl) {
        showToast(globalDispatch, "PDF URL not found", 4000, "error");
        return;
      }

      // Create a temporary link element
      const link = document.createElement('a');
      link.href = pdfUrl;
      link.target = '_blank';
      
      // Set filename for download
      const filename = themeTitle 
        ? `${themeTitle.toLowerCase().replace(/\s+/g, '_')}_preview.pdf`
        : 'theme_preview.pdf';
      link.download = filename;
      
      // Trigger download
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
    } catch (error) {
      console.error('PDF download error:', error);
      showToast(globalDispatch, "Failed to download PDF", 4000, "error");
    }
  };

  return (
    <>
      <div className="w-full text-7xl min-h-screen h-full text-gray-700 bg-[#E2E6EB] p-[1rem] md:pl-[3rem]">
      <div className="w-[95%] md:w-[90%] mx-auto md:mx-0">
      <h1 className="text-[20px] text-[#111928] font-semibold mb-6">Generate themes document</h1>

      {/* Progress Steps */}
      <div className="flex items-center justify-between mb-8 relative bg-white h-[98px] p-[1rem]">
        <div className="absolute left-8 right-8 top-1/4 md:top-1/3 h-0.5 bg-gray-200 w-[100% - 2rem]"/>
        <div className="flex flex-col items-start z-10 w-[28%]">
          <div className="w-10 h-10 border border-[#9C9C9C] bg-white rounded-full flex items-center justify-center">
            <div className="w-6 h-6 bg-[#389C14] rounded-full flex items-center  justify-center">
              <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"/>
              </svg>
            </div>
          </div>
          <Link to="/user/onboarding" className="text-xs md:text-sm hover:text-[#054FB1] transition-colors">Upload your resume & full history docs</Link>
        </div>
        <div className="flex flex-col items-center z-10 w-[24%]">
          <div className="w-10 h-10 border border-[#9C9C9C] bg-white rounded-full flex items-center justify-center">
            <div className="w-6 h-6 bg-[#389C14] rounded-full flex items-center  justify-center">
              <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"/>
              </svg>
            </div>
          </div>
          <Link to="/user/onboarding/form" className="text-xs md:text-sm hover:text-[#054FB1] transition-colors">Fill out questions form</Link>
        </div>
        <div className="flex flex-col items-end z-10 w-[24%]">
          <div className="w-8 h-8 bg-[#054FB1] rounded-full flex items-center justify-center text-gray-500 mb-2">
            <img src={Monitor} className="w-[20px] h-[20px]" alt="upload icon"/>
          </div>
          <Link to="/user/onboarding/theme" className="text-xs md:text-sm text-[#054FB1]">
            Generate Themes ({completedThemes.length}/{themes.length})
          </Link>
        </div>
      </div>
            
      <div className="text-right font-bold text-[14px] md:text-[16px] text-[#373A4B]">50% completed</div>

      <div className=" mt-[2rem] mx-auto p-8 bg-white">
        <div className="flex flex-col-reverse gap-[1rem] md:gap-0 md:flex-row md:justify-between md:items-center mb-8">
          <div className="text-lg">
            <span className="font-medium">Theme title: </span>
            <span>{themeTitle}</span>
          </div>

          <button 
            onClick={handlePDFDownload}
            className="flex items-center gap-2 cursor-pointer hover:opacity-80"
          >
            <img src={DocumentDownload} alt="document download"/>
            <span className="text-[#111928] font-medium text-[12px]">PDF Preview</span>
          </button>
        </div>

        <div className="border rounded-lg p-6 mb-8">
          <h2 className="text-lg font-medium mb-4">
            {initialQuestion}
          </h2>

          <div className="mb-4">
            <h3 className="font-normal mb-2 text-[16px]">Responses:</h3>
            <ul className="space-y-3 text-[16px] text-[#111928] leading-[24px] mt-[1rem] list-disc pl-5">
              {userResponses.map((response, index) => (
                <li key={index}>{response}</li>
              ))}
            </ul>
          </div>
        </div>

        <div className="space-y-4">
          <div className="flex justify-between items-start">
            <div className="flex flex-col gap-1">
              <p className="text-[#111928] font-semibold text-[16px] leading-[28px]">Not satisfied with the response?</p>
              <p className="text-[#373A4B] leading-[24px] text-[16px]">{themeTitle} <span className="md:hidden text-blue-700 underline cursor-pointer" onClick={handleNotSatisfied}>Click here</span></p>
            </div>
            
            <button 
              onClick={handleContinueToNextTheme}
              className="px-6 py-2 bg-[#054FB1] text-white rounded hover:bg-blue-700 transition-colors font-medium text-[16px] h-[50px] hidden md:flex justify-center items-center"
            >
              {nextUncompletedTheme 
                ? `CONTINUE TO ${nextUncompletedTheme.name}`
                : "SUBMIT AND GENERATE FINAL DOCUMENT"}
            </button>
          </div>
          <button 
              onClick={handleContinueToNextTheme}
              className="px-6 py-2 bg-[#054FB1] text-white rounded hover:bg-blue-700 transition-colors font-medium text-sm md:text-[16px] h-[50px] md:hidden"
            >
              {nextUncompletedTheme 
                ? `CONTINUE TO ${nextUncompletedTheme.name}`
                : "SUBMIT AND GENERATE FINAL DOCUMENT"}
            </button>
          
          <div className="flex justify-between items-center">
            <button 
              onClick={handleNotSatisfied}
              className="px-6 py-2 border border-[#054FB1] text-[#054FB1] text-[16px] font-medium h-[50px] rounded hover:bg-blue-50 transition-colors hidden md:flex justify-center items-center"
            >
              CLICK HERE
            </button>
            
        
            
            {/* Theme Switcher */}
            <div className="relative w-full md:w-auto">
              <button 
                onClick={() => setShowThemeDropdown(!showThemeDropdown)}
                className="px-6 py-2 border rounded flex items-center gap-2 text-[16px] h-[48px] w-[100%] md:w-[184px] text-[#637381]"
              >
                Switch theme
                <svg 
                  className={`w-4 h-4 transform transition-transform ${showThemeDropdown ? 'rotate-180' : ''}`} 
                  viewBox="0 0 20 20" 
                  fill="currentColor"
                >
                  <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </button>

              {/* Theme Dropdown */}
              {showThemeDropdown && (
                <div className="absolute right-0 bottom-[calc(100%+8px)] w-full md:w-64 bg-white border rounded-lg shadow-lg z-50 max-h-[200px] overflow-y-auto">
                  {themes.map((theme) => (
                    <button
                      key={theme.id}
                      onClick={() => {
                        localStorage.setItem(`selectedTheme_${userId}`, JSON.stringify(theme));
                        navigate('/user/onboarding/themecontd', {
                          state: {
                            theme_id: theme.ds_theme_id,
                            theme_documents_id: themeDocId
                          }
                        });
                        setShowThemeDropdown(false);
                      }}
                      className="w-full px-4 py-2 text-left hover:bg-gray-50 flex items-center justify-between border-b border-gray-100 last:border-b-0"
                    >
                      <span className="text-[#373A4B] text-[14px]">{theme.name}</span>
                      {JSON.parse(localStorage.getItem(`completedThemes_${userId}`) || '[]').includes(theme.id) && (
                        <svg className="w-4 h-4 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                      )}
                    </button>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      </div>
      </div>
      </>
  )
}
export default ThemePreview;
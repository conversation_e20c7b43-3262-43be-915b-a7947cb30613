import React from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { DocumentDownload } from "Assets/images";

const FinalPreview = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { allThemes } = location.state || {};

  // Get all themes' conversations from localStorage
  const themesWithResponses = allThemes.map(theme => {
    const conversation = JSON.parse(localStorage.getItem(`theme_${theme.id}_conversation`) || '[]');
    
    // Get AI and user responses alternately
    const responses = [];
    for (let i = 0; i < conversation.length; i++) {
      const message = conversation[i];
      if (message.role === 'assistant' && message.content) {
        responses.push({
          question: message.content,
          answer: conversation[i + 1]?.role === 'user' ? conversation[i + 1].content : ''
        });
      }
    }

    return {
      ...theme,
      responses
    };
  });

  return (
    <div className="w-full min-h-screen h-full text-gray-700 bg-[#E2E6EB] p-[1rem]">
      <div className="w-[90%]">
        {/* Progress Steps - same as ThemePreview */}
        
        <div className="mt-[2rem] mx-auto p-8 bg-white">
          <div className="flex justify-between items-center mb-8">
            <div className="text-lg">
              <span className="font-medium">
                Complete Guide: All Themes and Frameworks
              </span>
            </div>

            <div className="flex items-center gap-2">
              <img src={DocumentDownload} alt="document download"/>
              <span className="text-[#111928] font-medium text-[12px]">PDF Preview</span>
            </div>
          </div>

          <div className="border rounded-lg p-6">
            {themesWithResponses.map((theme, index) => (
              <div key={theme.id} className="mb-8 last:mb-0">
                <h2 className="text-lg font-medium mb-4 text-[#111928]">
                  {theme.name}
                </h2>
                <div className="space-y-6">
                  {theme.responses.map((response, idx) => (
                    <div key={idx} className="space-y-3">
                      <p className="text-[16px] text-[#111928] font-medium">
                        {response.question}
                      </p>
                      <p className="text-[16px] text-[#111928] leading-[24px] pl-5">
                        {response.answer}
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>

          <div className="mt-6 flex justify-end">
            <button 
              onClick={() => navigate('/user/onboarding/generatequiz')}
              className="px-6 py-2 bg-[#054FB1] text-white rounded hover:bg-blue-700 transition-colors font-medium text-[16px] h-[50px]"
            >
              CONTINUE TO QUIZ
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FinalPreview; 
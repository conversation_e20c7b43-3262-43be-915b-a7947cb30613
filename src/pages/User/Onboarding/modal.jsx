import React, { useState } from 'react';
import { useNavigate } from "react-router-dom";
import { AuthContext, tokenExpireError } from "Context/Auth";
import { GlobalContext, showToast } from "Context/Global";
import MkdSDK from "Utils/MkdSDK";

const Modal = ({ onClose }) => {
  const { dispatch } = React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const navigate = useNavigate();
  const [isGenerating, setIsGenerating] = useState(false);

  const handleUpgrade = () => {
    // Close the modal first
    onClose();
    // Navigate to profile page with current plan tab
    navigate('/user/profile', { 
      state: { 
        activeTab: 'Current plan',
        showUpgradeOptions: true 
      }
    });
  };

  return (
    <div className="fixed inset-0 md:bg-black/50 flex items-center justify-center z-50">
      <div className="bg-white rounded-md h-screen p-4 md:p-0 md:h-[321px] flex flex-col justify-center items-center relative" style={{ width: '770px' }}>
        {/* Close button */}
        <div className="pr-[28px] pt-[13px] absolute top-0 right-0 hidden md:block">
          <button 
            onClick={onClose}
            className="text-[#8F8F8F]"
          >
            <svg width="14" height="14" viewBox="0 0 14 14" fill="none">
              <path d="M1 1L13 13M1 13L13 1" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
            </svg>
          </button>
        </div>

        {/* Content */}
        <div className="px-[28px] pb-[13px] -mt-2">
          <h2 className="text-center font-inter text-[20px] leading-[28px] mb-4 text-[#111928]">
            You discovered a premium feature!
          </h2>
          
          <p className="text-center text-[16px] leading-[1.4] text-[#111928] mb-8 mx-auto" style={{ maxWidth: '460px' }}>
            Upgrade your plan to use this feature. You can learn more about our subscription packages by clicking on the 'Upgrade' button.
          </p>

          {/* Button container */}
          <div className="flex justify-center items-center gap-[10px]">
            <button
              onClick={onClose}
              className="h-[50px] w-full md:w-[202px] px-[24px] rounded-[6px] border border-[#E5E5E5] text-[16px] font-medium text-[#054FB1] hover:bg-gray-50"
            >
              CANCEL
            </button>
            <button
              onClick={handleUpgrade}
              className="h-[50px] w-full md:w-[202px] px-[24px] rounded-[6px] bg-[#054FB1] text-[16px] font-medium text-white hover:bg-[#054FB1]/90"
            >
              UPGRADE
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Modal;
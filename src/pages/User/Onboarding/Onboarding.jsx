import React, { useState } from "react";
import { AuthContext } from "Context/Auth";
import { GlobalContext, showToast } from "Context/Global";
import { BiSolidFileExport } from "react-icons/bi";
import { DocumentUpload, UploadIcon, Monitor, FormIcon } from "Assets/images";
import { Link, useNavigate } from "react-router-dom";
import MkdSDK from "Utils/MkdSDK";

const UserDashboardPage = () => {
  const { state: authState } = React.useContext(AuthContext);
  const { dispatch } = React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const navigate = useNavigate();
  const sdk = new MkdSDK();
  
  // Add userId to localStorage keys
  const userId = authState?.user;
  const [documentTitle, setDocumentTitle] = useState(() => {
    return localStorage.getItem(`documentTitle_${userId}`) || '';
  });
  const [resume, setResume] = useState(() => {
    const savedResume = localStorage.getItem(`resumeUrl_${userId}`);
    return savedResume ? { name: 'Previously uploaded resume', url: savedResume } : null;
  });
  const [history, setHistory] = useState(() => {
    const savedHistory = localStorage.getItem(`historyUrl_${userId}`);
    return savedHistory ? { name: 'Previously uploaded history', url: savedHistory } : null;
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  const handleDragOver = (e) => {
    e.preventDefault();
  };

  const handleDrop = (setter) => (e) => {
    e.preventDefault();
    const file = e.dataTransfer.files[0];
    setter({ name: file.name, file });
  };

  const handleFileChange = (setter) => (e) => {
    const file = e.target.files[0];
    setter({ name: file.name, file });
  };

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "user",
      },
    });
  }, []);

  React.useEffect(() => {
    if (documentTitle && userId) {
      localStorage.setItem(`documentTitle_${userId}`, documentTitle);
    }
  }, [documentTitle, userId]);

  const handleSubmit = async () => {
    try {
      setIsLoading(true);
      setError('');

      if (!documentTitle || !resume || !history) {
        showToast(globalDispatch, "Please fill in all required fields", 4000, "error");
        return;
      }

      const userId = authState?.user;
      
      if (!userId) {
        showToast(globalDispatch, "User ID not found. Please try logging in again.", 4000, "error");
        return;
      }

      let resumeUrl = resume.url;
      let historyUrl = history.url;

      // Only upload if we don't have URLs (new files)
      if (!resume.url && resume.file) {
        const resumeUploadResult = await sdk.upload(resume.file);
        if (!resumeUploadResult.url) {
          showToast(globalDispatch, "Resume upload failed", 4000, "error");
          return;
        }
        resumeUrl = resumeUploadResult.url;
        localStorage.setItem(`resumeUrl_${userId}`, resumeUrl);
      }

      if (!history.url && history.file) {
        const historyUploadResult = await sdk.upload(history.file);
        if (!historyUploadResult.url) {
          showToast(globalDispatch, "History document upload failed", 4000, "error");
          return;
        }
        historyUrl = historyUploadResult.url;
        localStorage.setItem(`historyUrl_${userId}`, historyUrl);
      }

      showToast(globalDispatch, "Documents uploaded successfully!", 4000, "success");
      navigate('/user/onboarding/form', {
        state: {
          resumeUrl,
          historyUrl,
          documentTitle
        }
      });

    } catch (error) {
      console.error('Upload error:', error);
      showToast(globalDispatch, error.message || "An error occurred during upload", 4000, "error");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <div className="md:pl-[3rem] w-full text-7xl min-h-screen h-full text-gray-700 bg-[#E2E6EB] p-[1rem]">
        <div className="w-[95%] md:w-[90%] mx-auto md:mx-0">
          <h1 className="text-[20px] text-[#111928] font-semibold mb-6">Generate themes document</h1>
          <div className="flex items-center justify-between mb-8 relative bg-white h-[98px] p-[1rem]">
            <div className="absolute left-8 right-8 top-1/4 md:top-1/3 h-0.5 bg-gray-200 w-[100% - 2rem]"/>
            <div className="flex flex-col items-start z-10 w-[33%]">
              <div className="w-10 h-10 border border-[#9C9C9C] bg-white rounded-full flex items-center justify-center">
                <div className="w-6 h-6 bg-[#389C14] rounded-full flex items-center justify-center">
                  <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"/>
                  </svg>
                </div>
              </div>
              <Link to="/user/onboarding" className="text-sm hover:text-[#054FB1] transition-colors">Upload your resume & full history docs</Link>
            </div>
            <div className="flex flex-col items-center z-10 w-[33%]">
              <div className="w-8 h-8 bg-[#054FB1] rounded-full flex items-center justify-center text-white mb-2">
                <img src={FormIcon} className="w-[16px] h-[16px]" alt="upload icon"/>
              </div>
              <Link to="/user/onboarding/form" className="text-sm text-[#054FB1]">Fill out questions form</Link>
            </div>
            <div className="flex flex-col items-end z-10 w-[33%]">
              <div className="w-8 h-8 bg-[#9C9C9C] rounded-full flex items-center justify-center text-gray-500 mb-2">
                <img src={Monitor} className="w-[20px] h-[20px]" alt="upload icon"/>
              </div>
              <Link to="/user/onboarding/theme" className="text-sm hover:text-[#054FB1] transition-colors">Generate Themes (0/20)</Link>
            </div>
          </div>

          {/* Document Title */}
          <div className="mb-8">
            <div className="flex justify-between items-center mb-[-1rem]">
              <label className="font-medium text-[16px] leading-[24px] text-[#111928] mt-[2rem]">Document Title</label>
              <span className="text-[12px] md:text-[16px] font-bold text-[#373A4B]">0% completed</span>
            </div>
            <input
              type="text"
              className="w-[211px] h-[46px] p-2 border border-gray-300 rounded-md"
              placeholder="Enter document name"
              value={documentTitle}
              onChange={(e) => setDocumentTitle(e.target.value)}
            />
          </div>

          {/* Resume Upload */}
          <div className="mb-8 bg-white p-[2rem]">
            <h2 className="font-semibold mb-2 text-[16px]">Resume Upload</h2>
            <p className="text-sm text-gray-600 mb-4">Upload your professional resume highlighting your skills and experience</p>
            <div
              className="border-2 border-dashed border-blue-200 rounded-lg p-8 bg-blue-50"
              onDragOver={handleDragOver}
              onDrop={handleDrop(setResume)}
            >
              <div className="flex flex-col items-center">
                <img src={UploadIcon} alt="upload icon"/>
                <p className="text-sm text-gray-600 mb-2">
                  {resume ? (resume.url ? 'File already uploaded' : resume.name) : 'Drag & drop files or '}
                  <label className="text-blue-600 cursor-pointer underline">
                    Browse
                    <input
                      type="file"
                      className="hidden"
                      onChange={handleFileChange(setResume)}
                      accept=".pdf,.doc,.docx"
                    />
                  </label>
                </p>
                <p className="text-xs text-gray-500">Supported formats: PDF, Word</p>
              </div>
            </div>
          </div>

          {/* History Upload */}
          <div className="mb-8 bg-white p-[2rem]">
            <h2 className="font-semibold mb-2 text-[16px]">Full History Upload</h2>
            <p className="text-sm text-gray-600 mb-4">Upload your complete work and educational history document</p>
            <div
              className="border-2 border-dashed border-blue-200 rounded-lg p-8 bg-blue-50"
              onDragOver={handleDragOver}
              onDrop={handleDrop(setHistory)}
            >
              <div className="flex flex-col items-center">
                <img src={UploadIcon} alt="upload icon"/>
                <p className="text-sm text-gray-600 mb-2">
                  {history ? (history.url ? 'File already uploaded' : history.name) : 'Drag & drop files or '}
                  <label className="text-blue-600 cursor-pointer underline">
                    Browse
                    <input
                      type="file"
                      className="hidden"
                      onChange={handleFileChange(setHistory)}
                      accept=".pdf,.doc,.docx"
                    />
                  </label>
                </p>
                <p className="text-xs text-gray-500">Supported formats: PDF, Word</p>
              </div>
            </div>
          </div>

          {/* Continue Button */}
          <div className="flex justify-end">
            <button 
              onClick={handleSubmit}
              disabled={isLoading}
              className={`bg-[#054FB1] text-white text-[16px] leading-[24px] px-6 py-2 rounded-md hover:bg-blue-700 transition-colors w-[231px] h-[50px] ${
                isLoading ? 'opacity-50 cursor-not-allowed' : ''
              }`}
            >
              {isLoading ? 'UPLOADING...' : 'CONTINUE'}
            </button>
          </div>
        </div>
      </div>
    </>
  );
};

export default UserDashboardPage;


import React,{useState} from "react";
import { AuthContext } from "Context/Auth";
import { GlobalContext } from "Context/Global";
import { BiSolidFileExport } from "react-icons/bi";
import { Document } from "Assets/images";
import export_default from "vite-plugin-compression";

const Form = () => {
  const { dispatch } = React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);

  
  return (
    <>
      <div className="w-full text-7xl min-h-screen h-full text-gray-700 bg-[#E2E6EB] p-[1rem]">
        <div className="w-[90%]">
      <h1 className="text-[20px] text-[#111928] font-semibold mb-6">Generate themes document</h1>
      </div>
      </div>
      </>
  )
}
export default Form;
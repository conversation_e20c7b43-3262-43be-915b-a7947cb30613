import React, { useState } from "react";
import { AuthContext, tokenExpireError } from "Context/Auth";
import { GlobalContext, showToast } from "Context/Global";
import { BiSolidFileExport } from "react-icons/bi";
import { Document } from "Assets/images";
import { Link, useNavigate, useLocation } from "react-router-dom";
import { DocumentUpload, UploadIcon, Monitor, FormIcon } from "Assets/images";
import MkdSDK from "Utils/MkdSDK";

const Form = () => {
  const { dispatch, state: authState } = React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const navigate = useNavigate();
  const location = useLocation();
  const sdk = new MkdSDK();
  
  const { resumeUrl, historyUrl, documentTitle } = location.state || {};
  const userId = authState?.user;

  // Define all questions with their properties
  const questionCategories = {
    highPerformanceTeams: {
      title: "High Performance Teams",
      questions: [
        { id: 'sportsStories', text: 'Any memorable stories from your time playing sports? Be Specific.', isOptional: false },
        { id: 'teamChallenges', text: 'What kind of challenges did you and your sports teammates overcome?', isOptional: true },
        { id: 'bestTeam', text: 'What is the best performing work team you have ever been on?', isOptional: false },
        { id: 'bestCrew', text: 'Think about the best crew you have ever been on at work... who was on it and why did it work so well?', isOptional: true },
        { id: 'hardestEducation', text: 'What was the hardest part of your education?', isOptional: false },
        { id: 'teamMistake', text: 'Have you ever been on a team where there was a massive mistake to overcome?', isOptional: true },
        { id: 'workMistake', text: 'Can you remember a time when you got in trouble at work for making a mistake?', isOptional: false },
        { id: 'volunteerStories', text: 'Are there any memorable stories from your time at church, while volunteering, or with family?', isOptional: true },
        { id: 'likedEveryone', text: "When's the last time you walked into a place and liked every single person there? Why?", isOptional: true },
        { id: 'taskReliance', text: 'Have you ever had to deeply rely on someone to get an important task or project completed?', isOptional: false },
        { id: 'helpedChallenge', text: 'Was there ever a major setback or challenge that you helped someone get through?', isOptional: false },
        { id: 'uncomfortableTask', text: "When's the last time someone delegated a task to you that you felt uncomfortable about completing?", isOptional: false },
        { id: 'trainingDifficulty', text: "Have you ever had to train someone that wasn't understanding the important concepts of the role?", isOptional: false },
        { id: 'significantTeam', text: 'Describe a time you were part of a team that achieved something significant. What was your role?', isOptional: false },
        { id: 'teamworkObstacle', text: 'Share an experience where teamwork helped you overcome a major obstacle.', isOptional: true },
        { id: 'deadlinePressure', text: 'Have you ever worked with a team under intense pressure to meet a deadline?', isOptional: false },
        { id: 'poorTeamDynamics', text: 'Describe a situation where poor team dynamics affected the outcome.', isOptional: false },
        { id: 'motivatingOthers', text: 'Tell us about a time when you motivated others to work together toward a common goal.', isOptional: true },
        { id: 'unclearRoles', text: "Have you ever been part of a team where roles and responsibilities weren't clear?", isOptional: false },
        { id: 'rulesBending', text: 'Have you ever been tempted to do some things that went against the rules or best practices?', isOptional: true }
      ]
    },
    situationalAwareness: {
      title: "Situational Awareness",
      questions: [
        { id: 'stressedOut', text: 'Share a time when you felt really stressed out about all the things you had to accomplish piling up.', isOptional: false },
        { id: 'preventAccident', text: 'Share a time when being aware of your surroundings prevented an accident or problem.', isOptional: false },
        { id: 'quickThinking', text: 'Describe a situation where quick thinking and awareness helped you adapt to unexpected changes.', isOptional: true },
        { id: 'noticeAction', text: "Tell us about a time when you noticed something others didn't and took action.", isOptional: true },
        { id: 'multipleRisks', text: 'Have you ever had to assess multiple risks at once? What did you do?', isOptional: true },
        { id: 'missedImportant', text: 'Recall a moment when you failed to notice something important. What was the impact, and what did you learn?', isOptional: false },
        { id: 'protectOthers', text: 'Describe a time you relied on situational awareness to protect others.', isOptional: true }
      ]
    },
    problemSolving: {
      title: "Good Problem Solving",
      questions: [
        { id: 'complexProblem', text: 'Share a time when you solved a complex problem under pressure.', isOptional: false },
        { id: 'creativeThinking', text: 'Describe a situation where your creative thinking led to a solution nobody else considered.', isOptional: true },
        { id: 'outsideExpertise', text: 'Have you ever been tasked with fixing something outside of your expertise? What was your approach?', isOptional: false },
        { id: 'limitedResources', text: 'Tell us about a time when you had limited resources and still managed to resolve an issue.', isOptional: true },
        { id: 'multipleInput', text: 'Recall a problem that required input from multiple people. How did you coordinate the solution?', isOptional: false },
        { id: 'dataAnalysis', text: 'Describe a moment when you analyzed data or information to solve a problem effectively.', isOptional: true },
        { id: 'complicatedSolution', text: 'Have you ever had to communicate a complicated solution to solve a big problem?', isOptional: false },
        { id: 'trustedAdvisor', text: 'Have you ever been a trusted advisor or has anyone ever confided in you deep personal information?', isOptional: false },
        { id: 'calmArgument', text: 'Have you ever had to calm down an argument between two or more people?', isOptional: false },
        { id: 'angryCalming', text: "When's the last time you got really mad at someone and had to figure out how to calm down?", isOptional: false }
      ]
    },
    customerService: {
      title: "Customer Service",
      questions: [
        { id: 'aboveAndBeyond', text: 'Share an experience where you went above and beyond to help a customer or client.', isOptional: false },
        { id: 'angryCustomer', text: 'Describe a time when you had to manage an upset or angry customer. What did you do?', isOptional: false },
        { id: 'clearCommunication', text: "Tell us about a situation where clear communication improved a customer's experience.", isOptional: true },
        { id: 'balanceRules', text: 'Recall a time when you had to balance customer satisfaction with organizational rules.', isOptional: true },
        { id: 'tightDeadline', text: 'Have you ever solved a problem for a customer that had a tight deadline? How did you handle it?', isOptional: true },
        { id: 'diverseBackground', text: 'Share an example of when you helped someone from a different background feel comfortable or understood.', isOptional: false },
        { id: 'disabledExperience', text: 'What kind of direct experience do you have with people who are terminally ill or disabled? Did you solve a problem for them?', isOptional: false },
        { id: 'languageBarrier', text: "Have you ever had to translate instructions or help someone that didn't speak English? How did you handle it?", isOptional: false }
      ]
    },
    buildingConstruction: {
      title: "Building Construction and Mechanical Aptitude",
      questions: [
        { id: 'complexBuild', text: 'Have you ever fixed or built something complex? What tools or techniques did you use?', isOptional: false },
        { id: 'mechanicalKnowledge', text: 'Share an example of a time when mechanical knowledge helped you solve a problem.', isOptional: false },
        { id: 'buildingLayout', text: "Tell us about a situation where understanding a building's layout or construction was critical.", isOptional: false },
        { id: 'mechanicalIssue', text: 'Have you ever identified a mechanical issue before it became a larger problem? How?', isOptional: true },
        { id: 'challengingConditions', text: 'Describe a time you worked with machinery or tools in challenging conditions.', isOptional: true },
        { id: 'teachingMechanical', text: 'Recall a moment when you taught someone else about mechanical or construction concepts.', isOptional: true },
        { id: 'workShortcuts', text: 'What kind of shortcuts do you use at work to help you make jobs more efficient or safer?', isOptional: false }
      ]
    },
    emergencyMedical: {
      title: "Emergency Medical Experience",
      questions: [
        { id: 'medicalAssistance', text: 'Share a time when you provided medical assistance in an emergency.', isOptional: false },
        { id: 'calmMedical', text: 'Describe a situation where you stayed calm and helped someone in a high-stress medical situation.', isOptional: false },
        { id: 'prioritizeCare', text: 'Tell us about a time when you had to prioritize care for multiple individuals.', isOptional: true },
        { id: 'firstAidSkills', text: 'Have you ever had to use first aid skills to stabilize someone? What steps did you take?', isOptional: true },
        { id: 'healthEducation', text: 'Recall a moment when you educated someone about health or safety.', isOptional: true },
        { id: 'medicalKnowledge', text: 'Share an example of when your medical knowledge directly impacted a positive outcome.', isOptional: false },
        { id: 'correctMedical', text: 'Have you ever had to correct someone about the way they were handling a medical situation?', isOptional: false }
      ]
    },
    mentalPhysicalHealth: {
      title: "Mental and Physical Health",
      questions: [
        { id: 'personalChallenge', text: 'Describe a time when you overcame a personal challenge through resilience and determination.', isOptional: false },
        { id: 'helpedCope', text: 'Share an example of when you helped someone else cope with stress or hardship.', isOptional: false },
        { id: 'suicidalSituation', text: 'Have you ever had to deal with someone that was suicidal or looking to harm themselves physically?', isOptional: false },
        { id: 'physicalFitness', text: 'Tell us about a situation where maintaining physical fitness made a significant difference in your performance.', isOptional: false },
        { id: 'healthImprovement', text: 'Have you ever made a change to improve your mental or physical health? What was the result?', isOptional: true },
        { id: 'balanceStress', text: 'Recall a moment when you balanced multiple stressful responsibilities without compromising your well-being.', isOptional: true },
        { id: 'supportTeamHealth', text: "Share an experience where you supported a team member's mental or physical health during a tough time.", isOptional: true }
      ]
    }
  };

  const [answers, setAnswers] = useState(() => {
    // Initialize from localStorage if exists, otherwise create empty answers object
    if (userId) {
      const savedAnswers = localStorage.getItem(`formAnswers_${userId}`);
      if (savedAnswers) {
        return JSON.parse(savedAnswers);
      }
    }
    
    // Create default empty answers object based on all questions
    const defaultAnswers = {};
    Object.values(questionCategories).forEach(category => {
      category.questions.forEach(question => {
        defaultAnswers[question.id] = '';
      });
    });
    return defaultAnswers;
  });

  const [characterCounts, setCharacterCounts] = useState(() => {
    // Initialize character counts based on loaded answers
    const counts = {};
    Object.keys(answers).forEach(questionId => {
      counts[questionId] = answers[questionId].length;
    });
    return counts;
  });

  const [isLoading, setIsLoading] = useState(false);

  const handleInputChange = (questionId, value) => {
    const newAnswers = {
      ...answers,
      [questionId]: value
    };
    setAnswers(newAnswers);
    setCharacterCounts(prev => ({
      ...prev,
      [questionId]: value.length
    }));

    // Save to localStorage whenever answers change
    if (userId) {
      localStorage.setItem(`formAnswers_${userId}`, JSON.stringify(newAnswers));
    }
  };

  const handleSubmit = async () => {
    try {
      setIsLoading(true);

      // Validate minimum character count for mandatory questions
      const mandatoryQuestions = Object.values(questionCategories)
        .flatMap(category => category.questions)
        .filter(q => !q.isOptional);

      const incompleteMandatory = mandatoryQuestions.filter(q => 
        characterCounts[q.id] < 100
      );

      if (incompleteMandatory.length > 0) {
        showToast(globalDispatch, "All mandatory questions must have at least 100 characters", 4000, "error");
        return;
      }

      // Validate that we have all required data
      if (!resumeUrl || !historyUrl || !documentTitle) {
        showToast(globalDispatch, "Missing required document information", 4000, "error");
        return;
      }

      // Format answers for API - Fix the formatting logic
      const formattedAnswers = Object.values(questionCategories)
        .flatMap(category => 
          category.questions.map(question => ({
            question: question.text,
            answer: answers[question.id] || ''
          }))
        );

      // Call the API with the actual values
      const result = await sdk.callRawAPI(
        "/v3/api/custom/theme-document/upload",
        {
          answer: JSON.stringify(formattedAnswers),
          resume_url: resumeUrl,         
          history_doc_url: historyUrl, 
          title: documentTitle          
        },
        "POST"
      );

      if (!result.error) {
        localStorage.setItem('theme_documents_id', result.data.toString());
        showToast(globalDispatch, "Successfully submitted answers", 4000, "success");
        navigate("/user/onboarding/theme");
      } else {
        if (result.validation) {
          const messages = Object.values(result.validation).join(", ");
          showToast(globalDispatch, messages, 4000, "error");
        }
      }
    } catch (error) {
      console.error('Submission error:', error);
      showToast(globalDispatch, error.message || "An error occurred while submitting the form", 4000, "error");
      tokenExpireError(dispatch, error.message);
    } finally {
      setIsLoading(false);
    }
  };

  // Add validation for missing data
  React.useEffect(() => {
    if (!resumeUrl || !historyUrl || !documentTitle) {
      showToast(globalDispatch, "Missing required document information. Please upload documents first.", 4000, "error");
      navigate('/user/onboarding');
    }
  }, [resumeUrl, historyUrl, documentTitle]);

  // Add this useEffect to scroll to top on component mount
  React.useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  return (
    <>
      <div className="w-full text-7xl min-h-screen h-full text-gray-700 bg-[#E2E6EB] md:p-[1rem] md:pl-[3rem] p-0">
      <div className="w-[95%] md:w-[90%] mx-auto md:mx-0">
      <h1 className="text-[20px] text-[#111928] font-semibold mb-6">Generate themes document</h1>

      <div className="space-y-8">

            {/* Progress Steps */}
            <div className="flex items-center justify-between mb-8 relative bg-white h-[98px] p-[1rem]">
            <div className="absolute left-8 right-8 top-1/4 md:top-1/3 h-0.5 bg-gray-200 w-[100% - 2rem]"/>
              <div className="flex flex-col items-start z-10 w-[33%]">
                <div className="w-10 h-10 border border-[#9C9C9C] bg-white rounded-full flex items-center justify-center">
                <div className="w-6 h-6 bg-[#389C14] rounded-full flex items-center  justify-center">
                  <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"/>
                  </svg>
                </div>
                </div>
                <Link to="/user/onboarding" className="text-sm hover:text-[#054FB1] transition-colors">Upload your resume & full history docs</Link>
              </div>
              <div className="flex flex-col items-center z-10 w-[33%]">
              <div className="w-8 h-8 bg-[#054FB1] rounded-full flex items-center justify-center text-white mb-2">
                <img src={FormIcon} className="w-[16px] h-[16px]" alt="upload icon"/>
              </div>
              <Link to="/user/onboarding/form" className="text-sm text-[#054FB1]">Fill out questions form</Link>
              </div>
              <div className="flex flex-col items-end z-10 w-[33%]">
                <div className="w-8 h-8 bg-[#9C9C9C] rounded-full flex items-center justify-center text-gray-500 mb-2">
                  <img src={Monitor} className="w-[20px] h-[20px]" alt="upload icon"/>
                </div>
                <Link to="/user/onboarding/theme" className="text-sm hover:text-[#054FB1] transition-colors">Generate Themes (0/20)</Link>
              </div>
            </div>

            <div className="text-[12px] md:text-[16px] font-bold text-[#373A4B] text-right">25% completed</div>
            

            {/* Form Fields */}
            <div className="space-y-6">
              {Object.entries(questionCategories).map(([categoryKey, category]) => (
                <div key={categoryKey} className="bg-white p-[1rem] rounded-lg">
                  <h2 className="text-xl font-bold mb-4 text-[#111928]">{category.title}</h2>
                  {category.questions.map((question) => (
                    <div key={question.id} className="mb-6">
                      <label className="block text-sm font-medium mb-2 text-[#111928] text-[16px]">
                        <span className="text-gray-600">
                          {!question.isOptional && <span className="text-red-600 mr-1">*</span>}
                          {question.text}
                        </span>
                        {question.isOptional && <span className="ml-2 text-gray-500">(Optional)</span>}
                      </label>
                      <textarea
                        className="w-full h-32 p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 [&::-webkit-scrollbar]:w-1 [&::-webkit-scrollbar-thumb]:bg-gray-300 [&::-webkit-scrollbar-thumb]:rounded-lg [&::-webkit-scrollbar-track]:bg-gray-100"
                        placeholder="Type your answer here..."
                        value={answers[question.id]}
                        onChange={(e) => handleInputChange(question.id, e.target.value)}
                      />
                      <div className="flex justify-between mt-1 text-xs text-gray-500">
                        <span className="text-[#373A4B99] text-[14px]">
                          {!question.isOptional ? 'Minimum 100 characters' : ''}
                        </span>
                        <span className="text-[#373A4B99] text-[14px]">
                          {characterCounts[question.id]} characters
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              ))}
            </div>

            <div className="flex">
              <button
                onClick={handleSubmit}
                disabled={isLoading}
                className={`bg-[#054FB1] text-white text-[16px] leading-[24px] px-6 py-2 rounded-md hover:bg-blue-700 transition-colors w-[231px] h-[50px] ml-auto ${
                  isLoading ? 'opacity-50 cursor-not-allowed' : ''
                }`}
              >
                {isLoading ? 'SUBMITTING...' : 'CONTINUE'}
              </button>
            </div>
          </div>
        </div>
      </div>
      </>
  )
}
export default Form;
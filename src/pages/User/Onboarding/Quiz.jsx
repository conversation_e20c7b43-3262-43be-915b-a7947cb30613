import React, { useState } from "react";
import { AuthContext,tokenExpireError } from "Context/Auth";
import { GlobalContext,showToast } from "Context/Global";
import { BiSolidFileExport } from "react-icons/bi";
import { Document } from "Assets/images";
import { useNavigate } from "react-router-dom";
import MkdSDK from "Utils/MkdSDK";
const Quiz = () => {
  const { state, dispatch } = React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const navigate = useNavigate();
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState({});
  const [quizType, setQuizType] = useState(() => localStorage.getItem('quizType') || '1');
  const [quizData, setQuizData] = useState(() => {
    const data = localStorage.getItem('generatedQuiz');
    return data ? JSON.parse(data) : null;
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const questions = quizData?.quiz_data || [];
  const currentQuestion = questions[currentQuestionIndex];
  const currentAnswer = answers[currentQuestionIndex] || '';

  const handlePrevious = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(prev => prev - 1);
    }
  };

  const handleNext = () => {
    if (currentQuestionIndex < questions.length - 1) {
      setCurrentQuestionIndex(prev => prev + 1);
      if (!answers[currentQuestionIndex + 1]) {
        setAnswers(prev => ({
          ...prev,
          [currentQuestionIndex + 1]: ''
        }));
      }
    }
  };

  const handleSubmitQuiz = async () => {
    try {
      setIsSubmitting(true);
      const sdk = new MkdSDK();
      
      // Get PDF URL and quiz ID from localStorage
      const pdfUrl = localStorage.getItem('mergedPdfUrl');
      const quizId = localStorage.getItem('quizId');
      const generatedQuiz = localStorage.getItem('generatedQuiz');
      
      console.log('Stored Quiz ID:', quizId);
      console.log('Generated Quiz Data:', generatedQuiz);
      
      // Try to get quiz ID from different sources
      let finalQuizId = quizId;
      if (!finalQuizId && generatedQuiz) {
        try {
          const parsedQuiz = JSON.parse(generatedQuiz);
          finalQuizId = parsedQuiz.id || parsedQuiz.data?.id;
          console.log('Retrieved Quiz ID from generatedQuiz:', finalQuizId);
        } catch (e) {
          console.error('Error parsing generatedQuiz:', e);
        }
      }
      
      if (!finalQuizId) {
        showToast(globalDispatch, "Quiz ID not found. Please regenerate the quiz.", 4000, "error");
        return;
      }

      sdk.setTable("quiz");

      // Format quiz data as an array of questions with answers
      const quizArray = questions.map((question, index) => ({
        quiz: question.question,
        correct_answer: question.correct_answer || "",
        user_answer: answers[index] || ""
      }));
      
      console.log('Final Quiz ID being used:', finalQuizId);
      
      const payload = {
        id: parseInt(finalQuizId),
        quiz: JSON.stringify(quizArray),
        update_at: new Date().toISOString().slice(0, 19).replace('T', ' ')
      };

      console.log('Quiz Update Payload:', payload);

      const result = await sdk.callRestAPI(
        payload,
        "PUT"
      );

      if (!result || result.error) {
        showToast(globalDispatch, result?.message || "Failed to save quiz", 4000, "error");
        return;
      }

      // Clear quiz data from localStorage
      localStorage.removeItem('generatedQuiz');
      localStorage.removeItem('quizType');
      localStorage.removeItem('quizId');

      showToast(globalDispatch, "Quiz submitted successfully!", 4000, "success");
      navigate('/user/onboarding/allquizzes');

    } catch (error) {
      console.error('Error submitting quiz:', error);
      showToast(globalDispatch, error.message || "Failed to submit quiz", 4000, "error");
      tokenExpireError(dispatch, error.message);
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderQuestionByType = () => {
    if (!currentQuestion) return null;

    switch (quizType) {
      case '1':
        return (
          <input
            type="text"
            placeholder="Type your response here"
            value={currentAnswer}
            onChange={(e) => setAnswers(prev => ({
              ...prev,
              [currentQuestionIndex]: e.target.value
            }))}
            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        );
      case '2':
        return (
          <div className="space-y-2">
            {currentQuestion.options.map((option, index) => (
              <div key={index} className="flex items-center">
                <input
                  type="radio"
                  name="mcq"
                  id={`option-${index}`}
                  checked={currentAnswer === option}
                  onChange={() => setAnswers(prev => ({
                    ...prev,
                    [currentQuestionIndex]: option
                  }))}
                  className="mr-2"
                />
                <label htmlFor={`option-${index}`} className="text-[16px]">{option}</label>
              </div>
            ))}
          </div>
        );
      case '3':
        return (
          <div className="space-x-4">
            {currentQuestion.options.map((option, index) => (
              <label key={index} className="inline-flex items-center mr-4 text-[16px]">
                <input
                  type="radio"
                  name="tf"
                  checked={currentAnswer === option}
                  onChange={() => setAnswers(prev => ({
                    ...prev,
                    [currentQuestionIndex]: option
                  }))}
                  className="mr-2"
                />
                {option}
              </label>
            ))}
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <>
      <div className="w-full text-7xl h-screen text-gray-700 bg-[#1E1E1E] md:bg-[#E2E6EB] p-[1rem] md:pl-[3rem] overflow-y-auto fixed md:static top-0 left-0">
        <div className="w-[95%] md:w-[90%] mx-auto md:mx-0 pb-[120px] md:pb-0">
          <h1 className="text-[20px] mt-[1rem] md:mt-0 text-white md:text-[#111928] font-semibold mb-6">Document title: Fire fighter 2024</h1>

          {/* Quiz Container */}
          <div className="bg-[#1E1E1E] rounded-lg mb-6 py-[2rem] md:py-[5rem] relative min-h-[calc(100vh-200px)] md:min-h-0 flex md:block items-center">
            {/* Question Dialog */}
            <div className="bg-white rounded-lg p-4 md:p-8 mb-6 xl:w-[665px] md:w-[90%] w-[95%] mx-auto">
              <div className="flex justify-between items-start mb-4">
                <h2 className="text-[20px] font-semibold text-[#111928] mt-[1rem] md:mt-[3rem]">
                  Question #{currentQuestionIndex + 1}
                </h2>
              </div>

              <p className="text-[#373A4B] text-[16px] leading-[24px] mb-6">
                {currentQuestion?.question || "Loading question..."}
              </p>

              {renderQuestionByType()}
            </div>
          </div>

          {/* Navigation Buttons */}
          <div className="fixed bottom-[20px] left-0 right-0 p-4 bg-[#1E1E1E] md:bg-transparent md:p-0 md:static flex flex-col-reverse md:flex-row gap-[1rem] md:gap-0 justify-between items-center">
            <button 
              onClick={handlePrevious}
              disabled={currentQuestionIndex === 0}
              className="px-6 py-2 border border-blue-600 text-[#054FB1] bg-white rounded hover:bg-blue-50 transition-colors text-[16px] font-medium w-full md:w-[201px] h-[50px] disabled:opacity-50"
            >
              PREVIOUS
            </button>
            <button 
              onClick={currentQuestionIndex === questions.length - 1 ? handleSubmitQuiz : handleNext}
              disabled={isSubmitting}
              className="px-6 py-2 bg-[#054FB1] text-white rounded hover:bg-blue-700 transition-colors text-[16px] font-medium w-full md:w-[201px] h-[50px]"
            >
              {isSubmitting ? 'SUBMITTING...' : currentQuestionIndex === questions.length - 1 ? 'SUBMIT QUIZ' : 'NEXT'}
            </button>
          </div>
        </div>
      </div>
    </>
  );
};

export default Quiz;
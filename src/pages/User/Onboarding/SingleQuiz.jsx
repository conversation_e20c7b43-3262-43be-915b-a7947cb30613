import React, { useState, useEffect } from "react";
import { AuthContext, tokenExpireError } from "Context/Auth";
import { GlobalContext, showToast } from "Context/Global";
import { BiLoaderAlt } from "react-icons/bi";
import { Document } from "Assets/images";
import { Link, useParams } from "react-router-dom";
import MkdSDK from "Utils/MkdSDK";

const SingleQuiz = () => {
  const { id } = useParams();
  const { dispatch } = React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const [quiz, setQuiz] = useState(null);
  const [loading, setLoading] = useState(true);
  const [quizType, setQuizType] = useState(null);


  useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "quiz",
      },
    });



    (async function () {
      try {
        const sdk = new MkdSDK();
        sdk.setTable("quiz");
        const result = await sdk.callRestAPI({ id: Number(id) }, "GET");
        
        console.log("Quiz Response:", result);

        if (!result.error && result.model) {
          try {
            let quizData;
            // First try parsing the quiz string
            const parsedData = JSON.parse(result.model.quiz);
            
            // Handle both array and object formats
            if (Array.isArray(parsedData)) {
              quizData = parsedData;
            } else if (parsedData.quiz_data) {
              // Handle the case where quiz data is nested
              quizData = parsedData.quiz_data.map(q => ({
                quiz: q.question,
                user_answer: q.user_answer || '',
                correct_answer: q.correct_answer || ''
              }));
            } else {
              throw new Error('Unexpected quiz data format');
            }

            setQuiz(quizData);
            setQuizType(result.model.quiz_type);
          } catch (parseError) {
            console.error('Error parsing quiz data:', parseError);
            showToast(globalDispatch, "Invalid quiz data format", 4000, "error");
          }
        } else {
          showToast(globalDispatch, "Failed to fetch quiz", 4000, "error");
        }
      } catch (error) {
        console.error('Error fetching quiz:', error);
        showToast(globalDispatch, error.message || "Failed to fetch quiz", 4000, "error");
        tokenExpireError(dispatch, error.message);
      } finally {
        setLoading(false);
      }
    })();
  }, [id, dispatch, globalDispatch]);

  return (
    <>
      <div className="w-full text-7xl min-h-screen h-full text-gray-700 bg-[#E2E6EB] p-[1rem] md:pl-[3rem]">
      <div className="w-[95%] md:w-[90%] mx-auto md:mx-0">
          <h1 className="text-[16px] md:text-[20px] text-[#111928] font-semibold mb-6">Document title: Fire fighter exams 2025</h1>
          
          <p className="text-[#373A4B] text-[14px] md:text-[16px] font-bold text-right">Quiz {id} details</p>
        
          <div className="bg-white border border-gray-200 rounded-lg overflow-hidden mt-[1rem] p-[2rem] pb-[4rem]">
            <div className="overflow-x-auto" style={{ 
              msOverflowStyle: 'none',
              scrollbarWidth: 'none',
              WebkitOverflowScrolling: 'touch'
            }}>
              <div style={{ 
                width: '100%', 
                minWidth: '600px'  // Ensures table doesn't get too squished on mobile
              }}>
                {loading ? (
                  <div className="flex justify-center items-center py-8">
                    <BiLoaderAlt className="animate-spin text-[#054FB1] text-4xl" />
                  </div>
                ) : (
                  <table className="w-full">
                    <thead>
                      <tr className="bg-[#E4E4E4]">
                        <th className="text-left py-3 px-4 text-sm font-semibold text-gray-700 min-w-[20rem] max-w-[20rem]">Question asked</th>
                        <th className="text-left py-3 px-4 text-sm font-semibold text-gray-700 min-w-[12rem] max-w-[20rem]">Answer provided</th>
                        <th className="text-left py-3 px-4 text-sm font-semibold text-gray-700 min-w-[12rem] max-w-[20rem]">Correct answer</th>
                       {quizType !== 1 && <th className="text-left py-3 px-4 text-sm font-semibold text-gray-700">Status</th>}
                      </tr>
                    </thead>
                    <tbody>
                      {quiz && quiz.map((question, index) => (
                        <tr 
                          key={index}
                          className={`${
                            index % 2 === 0 
                              ? 'bg-white hover:bg-[#efefefb8]' 
                              : 'bg-[#EFEFEF]'
                          }`}
                        >
                          <td className="py-3 px-4 text-sm text-gray-900">
                            {question.quiz}
                          </td>
                          <td className="py-3 px-4 text-sm text-gray-900">
                            {question.user_answer}
                          </td>
                          <td className="py-3 px-4 text-sm text-gray-900">
                            {question.correct_answer}
                          </td>
                          {quizType !== 1 && (
                          <td className="py-3 px-4 text-sm">
                            <span className={`px-2 py-1 rounded ${
                              question.user_answer === question.correct_answer 
                                ? 'bg-green-100 text-green-800' 
                                : 'bg-red-100 text-red-800'
                            }`}>
                              {question.user_answer === question.correct_answer ? 'Correct' : 'Incorrect'}
                            </span>
                          </td>
                          )}
                        </tr>
                      ))}
                    </tbody>
                  </table>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default SingleQuiz;
import React, { useState, useEffect } from "react";
import { AuthContext, tokenExpireError } from "Context/Auth";
import { GlobalContext, showToast } from "Context/Global";
import { BiSolidFileExport, BiLoaderAlt } from "react-icons/bi";
import { Document } from "Assets/images";
import { Link } from "react-router-dom";
import MkdSDK from "Utils/MkdSDK";
import DeleteModal from "./DeleteModal";

const AllQuizzes = () => {
  const { state, dispatch } = React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const [quizzes, setQuizzes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [quizToDelete, setQuizToDelete] = useState(null);

  useEffect(() => {
    const fetchQuizzes = async () => {
      try {
        setLoading(true);
        const sdk = new MkdSDK();
        
        const response = await sdk.callRawAPI( `/v3/api/custom/jordan/user/quizzes`,
          {
            page: 1,
            limit: 10
          },
          "GET",
        );
        
        console.log("Quizzes Response:", response);

        if (!response.error && response.list) {
          setQuizzes(response.list);
        } else {
          showToast(globalDispatch, "Failed to fetch quizzes", 4000, "error");
        }
      } catch (error) {
        console.error('Error fetching quizzes:', error);
        showToast(globalDispatch, error.message || "Failed to fetch quizzes", 4000, "error");
        tokenExpireError(dispatch, error.message);
      } finally {
        setLoading(false);
      }
    };

    fetchQuizzes();
  }, [dispatch, globalDispatch]);

  const handleDelete = async (quizId) => {
    setQuizToDelete(quizId);
    setIsDeleteModalOpen(true);
  };

  const confirmDelete = async () => {
    try {
      const sdk = new MkdSDK();
      sdk.setTable("quiz");
      
      const result = await sdk.callRestAPI(
        { id: quizToDelete },
        "DELETE"
      );

      if (!result || result.error) {
        showToast(globalDispatch, "Failed to delete quiz", 4000, "error");
        return;
      }

      setQuizzes(prevQuizzes => prevQuizzes.filter(quiz => quiz.id !== quizToDelete));
      showToast(globalDispatch, "Quiz deleted successfully!", 4000, "success");
    } catch (error) {
      console.error('Error deleting quiz:', error);
      showToast(globalDispatch, error.message || "Failed to delete quiz", 4000, "error");
      tokenExpireError(dispatch, error.message);
    } finally {
      setIsDeleteModalOpen(false);
      setQuizToDelete(null);
    }
  };

  return (
    <>
      <div className="w-full text-7xl min-h-screen h-full text-gray-700 bg-[#E2E6EB] p-[1rem] md:pl-[3rem]">
      <div className="w-[95%] md:w-[90%] mx-auto md:mx-0">
          <h1 className="text-[20px] text-[#111928] font-semibold mb-6">Document title: Fire fighter exams 2025</h1>
          
          <p className="text-[#373A4B] text-[16px] font-bold text-right">All Quizzes</p>
        
          <div className="bg-white border border-gray-200 rounded-lg overflow-hidden mt-[1rem] p-[2rem] pb-[4rem]">
            <div className="overflow-x-auto" style={{ 
              msOverflowStyle: 'none',
              scrollbarWidth: 'none',
              WebkitOverflowScrolling: 'touch'
            }}>
              <div style={{ 
                width: '100%', 
                minWidth: '600px'  // Ensures table doesn't get too squished on mobile
              }}>
                {loading ? (
                  <div className="flex justify-center items-center py-8">
                    <BiLoaderAlt className="animate-spin text-[#054FB1] text-4xl" />
                  </div>
                ) : (
                  <table className="w-full">
                    <thead>
                      <tr className="bg-[#E4E4E4]">
                        <th className="text-left py-3 px-4 text-sm font-semibold text-gray-700">Quiz number</th>
                        <th className="text-left py-3 px-4 text-sm font-semibold text-gray-700">Date submitted</th>
                        <th className="text-left py-3 px-4 text-sm font-semibold text-gray-700">Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {quizzes.length > 0 ? (
                        quizzes.map((quiz, index) => {
                          const quizData = JSON.parse(quiz.quiz || '[]');
                          return (
                            <tr 
                              key={quiz.id}
                              className={`cursor-pointer ${
                                index % 2 === 0 
                                  ? 'bg-white hover:bg-[#efefefb8]' 
                                  : 'bg-[#EFEFEF]'
                              }`}
                            >
                              <td className="py-3 px-4 text-sm text-gray-900">
                                Quiz {index + 1}
                              </td>
                              <td className="py-3 px-4 text-sm text-gray-900">
                                {new Date(quiz.create_at).toLocaleDateString()}
                              </td>
                              <td className="py-3 px-4 text-sm">
                                <div className="flex gap-6">
                                  <Link to={`/user/onboarding/singlequiz/${quiz.id}`}>
                                    <button className="text-blue-600 hover:text-blue-800 underline">
                                      View details
                                    </button>
                                  </Link>
                                  <button 
                                    onClick={() => handleDelete(quiz.id)}
                                    className="text-blue-600 hover:text-blue-800 underline"
                                  >
                                    Delete
                                  </button>
                                </div>
                              </td>
                            </tr>
                          );
                        })
                      ) : (
                        <tr>
                          <td colSpan="3" className="text-center py-4">
                            No quizzes found
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <DeleteModal
        isOpen={isDeleteModalOpen}
        onClose={() => {
          setIsDeleteModalOpen(false);
          setQuizToDelete(null);
        }}
        onConfirm={confirmDelete}
        itemName="quiz"
      />
    </>
  );
};

export default AllQuizzes;
import React, { useState, useEffect } from "react";
import { AuthContext, tokenExpireError } from "Context/Auth";
import { GlobalContext, showToast } from "Context/Global";
import { useLocation, useNavigate, Link } from "react-router-dom";
import { BiSolidFileExport } from "react-icons/bi";
import { HiSpeakerWave } from "react-icons/hi2";
import { DocumentUpload, UploadIcon, Monitor, FormIcon } from "Assets/images";
import MkdSDK from "Utils/MkdSDK";

const ThemeContd = () => {
  const { dispatch, state: authState } = React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const location = useLocation();
  const navigate = useNavigate();
  const sdk = new MkdSDK();

  const [userInput, setUserInput] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [conversation, setConversation] = useState([]);
  const [conversationId, setConversationId] = useState(null);
  const [chatId, setChatId] = useState(null);
  const [isEnded, setIsEnded] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [voices, setVoices] = useState([]);
  const [selectedVoice, setSelectedVoice] = useState(null);
  const [showVoices, setShowVoices] = useState(false);
  const [themeTitle, setThemeTitle] = useState('');
  const [initialQuestion, setInitialQuestion] = useState('');
  const [userResponses, setUserResponses] = useState([]);
  const [showThemeDropdown, setShowThemeDropdown] = useState(false);
  const [allThemes] = useState(() => {
    const stored = localStorage.getItem('allThemes');
    return stored ? JSON.parse(stored) : [];
  });
  const userId = authState?.user;
  const [isGenerating, setIsGenerating] = useState(false);
  const [completedThemes] = useState(() => {
    const stored = localStorage.getItem(`completedThemes_${userId}`);
    return stored ? JSON.parse(stored) : [];
  });


  // Get theme data from navigation state
  const { theme_id, theme_documents_id, isRegenerating } = location.state || {};

  useEffect(() => {
    // Validate required data
    if (!theme_id || !theme_documents_id) {
      showToast(globalDispatch, "Missing required theme information", 4000, "error");
      navigate("/user/onboarding/theme");
      return;
    }

    // Get theme title from localStorage
    const storedTheme = localStorage.getItem('selectedTheme');
    if (storedTheme) {
      const theme = JSON.parse(storedTheme);
      setThemeTitle(theme.name);
    }

    // Initial API call to start conversation
    initiateChat();
  }, []);

  useEffect(() => {
    // Load available voices when component mounts
    const loadVoices = () => {
      const availableVoices = window.speechSynthesis.getVoices();
      // Filter for English voices only
      const englishVoices = availableVoices.filter(voice => 
        voice.lang.includes('en')
      );
      setVoices(englishVoices);
      // Set default voice
      if (englishVoices.length > 0) {
        setSelectedVoice(englishVoices[0]);
      }
    };

    // Chrome needs this event listener
    if (window.speechSynthesis.onvoiceschanged !== undefined) {
      window.speechSynthesis.onvoiceschanged = loadVoices;
    }
    loadVoices();
  }, []);

  // Add effect to handle theme changes
  useEffect(() => {
    if (location.state?.theme_id) {
      // Reset states
      setConversation([]);
      setUserInput('');
      setIsLoading(true);
      
      // Update theme title from localStorage
      const storedTheme = localStorage.getItem('selectedTheme');
      if (storedTheme) {
        const theme = JSON.parse(storedTheme);
        setThemeTitle(theme.name);
      }
      
      // Initiate new chat with selected theme
      initiateChat();
    }
  }, [location.state?.theme_id]); // Dependency on theme_id change

  const initiateChat = async () => {
    try {
      setIsLoading(true);
      
      const response = await sdk.callRawAPI(
        "/v3/api/custom/jordan/ai-chat/get-answer",
        {
          theme_id: location.state?.theme_id,
          theme_documents_id: location.state?.theme_documents_id
        },
        "POST"
      );

      if (!response.error) {
        setConversation([{
          role: 'ai',
          content: response.data.message
        }]);
        setInitialQuestion(response.data.message);
        if (response.data.conversation_id) {
          setConversationId(response.data.conversation_id);
        }
        if (response.data.chat_id) {
          setChatId(response.data.chat_id);
        }
        setIsEnded(response.data.end || false);
      }
    } catch (error) {
      console.error('Chat initiation error:', error);
      showToast(globalDispatch, error.message || "Failed to start conversation", 4000, "error");
      tokenExpireError(dispatch, error.message);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = async (e) => {
    if (e.key === 'Enter' && !e.shiftKey && userInput.trim()) {
      e.preventDefault();
      try {
        setIsLoading(true);
        
        // Store the input before clearing it
        const currentInput = userInput;
        
        // Clear input immediately
        setUserInput('');
        
        setUserResponses(prev => [...prev, currentInput]);

        setConversation(prev => [...prev, {
          role: 'user',
          content: currentInput
        }]);

        const response = await sdk.callRawAPI(
          "/v3/api/custom/jordan/ai-chat/get-answer",
          {
            theme_id: String(location.state?.theme_id),
            theme_documents_id: String(location.state?.theme_documents_id),
            conversation_id: String(conversationId),
            chat_id: String(chatId),
            answer: currentInput
          },
          "POST"
        );

        if (!response.error) {
          setConversation(prev => [...prev, {
            role: 'ai',
            content: response.data.message
          }]);
          if (response.data.conversation_id) {
            setConversationId(response.data.conversation_id);
          }
          if (response.data.chat_id) {
            setChatId(response.data.chat_id);
          }
          setIsEnded(response.data.end || false);
        }
      } catch (error) {
        console.error('Message send error:', error);
        showToast(globalDispatch, error.message || "Failed to send message", 4000, "error");
        tokenExpireError(dispatch, error.message);
      } finally {
        setIsLoading(false);
      }
    }
  };

  const startRecording = () => {
    if (!('webkitSpeechRecognition' in window)) {
      showToast(globalDispatch, "Voice recognition is not supported in your browser", 4000, "error");
      return;
    }

    const recognition = new window.webkitSpeechRecognition();
    recognition.continuous = true;
    recognition.interimResults = true;
    recognition.lang = 'en-US';

    recognition.onstart = () => {
      setIsRecording(true);
    };

    recognition.onresult = (event) => {
      const transcript = Array.from(event.results)
        .map(result => result[0])
        .map(result => result.transcript)
        .join('');
      
      setUserInput(transcript);
    };

    recognition.onerror = (event) => {
      console.error('Speech recognition error:', event.error);
      setIsRecording(false);
      showToast(globalDispatch, "Error recording voice", 4000, "error");
    };

    recognition.onend = () => {
      setIsRecording(false);
    };

    recognition.start();

    // Store recognition instance to stop it later
    window.recognition = recognition;
  };

  const stopRecording = () => {
    if (window.recognition) {
      window.recognition.stop();
    }
    setIsRecording(false);
  };

  const speakMessage = (message) => {
    if (!window.speechSynthesis) {
      showToast(globalDispatch, "Text-to-speech is not supported in your browser", 4000, "error");
      return;
    }

    window.speechSynthesis.cancel();

    const utterance = new SpeechSynthesisUtterance(message);
    if (selectedVoice) {
      utterance.voice = selectedVoice;
    }
    utterance.lang = 'en-US';
    utterance.rate = 1.0;
    utterance.pitch = 1.0;

    utterance.onstart = () => {
      setIsSpeaking(true);
    };

    utterance.onend = () => {
      setIsSpeaking(false);
    };

    utterance.onerror = () => {
      setIsSpeaking(false);
      showToast(globalDispatch, "Error reading message", 4000, "error");
    };

    window.speechSynthesis.speak(utterance);
  };

  const stopSpeaking = () => {
    window.speechSynthesis.cancel();
    setIsSpeaking(false);
  };

  const handleGenerateTheme = async () => {
    try {
      setIsGenerating(true);
      const sdk = new MkdSDK();

      // Get current theme from localStorage
      const currentTheme = JSON.parse(localStorage.getItem(`selectedTheme_${userId}`));
      
      // Make the API call to generate theme
      const response = await sdk.callRawAPI(
        "/v3/api/custom/theme/generate",
        {
          chat_id: String(chatId),
          theme_documents_id: String(theme_documents_id),
          theme_id: String(theme_id)
        },
        "POST"
      );

      if (!response.error) {
        // Get completed themes from localStorage
        const completedThemes = JSON.parse(localStorage.getItem(`completedThemes_${userId}`) || '[]');
        
        // Add current theme to completed themes if not already present
        if (!completedThemes.includes(currentTheme.id)) {
          const updatedCompletedThemes = [...completedThemes, currentTheme.id];
          localStorage.setItem(`completedThemes_${userId}`, JSON.stringify(updatedCompletedThemes));
        }

        // Get generated themes from localStorage
        const generatedThemes = JSON.parse(localStorage.getItem(`generatedThemes_${userId}`) || '[]');
        
        // Add current theme to generated themes
        const updatedGeneratedThemes = [...generatedThemes, {
          id: currentTheme.id,
          name: currentTheme.name,
          pdfUrl: response.data
        }];
        localStorage.setItem(`generatedThemes_${userId}`, JSON.stringify(updatedGeneratedThemes));

        // Trigger theme completed event
        document.dispatchEvent(new Event('themeCompleted'));

        // Navigate to preview
        navigate('/user/onboarding/themepreview', {
          state: {
            themeTitle: currentTheme.name,
            initialQuestion: initialQuestion,
            userResponses: conversation.filter(msg => msg.role === 'user').map(msg => msg.content),
            theme_id: currentTheme.id,
            pdfUrl: response.data
          }
        });
      } else {
        showToast(globalDispatch, "Failed to generate theme", 4000, "error");
      }

    } catch (error) {
      console.error('Generation error:', error);
      showToast(globalDispatch, error.message || "Failed to generate theme", 4000, "error");
      tokenExpireError(dispatch, error.message);
    } finally {
      setIsGenerating(false);
    }
  };

  const handleThemeSwitch = (theme) => {
    localStorage.setItem(`selectedTheme_${userId}`, JSON.stringify(theme));
    navigate('/user/onboarding/themecontd', {
      state: {
        theme_id: theme.ds_theme_id,
        theme_documents_id
      }
    });
  };

  // Add this new function to handle send button click
  const handleSendMessage = async () => {
    if (userInput.trim()) {
      // Simulate Enter key press without shift
      await handleKeyPress({ 
        key: 'Enter', 
        shiftKey: false, 
        preventDefault: () => {} 
      });
      // Clear input after sending
      setUserInput('');
    }
  };

  return (
    <>
      <div className="w-full text-7xl min-h-screen h-full text-gray-700 bg-[#E2E6EB] p-[1rem] md:pl-[3rem]">
      <div className="w-[95%] md:w-[90%] mx-auto md:mx-0">
          <h1 className="text-[20px] text-[#111928] font-semibold mb-6">Generate themes document</h1>
          <div className="flex items-center justify-between mb-8 relative bg-white h-[98px] p-[1rem]">
            <div className="absolute left-8 right-8 top-1/4 md:top-1/3 h-0.5 bg-gray-200 w-[100% - 2rem]"/>
            <div className="flex flex-col items-start z-10 w-[28%]">
              <div className="w-10 h-10 border border-[#9C9C9C] bg-white rounded-full flex items-center justify-center">
                <div className="w-6 h-6 bg-[#389C14] rounded-full flex items-center  justify-center">
                  <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"/>
                  </svg>
                </div>
              </div>
              <Link to="/user/onboarding" className="text-xs md:text-sm hover:text-[#054FB1] transition-colors">Upload your resume & full history docs</Link>
            </div>
            <div className="flex flex-col items-center z-10 w-[24%]">
              <div className="w-10 h-10 border border-[#9C9C9C] bg-white rounded-full flex items-center justify-center">
                <div className="w-6 h-6 bg-[#389C14] rounded-full flex items-center  justify-center">
                  <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"/>
                  </svg>
                </div>
              </div>
              <Link to="/user/onboarding/form" className="text-xs md:text-sm hover:text-[#054FB1] transition-colors">Fill out questions form</Link>
            </div>
            <div className="flex flex-col items-end z-10 w-[24%]">
              <div className="w-8 h-8 bg-[#054FB1] rounded-full flex items-center justify-center text-gray-500 mb-2">
                <img src={Monitor} className="w-[20px] h-[20px]" alt="upload icon"/>
              </div>
              <Link to="/user/onboarding/theme" className="text-xs md:text-sm text-[#054FB1]">
                Generate Themes ({completedThemes.length}/{allThemes.length})
              </Link>
            </div>
          </div>

          <div className="text-right font-bold text-xs md:text-[16px] text-[#373A4B]">50% completed</div>

          <div className=" bg-white p-8 flex flex-col mt-[2rem]">
            {/* Header Section */}
            <div className="flex justify-between items-center mb-6">
              <div className="flex flex-col md:flex-row md:items-center gap-2 text-[16px]">
                <span className="text-[#111928] font-medium">Theme title:</span>
                <span className="text-[#111928] text-[14px] sm:text-[16px]">{themeTitle}</span>
              </div>
              
              <div className="flex items-center gap-2 border border-[#DFE4EA] rounded-[6px] p-2 relative">
                <button 
                  onClick={() => {
                    if (isSpeaking) {
                      stopSpeaking();
                    } else if (conversation.length > 0) {
                      const lastAiMessage = [...conversation]
                        .reverse()
                        .find(msg => msg.role === 'ai');
                      if (lastAiMessage) {
                        speakMessage(lastAiMessage.content);
                      }
                    }
                  }}
                  className={`p-2 hover:bg-gray-100 rounded transition-colors ${
                    isSpeaking ? 'text-[#054FB1]' : 'text-[#373A4B]'
                  }`}
                >
                  <HiSpeakerWave className={`text-[24px] ${
                    isSpeaking ? 'animate-pulse' : ''
                  }`}/>
                </button>
                
                <button 
                  onClick={() => setShowVoices(!showVoices)}
                  className="p-2 hover:bg-gray-100 rounded"
                >
                  <svg 
                    width="20" 
                    height="20" 
                    viewBox="0 0 24 24" 
                    fill="none" 
                    stroke="currentColor" 
                    strokeWidth="2" 
                    strokeLinecap="round" 
                    strokeLinejoin="round"
                    className={`transform transition-transform ${showVoices ? 'rotate-180' : ''}`}
                  >
                    <polyline points="6 9 12 15 18 9"/>
                  </svg>
                </button>

                {/* Voice Selection Dropdown */}
                {showVoices && (
                  <div className="absolute top-full right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-[200px] overflow-y-auto w-[200px]">
                    {voices.map((voice, index) => (
                      <button
                        key={index}
                        onClick={() => {
                          setSelectedVoice(voice);
                          setShowVoices(false);
                        }}
                        className={`w-full px-4 py-2 text-left hover:bg-gray-100 text-sm ${
                          selectedVoice === voice ? 'bg-gray-50 text-[#054FB1]' : 'text-gray-700'
                        }`}
                      >
                        {voice.name}
                      </button>
                    ))}
                  </div>
                )}
              </div>
            </div>

            {/* Only show feedback text if isRegenerating is true */}
            {location.state?.isRegenerating && (
              <p className="font-bold text-[#111928] text-[16px] mb-[2rem]">Share your feedback 😊</p>
            )}

            {/* Content Section */}
            <div className="space-y-6 flex-grow">
              {conversation.map((message, index) => (
                <div key={index} className={`${
                  message.role === 'ai' 
                    ? "bg-gray-100 rounded-lg p-4 inline-block w-full md:max-w-[75%]" 
                    : "flex flex-col bg-[#2F3B4B] text-white rounded-lg p-4 w-fit max-w-[90%] md:max-w-[75%] ml-auto"
                }`}>
                  <div className={`font-medium mb-2 text-[14px] font-semibold ${
                    message.role === 'ai' ? "text-[#111928]" : "text-white"
                  }`}>
                    {message.role === 'ai' ? 'RCA AI' : 'You'}
                  </div>
                  <div>
                    <p className={`text-[14px] mt-[1rem] leadig-[22px] ${
                      message.role === 'ai' ? "text-[#111928]" : "text-white"
                    }`}>
                      {message.content}
                    </p>
                  </div>
                </div>
              ))}
              
              {/* AI Typing Indicator */}
              {isLoading && (
                <div className="bg-gray-100 rounded-lg p-4 inline-block max-w-[75%]">
                  <div className="font-medium mb-2 text-[14px] font-semibold text-[#111928]">
                  RCA AI
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
                  </div>
                </div>
              )}
            </div>

            {/* Bottom Section with Input, Mic, and Theme Switcher */}
            <div className="mt-[5rem]">
              {isEnded ? (
                location.state?.isRegenerating ? (
                  <button 
                    onClick={handleGenerateTheme}
                    disabled={isGenerating}
                    className={`flex items-center justify-center gap-2 px-6 py-2 bg-[#054FB1] text-white rounded hover:bg-blue-700 transition-colors font-medium text-[16px] h-[50px] ${isGenerating ? 'opacity-75 cursor-not-allowed' : ''}`}
                  >
                    {isGenerating ? (
                      <>
                        <svg 
                          className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" 
                          xmlns="http://www.w3.org/2000/svg" 
                          fill="none" 
                          viewBox="0 0 24 24"
                        >
                          <circle 
                            className="opacity-25" 
                            cx="12" 
                            cy="12" 
                            r="10" 
                            stroke="currentColor" 
                            strokeWidth="4"
                          />
                          <path 
                            className="opacity-75" 
                            fill="currentColor" 
                            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                          />
                        </svg>
                        GENERATING...
                      </>
                    ) : (
                      "SUBMIT FEEDBACK AND REGENERATE"
                    )}
                  </button>
                ) : (
                  <button
                    onClick={handleGenerateTheme}
                    disabled={isGenerating}
                    className={`flex items-center justify-center gap-2 bg-[#054FB1] text-white text-[16px] leading-[24px] px-6 py-2 rounded-md hover:bg-blue-700 transition-colors w-[231px] h-[50px] ${isGenerating ? 'opacity-75 cursor-not-allowed' : ''}`}
                  >
                    {isGenerating ? (
                      <>
                        <svg 
                          className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" 
                          xmlns="http://www.w3.org/2000/svg" 
                          fill="none" 
                          viewBox="0 0 24 24"
                        >
                          <circle 
                            className="opacity-25" 
                            cx="12" 
                            cy="12" 
                            r="10" 
                            stroke="currentColor" 
                            strokeWidth="4"
                          />
                          <path 
                            className="opacity-75" 
                            fill="currentColor" 
                            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                          />
                        </svg>
                        GENERATING...
                      </>
                    ) : (
                      "GENERATE THEME"
                    )}
                  </button>
                )
              ) : (
                <div className="flex flex-col md:flex-row items-center gap-4">
                  {/* Input and Mic Group - Takes more space on md+ screens */}
                  <div className="flex items-center gap-4 w-full md:w-[75%]">
                    <input
                      type="text"
                      value={userInput}
                      onChange={(e) => setUserInput(e.target.value)}
                      onKeyPress={handleKeyPress}
                      placeholder="Type your response here"
                      disabled={isLoading}
                      className="flex-1 px-4 py-2 outline-none text-sm rounded-lg border-[#054FB1] border-[1px] border-solid h-[48px]"
                    />
                    
                    {/* Mic/Stop/Send Button */}
                    <button 
                      onClick={
                        isRecording 
                          ? stopRecording 
                          : userInput.trim() 
                            ? handleSendMessage 
                            : startRecording
                      }
                      className={`p-2 rounded-lg h-[48px] w-[48px] flex-shrink-0 flex items-center justify-center transition-colors ${
                        isRecording 
                          ? 'bg-red-500 text-white hover:bg-red-600'
                          : userInput.trim() 
                            ? 'bg-[#054FB1] text-white hover:bg-blue-600'
                            : 'bg-gray-100 text-[#054FB1] hover:bg-gray-200'
                      }`}
                    >
                      {isRecording ? (
                        // Stop Recording Icon
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                          <rect x="6" y="6" width="12" height="12" />
                        </svg>
                      ) : userInput.trim() ? (
                        // Send Icon
                        <svg 
                          width="20" 
                          height="20" 
                          viewBox="0 0 24 24" 
                          fill="none" 
                          stroke="currentColor" 
                          strokeWidth="2" 
                          strokeLinecap="round" 
                          strokeLinejoin="round"
                        >
                          <line x1="22" y1="2" x2="11" y2="13"/>
                          <polygon points="22 2 15 22 11 13 2 9 22 2"/>
                        </svg>
                      ) : (
                        // Microphone Icon
                        <svg 
                          width="20" 
                          height="20" 
                          viewBox="0 0 24 24" 
                          fill="none" 
                          stroke="currentColor" 
                          strokeWidth="2" 
                          strokeLinecap="round" 
                          strokeLinejoin="round"
                        >
                          <path d="M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z"/>
                          <path d="M19 10v2a7 7 0 0 1-14 0v-2"/>
                          <line x1="12" y1="19" x2="12" y2="23"/>
                          <line x1="8" y1="23" x2="16" y2="23"/>
                        </svg>
                      )}
                    </button>
                  </div>

                  {/* Theme Switcher */}
                  {!location.state?.isRegenerating && !isEnded && (
                    <div className="w-full md:w-[25%] relative">
                      <button 
                        onClick={() => setShowThemeDropdown(!showThemeDropdown)}
                        className="w-full flex items-center justify-between text-[16px] text-[#637381] border rounded-lg px-4 py-2 h-[48px]"
                      >
                        Switch theme
                        <svg 
                          width="16" 
                          height="16" 
                          viewBox="0 0 24 24" 
                          fill="none" 
                          stroke="currentColor" 
                          strokeWidth="2" 
                          strokeLinecap="round" 
                          strokeLinejoin="round"
                          className={`transform transition-transform ${showThemeDropdown ? 'rotate-180' : ''}`}
                        >
                          <polyline points="6 9 12 15 18 9"/>
                        </svg>
                      </button>

                      {/* Theme Dropdown */}
                      {showThemeDropdown && (
                        <div className="absolute right-0 bottom-[calc(100%+8px)] w-64 bg-white border rounded-lg shadow-lg z-50 max-h-[200px] overflow-y-auto">
                          {allThemes.map((theme) => (
                            <button
                              key={theme.id}
                              onClick={() => handleThemeSwitch(theme)}
                              className="w-full px-4 py-2 text-left hover:bg-gray-50 flex items-center justify-between border-b border-gray-100 last:border-b-0"
                            >
                              <span className="text-[#373A4B] text-[14px]">{theme.name}</span>
                              {completedThemes.includes(theme.id) && (
                                <svg className="w-4 h-4 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                </svg>
                              )}
                            </button>
                          ))}
                        </div>
                      )}
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  )
}
export default ThemeContd;
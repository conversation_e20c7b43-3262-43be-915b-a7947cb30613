import React, { useState } from 'react';
import { Visa, Paypal, Amex } from "Assets/images";

const PaymentModal = ({ onClose }) => {
  const [cardNumber, setCardNumber] = useState('');
  const [expiry, setExpiry] = useState('');
  const [cvv, setCvv] = useState('');
  const [country, setCountry] = useState('');

  const handleSubmit = (e) => {
    e.preventDefault();
    // Handle payment submission logic here
    console.log('Payment submitted');
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center">
      <div className="bg-white rounded-lg p-6 w-[480px] relative">
        <button 
          onClick={onClose}
          className="absolute right-4 top-4 text-gray-500 hover:text-gray-700"
        >
          ✕
        </button>
        
        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <label className="block text-[16px] font-medium text-[#111928] mb-2">
              Card Number
            </label>
            <div className="relative">
              <input
                type="text"
                value={cardNumber}
                onChange={(e) => setCardNumber(e.target.value)}
                placeholder="1234 1234 1234 1234"
                className="w-full p-2 border rounded-md"
              />
              <div className="absolute right-2 top-2 flex space-x-2">
                <img src={Visa} alt="Visa" className="h-5" />
                <img src={Paypal} alt="Mastercard" className="h-5" />
                <img src={Amex} alt="Amex" className="h-5" />
              </div>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-[16px] font-medium text-[#111928] mb-2 ">
                Expiry date
              </label>
              <div className="relative">
                <input
                  type="text"
                  value={expiry}
                  onChange={(e) => setExpiry(e.target.value)}
                  placeholder="MM/YY"
                  className="w-full p-2 border rounded-md"
                />
                <span className="absolute right-2 top-2">📅</span>
              </div>
            </div>

            <div>
              <label className="block text-[16px] font-medium text-[#111928] mb-2">
                CVV
              </label>
              <div className="relative">
                <input
                  type="text"
                  value={cvv}
                  onChange={(e) => setCvv(e.target.value)}
                  placeholder="123"
                  className="w-full p-2 border rounded-md"
                />
                <span className="absolute right-2 top-2">🔒</span>
              </div>
            </div>
          </div>

          <div>
            <label className="block text-[16px] font-medium text-[#111928] mb-2">
              Country
            </label>
            <select 
              value={country}
              onChange={(e) => setCountry(e.target.value)}
              className="w-full p-2 border rounded-md"
            >
              <option value="">Select a country</option>
              <option value="pakistan">Pakistan</option>
              <option value="other">Other countries...</option>
            </select>
          </div>

          <button
            type="submit"
            className="w-full bg-blue-600 text-white py-3 rounded-md hover:bg-blue-700 transition-colors"
          >
            PAY NOW
          </button>
        </form>
      </div>
    </div>
  );
};

export default PaymentModal;
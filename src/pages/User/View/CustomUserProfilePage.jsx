import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { PiUserCircleFill } from "react-icons/pi";
import PaymentModal from '../Payment/PaymentModal';
import { AuthContext, tokenExpireError } from "Context/Auth";
import { GlobalContext, showToast } from "Context/Global";
import MkdSDK from "Utils/MkdSDK";
import { Elements } from '@stripe/react-stripe-js';
import { loadStripe } from '@stripe/stripe-js';
import { CardElement } from '@stripe/react-stripe-js';
import StripePriceComponent from "../Payment/StripePriceComponent"
import { Logo, Visa, Paypal, Amex } from "Assets/images";
import CardDetails from '../Payment/CardDetails';


const stripePromise = loadStripe('pk_test_51R3UZOBazfqAX4xwvEnCZFjXvlOqYkch3qznif64teEvrxi2IzKXj2XKc5LTHhunoQiUE266ZCokcSNHAvswJOt900QLGddBPx');
const sdk = new MkdSDK();

const EyeIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z" />
    <circle cx="12" cy="12" r="3" />
  </svg>
);

const EyeOffIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-10-7-10-7a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 10 7 10 7a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24" />
    <line x1="1" y1="1" x2="23" y2="23" />
  </svg>
);

const UserProfile = () => {
  const { dispatch } = React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const location = useLocation();
  const [activeTab, setActiveTab] = useState(location.state?.activeTab || 'Details');
  const [showUpgradeOptions, setShowUpgradeOptions] = useState(location.state?.showUpgradeOptions || false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [showCancelConfirmation, setShowCancelConfirmation] = useState(false);
  const [loading, setLoading] = useState(true);
  const [showPasswords, setShowPasswords] = useState({
    current: false,
    new: false,
    confirm: false
  });
  const [formData, setFormData] = useState({
    email: '',
    firstName: '',
    lastName: '',
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
    userId: null
  });
  const [plans, setPlans] = useState([]);
  const [selectedPlan, setSelectedPlan] = useState(null);
  const [currentPlan, setCurrentPlan] = useState(null);
  const [subscriptionLoading, setSubscriptionLoading] = useState(false);
  const [customerSubscription, setCustomerSubscription] = useState(null);
  const [showOneTimePayment, setShowOneTimePayment] = useState(false);


  // Add fetchUserData function
  const fetchUserData = async () => {
    try {
      setLoading(true);
      const sdk = new MkdSDK();
      const result = await sdk.getProfile();
      
      if (!result.error) {
        setFormData({
          ...formData,
          email: result.email || '',
          firstName: result.first_name || '',
          lastName: result.last_name || '',
          userId: result.id
        });
        
        // Update profile in auth context if needed
        dispatch({
          type: "UPDATE_PROFILE",
          payload: result,
        });
      }
    } catch (error) {
      console.error('Error fetching user data:', error);
      showToast(globalDispatch, "Failed to fetch user data", 4000, "error");
      tokenExpireError(dispatch, error.message);
    } finally {
      setLoading(false);
    }
  };

  // Add fetchPlans function
  const fetchPlans = async () => {
    try {
      const sdk = new MkdSDK();
      
      const response = await sdk.callRawAPI(
        "/v4/api/records/stripe_price",
        { 
          order: "id,desc",
          page: "1,10"
        },
        "GET"
      );

      if (!response.error && response.list) {
        const formattedPlans = response.list.map(plan => {
          // Parse the Stripe object JSON string
          const stripeObject = JSON.parse(plan.object);
          
          return {
            ...plan,
            price_id: plan.id,
            displayAmount: `$${plan.amount}`,
            interval: stripeObject.recurring ? 'Monthly' : 'One-time',
            features: [
              plan.type === 'recurring' ? 'Monthly subscription' : 'One-time payment',
              plan.is_usage_metered ? 'Usage-based billing' : 'Fixed price',
              stripeObject.recurring?.interval === 'month' ? 'Billed monthly' : null,
            ].filter(Boolean)
          };
        });

        // Sort plans by amount
        formattedPlans.sort((a, b) => a.amount - b.amount);
        setPlans(formattedPlans);
      }
    } catch (error) {
      console.error('Error fetching plans:', error);
      showToast(globalDispatch, "Failed to fetch plans", 4000, "error");
    }
  };

  // Add this function to fetch subscription
  const fetchSubscriptionStatus = async () => {
    try {
      // const subscription = await sdk.callRawAPI("/v3/api/custom/jordan/user/subscription",{},"GET")
      const sdk = new MkdSDK();
       const subscription = await sdk.getCustomerStripeSubscription();
      console.log("Subscription response:", subscription);
      setCustomerSubscription(subscription);
    } catch (error) {
      console.error("Subscription check error:", error);
      showToast(globalDispatch, "Failed to fetch subscription status", 4000, "error");
    }
  };

  // Update useEffect to include subscription fetch
  useEffect(() => {
    fetchUserData();
    fetchPlans();
    fetchSubscriptionStatus();
  }, []);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSaveChanges = async (e) => {
    e.preventDefault();
    try {
      const sdk = new MkdSDK();
      
      const result = await sdk.updateProfile({
        first_name: formData.firstName,
        last_name: formData.lastName,
      });

      if (!result.error) {
        showToast(globalDispatch, "Profile Updated Successfully", 4000, "success");
        fetchUserData(); // Refresh data after update
      }
    } catch (error) {
      console.error('Error updating profile:', error);
      showToast(globalDispatch, "Failed to update profile", 4000, "error");
      tokenExpireError(dispatch, error.message);
    }
  };

  const handlePasswordUpdate = async (e) => {
    e.preventDefault();
    if (formData.newPassword !== formData.confirmPassword) {
      showToast(globalDispatch, "Passwords do not match", 4000, "error");
      return;
    }

    try {
      const sdk = new MkdSDK();
      const result = await sdk.updatePassword(formData.newPassword);

      if (!result.error) {
        showToast(globalDispatch, "Password Updated Successfully", 4000, "success");
        setFormData({
          ...formData,
          currentPassword: '',
          newPassword: '',
          confirmPassword: ''
        });
      }
    } catch (error) {
      console.error('Error updating password:', error);
      showToast(globalDispatch, "Failed to update password", 4000, "error");
      tokenExpireError(dispatch, error.message);
    }
  };

  const togglePasswordVisibility = (field) => {
    setShowPasswords(prev => ({
      ...prev,
      [field]: !prev[field]
    }));
  };

  const handleSubscribe = (plan) => {
    setSelectedPlan(plan);
    if (plan.type === 'one_time') {
      setShowOneTimePayment(true);
    } else {
      setShowPaymentModal(true);
    }
  };


  // Helper function to get plan name
  const getPlanName = (planId) => {
    switch (planId) {
      case 5:
        return 'Basic Plan';
      case 6:
        return 'Starter Plan';
      case 7:
        return 'Pro Plan';
      default:
        return 'No Plan';
    }
  };

  const handleSubscriptionSuccess = () => {
    // Fetch updated subscription status
    fetchSubscriptionStatus();
    // Close modals and reset states
    setShowPaymentModal(false);
    setShowUpgradeOptions(false);
  };

  const handleCancelSubscription = async () => {
    try {
      if (!customerSubscription?.customer?.subId) {
        showToast(globalDispatch, "No active subscription found", 4000, "error");
        return;
      }

      const sdk = new MkdSDK();
      const result = await sdk.callRawAPI(
        `/v3/api/custom/jordan/user/cancel-subscription/${customerSubscription.customer.subId}`,
        {},
        "POST"
      );

      if (!result.error) {
        showToast(globalDispatch, "Subscription cancelled successfully", 4000, "success");
        // Refresh subscription status
        fetchSubscriptionStatus();
        // Close the confirmation modal
        setShowCancelConfirmation(false);
      } else {
        throw new Error("Failed to cancel subscription");
      }
    } catch (error) {
      console.error('Error cancelling subscription:', error);
      showToast(globalDispatch, "Something went wrong", 4000, "error");
      tokenExpireError(dispatch, error.message);
      // Close the confirmation modal even on error
      setShowCancelConfirmation(false);
    }
  };

  const CancelConfirmationModal = () => (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 transition-opacity" aria-hidden="true">
          <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
        </div>
        <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div className="inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6">
          <div>
            <div className="mt-3 text-center sm:mt-5">
              <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                Cancel Subscription
              </h3>
              <div className="mt-2">
                <p className="text-sm text-gray-500">
                  Are you sure you want to cancel your subscription? This action cannot be undone.
                </p>
              </div>
            </div>
          </div>
          <div className="mt-5 sm:mt-6 sm:grid sm:grid-cols-2 sm:gap-3 sm:grid-flow-row-dense">
            <button
              type="button"
              onClick={handleCancelSubscription}
              className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none sm:col-start-2 sm:text-sm"
            >
              Cancel Subscription
            </button>
            <button
              type="button"
              onClick={() => setShowCancelConfirmation(false)}
              className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none sm:mt-0 sm:col-start-1 sm:text-sm"
            >
              Keep Subscription
            </button>
          </div>
        </div>
      </div>
    </div>
  );

  const renderDetailsTab = () => (
    <div className="md:p-6 pt-[1rem] md:pt-0">
      {loading ? (
        <div className="flex justify-center items-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
        </div>
      ) : (
        <div className="mb-6 flex items-center gap-2 mt-[1rem]">
          <PiUserCircleFill className='text-[20px]' />
          <span className='text-[16px] text-[#373A4B]'>{formData.email}</span>
        </div>
      )}
      
      <form onSubmit={handleSaveChanges}>
        <div className="space-y-4 mt-[2rem]">
          <div className='flex flex-col gap-2'>
            <label className="mb-2 block text-sm font-medium text-[16px] text-[#111928]">First Name</label>
            <input
              type="text"
              name="firstName"
              className=" w-full md:w-[433px] h-[46px] rounded-md border border-gray-300 px-3 py-2"
              placeholder="Enter first name"
              value={formData.firstName}
              onChange={handleInputChange}
            />
          </div>

          <div className='flex flex-col gap-2'>
            <label className="mb-2 block text-sm font-medium text-[16px] text-[#111928]">Last Name</label>
            <input
              type="text"
              name="lastName"
              className=" w-full md:w-[433px] h-[46px] rounded-md border border-gray-300 px-3 py-2"
              placeholder="Enter last name"
              value={formData.lastName}
              onChange={handleInputChange}
            />
          </div>

          <div className='flex flex-col gap-2'>
            <label className="mb-2 block text-sm font-medium text-[16px] text-[#111928]">Email</label>
            <input
              type="email"
              name="email"
              className="rounded-md border border-gray-300 px-3 py-2  w-full md:w-[433px] h-[46px]"
              placeholder="Enter email"
              value={formData.email}
              onChange={handleInputChange}
            />
          </div>

          <div>
            <button
              type="submit"
              className="rounded-md bg-[#054FB1] px-4  w-full md:w-[433px] h-[50px] text-medium text-[16px] py-2 text-white hover:bg-blue-700"
            >
              Save Changes
            </button>
          </div>
        </div>
      </form>
    </div>
  );

  const renderSecurityTab = () => (
    <div className="md:p-6 pt-[1rem] md:pt-0">
      <div className="mb-6 flex items-center gap-2 mt-[1rem]">
        <PiUserCircleFill className='text-[20px]' />
        <span className='text-[16px] text-[#373A4B]'>{formData.email}</span>
      </div>
      
      <form onSubmit={handlePasswordUpdate}>
        <div className="space-y-4 mt-[2rem]">
          <div className='flex flex-col'>
            <label className="mb-2 block text-sm font-medium text-[16px] text-[#111928]">Current Password</label>
            <div className="relative w-full md:w-[433px] mt-[-1.5rem]">
              <input
                type="text"
                name="currentPassword"
                className="w-full h-[46px] rounded-md border border-gray-300 px-3 py-2"
                placeholder="*****"
                value={formData.currentPassword}
                onChange={handleInputChange}
                disabled
              />
            </div>
          </div>

          <div className='flex flex-col'>
            <label className="mb-2 block text-sm font-medium text-[16px] text-[#111928]">New Password</label>
            <div className="relative  w-full md:w-[433px] mt-[-1.5rem]">
              <input
                type={showPasswords.new ? 'text' : 'password'}
                name="newPassword"
                className="w-full h-[46px] rounded-md border border-gray-300 px-3 py-2 pr-10"
                placeholder="Enter new password"
                value={formData.newPassword}
                onChange={handleInputChange}
              />
              <button
                type="button"
                onClick={() => togglePasswordVisibility('new')}
                className="absolute right-3 top-1/2 transform text-gray-500 bg-white"
              >
                {showPasswords.new ? <EyeOffIcon /> : <EyeIcon />}
              </button>
            </div>
          </div>

          <div className='flex flex-col'>
            <label className="mb-2 block text-sm font-medium text-[16px] text-[#111928]">Confirm New Password</label>
            <div className="relative  w-full md:w-[433px] mt-[-1.5rem] ">
              <input
                type={showPasswords.confirm ? 'text' : 'password'}
                name="confirmPassword"
                className="w-full h-[46px] rounded-md border border-gray-300 px-3 py-2 pr-10"
                placeholder="Confirm new password"
                value={formData.confirmPassword}
                onChange={handleInputChange}
              />
              <button
                type="button"
                onClick={() => togglePasswordVisibility('confirm')}
                className="absolute right-3 top-1/2 transform text-gray-500 bg-white"
              >
                {showPasswords.confirm ? <EyeOffIcon /> : <EyeIcon />}
              </button>
            </div>
          </div>

          <div>
            <button
              type="submit"
              className="rounded-md bg-[#054FB1] px-4  w-full md:w-[433px] h-[50px] text-medium text-[16px] py-2 text-white hover:bg-blue-700"
            >
              Update Password
            </button>
          </div>
        </div>
      </form>
    </div>
  );

  const renderCurrentPlanTab = () => (
    <div className="">
      {!showUpgradeOptions ? (
        <div>
          <div className="flex items-center justify-between bg-white p-6">
            <div>
              <p className="text-sm text-gray-600">Current Plan</p>
              <p className="text-lg font-semibold">
                {customerSubscription?.customer?.planId ? 
                  getPlanName(customerSubscription.customer.planId) : 
                  'No Active Plan'
                }
              </p>
              {customerSubscription?.customer?.subId && (
                <p className="text-sm text-gray-500 mt-1">
                  Subscription ID: {customerSubscription.customer.subId}
                </p>
              )}
            </div>
            <button 
              onClick={() => setShowUpgradeOptions(true)}
              className="rounded-md bg-blue-600 px-4 py-2 text-white hover:bg-blue-700 text-[14px] md:text-[16px] font-medium h-[50px] w-[100px] md:w-[194px]"
            >
              UPGRADE
            </button>
          </div>
          {customerSubscription?.customer?.planId && (
            <div className="mt-4 flex justify-end">
              <button 
                onClick={() => setShowCancelConfirmation(true)}
                className="text-[#054FB1] text-[16px] font-semibold hover:text-blue-700"
              >
                Cancel subscription
              </button>
            </div>
          )}
        </div>
      ) : (
        <div>
          <h3 className="text-xl font-semibold mb-6 mt-4">Available Plans</h3>
          <div className="flex flex-col md:flex-row gap-6">
            {plans.filter(plan => plan.type === 'recurring').map((plan) => {
              const stripeObject = JSON.parse(plan.object);
              const features = [
                'Monthly subscription',
                plan.is_usage_metered ? 'Usage-based billing' : 'Fixed price',
                stripeObject.recurring?.interval === 'month' ? 'Billed monthly' : null,
              ].filter(Boolean);

              return (
                <div key={plan.id} className="rounded-lg border border-gray-200 p-6 bg-white">
                  <h3 className="mb-2 text-lg font-semibold">{plan.name}</h3>
                  <p className="mb-4 text-2xl font-bold">${plan.amount}/mo</p>
                  <div className="mb-4 space-y-2">
                       <p className="text-[#373A4B] text-[16px] leading-[24px] flex items-center">
                        <span className="mr-2">•</span>
                       Monthly Subscription
                      </p>
                      <p className="text-[#373A4B] text-[16px] leading-[24px] flex items-center">
                        <span className="mr-2">•</span>
                       Recurring Billing
                      </p>
                      <p className="text-[#373A4B] text-[16px] leading-[24px] flex items-center">
                        <span className="mr-2">•</span>
                        {plan.usage_limit} quizzes genneration
                      </p>
                    
                  </div>
                  <button 
                    onClick={() => handleSubscribe(plan)}
                    disabled={currentPlan?.id === plan.id}
                    className={`rounded-md px-4 py-2 text-white text-[16px] font-medium h-[50px] w-[213px] ${
                      currentPlan?.id === plan.id 
                        ? 'bg-gray-400 cursor-not-allowed' 
                        : 'bg-blue-600 hover:bg-blue-700'
                    }`}
                  >
                    {currentPlan?.id === plan.id ? 'CURRENT PLAN' : 'SUBSCRIBE'}
                  </button>
                </div>
              );
            })}

            {/* <div className="rounded-lg border border-gray-200 p-6 bg-white">
              <h3 className="mb-2 text-lg font-semibold">Pro Access</h3>
              <p className="mb-4 text-2xl font-bold">$40</p>
              <div className="mb-4 space-y-2">
                <p className="text-[#373A4B] text-[16px] leading-[24px] flex items-center">
                  <span className="mr-2">•</span>
                  One-time payment
                </p>
                <p className="text-[#373A4B] text-[16px] leading-[24px] flex items-center">
                  <span className="mr-2">•</span>
                  Lifetime access
                </p>
                <p className="text-[#373A4B] text-[16px] leading-[24px] flex items-center">
                  <span className="mr-2">•</span>
                  15 Quizzes generation
                </p>
              </div>
              <button 
                onClick={() => handleSubscribe({
                  id: '7', // Pro Plan ID
                  amount: 40,
                  name: 'Pro Access',
                  type: 'one_time'
                })}
                className="rounded-md px-4 py-2 text-white text-[16px] font-medium h-[50px] w-[213px] bg-blue-600 hover:bg-blue-700"
              >
                BUY NOW
              </button>
            </div>*/}
          </div> 

          <div className="mt-6">
            <button 
              onClick={() => setShowUpgradeOptions(false)}
              className="rounded-md border border-blue-600 px-4 py-2 h-[50px] w-[85px] mt-[3rem] hover:bg-blue-50 font-medium text-[16px] text-[#054FB1]"
            >
              BACK
            </button>
          </div>
        </div>
      )}
      {showPaymentModal && (
        <Elements stripe={stripePromise}>
          <PaymentModal 
            onClose={() => setShowPaymentModal(false)} 
            planId={selectedPlan?.id}
            price={selectedPlan?.amount}
            trial={selectedPlan?.trial_days}
            onSuccess={handleSubscriptionSuccess}
          />
        </Elements>
      )}
      {showCancelConfirmation && <CancelConfirmationModal />}
      {showOneTimePayment && (
        <Elements stripe={stripePromise}>
          <div className="fixed inset-0 z-50 bg-black bg-opacity-50 flex items-center justify-center">
            <CardDetails 
              onClose={() => setShowOneTimePayment(false)}
              onSubmit={handlePaymentSubmit}
              price={selectedPlan?.amount}
              loading={subscriptionLoading}
              userId={formData.email}
              priceId={selectedPlan?.id}
              onSuccess={handleSubscriptionSuccess}
            />
          </div>
        </Elements>
      )}
    </div>
  );

  return (
    <div className="w-full text-7xl min-h-screen h-full text-gray-700 bg-[#E2E6EB] md:p-[1rem]">
        <div className="w-[98%] md:w-[90%] mx-auto md:mx-0">
    <div className="mx-auto max-w-5xl p-6">
      <div className="rounded-lg border border-gray-200 ">
        <div className="border-b border-gray-200">
          <div className="flex justify-between md:justify-evenly md:space-x-[6rem] md:px-6 bg-white h-[54px]">
            {['Details', 'Security', 'Current plan'].map((tab) => (
              <button
                key={tab}
                onClick={() => setActiveTab(tab)}
                className={`relative px-3 py-2 text-[16px] font-medium ${
                  activeTab === tab
                    ? 'text-blue-600'
                    : 'text-gray-500 hover:text-[#637381]'
                }`}
              >
                {tab}
                {activeTab === tab && (
                  <div className="absolute bottom-0 left-0 h-0.5 w-full bg-blue-600" />
                )}
              </button>
            ))}
          </div>

        </div>

        {activeTab === 'Details' && renderDetailsTab()}
        {activeTab === 'Security' && renderSecurityTab()}
        {activeTab === 'Current plan' && renderCurrentPlanTab()}
      </div>
    </div>
    </div>
    </div>
  );
};

export default UserProfile;
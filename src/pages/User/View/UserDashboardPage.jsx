import React, { useState, useEffect } from "react";
import { AuthContext } from "Context/Auth";
import { GlobalContext } from "Context/Global";
import { BiSolidFileExport } from "react-icons/bi";
import { HiSpeakerWave } from "react-icons/hi2";
import { TbWindowMaximize } from "react-icons/tb";
import { Document, videoPlayerBg } from "Assets/images";
import {Link} from "react-router-dom"
import MkdSDK from "Utils/MkdSDK";

const UserDashboardPage = () => {
  const { dispatch } = React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const [userData, setUserData] = useState({ documents: 0, quizzes: 0 });
  const [isLoading, setIsLoading] = useState(true);
  const sdk = new MkdSDK();

  useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "user",
      },
    });

    const fetchUserInfo = async () => {
      try {
        const result = await sdk.callRawAPI(
          `/v3/api/custom/jordan/user/user-info`,
          {},
          "GET"
        );

        if (!result.error && result.data) {
          setUserData(result.data);
        }
      } catch (error) {
        console.error("Error fetching user info:", error);
        tokenExpireError(dispatch, error.message);
      } finally {
        setIsLoading(false);
      }
    };

    fetchUserInfo();
  }, []);

  return (
    <>
      <div className="md:pl-[3rem] w-full text-7xl h-full text-gray-700 bg-[#E2E6EB] p-[1rem]">
        <div className="w-[95%] md:w-[90%] mx-auto md:mx-0">
          <h1 className="text-[20px] text-[#111928] font-semibold mb-6">Dashboard</h1>
        
        {/* Stats Cards Row */}
        <div className="flex flex-col md:flex-row gap-6 mb-6">
          <div className="bg-white rounded-lg p-6 flex-1 shadow-sm">
            <div className="flex justify-between items-center">
              <div>
                <h2 className="text-[16px] text-[#111928] leading-[24px] mb-2 font-normal">Total documents generated</h2>
                <p className="text-[32px] text-[#111928] leading-[24px] font-semibold">{userData.documents}</p>
              </div>
              <div className="bg-gray-100 p-2  rounded-[40px]">
                <img src={Document} alt="Document" className="w-[32px] h-[32px]" />
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg p-6 flex-1 shadow-sm">
            <div className="flex justify-between items-center">
              <div>
                <h2 className="text-[16px] text-[#111928] leading-[24px] mb-2 font-normal">Total quizzes generated</h2>
                <p className="text-[32px] text-[#111928] leading-[24px] font-semibold">{userData.quizzes}</p>
              </div>
              <div className="bg-gray-100 p-2 rounded-[40px]">
              <img src={Document} alt="Document" className="w-[32px] h-[32px]" />
              </div>
            </div>
          </div>
        </div>
        
        {/* Main Content Card */}
        <div className="bg-white rounded-lg p-8 shadow-sm mb-6">
          <h2 className="text-xl font-semibold mb-4">Rescue Career Academy</h2>
          <p className="mb-6 leading-relaxed text-[16px] font-normal text-[#373A4B] leading-[24px]">
            Our platform is designed to equip users with practical tools and strategies to enhance their skills in high-performance teamwork, situational awareness, problem-solving, and customer service. By providing structured frameworks like STARTPOP, we guide users to confidently handle real-life challenges, refine their communication skills, and achieve personal and professional growth.
          </p>
          <p className="text-gray-600 mb-6 leading-relaxed text-[16px] font-normal text-[#373A4B] leading-[24px]">
            Whether you're navigating complex scenarios or striving for continuous improvement, we are here to help you succeed
          </p>
          <p className="text-gray-600 mb-4 text-[16px] font-normal text-[#373A4B] leading-[24px]">Get Started on Your themes generation</p>
          <Link to="/user/onboarding">
          <button className="bg-[#054FB1] text-white text-[16px] font-medium leading-[24px] px-6 py-2 rounded-md hover:bg-blue-700 transition-colors  w-[231px] h-[50px]">
            GET STARTED
          </button>
          </Link>
        </div>
        
        {/* Video Section */}
        <div className="bg-white rounded-lg p-8 shadow-sm">
          <h2 className="text-xl font-semibold mb-4">Getting Started Guide</h2>
          <div className="relative w-full aspect-video">
            <iframe
              width="100%"
              height="100%"
              src="https://www.youtube.com/embed/8Hf-XrPZ0mk"
              title="Getting Started Guide"
              frameBorder="0"
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
              allowFullScreen
            ></iframe>
          </div>
          {/* Removed custom video controls since YouTube provides its own */}
        </div>
      </div>
      </div>
    </>
  );
};

export default UserDashboardPage;
  
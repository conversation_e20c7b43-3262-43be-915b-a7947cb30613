import React, { useState, useEffect } from 'react';
import { Elements } from '@stripe/react-stripe-js';
import { loadStripe } from '@stripe/stripe-js';
import { AuthContext, tokenExpireError } from "Context/Auth";
import { GlobalContext, showToast } from "Context/Global";
import MkdSDK from "Utils/MkdSDK";
import PaymentModal from './PaymentModal';

const stripePromise = loadStripe('pk_test_51R3UZOBazfqAX4xwvEnCZFjXvlOqYkch3qznif64teEvrxi2IzKXj2XKc5LTHhunoQiUE266ZCokcSNHAvswJOt900QLGddBPx');

const StripePlansComponent = () => {
  const sdk = new MkdSDK();
  const { dispatch } = React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const [plans, setPlans] = useState([]);
  const [userSubscription, setUserSubscription] = useState({});
  const [subscriptionLoading, setSubscriptionLoading] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState(null);

  const cancel = async (subId) => {
    try {
      const data = await sdk.cancelStripeSubscription(subId);
      if (data.error) {
        console.error(data.message);
        showToast(globalDispatch, data.message, 7500, "error");
        return;
      }
      showToast(globalDispatch, data.message, 10000, "success");
      fetchUserSubscription();
      fetchPlans();
    } catch (error) {
      console.error(error);
      showToast(globalDispatch, error.message, 7500, "error");
      tokenExpireError(dispatch, error.code);
    }
  };

  const handlePaymentSubmit = async (result) => {
    if (result.error) {
      showToast(globalDispatch, result.error.message, 4000, "error");
      return;
    }

    setSubscriptionLoading(true);
    try {
      if (selectedPlan.type === "recurring") {
        const data = await sdk.createStripeSubscription({ 
          planId: selectedPlan.id,
          cardToken: result.token.id
        });
        if (data.error) {
          showToast(globalDispatch, data.message, 7500, "error");
          return;
        }
        showToast(globalDispatch, "Subscription created successfully", 10000, "success");
      } else {
        const params = {
          success_url: `${sdk.fe_baseurl}/user/checkout?success=true&session_id={CHECKOUT_SESSION_ID}`,
          cancel_url: `${sdk.fe_baseurl}/user/checkout?success=false&session_id={CHECKOUT_SESSION_ID}`,
          mode: "payment",
          payment_method_types: ["card"],
          line_items: [
            {
              price: selectedPlan.price_id,
              quantity: 1,
            },
          ],
          payment_intent_data: {
            metadata: {
              app_price_id: selectedPlan.id,
              is_lifetime_subscription: selectedPlan.type === "lifetime" ? "true" : "false",
            },
          },
        };

        const data = await sdk.initCheckoutSession(params);
        if (data.error) {
          showToast(globalDispatch, data.message || "Something went wrong");
          return;
        }
        if (data?.model?.url) location.href = data.model.url;
      }
    } catch (error) {
      console.error(error);
      showToast(globalDispatch, error.message, 7500, "error");
      tokenExpireError(dispatch, error.code);
    } finally {
      setSubscriptionLoading(false);
      setShowPaymentModal(false);
    }
  };

  const handleSubscribe = (plan) => {
    setSelectedPlan(plan);
    setShowPaymentModal(true);
  };

  const fetchPlans = async () => {
    try {
      const data = await sdk.getStripePrices(
        { limit: "all" },
        { type: "recurring,lifetime,one_time" }
      );
      if (data.error) {
        showToast(globalDispatch, data.message, 7500);
        return;
      }
      setPlans(data.list);
    } catch (error) {
      console.error(error);
      showToast(globalDispatch, error.message, 7500, "error");
      tokenExpireError(dispatch, error.code);
    }
  };

  const fetchUserSubscription = async () => {
    try {
      const data = await sdk.getCustomerStripeSubscription();
      if (data.error) {
        showToast(globalDispatch, data.message, 7500);
        return;
      }
      setUserSubscription(data.customer);
    } catch (error) {
      showToast(globalDispatch, error.message, 7500, "error");
      tokenExpireError(dispatch, error.code);
    }
  };

  useEffect(() => {
    fetchPlans();
    fetchUserSubscription();
  }, []);

  return (
    <div className="my-4 rounded bg-white p-5 shadow-lg">
      <h2 className="mb-3 mt-0 text-2xl font-medium leading-tight text-black">
        Plans
      </h2>
      <div className="flex w-full flex-wrap justify-center">
        {plans.map((plan, index) => (
          <div
            className="flex w-full justify-center md:w-1/2 xl:w-1/4"
            key={index}
          >
            <div className="m-2 w-full max-w-sm rounded-lg border bg-white text-center shadow-lg">
              <div className="border-b border-gray-300 px-6 py-3">
                {plan.product_name ?? "n/a"}
              </div>
              <div className="flex flex-col justify-center p-6">
                <h5 className="mb-2 text-xl font-medium text-gray-900">
                  {plan.name}
                </h5>
                <p className="mb-4 text-base text-gray-700">
                  ${+plan.amount}
                </p>

                {+userSubscription?.planId === +plan.id ? (
                  <button
                    onClick={() => cancel(userSubscription.subId)}
                    type="button"
                    className="inline-block rounded bg-blue-600 px-6 py-2.5 text-sm font-medium uppercase leading-tight text-white shadow-md transition duration-150 ease-in-out hover:bg-blue-700 hover:shadow-lg focus:bg-blue-700 focus:shadow-lg focus:outline-none focus:ring-0 active:bg-blue-800 active:shadow-lg"
                  >
                    Cancel
                  </button>
                ) : (
                  <button
                    onClick={() => handleSubscribe(plan)}
                    type="button"
                    disabled={subscriptionLoading}
                    className="inline-block rounded bg-blue-600 px-6 py-2.5 text-sm font-medium uppercase leading-tight text-white shadow-md transition duration-150 ease-in-out hover:bg-blue-700 hover:shadow-lg focus:bg-blue-700 focus:shadow-lg focus:outline-none focus:ring-0 active:bg-blue-800 active:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {subscriptionLoading ? 'Processing...' : 'Subscribe'}
                  </button>
                )}
              </div>
              <div className="border-t border-gray-300 px-6 py-3 text-gray-600">
                {+plan.trial_days > 0
                  ? `${+plan.trial_days} Days`
                  : "No trial"}
              </div>
            </div>
          </div>
        ))}
      </div>

      {showPaymentModal && (
        <Elements stripe={stripePromise}>
          <PaymentModal
            onClose={() => setShowPaymentModal(false)}
            onSubmit={handlePaymentSubmit}
            loading={subscriptionLoading}
          />
        </Elements>
      )}
    </div>
  );
};

export default StripePlansComponent; 
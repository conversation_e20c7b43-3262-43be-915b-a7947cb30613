import React, { useState } from 'react';
import { useForm } from "react-hook-form";
import { useNavigate } from "react-router";
import { GlobalContext, showToast } from "Context/Global";
import { AuthContext, tokenExpireError } from "Context/Auth";
import {
  formatCreditCardNumber,
  formatCVC,
  formatExpirationDate,
} from "Utils/utils";
import MkdSDK from "Utils/MkdSDK";

let sdk = new MkdSDK();

const PaymentModal = ({ onClose, planId, price, trial, onSuccess }) => {
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const { dispatch } = React.useContext(AuthContext);
  const [loading, setLoading] = React.useState(false);
  const [state, setState] = React.useState({
    number: "",
    name: "",
    expiry: "",
    cvc: "",
    issuer: "",
    focused: "",
    formData: null,
  });
  let form = React.useRef(null);
  const navigate = useNavigate();

  const handleCallback = ({ issuer }, isValid) => {
    if (isValid) {
      setState({ issuer });
    }
  };

  const handleInputFocus = ({ target }) => {
    setState({
      focused: target.name,
    });
  };

  const handleInputChange = ({ target }) => {
    if (target.name === "number") {
      target.value = formatCreditCardNumber(target.value);
    } else if (target.name === "expiry") {
      target.value = formatExpirationDate(target.value);
    } else if (target.name === "cvc") {
      target.value = formatCVC(target.value);
    }

    setState({ [target.name]: target.value });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    const formData = [...e.target.elements]
      .filter((d) => d.name)
      .reduce((acc, d) => {
        acc[d.name] = d.value;
        return acc;
      }, {});
    setState({ formData });

    const toastMsg = trial
      ? `After trial, you will be billed $${price} monthly.`
      : `Your $${price} payment was successful.`;

    setLoading(true);
    try {
      const res = await sdk.addStripeCard(formData);

      if (res.error || !res.id) {
        throw new Error(res.error.code || "Error adding stripe card");
      }

      const cardAddRes = await sdk.callRawAPI(
        "/v2/api/lambda/stripe/customer/card",
        { sourceToken: res?.id },
        "POST"
      );

      if (cardAddRes.error)
        throw new Error(
          cardAddRes.message || "Error adding stripe card (lambda)"
        );

      const payment = await sdk.callRawAPI(
        `/v3/api/custom/jordan/user/subscription`,
        {
          planId: planId,
        },
        "POST"
      );
      if (payment.error)
        throw new Error(payment.message || "Error occurred while paying");

      showToast(globalDispatch, toastMsg, 10000, "success");
      setLoading(false);
      if (onSuccess) {
        onSuccess();
      }
      onClose();
      navigate("/user/profile");
    } catch (error) {
      showToast(globalDispatch, error.message, 4000, "error");
      setLoading(false);
      tokenExpireError(dispatch, error.message);
    }
  };

  return (
    <div className="fixed inset-0 z-50 bg-black bg-opacity-50 flex items-center justify-center">
      <div className="bg-white h-[calc(100vh-7rem)] md:h-auto md:rounded-lg p-8 w-screen md:w-[480px] relative">
        <button 
          onClick={onClose}
          className="absolute right-6 top-6 text-gray-500 hover:text-gray-700"
        >
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M18 6L6 18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M6 6L18 18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </button>
        
        <h2 className="text-xl font-semibold mb-6">Payment Details</h2>
        
        <form ref={(c) => (form = c)} onSubmit={handleSubmit} className="space-y-6">
          <div>
            <label className="block text-[16px] font-medium text-[#111928] mb-2">
              Card Number
            </label>
            <input
              type="tel"
              name="number"
              className="w-full p-3 border border-gray-300 rounded-md"
              placeholder="Card Number"
              pattern="[\d| ]{16,22}"
              required
              onChange={handleInputChange}
              onFocus={handleInputFocus}
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-[16px] font-medium text-[#111928] mb-2">
                Expiry date
              </label>
              <input
                type="tel"
                name="expiry"
                className="w-full p-3 border border-gray-300 rounded-md"
                placeholder="MM/YY"
                pattern="\d\d/\d\d"
                required
                onChange={handleInputChange}
                onFocus={handleInputFocus}
              />
            </div>

            <div>
              <label className="block text-[16px] font-medium text-[#111928] mb-2">
                CVV
              </label>
              <input
                type="tel"
                name="cvc"
                className="w-full p-3 border border-gray-300 rounded-md"
                placeholder="CVC"
                pattern="\d{3,4}"
                required
                onChange={handleInputChange}
                onFocus={handleInputFocus}
              />
            </div>
          </div>

          <button
            type="submit"
            disabled={loading}
            className="w-full bg-[#054FB1] text-white py-3 rounded-md hover:bg-blue-700 transition-colors text-[16px] font-medium h-[50px] disabled:opacity-50"
          >
            {loading ? "Processing..." : "PAY NOW"}
          </button>
        </form>
      </div>
    </div>
  );
};

export default PaymentModal;
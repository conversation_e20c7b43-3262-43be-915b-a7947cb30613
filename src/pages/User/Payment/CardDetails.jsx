import { Logo, Visa, Paypal, Amex } from "Assets/images";
import { CardElement, useStripe, useElements } from '@stripe/react-stripe-js';
import { useState } from 'react';

export default function CardDetails({ onClose, onSubmit, price, loading }) {
  const stripe = useStripe();
  const elements = useElements();
  const [postalCode, setPostalCode] = useState('');

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (onSubmit && stripe && elements) {
      await onSubmit({ stripe, elements, postalCode });
    }
  };

  const cardElementOptions = {
    style: {
      base: {
        fontSize: '16px',
        color: '#424770',
        '::placeholder': {
          color: '#aab7c4',
        },
        padding: '12px',
      },
      invalid: {
        color: '#9e2146',
      },
    },
    hidePostalCode: true,
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4">
      <div className="relative w-full max-w-4xl bg-white rounded-2xl shadow-2xl overflow-hidden">
        {/* Subtle background pattern */}
        {/* <div className="absolute inset-0 opacity-5 bg-[radial-gradient(#4F46E5_1px,transparent_1px)] [background-size:16px_16px]"></div> */}
        
        {/* Close button */}
        <button 
          onClick={onClose}
          className="absolute right-6 top-6 p-2 hover:bg-gray-100 rounded-full transition-colors duration-200"
        >
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#666">
            <path d="M18 6L6 18M6 6l12 12" strokeWidth="2" strokeLinecap="round"/>
          </svg>
        </button>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-12 p-12">
          {/* Left Column */}
          <div className="flex flex-col">
            <img 
              src={Logo} 
              alt="Logo" 
              className="h-[73px] w-[54.75px] mb-12"
            />
            <h1 className="text-4xl font-semibold mb-6 leading-tight text-gray-900">
              Make a One-Time Payment
            </h1>
            <p className="text-lg leading-relaxed text-gray-600">
              Enjoy lifetime access to unlimited prompts and PDF generation. Pay once and unlock endless possibilities to boost your creativity and productivity!
            </p>
            
            {/* Price display for mobile */}
            <div className="md:hidden mt-8 p-4 bg-gray-50 rounded-lg">
              <div className="text-center">
                <span className="text-2xl font-bold text-gray-900">${price}</span>
                <span className="ml-2 text-gray-600">USD</span>
              </div>
            </div>
          </div>

          {/* Right Column */}
          <div className="flex flex-col">
            {/* Price display for desktop */}
            <div className="hidden md:block mb-8 p-4 bg-gray-50 rounded-lg">
              <div className="text-center">
                <span className="text-2xl font-bold text-gray-900">${price}</span>
                <span className="ml-2 text-gray-600">USD</span>
              </div>
            </div>

            <form className="rounded-xl border border-gray-200 p-8 bg-white shadow-sm" onSubmit={handleSubmit}>
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">Card Information</label>
                <div className="relative mb-4">
                  <CardElement
                    options={cardElementOptions}
                    className="p-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                
                {/* Custom Postal Code Input */}
                <div className="mt-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Postal Code
                  </label>
                  <input
                    type="text"
                    value={postalCode}
                    onChange={(e) => setPostalCode(e.target.value.toUpperCase())}
                    placeholder="Enter postal code"
                    className="w-full p-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    required
                  />
                </div>
              </div>

              <button 
                type="submit"
                disabled={!stripe || !elements || loading || !postalCode}
                className="w-full rounded-lg bg-blue-600 py-3 px-4 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors duration-200"
              >
                {loading ? (
                  <span className="flex items-center justify-center">
                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Processing...
                  </span>
                ) : (
                  "Pay Now"
                )}
              </button>
            </form>

            <div className="mt-6 text-center text-sm text-gray-500">
              Your payment is secure and encrypted
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
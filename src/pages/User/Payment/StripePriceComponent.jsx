import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { useNavigate } from "react-router";
import { GlobalContext, showToast } from "Context/Global";
import { AuthContext, tokenExpireError } from "Context/Auth";
import {
  formatCreditCardNumber,
  formatCVC,
  formatExpirationDate,
} from "Utils/utils";
import MkdSDK from "Utils/MkdSDK";

let sdk = new MkdSDK();
const StripePaymentMethod = ({ planId=6, price=50, trial=2 }) => {
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const { dispatch } = React.useContext(AuthContext);
  // const [currentData, setCurrentTableData] = React.useState([]);
  const [loading, setLoading] = React.useState(false);
  const [state, setState] = React.useState({
    number: "",
    name: "",
    expiry: "",
    cvc: "",
    issuer: "",
    focused: "",
    formData: null,
  });
  let form = React.useRef(null);
  const navigate = useNavigate();
  const handleCallback = ({ issuer }, isValid) => {
    if (isValid) {
      setState({ issuer });
    }
  };

  const handleInputFocus = ({ target }) => {
    setState({
      focused: target.name,
    });
  };

  const handleInputChange = ({ target }) => {
    if (target.name === "number") {
      target.value = formatCreditCardNumber(target.value);
    } else if (target.name === "expiry") {
      target.value = formatExpirationDate(target.value);
    } else if (target.name === "cvc") {
      target.value = formatCVC(target.value);
    }

    setState({ [target.name]: target.value });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    // setLoading(true);
    const formData = [...e.target.elements]
      .filter((d) => d.name)
      .reduce((acc, d) => {
        acc[d.name] = d.value;
        return acc;
      }, {});
    setState({ formData });

    const toastMsg = trial
      ? `After a ${trial}-day trial, you will be billed $${price} monthly.`
      : `Your $${price} payment was successful. Starting next month, you will be billed $${price} monthly.`;

    setLoading(true);
    try {
      const res = await sdk.addStripeCard(formData);

      if (res.error || !res.id) {
        throw new Error(res.error.code || "Error adding stripe card");
      }
console.log("res",res)
      const cardAddRes = await sdk.callRawAPI(
        "/v2/api/lambda/stripe/customer/card",
        { sourceToken: res?.id },
        "POST"
      );

      if (cardAddRes.error)
        throw new Error(
          cardAddRes.message || "Error adding stripe card (lambda)"
        );

      // const defaultRes = await sdk.callRawAPI(
      //   `/v2/api/lambda/stripe/customer/card/${cardAddRes.model?.id}/set-default`,
      //   {},
      //   "PUT"
      // );

      const payment = await sdk.callRawAPI(
        `/v2/api/lambda/stripe/customer/subscription`,
        {
          planId: planId,
        },
        "POST"
      );
      if (payment.error)
        throw new Error(payment.message || "Error occurred while paying");

      // showToast(globalDispatch, "Payment successful");
      showToast(globalDispatch, toastMsg, 10000, "success");
      setLoading(false);
      navigate("/user/profile");
    } catch (error) {
      showToast(globalDispatch, error.message, 4000, "error");
      setLoading(false);
      tokenExpireError(dispatch, error.message);
    }
  };

  return (
    <>
      <div className="flex items-center justify-center ">
        <div className="relative mx-auto my-6 w-10/12 max-w-3xl">
          {/*content*/}
          <div className="relative flex w-full flex-col rounded-lg border-0 bg-white shadow-lg outline-none focus:outline-none">
            {/*header*/}
            <div className="flex items-start justify-between rounded-t border-b border-solid border-slate-200 p-5">
              <h3 className="text-3xl font-semibold">Payment</h3>
            </div>
            <form
              ref={(c) => (form = c)}
              onSubmit={handleSubmit}
              className="my-6 px-10"
            >
              <div className="form-group">
                <input
                  type="tel"
                  name="number"
                  className="focus:shadow-outline mb-4 w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none"
                  placeholder="Card Number"
                  pattern="[\d| ]{16,22}"
                  required
                  onChange={handleInputChange}
                  onFocus={handleInputFocus}
                />
                <small className="text-gray-400">(mm/yy)</small>
              </div>
              <div className="row">
                <div className="col-6">
                  <input
                    type="tel"
                    name="expiry"
                    className="focus:shadow-outline mb-4 w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none "
                    placeholder="Expiration"
                    pattern="\d\d/\d\d"
                    required
                    onChange={handleInputChange}
                    onFocus={handleInputFocus}
                  />
                </div>

                <div className="col-6">
                  <input
                    type="tel"
                    name="cvc"
                    className="focus:shadow-outline mb-4 w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none"
                    placeholder="CVC"
                    pattern="\d{3,4}"
                    required
                    onChange={handleInputChange}
                    onFocus={handleInputFocus}
                  />
                </div>
              </div>
              <input type="hidden" name="issuer" value={state.issuer} />
              {/* <div className="form-actions">
                <button className="btn btn-primary btn-block">PAY</button>
              </div> */}
              <div className="flex items-center rounded-b border-t border-solid border-slate-200 p-6">
                {/* <button
                  className="mb-1 mr-4 rounded bg-blue-500 px-6 py-3 text-sm font-bold uppercase text-white shadow outline-none transition-all  duration-150 ease-linear hover:shadow-lg focus:outline-none active:bg-emerald-600 "
                  type="button"
                  onClick={() => setShowModal(false)}
                >
                  Close
                </button> */}
                {loading ? (
                  <>
                    {" "}
                    <p className="mb-1 mr-1 inline-block rounded bg-blue-500 px-6 py-3 text-sm font-bold uppercase text-white shadow outline-none transition-all duration-150 ease-linear hover:shadow-lg focus:outline-none active:bg-emerald-600 disabled:opacity-60 ">
                      Loading...
                    </p>
                  </>
                ) : (
                  <button className="mb-1 mr-1 rounded bg-blue-500 px-6 py-3 text-sm font-bold uppercase text-white shadow outline-none transition-all duration-150 ease-linear hover:shadow-lg focus:outline-none active:bg-emerald-600 disabled:opacity-60">
                    Pay
                  </button>
                )}
              </div>
            </form>
          </div>
        </div>
      </div>
    </>
  );
};

export default StripePaymentMethod;
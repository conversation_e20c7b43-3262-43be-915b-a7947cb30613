import React,{useState} from "react";
import { AuthContext } from "Context/Auth";
import { GlobalContext } from "Context/Global";
import { BiSolidFileExport } from "react-icons/bi";
import { Document } from "Assets/images";

const GetCoaching = () => {
  const { dispatch } = React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const [selectedPlan, setSelectedPlan] = useState(1); // Default to middle plan

  const plans = [
    {
      title: '3-Hour Package',
      description: 'Perfect for quick guidance and specific goals',
      price: 299,
      features: [
        '3 hours of one-on-one coaching',
        'Personalized action plan',
        'Email support between sessions'
      ],
      popular: false
    },
    {
      title: '6-Hour Package',
      description: 'Ideal for comprehensive development',
      price: 549,
      features: [
        '6 hours of one-on-one coaching',
        'Detailed assessment and planning',
        'Priority email support',
        'Progress tracking tools'
      ],
      popular: true
    },
    {
      title: '10-Hour Package',
      description: 'Best value for long-term growth',
      price: 849,
      features: [
        '10 hours of one-on-one coaching',
        'Comprehensive development plan',
        '24/7 priority support',
        'Advanced assessment tools',
        'Monthly progress reports'
      ],
      popular: false
    }
  ];

  
  return (
    <>
      <div className=" md:pl-[3rem] w-full text-7xl min-h-screen h-full text-gray-700 bg-[#E2E6EB] p-[1rem]">
        <div className="w-[90%]">
      <h1 className="text-[20px] text-[#111928] font-semibold mb-6">Generate themes document</h1>

      <div className="flex justiy-center gap-6">
        {plans.map((plan, index) => (
          <div
            key={index}
            onClick={() => setSelectedPlan(index)}
            className={`relative bg-white rounded-lg p-6 w-80 cursor-pointer transition-all flex flex-col justify-between
              ${selectedPlan === index ? 'border-2 border-blue-600' : 'border border-gray-200'}`}
          >
            {plan.popular && (
              <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                <div className="relative">
                  <div className="text-sm text-white bg-blue-600 rounded-full px-4 py-1">
                    Most popular
                  </div>
                </div>
              </div>
            )}
            
            <div className="flex-grow">
              <h2 className="text-xl font-medium mb-2 mt-4">{plan.title}</h2>
              <p className="text-[#373A4B] text-[16px] mb-4">{plan.description}</p>
              
              <div className="mb-6">
                <span className="text-[24px] font-semibold">${plan.price}</span>
                <span className="text-gray-[#373A4B] text-[16px]"> / package</span>
              </div>
              
              <ul className="space-y-3 mb-8">
                {plan.features.map((feature, fIndex) => (
                  <li key={fIndex} className="flex items-start">
                    <svg 
                      viewBox="0 0 24 24"
                      className="w-5 h-5 text-blue-600 mr-2 mt-0.5 flex-shrink-0"
                    >
                      <path
                        fill="currentColor"
                        d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z"
                      />
                    </svg>
                    <span className="text-[#373A4B] text-[12px]">{feature}</span>
                  </li>
                ))}
              </ul>
            </div>
            
            <button 
              className="bg-[#054FB1] text-white py-3 rounded-md font-medium hover:bg-blue-700 transition-colors text-[16px] h-[50px] w-full mt-auto"
              onClick={(e) => {
                e.stopPropagation();
                // Handle purchase logic here
              }}
            >
              BUY NOW
            </button>
          </div>
        ))}
      </div>
      </div>
      </div>
      </>
  )
}
export default GetCoaching;
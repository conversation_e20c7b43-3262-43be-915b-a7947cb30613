import React from "react";
import { Link, NavLink, useLocation } from "react-router-dom";
import { PiUsersThreeFill } from "react-icons/pi";
import MkdSDK from "Utils/MkdSDK";
import { MdDashboardCustomize } from "react-icons/md";
import { IoDocumentText } from "react-icons/io5";
import { FaUser, FaCaretDown } from "react-icons/fa6";
import { RiGraduationCapFill } from "react-icons/ri";
import { GlobalContext } from "Context/Global";
import { AuthContext, tokenExpireError } from "Context/Auth";
import { Logo } from "Assets/images";
let sdk = new MkdSDK();

const NAV_ITEMS = [
  {
    to: "/user/dashboard",
    text: "Dashboard",
    icon: <MdDashboardCustomize className="text-[24px] text-[#373A4B]" />,
    value: "admin",
  },
  {
    to: "/user/onboarding",
    text: "Onboarding",
    icon: <FaUser className="text-[24px] text-[#373A4B]" />,
    value: "onboarding",
  },
  // {
  //   text: "Onboarding",
  //   icon: <FaUser className="text-[24px] text-[#373A4B]" />,
  //   value: "onboarding",
  //   hasSubMenu: true,
  //   subItems: [
  //     {
  //       to: "/user/onboarding",
  //       text: "Create My Answers",
  //       value: "create-answers",
  //     },
  //     {
  //       to: "/user/onboarding/quiz",
  //       text: "Test My Understanding",
  //       value: "test-understanding",
  //     },
  //   ],
  // },
  {
    to: "/user/document-library",
    text: "Documents library",
    icon: <IoDocumentText className="text-[24px] text-[#373A4B]" />,
    value: "documents",
  }, 
  {
    to: "/user/get-coaching",
    text: "Coaching",
    icon: <RiGraduationCapFill className="text-[24px] text-[#373A4B]" />,
    value: "coaching",
  },
];

export const UserHeader = () => {
  const {
    state: { isOpen, path },
    dispatch: gobalDispatch,
  } = React.useContext(GlobalContext);
  const { state: authState, dispatch } = React.useContext(AuthContext);
  const [openDropdown, setOpenDropdown] = React.useState(false);
  const [isHovering, setIsHovering] = React.useState(false);
  const [expandedItem, setExpandedItem] = React.useState(null);
  const location = useLocation();

  let toggleOpen = (open) => {
    gobalDispatch({
      type: "OPEN_SIDEBAR",
      payload: { isOpen: open },
    });
  };

  const toggleSubmenu = (value) => {
    setExpandedItem(expandedItem === value ? null : value);
  };

  React.useEffect(() => {
    async function fetchData() {
      try {
        const result = await sdk.getProfile();
        dispatch({
          type: "UPDATE_PROFILE",
          payload: result,
        });
      } catch (error) {
        console.log("Error", error);
        tokenExpireError(
          dispatch,
          error.response.data.message
            ? error.response.data.message
            : error.message
        );
      }
    }

    fetchData();
  }, []);

  // Add effect to handle window resize
  React.useEffect(() => {
    const handleResize = () => {
      gobalDispatch({
        type: "OPEN_SIDEBAR",
        payload: { isOpen: window.innerWidth >= 768 },
      });
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [gobalDispatch]);

  // sidebar-holder
  return (
    <>
      <div
        className={`z-50 flex max-h-screen flex-1 flex-col bg-white py-4 text-[#A8A8A8] transition-all ${
          isOpen
            ? "fixed lg:sticky lg:top-0 h-screen w-[318px] min-w-[318px] max-w-[318px] overflow-y-auto"
            : "hidden"
        }`}
      >
        <div
          className={`text-[#393939] ${
    isOpen ? "flex w-full" : "flex items-center justify-center"
  } `}
        >
          <div></div>
          {isOpen && (
            <div className="flex gap-2 w-full px-[2rem]">
              <Link to="/user/dashboard" className="flex gap-2">
                <img src={Logo} alt="Logo" className="h-[29px] w-[22px]" />
                <p className="text-[20px] font-semibold leading-[28px] text-[#020617]">Rescue Career Academy</p>
              </Link>
            </div>
          )}
        </div>

        <div className="h-fit w-auto flex-1">
          <div className="sidebar-list w-auto">
            <ul className="flex flex-wrap px-2 text-sm mt-[1rem]">
              {NAV_ITEMS.map((item) => (
                <li key={item.value} className="w-full">
                  {item.hasSubMenu ? (
                    <div className="block w-full list-none py-[0.75rem] ml-[0.6rem] px-[1rem]">
                      <div
                        className={`flex items-center justify-between cursor-pointer w-full ${
                          path === item.value ? "active-nav" : ""
                        }`}
                        onClick={() => toggleSubmenu(item.value)}
                      >
                        <div className="flex items-center gap-3">
                          {item.icon}
                          {isOpen && (
                            <span className="text-[16px] font-normal text-[#373A4B] leading-[24px]">
                              {item.text}
                            </span>
                          )}
                        </div>
                        {isOpen && (
                          <FaCaretDown
                            className={`text-[24px] text-[#373A4B] transition-transform ${
                              expandedItem === item.value ? "rotate-180" : ""
                            }`}
                          />
                        )}
                      </div>
                      {isOpen && expandedItem === item.value && (
                        <ul className="ml-8 mt-2">
                          {item.subItems.map((subItem) => (
                            <li key={subItem.value}>
                              <NavLink
                                to={subItem.to}
                                className={`block py-2 text-[14px] text-[#373A4B] hover:text-[#000] ${
                                  path === subItem.value ? "font-medium" : ""
                                }`}
                              >
                               <p className="text-[16px] font-normal text-[#373A4B] leading-[24px]"> {subItem.text}</p>
                              </NavLink>
                            </li>
                          ))}
                        </ul>
                      )}
                    </div>
                  ) : (
                    <div 
                      className={`block w-full list-none py-[0.75rem] px-[1rem] ${
                        location.pathname === item.to ? 'bg-[#054FB1]' : ''
                      } hover:bg-[#054FB1] group transition-all duration-300 ease-in-out`}
                    >
                      <NavLink 
                        to={item.to} 
                        style={{ background: 'transparent' }}
                        className="block w-full"
                      >
                        <div className="flex items-center gap-3 bg-transparent">
                          {React.cloneElement(item.icon, {
                            className: `text-[24px] ${
                              location.pathname === item.to ? 'text-white' : 'text-[#373A4B]'
                            } group-hover:text-white transition-colors duration-300 ease-in-out`
                          })}
                          {isOpen && (
                            <span className={`text-[16px] font-normal leading-[24px] ${
                              location.pathname === item.to ? 'text-white' : 'text-[#373A4B]'
                            } group-hover:text-white transition-colors duration-300 ease-in-out`}>
                              {item.text}
                            </span>
                          )}
                        </div>
                      </NavLink>
                    </div>
                  )}
                </li>
              ))}
            </ul>
          </div>
        </div>
        <div className="flex justify-end">
          <div className="mr-3 cursor-pointer rounded-lg border border-[#E0E0E0] bg-white p-2 text-2xl text-gray-400">
            <span onClick={() => toggleOpen(!isOpen)}>
              <svg
                className={`transition-transform ${
    !isOpen ? "rotate-180" : ""
  }`}
                xmlns="http:www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
              >
                <path
                  fillRule="evenodd"
                  clipRule="evenodd"
                  d="M2 12C2 17.5228 6.47715 22 12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12ZM10.4142 11L11.7071 9.70711C12.0976 9.31658 12.0976 8.68342 11.7071 8.29289C11.3166 7.90237 10.6834 7.90237 10.2929 8.29289L7.82322 10.7626C7.13981 11.446 7.13981 12.554 7.82322 13.2374L10.2929 15.7071C10.6834 16.0976 11.3166 16.0976 11.7071 15.7071C12.0976 15.3166 12.0976 14.6834 11.7071 14.2929L10.4142 13H16C16.5523 13 17 12.5523 17 12C17 11.4477 16.5523 11 16 11H10.4142Z"
                  fill="#A8A8A8"
                />
              </svg>
            </span>
          </div>
        </div>
      </div>
      
      {/* Add new toggle button that shows only when sidebar is closed on large screens */}
      {!isOpen && (
        <div className="hidden lg:block fixed bottom-4 left-4">
          <div className="cursor-pointer rounded-lg border border-[#E0E0E0] bg-white p-2 text-2xl text-gray-400">
            <span onClick={() => toggleOpen(true)}>
              <svg
                className="rotate-180"
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
              >
                <path
                  fillRule="evenodd"
                  clipRule="evenodd"
                  d="M2 12C2 17.5228 6.47715 22 12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12ZM10.4142 11L11.7071 9.70711C12.0976 9.31658 12.0976 8.68342 11.7071 8.29289C11.3166 7.90237 10.6834 7.90237 10.2929 8.29289L7.82322 10.7626C7.13981 11.446 7.13981 12.554 7.82322 13.2374L10.2929 15.7071C10.6834 16.0976 11.3166 16.0976 11.7071 15.7071C12.0976 15.3166 12.0976 14.6834 11.7071 14.2929L10.4142 13H16C16.5523 13 17 12.5523 17 12C17 11.4477 16.5523 11 16 11H10.4142Z"
                  fill="#A8A8A8"
                />
              </svg>
            </span>
          </div>
        </div>
      )}

      {/* Overlay backdrop */}
      {isOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={() => gobalDispatch({
            type: "OPEN_SIDEBAR",
            payload: { isOpen: false },
          })}
        />
      )}
    </>
  );
};

export default UserHeader;


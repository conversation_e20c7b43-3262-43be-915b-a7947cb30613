import React, {useState, useEffect} from "react";
import { Link, NavLink } from "react-router-dom";
import { PiUsersThreeFill } from "react-icons/pi";
import MkdSDK from "Utils/MkdSDK";
import { MdDashboardCustomize } from "react-icons/md";
import { FaUser } from "react-icons/fa6";
import { RiGraduationCapFill } from "react-icons/ri";
import { GlobalContext } from "Context/Global";
import { AuthContext, tokenExpireError } from "Context/Auth";
import { Logo } from "Assets/images";
let sdk = new MkdSDK();

const NAV_ITEMS = [
  {
    to: "/user/dashboard",
    text: "Dashboard",
    icon: <MdDashboardCustomize className="text-[24px] text-[#373A4B]" />,
    value: "admin",
  },

  

  {
    to: "/user/onboarding",
    text: "Onboardingfsdf",
    icon: <FaUser className="text-[24px] text-[#373A4B]" />,
    value: "onboarding",
  }, 
  {
    to: "/user/documents",
    text: "Documentsgsadf",
    icon: <FaUser className="text-[24px] text-[#373A4B]" />,
    value: "documents",
  }, 
  {
    to: "/user/coaching",
    text: "Coaching",
    icon: <RiGraduationCapFill className="text-[24px] text-[#373A4B]" />,
    value: "coaching",
  },
];

export const Sidebar = () => {
  const {
    state: { isOpen },
    dispatch: gobalDispatch,
  } = React.useContext(GlobalContext);
  const { state: authState, dispatch } = React.useContext(AuthContext);
  const userId = authState?.user;
  const [openDropdown, setOpenDropdown] = React.useState(false);
  const [isHovering, setIsHovering] = React.useState(false);
  const [themes, setThemes] = useState([]);
  const [selectedTheme, setSelectedTheme] = useState(null);
  const [completedThemes, setCompletedThemes] = useState(() => {
    const stored = localStorage.getItem(`completedThemes_${userId}`);
    return stored ? JSON.parse(stored) : [];
  });
  const [, forceUpdate] = useState({});

  // Listen for theme completion updates
  useEffect(() => {
    const handleStorageChange = () => {
      const stored = localStorage.getItem(`completedThemes_${userId}`);
      if (stored) {
        setCompletedThemes(JSON.parse(stored));
        forceUpdate({}); // Force a re-render
      }
    };

    // Create a custom event listener
    const handleThemeComplete = () => {
      const stored = localStorage.getItem(`completedThemes_${userId}`);
      if (stored) {
        setCompletedThemes(JSON.parse(stored));
        forceUpdate({}); // Force a re-render
      }
    };

    // Listen for both storage and custom events
    window.addEventListener('storage', handleStorageChange);
    document.addEventListener('themeCompleted', handleThemeComplete);

    // Initial load
    handleStorageChange();

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      document.removeEventListener('themeCompleted', handleThemeComplete);
    };
  }, [userId]);

  // Fetch themes and store in localStorage
  React.useEffect(() => {
    const fetchThemes = async () => {
      try {
        sdk.setTable("themes");
        const response = await sdk.callRestAPI({}, "GETALL");
        if (response.list) {
          setThemes(response.list);
          // Store themes in localStorage
          localStorage.setItem(`allThemes_${userId}`, JSON.stringify(response.list));
        }
      } catch (error) {
        console.error('Error fetching themes:', error);
        tokenExpireError(dispatch, error.message);
      }
    };

    fetchThemes();
  }, [userId]);

  const handleThemeSelect = (theme) => {
    setSelectedTheme(theme);
    localStorage.setItem(`selectedTheme_${userId}`, JSON.stringify(theme));
    window.dispatchEvent(new Event('themesUpdated'));
  };

  // const handleMouseOver = () => {
  //   setIsHovering(true);
  // };

  // const handleMouseOut = () => {
  //   setIsHovering(false);
  // };
  let toggleOpen = (open) => {
    gobalDispatch({
      type: "OPEN_SIDEBAR",
      payload: { isOpen: open },
    });
  };

  React.useEffect(() => {
    async function fetchData() {
      try {
        const result = await sdk.getProfile();
        dispatch({
          type: "UPDATE_PROFILE",
          payload: result,
        });
      } catch (error) {
        console.log("Error", error);
        tokenExpireError(
          dispatch,
          error.response.data.message
            ? error.response.data.message
            : error.message
        );
      }
    }

    fetchData();
  }, []);
  // sidebar-holder
  return (
    <>
      {!isOpen && (
        <div className="z-50 hidden lg:flex h-screen py-4 fixed">
          <div className="px-4">
            <div className="flex items-center gap-6 text-white">
              <button 
                onClick={() => toggleOpen(true)}
                className="bg-[#054FB1] w-[20px] h-[20px] hover:bg-[#0441A3]"
              >
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M7.5 5L12.5 10L7.5 15" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </button>
            </div>
          </div>
        </div>
      )}

      {isOpen && (
        <>
          <div className="z-50 flex flex-col bg-white py-4 text-[#A8A8A8] fixed h-screen w-[318px] min-w-[318px] max-w-[318px] overflow-y-auto [&::-webkit-scrollbar]:w-[2px] [&::-webkit-scrollbar-thumb]:bg-gray-100 hover:[&::-webkit-scrollbar-thumb]:bg-gray-200 [&::-webkit-scrollbar-track]:bg-transparent">
            <div className="text-[#393939] flex w-full">
              <div className="flex gap-2 w-full px-[2rem]">
                <Link to="/user/dashboard" className="flex items-center gap-2">
                  <img src={Logo} alt="Logo" className="h-[29px] w-[22px]" />
                  <p className="text-[20px] font-semibold leading-[28px] text-[#020617]">Rescue Career Academy</p>
                </Link>
              </div>
            </div>

            <div className="px-4 py-2 border-b border-gray-100">
              <div className="flex items-center gap-6 text-white">
                <button 
                  onClick={() => toggleOpen(false)}
                  className="bg-[#054FB1] w-[20px] h-[20px] mt-[1rem] hover:bg-[#0441A3]"
                >
                  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12.5 5L7.5 10L12.5 15" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </button>
                <span className="font-medium text-[16px] mt-[1rem] text-[#373A4B]">Create my answers</span>
              </div>
            </div>

            {/* Themes List */}
            <div className="px-4 py-2">
              {themes.map((theme) => (
                <div 
                  key={theme.id}
                  className="flex items-center justify-between py-2 cursor-pointer"
                  onClick={() => handleThemeSelect(theme)}
                >
                  <span className="text-[#373A4B] text-base">{theme.name}</span>
                  <div className={`w-5 h-5 rounded-full flex items-center justify-center transition-colors
                    ${completedThemes.includes(theme.id) 
                      ? 'bg-green-500' 
                      : selectedTheme?.id === theme.id 
                        ? 'bg-[#054FB1]' 
                        : 'bg-gray-200'
                    }`}
                  >
                    {(completedThemes.includes(theme.id) || selectedTheme?.id === theme.id) && (
                      <svg 
                        width="12" 
                        height="12" 
                        viewBox="0 0 12 12" 
                        fill="none" 
                        xmlns="http://www.w3.org/2000/svg"
                        className="text-white"
                      >
                        <path 
                          d="M10 3L4.5 8.5L2 6" 
                          stroke="currentColor" 
                          strokeWidth="2" 
                          strokeLinecap="round" 
                          strokeLinejoin="round"
                        />
                      </svg>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Overlay backdrop for medium and small screens */}
          <div 
            className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
            onClick={() => gobalDispatch({
              type: "OPEN_SIDEBAR",
              payload: { isOpen: false },
            })}
          />
        </>
      )}
    </>
  );
};

export default Sidebar;


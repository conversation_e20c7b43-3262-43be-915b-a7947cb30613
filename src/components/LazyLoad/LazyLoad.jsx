
    import React, { memo, Suspense } from "react";
    import { SkeletonLoader } from "Components/Skeleton";

    const LazyLoad = ({
        children,
        counts = [1],
        count = 1,
        circle = false,
        }) => {
        const childrenArray = React.Children.toArray(children).filter(Boolean);
        const className = childrenArray.filter(Boolean)[0]?.props?.className
            ? childrenArray[0]?.props?.className
            : "";

        return (
            <Suspense
            fallback={
                <SkeletonLoader
                counts={counts}
                count={count}
                className={className}
                circle={circle}
                />
            }
            >
            {children}
            </Suspense>
        );
    };

    export default memo(LazyLoad);
  
    
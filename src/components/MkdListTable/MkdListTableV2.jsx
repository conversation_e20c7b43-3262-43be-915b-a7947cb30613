
 
import React from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import MkdSDK from "Utils/MkdSDK";
import { capitalize, getNonNullValue } from "Utils/utils";
import { PaginationBar } from "Components/PaginationBar";
import { AuthContext, tokenExpireError } from "Context/Auth";
import { GlobalContext } from "Context/Global";
import { TableActions } from "Components/MkdListTable";
import { SkeletonLoader } from "Components/Skeleton";
import { BiFilterAlt, BiSearch } from "react-icons/bi";
import { AiOutlineClose, AiOutlinePlus } from "react-icons/ai";
import { RiDeleteBin5Line } from "react-icons/ri";
import { AddButton } from "Components/AddButton";
import { ExportButton } from "Components/ExportButton";
import { LazyLoad } from "Components/LazyLoad";

import MkdListTableHead from "./MkdListTableHead";
import MkdListTableRow from "./MkdListTableRow";
import { ModalPrompt } from "Components/Modal";
import { Spinner } from "Assets/svgs";
import { colors } from "Utils/config";

let sdk = new MkdSDK();

const MkdListTableV2 = ({
  columns = [],
  actions = {
    view: { show: true, multiple: true, action: null },
    edit: { show: true, multiple: true, action: null },
    delete: { show: true, multiple: true, action: null },
    select: { show: true, multiple: true, action: null },
    add: {
      show: true,
      multiple: true,
      action: null,
      showChildren: true,
      children: "Add New",
    },
    export: { show: true, multiple: true, action: null },
  },
  actionPostion = "ontable",
  actionId = "id",

  table ,
  tableRole,
  tableTitle = "",

  hasFilter = true,
  schemaFields = [],
  showPagination = true,

  refreshRef = null,
}) => {
  const { dispatch } = React.useContext(AuthContext);

  const [currentTableData, setCurrentTableData] = React.useState([]);
  const [pageSize, setPageSize] = React.useState(10);
  const [pageCount, setPageCount] = React.useState(0);

  const [currentPage, setPage] = React.useState(0);
  const [canPreviousPage, setCanPreviousPage] = React.useState(false);
  const [canNextPage, setCanNextPage] = React.useState(false);
  const [showDeleteModal, setShowDeleteModal] = React.useState(false);
  const [deleteLoading, setDeleteLoading] = React.useState(false);
  const [openFilter, setOpenFilter] = React.useState(false);
  const [showFilterOptions, setShowFilterOptions] = React.useState(false);
  const [selectedOptions, setSelectedOptions] = React.useState([]);
  const [filterConditions, setFilterConditions] = React.useState([]);
  const [selectedItems, setSelectedItems] = React.useState([]);
  const [optionValue, setOptionValue] = React.useState("eq");

  const [loading, setLoading] = React.useState(true);
  const dropdownFilterRef = React.useRef(null);
  const schema = yup.object({});
  const [deleteId, setIdToDelete] = React.useState(null);

  const [areAllRowsSelected, setAreAllRowsSelected] = React.useState(false);
  const [selectedIds, setSelectedIds] = React.useState([]);
  const { state, dispatch: globalDispatch } = React.useContext(GlobalContext);
  function handleSelectRow(id) {
 
    const tempIds = selectedIds;

    if (actions?.select?.multiple) {
      if (tempIds.includes(id)) {
        const newIds = tempIds.filter((selectedId) => selectedId !== id);
        setSelectedIds(() => [...newIds]);
        setSelectedItems(newIds);
      } else {
        const newIds = [...tempIds, id];
        setSelectedIds(() => [...newIds]);
        setSelectedItems(newIds);
      }
    } else {
      if (tempIds.includes(id)) {
        const newIds = tempIds.filter((selectedId) => selectedId !== id);
        setSelectedIds(() => [...newIds]);
        setSelectedItems(newIds);
      } else {
        const newIds = [id];
        setSelectedIds(() => [...newIds]);
        setSelectedItems(newIds);
      }
    }
  }

  const handleSelectAll = () => {
    setAreAllRowsSelected((prevSelectAll) => !prevSelectAll);
    if (!areAllRowsSelected) {
      const allIds = currentTableData.map((item) => item[actionId]);
      setSelectedIds(allIds);
      setSelectedItems(allIds);
    } else {
      setSelectedIds([]);
      setSelectedItems([]);
    }
  };

  const setDeleteId = async (id) => {
    setShowDeleteModal(true);
    setIdToDelete(id);
  };

  React.useEffect(() => {
    if (selectedIds.length <= 0) {
 
      setAreAllRowsSelected(false);
    }
    if (selectedIds.length === currentTableData.length) {
      setAreAllRowsSelected(true);
 
    }
    if (
      selectedIds.length < currentTableData.length &&
      selectedIds.length > 0
    ) {
      setAreAllRowsSelected(false);
    }
  }, [selectedIds, currentTableData]);
  const {
    handleSubmit,

    reset,
  } = useForm({
    resolver: yupResolver(schema),
  });

  function onSort(columnIndex) {
    if (columns[columnIndex].isSorted) {
      columns[columnIndex].isSortedDesc = !columns[columnIndex].isSortedDesc;
    } else {
      columns.map((i) => (i.isSorted = false));
      columns.map((i) => (i.isSortedDesc = false));
      columns[columnIndex].isSorted = true;
    }

    (async function () {
      await getData(0, pageSize);
    })();
  }

  function previousPage() {
    getData(currentPage - 1, pageSize);
  }

  function nextPage() {
    getData(currentPage + 1, pageSize);
  }
  const addFilterCondition = (option, selectedValue, inputValue) => {
    const input =
  selectedValue === "eq" && isNaN(inputValue)
    ? `${inputValue}`
    : inputValue;
const condition = `${option},${selectedValue},${input}` 
    setFilterConditions((prevConditions) => {
      const newConditions = prevConditions.filter(
        (condition) => !condition.includes(option)
      );
      return [...newConditions, condition];
    });
  };

  const handleFilter = () => {
    getData(0, pageSize, {}, filterConditions);
  };
  const deleteFilter = (deleted) => {
    getData(0, pageSize, {}, deleted);
  }

  async function getData(pageNum, limitNum, data = {}, filters = []) {
    setLoading(true);
    try {
      sdk.setTable(table);

      const result = await sdk.callRestAPI(
        {
          payload: {
            ...data,
          },
          page: pageNum,
          limit: limitNum,
          filter: filters,
        },
        "PAGINATE"
      );

      if (result) {
        setLoading(false);
      }
      const { list, total, limit, num_pages, page } = result;
 
      setCurrentTableData(list);
      setPageSize(limit);
      setPageCount(num_pages);
      setPage(page);
 
      setCanPreviousPage(page > 1);
      setCanNextPage(page + 1 <= num_pages);
    } catch (error) {
      setLoading(false);
      console.log("ERROR", error);
      tokenExpireError(dispatch, error.message);
    }
  }
  async function refreshData(pageNum, limitNum, data = {}, filters = []) {
  
   
      sdk.setTable(table);

      const result = await sdk.callRestAPI(
        {
          payload: {
            ...data,
          },
          page: pageNum,
          limit: limitNum,
          filter: filters,
        },
        "PAGINATE"
      );

      
      const { list  } = result;

      setCurrentTableData(list);
 
      globalDispatch({
        type: "REFRESH_DATA",
        payload: {
          refreshData: false,
        },
      });
    
  }
  const deleteItem = async (id) => {
    async function deleteId(id) {
      try {
        setDeleteLoading(true);
        sdk.setTable(table);
        const result = await sdk.callRestAPI({ id }, "DELETE");
        if (!result?.error) {
          setCurrentTableData((list) =>
            list.filter((x) => Number(x.id) !== Number(id))
          );
          setDeleteLoading(false);
          setShowDeleteModal(false);
        }
      } catch (err) {
        setDeleteLoading(false);
        setShowDeleteModal(false);
        tokenExpireError(dispatch, err?.message);
        throw new Error(err);
      }
    }

    if (typeof id === "object") {
      id.forEach(async (idToDelete) => {
        await deleteId(idToDelete);
      });
    } else if (typeof id === "number") {
      await deleteId(id);
    }
  };

  const exportTable = async (id) => {
    try {
      sdk.setTable(table);
      const result = await sdk.exportCSV();
    } catch (err) {
      throw new Error(err);
    }
  };

  const onSubmit = (data) => {
    const filters = columns
      .filter((col) => col.accessor)
      .map((col) => {
        const value = getNonNullValue(data[col.accessor]);
        return value ? `${col.accessor},cs,${value}` : null;
      })
      .filter(Boolean);
    getData(0, pageSize, {}, filters);
  };

  async function updateTableData(id, key, updatedData) {
    try {
      sdk.setTable(table);
      const result = await sdk.callRestAPI(
        {
          id,
          [key]: updatedData,
        },
        "PUT"
      );
    } catch (error) {
      console.log("ERROR", error);
      tokenExpireError(dispatch, error.message);
    }
  }
   
  const clearAllFilters = () => {
    setSelectedOptions([]);
    setFilterConditions([]);
    getData(1, pageSize);
  };

  async function handleTableCellChange(id, newValue, index, newValueKey) {
    let runApiCall;
    newValue = isNaN(Number.parseInt(newValue))
      ? newValue
      : Number.parseInt(newValue);
    try {
      clearTimeout(runApiCall);
      runApiCall = setTimeout(async () => {
        await updateTableData(id, newValueKey, newValue);
      }, 200);
      setCurrentTableData((prevData) => {
        const updatedData = prevData.map((item, i) => {
          if (i === index) {
            return {
              ...item,
              [newValueKey]: newValue,
            };
          }
          return item;
        });
        return updatedData;
      });
    } catch (error) {
      console.error(error);
    }
  }

  React.useEffect(() => {
    if (actions?.select?.action) {
      actions.select.action();
    }
  }, [selectedItems.length]);

  React.useEffect(() => {
    const delay = 700;
    const timeoutId = setTimeout(async () => {
      await getData(1, pageSize);
    }, delay);

    return () => {
      clearTimeout(timeoutId);
    };
  }, []);

  React.useEffect(() => {
 
      refreshData(1, pageSize);
 
  }, [state.refreshData]);

  const handleClickOutside = (event) => {
    if (
      dropdownFilterRef.current &&
      !dropdownFilterRef.current.contains(event.target)
    ) {
      setOpenFilter(false);
    }
  };

  React.useEffect(() => {
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <div className="px-8">
      {refreshRef && (
        <button
          ref={refreshRef}
          onClick={() => getData(1, pageSize)}
          className="hidden"
        ></button>
      )}
      <div
        className={`flex gap-3 ${
          tableTitle ? "flex-col" : "h-fit items-center"
        }`}
      >
        {hasFilter ? (
          <div className="flex w-auto items-center justify-between ">
           <form
            className="relative rounded bg-white"
            onSubmit={handleSubmit(onSubmit)}
          >
            <div className="flex items-center gap-4 text-gray-700 text-nowrap" >
              <div className="relative" ref={dropdownFilterRef}>
                <div
                  className="flex cursor-pointer items-center justify-between gap-3 rounded-md border border-gray-200 px-3 py-1"
                  onClick={() => setOpenFilter(!openFilter)}
                >
                  <BiFilterAlt />
                  <span>Filters</span>
                  {selectedOptions.length > 0 && (
                    <span className="flex h-6 w-6 items-center justify-center rounded-full bg-gray-800 text-start text-white">
                      {selectedOptions.length}
                    </span>
                  )}
                </div>
                {openFilter && (
                  <div className="absolute z-10 mt-4 w-[500px] min-w-[90%] top-fill left-0 filter-form-holder bg-white border border-gray-200 rounded-md shadow-lg">
                    <div className="p-4">
                      {selectedOptions?.map((option, index) => (
                        <div key={index} className="mb-2 flex w-full items-center justify-between gap-3 text-gray-600">
                          <div className=" mb-3 w-1/3 rounded-md border border-gray-300 px-3 py-2 leading-tight text-gray-700 outline-none">
                            {option}
                          </div>
                          <select
                            className="mb-3 w-[40%] rounded-md border px-3 py-2 leading-tight text-gray-700 outline-none"
                            onChange={(e) => {
                              setOptionValue(e.target.value);
                            }}
                          >
                            <option value="eq" selected>equals</option>
                            <option value="cs">contains</option>
                            <option value="sw">start with</option>
                            <option value="ew">ends with</option>
                            <option value="lt">lower than</option>
                            <option value="le">lower or equal</option>
                            <option value="ge">greater or equal</option>
                            <option value="gt">greater than</option>
                            <option value="bt">between</option>
                            <option value="in">in</option>
                            <option value="is">is null</option>
                          </select>

                          <input
                            type="text"
                            placeholder="Enter value..."
                            className=" mb-3 w-1/3 rounded-md border px-3 py-2 leading-tight text-gray-700 outline-none"
                            onChange={(e) =>
                              addFilterCondition(
                                option,
                                optionValue,
                                e.target.value
                              )
                            }
                            onKeyDown={(e) => {
                              if (e.key === 'Enter') {
                                e.preventDefault();
                              }
                            }}
                          />

                          <div className="w-1/12 mt-[-10px]">
                            <RiDeleteBin5Line
                              className=" cursor-pointer text-xl"
                              onClick={() => {
                                setSelectedOptions((prevOptions) =>
                                  prevOptions.filter((op) => op !== option)
                                );
                                setFilterConditions((prevConditions) => {
                                  const newConditions = prevConditions.filter(
                                    (condition) => !condition.includes(option)
                                  );
                     
                                  deleteFilter(newConditions); // Re-apply filters after deletion
                                  return newConditions;
                                });
                              }}
                            />
                          </div>
                        </div>
                      ))}

                      <div className="search-buttons relative flex items-center justify-between font-semibold">
                        <div
                          className="mr-2 flex w-auto cursor-pointer items-center gap-2 rounded bg-gray-100 px-4 py-2.5 font-medium leading-tight text-gray-600 transition duration-150 ease-in-out "
                          onClick={() => {
                            setShowFilterOptions(!showFilterOptions);
                          }}
                        >
                          <AiOutlinePlus />
                          Add filter
                        </div>

                        {showFilterOptions && (
                          <div className="absolute top-11 z-10 bg-white px-5 py-3 text-gray-600 shadow-md">
                            <ul className="flex flex-col gap-2 text-gray-500">
                              {columns.slice(0, -1).map((column) => (
                                <li
                                  key={column.accessor}
                                  className={`${selectedOptions.includes(column.accessor)
                                    ? "cursor-not-allowed text-gray-400"
                                    : "cursor-pointer"
                                    }`}
                                  onClick={() => {
                                    if (!selectedOptions.includes(column.accessor)) {
                                      setSelectedOptions((prev) => [
                                        ...prev,
                                        column.accessor,
                                      ]);
                                    }
                                    setShowFilterOptions(false);
                                  }}
                                >
                                  {column.header}
                                </li>
                              ))}
                            </ul>
                          </div>
                        )}
                        {selectedOptions.length > 0 && (
                          <div
                            onClick={clearAllFilters}
                            className="inline-block cursor-pointer  rounded px-6  py-2.5 font-medium leading-tight text-gray-600  transition duration-150 ease-in-out"
                          >
                            Clear all filter
                          </div>
                        )}
                      </div>
                      <button
                        type="button"
                        onClick={handleFilter}
                        className="mt-4 inline-block cursor-pointer rounded bg-blue-500 px-6 py-2.5 font-medium leading-tight text-white transition duration-150 ease-in-out"
                      >
                        Apply Filters
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </form>
            {/* <AddButton link={"/admin/add-wireframe"} /> */}
          </div>
        ) : null}

        <div className="flex h-fit w-full justify-between text-center">
          <h4 className="text-2xl font-medium capitalize">
            {tableTitle ? tableTitle : ""}
          </h4>
          <div className="flex h-full gap-2">
            {selectedItems?.length && actionPostion === "abovetable" ? (
              <LazyLoad>
                <TableActions actions={actions} selectedItems={selectedItems} />
              </LazyLoad>
            ) : null}

            {actions?.export?.show && (
              <ExportButton
                showText={false}
                onClick={exportTable}
                className="mx-1"
              />
            )}

            {actions?.add?.show && (
              <AddButton
                onClick={() => {
                  if (actions?.add?.action) {
                    actions?.add?.action();
                  }
                }}
                showChildren={actions?.add?.showChildren}
              >
                {actions?.add?.children}
              </AddButton>
            )}
          </div>
        </div>
      </div>
      <div className="overflow-x-auto  rounded bg-white p-5 px-0">
        <>
          <div
            className={`${
              loading ? "" : "overflow-x-auto"
            } border-b border-gray-200 shadow`}
          >
            {loading ? (
              <div
                className={`flex max-h-fit min-h-fit min-w-fit max-w-full items-center justify-center  py-5`}
              >
               <SkeletonLoader />
              </div>
            ) : (
              <>
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <LazyLoad>
                      <MkdListTableHead
                        onSort={onSort}
                        columns={columns}
                        actions={actions}
                        actionPostion={actionPostion}
                        areAllRowsSelected={areAllRowsSelected}
                        handleSelectAll={handleSelectAll}
                      />
                    </LazyLoad>
                  </thead>
                  <tbody className="divide-y divide-gray-200 bg-white">
                    {currentTableData.map((row, i) => {
                      return (
                        <LazyLoad key={i}>
                          <MkdListTableRow
                            key={i}
                            i={i}
                            row={row}
                            columns={columns}
                            actions={actions}
                            actionPostion={actionPostion}
                            actionId={actionId}
                            handleTableCellChange={handleTableCellChange}
                            handleSelectRow={handleSelectRow}
                            selectedIds={selectedIds}
                            setDeleteId={setDeleteId}
                            table={table}
                            tableRole={tableRole}
                          />
                        </LazyLoad>
                      );
                    })}
                  </tbody>
                </table>
              </>
            )}
          </div>

          <LazyLoad>
            <ModalPrompt
              open={showDeleteModal}
              actionHandler={() => {
                deleteItem(deleteId);
              }}
              closeModalFunction={() => {
                setIdToDelete(null);
                setShowDeleteModal(false);
              }}
              title={`Delete ${capitalize(table)} `}
              message={`You are about to delete ${capitalize(
                table
              )} ${deleteId}, note that this action is irreversible`}
              acceptText={`DELETE`}
              rejectText={`CANCEL`}
              loading={deleteLoading}
            />
          </LazyLoad>
        </>
      </div>
      { (
         <PaginationBar
         currentPage={currentPage}
         pageCount={pageCount}
         pageSize={pageSize}
         canPreviousPage={canPreviousPage}
         canNextPage={canNextPage}
         updatePageSize={(newPageSize) => {
           setPageSize(newPageSize);
           getData(1, newPageSize);
         }}
         previousPage={previousPage}
         nextPage={nextPage}
       />
      )}
    </div>
  );
};

export default MkdListTableV2;

  
import React, { Suspense, memo } from "react";

import {AdminHeader} from "Components/AdminHeader";
import {TopHeader} from "Components/TopHeader";
import { Spinner } from "Assets/svgs";
const  navigation = []
const AdminWrapper = ({ children }) => {
  return (
    <div id="admin_wrapper" className={`flex w-full max-w-full flex-col bg-white`}>
      <div className={`flex min-h-screen w-full max-w-full `}>
        <AdminHeader
        
        />
        <div className={`mb-20 w-full overflow-hidden`}>
        <TopHeader />
          <Suspense
            fallback={
              <div
                className={`flex h-screen w-full items-center justify-center`}
              >
              {/* <Spinner size={40} color="#4F46E5" /> */}
              </div>
            }
          >
            <div className="w-full overflow-y-auto overflow-x-hidden pt-[calc(3.5rem+2rem)]">
              {children}
            </div>
          </Suspense>
        </div>
      </div>
    </div>
  );
};

export default memo(AdminWrapper);


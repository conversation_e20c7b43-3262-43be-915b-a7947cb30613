import React, { Suspense, memo } from "react";
import { useContext } from "react";
import {UserHeader} from "Components/UserHeader";
import {Sidebar} from "Components/Sidebar";
import {TopHeader} from "Components/TopHeader";
import { GlobalContext } from "Context/Global";
import { Spinner } from "Assets/svgs";

const navigation = []
const UserWrapper2 = ({ children }) => {
  const { state: { isOpen } } = useContext(GlobalContext);
  
  return (
    <div id="user_wrapper" className="flex w-full max-w-full bg-white">
      <Sidebar />
      <div className={`flex-1 w-full overflow-y-auto transition-all duration-300 ${
        isOpen ? 'pl-[318px]' : 'pl-0 sm:pl-[20px]'
      }`}>
        <TopHeader />
        <Suspense
          fallback={
            <div className="flex h-screen w-full items-center justify-center">
              {/* <Spinner size={40} color="#4F46E5" /> */}
            </div>
          }
        >
          <div className="w-full pt-[calc(3.5rem+2rem)]">
            {children}
          </div>
        </Suspense>
      </div>
    </div>
  );
};

export default memo(UserWrapper2);


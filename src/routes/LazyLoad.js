
    import {lazy} from 'react'

    export const CardDetailsPage = lazy(() => {
                const __import = import("../pages/User/Payment/CardDetails");
                __import.finally(() => {});
                return __import;
              });
      
     export const Onboarding = lazy(() => {
                const __import = import("../pages/User/Onboarding/Onboarding");
                __import.finally(() => {});
                return __import;
              });

     export const Form = lazy(() => {
                const __import = import("../pages/User/Onboarding/Form");
                __import.finally(() => {});
                return __import;
              });

      export const Theme = lazy(() => {
                const __import = import("../pages/User/Onboarding/Theme");
                __import.finally(() => {});
                return __import;
              });

       export const ThemeContd = lazy(() => {
                const __import = import("../pages/User/Onboarding/ThemeContd");
                __import.finally(() => {});
                return __import;
              });

        export const ThemePreview = lazy(() => {
                const __import = import("../pages/User/Onboarding/ThemePreview");
                __import.finally(() => {});
                return __import;
              });
              export const FinalPreview = lazy(() => {
                const __import = import("../pages/User/Onboarding/FinalPreview");
                __import.finally(() => {});
                return __import;
              });
      
              export const GenerateQuiz = lazy(() => {
                const __import = import("../pages/User/Onboarding/GenerateQuiz");
                __import.finally(() => {});
                return __import;
              });
              export const Quiz = lazy(() => {
                const __import = import("../pages/User/Onboarding/Quiz");
                __import.finally(() => {});
                return __import;
              });
              export const AllQuizzes = lazy(() => {
                const __import = import("../pages/User/Onboarding/AllQuizes");
                __import.finally(() => {});
                return __import;
              });
              export const SingleQuiz = lazy(() => {
                const __import = import("../pages/User/Onboarding/SingleQuiz");
                __import.finally(() => {});
                return __import;
              });
              export const DocumentLibrary = lazy(() => {
                const __import = import("../pages/User/DocumentLibrary/DocumentLibrary");
                __import.finally(() => {});
                return __import;
              });
              export const GetCoaching = lazy(() => {
                const __import = import("../pages/User/GetCoaching/GetCoaching");
                __import.finally(() => {});
                return __import;
              });
              export const Prev = lazy(() => {
                const __import = import("../pages/User/Onboarding/prev");
                __import.finally(() => {});
                return __import;
              });

    export const AdminAddPagesTablePage = lazy(() => {
                const __import = import("../pages/Admin/Add/AddAdminPagesTablePage");
                __import.finally(() => {});
                return __import;
              });

              export const AdminEditPagesTablePage = lazy(() => {
                const __import = import("../pages/Admin/Edit/AdminEditPagesTablePage");
                __import.finally(() => {});
                return __import;
              });
      export const AddAdminCmsPage = lazy(() => {
                const __import = import("../pages/Admin/Add/AddAdminCmsPage");
                __import.finally(() => {});
                return __import;
              });


      export const AddAdminEmailPage = lazy(() => {
                const __import = import("../pages/Admin/Add/AddAdminEmailPage");
                __import.finally(() => {});
                return __import;
              });


      export const AddAdminPhotoPage = lazy(() => {
                const __import = import("../pages/Admin/Add/AddAdminPhotoPage");
                __import.finally(() => {});
                return __import;
              });


      export const AdminCmsListPage = lazy(() => {
                const __import = import("../pages/Admin/List/AdminCmsListPage");
                __import.finally(() => {});
                return __import;
              });


      export const AdminEmailListPage = lazy(() => {
                const __import = import("../pages/Admin/List/AdminEmailListPage");
                __import.finally(() => {});
                return __import;
              });


      export const AdminStripeInvoicesListPageV2 = lazy(() => {
                const __import = import("../pages/Admin/List/AdminStripeInvoicesListPageV2");
                __import.finally(() => {});
                return __import;
              });

              export const AdmiListStripePriceTablePage = lazy(() => {
                const __import = import("../pages/Admin/List/AdminListStripePriceTablePage");
                __import.finally(() => {});
                return __import;
              });
              export const AdminEditStripePriceTablePage = lazy(() => {
                const __import = import("../pages/Admin/Edit/AdminEditStripePriceTablePage");
                __import.finally(() => {});
                return __import;
              });
              export const AdminAddStripePriceTablePage = lazy(() => {
                const __import = import("../pages/Admin/Add/AdminAddStripePriceTablePage");
                __import.finally(() => {});
                return __import;
              });
              export const AdminViewStripePriceTablePage = lazy(() => {
                const __import = import("../pages/Admin/View/AdminViewStripePriceTablePage");
                __import.finally(() => {});
                return __import;
              });

              export const  AdminStripeSubscriptionListPage = lazy(() => {
                const __import = import("../pages/Admin/List/AdminListStripeSubscriptionTablePage");
                __import.finally(() => {});
                return __import;
              });
              export const  AdminAddStripeSubscriptionTablePage = lazy(() => {
                const __import = import("../pages/Admin/Add/AdminAddStripeSubscriptionTablePage");
                __import.finally(() => {});
                return __import;
              });
              export const  AdminEditStripeSubscriptionTablePage = lazy(() => {
                const __import = import("../pages/Admin/Edit/AdminEditStripeSubscriptionTablePage");
                __import.finally(() => {});
                return __import;
              });
              export const  AdminViewStripeSubscriptionTablePage = lazy(() => {
                const __import = import("../pages/Admin/View/AdminViewStripeSubscriptionTablePage");
                __import.finally(() => {});
                return __import;
              });


      export const AdminPhotoListPage = lazy(() => {
                const __import = import("../pages/Admin/List/AdminPhotoListPage");
                __import.finally(() => {});
                return __import;
              });


      export const EditAdminCmsPage = lazy(() => {
                const __import = import("../pages/Admin/Edit/EditAdminCmsPage");
                __import.finally(() => {});
                return __import;
              });


      export const EditAdminEmailPage = lazy(() => {
                const __import = import("../pages/Admin/Edit/EditAdminEmailPage");
                __import.finally(() => {});
                return __import;
              });


      export const UserMagicLoginPage = lazy(() => {
                const __import = import("../pages/MagicLogin/UserMagicLoginPage");
                __import.finally(() => {});
                return __import;
              });


      export const MagicLoginVerifyPage = lazy(() => {
                const __import = import("../pages/MagicLogin/MagicLoginVerifyPage");
                __import.finally(() => {});
                return __import;
              });


      export const CustomAdminLoginPage = lazy(() => {
                const __import = import("../pages/Admin/Auth/CustomAdminLoginPage");
                __import.finally(() => {});
                return __import;
              });


      export const CustomAdminSignUpPage = lazy(() => {
                const __import = import("../pages/Admin/Auth/CustomAdminSignUpPage");
                __import.finally(() => {});
                return __import;
              });


      export const CustomAdminProfilePage = lazy(() => {
                const __import = import("../pages/Admin/View/CustomAdminProfilePage");
                __import.finally(() => {});
                return __import;
              });


      export const CustomUserLoginPage = lazy(() => {
                const __import = import("../pages/User/Auth/CustomUserLoginPage");
                __import.finally(() => {});
                return __import;
              });


      export const CustomUserSignUpPage = lazy(() => {
                const __import = import("../pages/User/Auth/CustomUserSignUpPage");
                __import.finally(() => {});
                return __import;
              });


      export const CustomUserProfilePage = lazy(() => {
                const __import = import("../pages/User/View/CustomUserProfilePage");
                __import.finally(() => {});
                return __import;
              });


      export const AdminListUserTablePage = lazy(() => {
                const __import = import("../pages/Admin/List/AdminListUserTablePage");
                __import.finally(() => {});
                return __import;
              });


      export const AdminAddUserTablePage = lazy(() => {
                const __import = import("../pages/Admin/Add/AdminAddUserTablePage");
                __import.finally(() => {});
                return __import;
              });


      export const AdminEditUserTablePage = lazy(() => {
                const __import = import("../pages/Admin/Edit/AdminEditUserTablePage");
                __import.finally(() => {});
                return __import;
              });


      export const AdminViewUserTablePage = lazy(() => {
                const __import = import("../pages/Admin/View/AdminViewUserTablePage");
                __import.finally(() => {});
                return __import;
              });


      export const CustomAdminDashboardPage = lazy(() => {
                const __import = import("../pages/Admin/Custom/CustomAdminDashboardPage");
                __import.finally(() => {});
                return __import;
              });


      export const AdminListPackagesTablePage = lazy(() => {
                const __import = import("../pages/Admin/List/AdminListPackagesTablePage");
                __import.finally(() => {});
                return __import;
              });


      export const AdminAddPackagesTablePage = lazy(() => {
                const __import = import("../pages/Admin/Add/AdminAddPackagesTablePage");
                __import.finally(() => {});
                return __import;
              });


      export const AdminEditPackagesTablePage = lazy(() => {
                const __import = import("../pages/Admin/Edit/AdminEditPackagesTablePage");
                __import.finally(() => {});
                return __import;
              });


      export const AdminViewPackagesTablePage = lazy(() => {
                const __import = import("../pages/Admin/View/AdminViewPackagesTablePage");
                __import.finally(() => {});
                return __import;
              });


      export const AdminListPagesTablePage = lazy(() => {
                const __import = import("../pages/Admin/List/AdminListPagesTablePage");
                __import.finally(() => {});
                return __import;
              });


      export const UserForgotPage = lazy(() => {
                const __import = import("../pages/User/Auth/UserForgotPage");
                __import.finally(() => {});
                return __import;
              });


      export const UserResetPage = lazy(() => {
                const __import = import("../pages/User/Auth/UserResetPage");
                __import.finally(() => {});
                return __import;
              });


      export const UserDashboardPage = lazy(() => {
                const __import = import("../pages/User/View/UserDashboardPage");
                __import.finally(() => {});
                return __import;
              });


      export const AdminForgotPage = lazy(() => {
                const __import = import("../pages/Admin/Auth/AdminForgotPage");
                __import.finally(() => {});
                return __import;
              });


      export const AdminResetPage = lazy(() => {
                const __import = import("../pages/Admin/Auth/AdminResetPage");
                __import.finally(() => {});
                return __import;
              });


      export const AdminDashboardPage = lazy(() => {
                const __import = import("../pages/Admin/View/AdminDashboardPage");
                __import.finally(() => {});
                return __import;
              });


      export const AdminUserListPage = lazy(() => {
                const __import = import("../pages/Admin/List/AdminUserListPage");
                __import.finally(() => {});
                return __import;
              });


      export const AddAdminUserPage = lazy(() => {
                const __import = import("../pages/Admin/Add/AddAdminUserPage");
                __import.finally(() => {});
                return __import;
              });


      export const EditAdminUserPage = lazy(() => {
                const __import = import("../pages/Admin/Edit/EditAdminUserPage");
                __import.finally(() => {});
                return __import;
              });

    
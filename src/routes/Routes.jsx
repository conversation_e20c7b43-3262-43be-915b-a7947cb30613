import React, { useEffect, useContext, useState, Suspense } from "react";
import { Routes, Route  } from "react-router-dom";
import { AuthContext } from "Context/Auth";
import { GlobalContext } from "Context/Global";

import PrivateRoute  from "./PrivateRoutes";
import PublicRoute from "./PublicRoutes";
import {PublicWrapper} from "Components/PublicWrapper";
import { NotFoundPage } from "Pages/404";
import {SnackBar} from "Components/SnackBar";
import { SessionExpiredModal } from "Components/SessionExpiredModal";

// generatePagesRoutes
import { UserWrapper } from "Components/UserWrapper";
import { UserWrapper2 } from "Components/UserWrapper2";
import { AdminWrapper } from "Components/AdminWrapper";


import {
AddAdminCmsPage,
AddAdminEmailPage,
AddAdminPhotoPage,
AdminCmsListPage,
AdminEmailListPage,
AdminEditStripePriceTablePage,
AdminAddStripePriceTablePage,
AdmiListStripePriceTablePage,
AdminViewStripePriceTablePage,
AdminStripeInvoicesListPageV2,
AdminPhotoListPage,
EditAdminCmsPage,
EditAdminEmailPage,
UserMagicLoginPage,
MagicLoginVerifyPage,
CustomAdminLoginPage,
CustomAdminSignUpPage,
CustomAdminProfilePage,
CustomUserLoginPage,
CustomUserSignUpPage,
CustomUserProfilePage,
AdminStripeSubscriptionListPage,
AdminViewStripeSubscriptionTablePage,
AdminListUserTablePage,
AdminAddUserTablePage,
AdminEditUserTablePage,
AdminViewUserTablePage,
CustomAdminDashboardPage,
AdminListPackagesTablePage,
AdminAddPackagesTablePage,
AdminEditPackagesTablePage,
AdminViewPackagesTablePage,
AdminListPagesTablePage,
UserForgotPage,
UserResetPage,
UserDashboardPage,
AdminForgotPage,
AdminResetPage,
AdminDashboardPage,
AdminUserListPage,
AddAdminUserPage,
EditAdminUserPage,
CardDetailsPage,
Onboarding,
Form,
Theme,
ThemeContd,
ThemePreview,
FinalPreview,
GenerateQuiz,
Quiz,
AllQuizzes,
SingleQuiz,
DocumentLibrary,
GetCoaching,
Prev
} from "./LazyLoad";
import AdminStripePriceListPage from "Pages/Admin/List/AdminListStripePriceTablePage";

export const DynamicWrapper = ({ isAuthenticated, role, children }) => {
  if (!isAuthenticated) {
    return <PublicWrapper>{children}</PublicWrapper>;
  }
  if (isAuthenticated) {
    
if (role === "user") {
    return <UserWrapper>{children}</UserWrapper>;
  }

if (role === "admin") {
    return <AdminWrapper>{children}</AdminWrapper>;
  }

  }
};

export const NotFound = ({ isAuthenticated, role }) => {
  if (!isAuthenticated) {
    return (
      <PublicWrapper>
        <NotFoundPage />
      </PublicWrapper>
    );
  }
  if (isAuthenticated) {
    
if (role === "user") {
  return (
    <UserWrapper>
      <NotFoundPage />
    </UserWrapper>
  );
}

if (role === "admin") {
  return (
    <AdminWrapper>
      <NotFoundPage />
    </AdminWrapper>
  );
}

  }
};


export default () => {
  const { state } = useContext(AuthContext);
  const {
    state: { isOpen },
    dispatch,
  } = useContext(GlobalContext);
  const [screenSize, setScreenSize] = useState(window.innerWidth);

  function setDimension(e) {
    if (e.currentTarget.innerWidth >= 1024) {
      toggleSideBar(true);
    } else toggleSideBar(false);
    setScreenSize(e.currentTarget.innerWidth);
  }

  // const toTop = () => {
  //   containerRef.current.scrollTo(0, 0);
  // };

  const toggleSideBar = (open) => {
    if (isOpen && screenSize < 1024) {
      dispatch({
        type: "OPEN_SIDEBAR",
        payload: { isOpen: open },
      });
    } else if (!isOpen && screenSize >= 1024) {
      dispatch({
        type: "OPEN_SIDEBAR",
        payload: { isOpen: open },
      });
    }
  };

  useEffect(() => {
    window.addEventListener("resize", setDimension);

    return () => {
      window.removeEventListener("resize", setDimension);
    };
  }, [screenSize]);

  return (
    <div
    onClick={() => {
      isOpen ? toggleSideBar(false) : null;
    }}
      className={`h-screen overflow-x-hidden bg-gradient-to-br from-[#FCF3F9] to-[#EAF8FB]`}
    >
      <Routes>
      
  <Route
  exact
  path="/admin/add-cms"
  element={
    <PrivateRoute
    access="admin"
    path={"/admin/add-cms"}
    element={
      <AdminWrapper>
      <AddAdminCmsPage />
      </AdminWrapper>
    }
    />
  }
  />
  
  <Route
  exact
  path="/admin/add-email"
  element={
    <PrivateRoute
    access="admin"
    path={"/admin/add-email"}
    element={
      <AdminWrapper>
      <AddAdminEmailPage />
      </AdminWrapper>
    }
    />
  }
  />
  
  <Route
  exact
  path="/admin/add-photo"
  element={
    <PrivateRoute
    access="admin"
    path={"/admin/add-photo"}
    element={
      <AdminWrapper>
      <AddAdminPhotoPage />
      </AdminWrapper>
    }
    />
  }
  />
  
  <Route
  exact
  path="/admin/cms"
  element={
    <PrivateRoute
    access="admin"
    path={"/admin/cms"}
    element={
      <AdminWrapper>
      <AdminCmsListPage />
      </AdminWrapper>
    }
    />
  }
  />
  
  <Route
  exact
  path="/admin/email"
  element={
    <PrivateRoute
    access="admin"
    path={"/admin/email"}
    element={
      <AdminWrapper>
      <AdminEmailListPage />
      </AdminWrapper>
    }
    />
  }
  />
  
  <Route
  exact
  path="/admin/photo"
  element={
    <PrivateRoute
    access="admin"
    path={"/admin/photo"}
    element={
      <AdminWrapper>
      <AdminPhotoListPage />
      </AdminWrapper>
    }
    />
  }
  />
  
  <Route
  exact
  path="/admin/edit-cms/:id"
  element={
    <PrivateRoute
    access="admin"
    path={"/admin/edit-cms/:id"}
    element={
      <AdminWrapper>
      <EditAdminCmsPage />
      </AdminWrapper>
    }
    />
  }
  />
  
  <Route
  exact
  path="/admin/edit-email/:id"
  element={
    <PrivateRoute
    access="admin"
    path={"/admin/edit-email/:id"}
    element={
      <AdminWrapper>
      <EditAdminEmailPage />
      </AdminWrapper>
    }
    />
  }
  />
  
        <Route
        path="/magic-login/:role"
        element={
          <PublicRoute
            path="/magic-login/:role"
            element={
              <PublicWrapper>
                <UserMagicLoginPage />
              </PublicWrapper>
            }
              />
              
        }
      />

        
        <Route
        path="/magic-login/verify"
        element={
          <PublicRoute
            path="/magic-login/verify"
            element={
              <PublicWrapper>
                <MagicLoginVerifyPage />
              </PublicWrapper>
            }
              />
              
        }
      />

        
        <Route
        path="/admin/login"
        element={
          <PublicRoute
            path="/admin/login"
            element={
              <PublicWrapper>
                <CustomAdminLoginPage />
              </PublicWrapper>
            }
              />
              
        }
      />

        
        <Route
        path="/admin/signup"
        element={
          <PublicRoute
            path="/admin/signup"
            element={
              <PublicWrapper>
                <CustomAdminSignUpPage />
              </PublicWrapper>
            }
              />
              
        }
      />

        
  <Route
  exact
  path="/admin/profile"
  element={
    <PrivateRoute
    access="admin"
    path={"/admin/profile"}
    element={
      <AdminWrapper>
      <CustomAdminProfilePage />
      </AdminWrapper>
    }
    />
  }
  />
  
        <Route
        path="/user/login"
        element={
          <PublicRoute
            path="/user/login"
            element={
              <PublicWrapper>
                <CustomUserLoginPage />
              </PublicWrapper>
            }
              />
              
        }
      />

        
        <Route
        path="/user/signup"
        element={
          <PublicRoute
            path="/user/signup"
            element={
              <PublicWrapper>
                <CustomUserSignUpPage />
              </PublicWrapper>
            }
              />
              
        }
      />

        
  <Route
  exact
  path="/user/profile"
  element={
    <PrivateRoute
    access="user"
    path={"/user/profile"}
    element={
      <Suspense fallback={null}>
      <UserWrapper>
      <CustomUserProfilePage />
      </UserWrapper>
      </Suspense>
    }
    />
  }
  />

  <Route
  exact
  path="/user/card-details"
  element={
    <PrivateRoute
    access="user"
    path={"/user/card-details"}
    element={
      <Suspense fallback={null}>
      <CardDetailsPage />
      </Suspense>
    }
    />
  }
  />
  <Route
  exact
  path="/user/onboarding"
  element={
    <PrivateRoute
    access="user"
    path={"/user/onboarding"}
    element={
      <Suspense fallback={null}>
      <UserWrapper>
      <Onboarding />
      </UserWrapper>
      </Suspense>
    }
    />
  }
  />
  <Route
  exact
  path="/user/onboarding/form"
  element={
    <PrivateRoute
    access="user"
    path={"/user/onboarding/form"}
    element={
      <Suspense fallback={null}>
      <UserWrapper>
      <Form />
      </UserWrapper>
      </Suspense>
    }
    />
  }
  />
  <Route
  exact
  path="/user/onboarding/theme"
  element={
    <PrivateRoute
    access="user"
    path={"/user/onboarding/theme"}
    element={
      <Suspense fallback={null}>
      <UserWrapper2>
      <Theme />
      </UserWrapper2>
      </Suspense>
    }
    />
  }
  />
  <Route
  exact
  path="/user/onboarding/themecontd"
  element={
    <PrivateRoute
    access="user"
    path={"/user/onboarding/themecontd"}
    element={
      <Suspense fallback={null}>
      <UserWrapper2>
      <ThemeContd />
      </UserWrapper2>
      </Suspense>
    }
    />
  }
  />
  <Route
  exact
  path="/user/onboarding/themepreview"
  element={
    <PrivateRoute
    access="user"
    path={"/user/onboarding/themepreview"}
    element={
      <Suspense fallback={null}>
      <UserWrapper2>
      <ThemePreview />
      </UserWrapper2>
      </Suspense>
    }
    />
  }
  />

<Route
  exact
  path="/user/onboarding/final-preview"
  element={
    <PrivateRoute
    access="user"
    path={"/user/onboarding/final-preview"}
    element={
      <Suspense fallback={null}>
      <UserWrapper2>
      <FinalPreview />
      </UserWrapper2>
      </Suspense>
    }
    />
  }
  />

<Route
  exact
  path="/user/onboarding/generatequiz"
  element={
    <PrivateRoute
    access="user"
    path={"/user/onboarding/generatequiz"}
    element={
      <Suspense fallback={null}>
      <UserWrapper2>
      <GenerateQuiz />
      </UserWrapper2>
      </Suspense>
    }
    />
  }
  />

<Route
  exact
  path="/user/onboarding/quiz"
  element={
    <PrivateRoute
    access="user"
    path={"/user/onboarding/quiz"}
    element={
      <Suspense fallback={null}>
      <UserWrapper>
      <Quiz />
      </UserWrapper>
      </Suspense>
    }
    />
  }
  />

<Route
  exact
  path="/user/onboarding/allquizzes"
  element={
    <PrivateRoute
    access="user"
    path={"/user/onboarding/allquizzes"}
    element={
      <Suspense fallback={null}>
      <UserWrapper2>
      <AllQuizzes />
      </UserWrapper2>
      </Suspense>
    }
    />
  }
  />

<Route
  exact
  path="/user/onboarding/singlequiz/:id"
  element={
    <PrivateRoute
    access="user"
    path={"/user/onboarding/singlequiz/:id"}
    element={
      <Suspense fallback={null}>
      <UserWrapper2>
      <SingleQuiz />
      </UserWrapper2>
      </Suspense>
    }
    />
  }
  />

<Route
  exact
  path="/user/document-library"
  element={
    <PrivateRoute
    access="user"
    path={"/user/document-library"}
    element={
      <Suspense fallback={null}>
      <UserWrapper>
      <DocumentLibrary />
      </UserWrapper>
      </Suspense>
    }
    />
  }
  />

<Route
  exact
  path="/user/get-coaching"
  element={
    <PrivateRoute
    access="user"
    path={"/user/get-coaching"}
    element={
      <Suspense fallback={null}>
      <UserWrapper>
      <GetCoaching />
      </UserWrapper>
      </Suspense>
    }
    />
  }
  />

<Route
  exact
  path="/user/prev"
  element={
    <PrivateRoute
    access="user"
    path={"/user/prev"}
    element={
      <Suspense fallback={null}>
      <UserWrapper2>
      <Prev />
      </UserWrapper2>
      </Suspense>
    }
    />
  }
  />

      <Route
      exact
      path="/admin/user"
      element={
        <PrivateRoute
        access="admin"
        path="/admin/user"
        element={
          <AdminWrapper>
          <AdminListUserTablePage />
          </AdminWrapper>
        }
        />
      }
      />
      
      <Route
      exact
      path="/admin/add-user"
      element={
        <PrivateRoute
        access="admin"
        path="/admin/add-user"
        element={
          <AdminWrapper>
          <AdminAddUserTablePage />
          </AdminWrapper>
        }
        />
      }
      />
      
      <Route
      exact
      path="/admin/edit-user/:id"
      element={
        <PrivateRoute
        access="admin"
        path="/admin/edit-user/:id"
        element={
          <AdminWrapper>
          <AdminEditUserTablePage />
          </AdminWrapper>
        }
        />
      }
      />
      
      <Route
      exact
      path="/admin/view-user/:id"
      element={
        <PrivateRoute
        access="admin"
        path="/admin/view-user/:id"
        element={
          <AdminWrapper>
          <AdminViewUserTablePage />
          </AdminWrapper>
        }
        />
      }
      />
      
  <Route
  exact
  path="/admin/dashboard"
  element={
    <PrivateRoute
    access="admin"
    path={"/admin/dashboard"}
    element={
      <AdminWrapper>
      <CustomAdminDashboardPage />
      </AdminWrapper>
    }
    />
  }
  />
  
      <Route
      exact
      path="/admin/packages"
      element={
        <PrivateRoute
        access="admin"
        path="/admin/packages"
        element={
          <AdminWrapper>
          <AdminListPackagesTablePage />
          </AdminWrapper>
        }
        />
      }
      />
       <Route
      exact
      path="/admin/view-stripe_subscription/:id"
      element={
        <PrivateRoute
        access="admin"
        path="/admin/view-stripe_subscription/:id"
        element={
          <AdminWrapper>
          <AdminViewStripeSubscriptionTablePage /> 
          </AdminWrapper>
        }
        />
      }
      /> 
        <Route
      exact
      path="/admin/stripe_subscription"
      element={
        <PrivateRoute
        access="admin"
        path="/admin/stripe_subscription"
        element={
          <AdminWrapper>
          <AdminStripeSubscriptionListPage /> 
          </AdminWrapper>
        }
        />
      }
      />
       <Route
      exact
      path="/admin/stripe_price"
      element={
        <PrivateRoute
        access="admin"
        path="/admin/stripe_price"
        element={
          <AdminWrapper>
          <AdminStripePriceListPage />
          </AdminWrapper>
        }
        />
      }
      />
       {/* <Route
      exact
      path="/admin/price/add"
      element={
        <PrivateRoute
        access="admin"
        path="/admin/stripe"
        element={
          <AdminWrapper>
          <AdminStripePriceListPage />
          </AdminWrapper>
        }
        />
      }
      /> */}
      <Route
      exact
      path="/admin/view-stripe_price/:id"
      element={
        <PrivateRoute
        access="admin"
        path="/admin/view-stripe_price/:id"
        element={
          <AdminWrapper>
          <AdminStripePriceListPage />
          </AdminWrapper>
        }
        />
      }
      />

{/* <Route
      exact
      path="/admin/view-stripe_price/:id"
      element={
        <PrivateRoute
        access="admin"
        path="/admin/view-stripe_price/:id"
        element={
          <AdminWrapper>
          <AdminEditStripePriceTablePage />
          </AdminWrapper>
        }
        />
      }
      />
       */}
      <Route
      exact
      path="/admin/add-packages"
      element={
        <PrivateRoute
        access="admin"
        path="/admin/add-packages"
        element={
          <AdminWrapper>
          <AdminAddPackagesTablePage />
          </AdminWrapper>
        }
        />
      }
      />
      
      <Route
      exact
      path="/admin/edit-packages/:id"
      element={
        <PrivateRoute
        access="admin"
        path="/admin/edit-packages/:id"
        element={
          <AdminWrapper>
          <AdminEditPackagesTablePage />
          </AdminWrapper>
        }
        />
      }
      />
      
      <Route
      exact
      path="/admin/view-packages/:id"
      element={
        <PrivateRoute
        access="admin"
        path="/admin/view-packages/:id"
        element={
          <AdminWrapper>
          <AdminViewPackagesTablePage />
          </AdminWrapper>
        }
        />
      }
      />
      
      <Route
      exact
      path="/admin/pages"
      element={
        <PrivateRoute
        access="admin"
        path="/admin/pages"
        element={
          <AdminWrapper>
          <AdminListPagesTablePage />
          </AdminWrapper>
        }
        />
      }
      />
      
      <Route
      exact
      path="/admin/pages"
      element={
        <PrivateRoute
        access="admin"
        path="/admin/pages"
        element={
          <AdminWrapper>
          <AdminListPagesTablePage />
          </AdminWrapper>
        }
        />
      }
      />
      
        <Route
        path="/user/forgot"
        element={
          <PublicRoute
            path="/user/forgot"
            element={
              <Suspense fallback={null}>
              <PublicWrapper>
                <UserForgotPage />
              </PublicWrapper>
              </Suspense>
            }
          />
        }
      />

        <Route
        path="/user/reset"
        element={
          <PublicRoute
            path="/user/reset"
            element={
              <Suspense fallback={null}>
              <PublicWrapper>
                <UserResetPage />
              </PublicWrapper>
              </Suspense>
            }
          />
        }
      />

        
  <Route
  exact
  path="/user/dashboard"
  element={
    <PrivateRoute
    access="user"
    path={"/user/dashboard"}
    element={
      <Suspense fallback={null}>
      <UserWrapper>
      <UserDashboardPage />
      </UserWrapper>
      </Suspense>
    }
    />
  }
  />
  
        <Route
        path="/admin/forgot"
        element={
          <PublicRoute
            path="/admin/forgot"
            element={
              <PublicWrapper>
                <AdminForgotPage />
              </PublicWrapper>
            }
              />
              
        }
      />

        
        <Route
        path="/admin/reset"
        element={
          <PublicRoute
            path="/admin/reset"
            element={
              <PublicWrapper>
                <AdminResetPage />
              </PublicWrapper>
            }
              />
              
        }
      />

        
  <Route
  exact
  path="/admin/dashboard"
  element={
    <PrivateRoute
    access="admin"
    path={"/admin/dashboard"}
    element={
      <AdminWrapper>
      <AdminDashboardPage />
      </AdminWrapper>
    }
    />
  }
  />
  
      <Route
      exact
      path="/admin/users"
      element={
        <PrivateRoute
        access="admin"
        path="/admin/users"
        element={
          <AdminWrapper>
          <AdminUserListPage />
          </AdminWrapper>
        }
        />
      }
      />
      
      <Route
      exact
      path="/admin/add-user"
      element={
        <PrivateRoute
        access="admin"
        path="/admin/add-user"
        element={
          <AdminWrapper>
          <AddAdminUserPage />
          </AdminWrapper>
        }
        />
      }
      />
      
  <Route
  exact
  path="/admin/edit-user/:id"
  element={
    <PrivateRoute
    access="admin"
    path={"/admin/edit-user/:id"}
    element={
      <AdminWrapper>
      <EditAdminUserPage />
      </AdminWrapper>
    }
    />
  }
  />
  

        <Route
          path={"*"}
          element={<PublicRoute path={"*"} element={<NotFound {...state} />} />}
        />
      </Routes>
      <SessionExpiredModal />
      <SnackBar />
    </div>
  );
};

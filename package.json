{"name": "adminportal", "private": true, "version": "0.0.0", "scripts": {"dev": "vite", "tw": "npx tailwindcss -i ./src/index.css -o ./src/output.css --watch", "build": "vite build", "commit": "git add . && git commit -m \"Update\" && git pull && git push", "commit:script": "node git-script add,commit,pull,push message=\"Update | API SECTION RESTRUCTURE IN PROGRESS\" origin=wireframe", "preview": "vite preview", "generate-pwa-assets": "pwa-assets-generator --preset minimal public/mkd_logo.png"}, "dependencies": {"@craftjs/core": "^0.2.0-beta.11", "@editorjs/attaches": "^1.3.0", "@editorjs/checklist": "^1.5.0", "@editorjs/code": "^2.8.0", "@editorjs/delimiter": "^1.3.0", "@editorjs/editorjs": "^2.26.5", "@editorjs/embed": "^2.5.3", "@editorjs/header": "^2.7.0", "@editorjs/image": "^2.8.1", "@editorjs/inline-code": "^1.4.0", "@editorjs/link": "^2.5.0", "@editorjs/list": "^1.8.0", "@editorjs/marker": "^1.3.0", "@editorjs/nested-list": "^1.3.0", "@editorjs/paragraph": "^2.9.0", "@editorjs/personality": "^2.0.2", "@editorjs/quote": "^2.5.0", "@editorjs/raw": "^2.4.0", "@editorjs/simple-image": "^1.5.1", "@editorjs/table": "^2.2.1", "@editorjs/underline": "^1.1.0", "@editorjs/warning": "^1.3.0", "@emotion/cache": "^11.11.0", "@emotion/react": "^11.11.1", "@emotion/serialize": "^1.1.2", "@emotion/utils": "^1.2.1", "@fontsource/inter": "^5.0.15", "@fontsource/poppins": "^4.5.10", "@fontsource/roboto-mono": "^5.0.16", "@fortawesome/fontawesome-svg-core": "^6.4.0", "@fortawesome/free-brands-svg-icons": "^6.4.0", "@fortawesome/free-regular-svg-icons": "^6.4.0", "@fortawesome/free-solid-svg-icons": "^6.4.0", "@fortawesome/react-fontawesome": "^0.2.0", "@fullcalendar/core": "^5.11.3", "@fullcalendar/daygrid": "^5.11.3", "@fullcalendar/interaction": "^5.11.3", "@fullcalendar/list": "^5.11.3", "@fullcalendar/react": "^5.11.2", "@fullcalendar/timegrid": "^5.11.3", "@headlessui/react": "^1.7.14", "@heroicons/react": "^2.0.18", "@hookform/resolvers": "^3.1.0", "@legendapp/state": "^0.23.4", "@mantine/core": "^6.0.19", "@mantine/hooks": "^6.0.19", "@react-google-maps/api": "^2.19.2", "@react-pdf-viewer/core": "^3.12.0", "@splidejs/react-splide": "^0.7.12", "@stripe/react-stripe-js": "^2.1.0", "@stripe/stripe-js": "^1.52.1", "@tailwindcss/forms": "^0.5.3", "@tippyjs/react": "^4.2.6", "@uppy/audio": "^1.1.1", "@uppy/aws-s3": "^3.2.1", "@uppy/aws-s3-multipart": "^3.4.1", "@uppy/compressor": "^1.0.2", "@uppy/core": "^3.7.1", "@uppy/dashboard": "^3.4.1", "@uppy/drag-drop": "^3.0.2", "@uppy/drop-target": "^2.0.1", "@uppy/dropbox": "^3.1.1", "@uppy/facebook": "^3.1.3", "@uppy/file-input": "^3.0.3", "@uppy/golden-retriever": "^3.1.0", "@uppy/google-drive": "^3.1.1", "@uppy/image-editor": "^2.1.2", "@uppy/instagram": "^3.1.3", "@uppy/onedrive": "^3.1.1", "@uppy/progress-bar": "^3.0.3", "@uppy/react": "^3.1.2", "@uppy/remote-sources": "^1.0.3", "@uppy/screen-capture": "^3.1.1", "@uppy/tus": "^3.4.0", "@uppy/webcam": "^3.3.1", "@uppy/xhr-upload": "^3.5.0", "apexcharts": "^3.40.0", "axios": "^1.5.0", "bootstrap": "^5.2.3", "codemirror": "^5.65.11", "codemirror-console": "^3.0.4", "codemirror-console-ui": "^3.0.4", "emoji-picker-textarea": "^1.0.1", "file-saver": "^2.0.5", "framer-motion": "^10.16.4", "fullcalendar": "^5.11.3", "html-to-image": "^1.11.11", "html2pdf.js": "^0.10.2", "jodit-react": "^1.3.39", "jspdf": "^3.0.0", "jszip": "^3.10.1", "moment": "^2.29.4", "nanoid": "^4.0.2", "openai": "^4.24.1", "papaparse": "^5.4.1", "pdfjs-dist": "^3.4.120", "pluralize": "^8.0.0", "pretty-rating-react": "^2.2.0", "qr-scanner": "^1.4.2", "qrcode": "^1.5.3", "react": "^18.2.0", "react-addons-update": "^15.6.3", "react-apexcharts": "^1.4.0", "react-calendar": "^4.2.1", "react-codemirror2": "^7.2.1", "react-confirm-alert": "^3.0.6", "react-contenteditable": "^3.3.7", "react-dnd": "^10.0.2", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "react-google-autocomplete": "^2.7.3", "react-google-maps": "^9.4.5", "react-hook-form": "^7.46.1", "react-icons": "^4.11.0", "react-infinite-scroll-component": "^6.1.0", "react-input-emoji": "^5.4.1", "react-loading-skeleton": "^3.3.1", "react-modal": "^3.16.1", "react-outside-click-handler": "^1.3.0", "react-pdf": "^7.6.0", "react-qr-reader": "^2.2.1", "react-quill": "^2.0.0", "react-ratings-declarative": "^3.4.1", "react-router": "^6.15.0", "react-router-dom": "^6.11.1", "react-select": "^5.8.0", "react-slick": "^0.29.0", "react-spinners": "^0.13.8", "react-timeago": "^7.2.0", "react-toggle": "^4.1.3", "redux": "^4.2.1", "slick-carousel": "^1.8.1", "swiper": "^9.3.1", "tw-elements": "^1.0.0-beta2", "twilio-video": "^2.27.0", "uppy": "^3.20.0", "use-debounce": "^9.0.4", "xlsx": "^0.18.5", "yup": "^1.2.0"}, "devDependencies": {"@editorjs/link-autocomplete": "^0.1.0", "@editorjs/opensea": "^1.0.2", "@editorjs/translate-inline": "^1.0.0-rc.0", "@types/react": "^18.2.6", "@types/react-dom": "^18.2.4", "@vite-pwa/assets-generator": "^0.0.8", "@vitejs/plugin-react": "^4.0.0", "@vitejs/plugin-react-refresh": "^1.3.6", "autoprefixer": "^10.4.14", "eslint": "^8.40.0", "eslint-config-react-app": "^7.0.1", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "husky": "^8.0.3", "lint-staged": "^13.2.2", "postcss": "^8.4.23", "prettier": "^2.8.8", "prettier-plugin-tailwindcss": "^0.2.8", "tailwindcss": "^3.3.2", "vite": "^4.3.5", "vite-plugin-compression": "^0.5.1", "vite-plugin-eslint": "^1.8.1", "vite-plugin-pwa": "^0.16.4"}}
import{j as e}from"./@react-google-maps/api-4794cf1a.js";import{R as N,r as L,u as I,b as G,L as o}from"./vendor-dd4ba10b.js";import{u as _}from"./react-hook-form-a6ecef1c.js";import{o as q}from"./yup-f7f8305f.js";import{c as D,a as S}from"./yup-79911193.js";import{M as $,A as O,G as H,s as A}from"./index-3efdd896.js";import{I as K}from"./InteractiveButton-6ddb3b9d.js";import{L as T}from"./login-new-bg-eb709951.js";import"./@hookform/resolvers-61c06af0.js";import"./react-confirm-alert-5d5c0db6.js";import"./html2pdf.js-19c9759c.js";import"./@headlessui/react-7b0d4887.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-1762f471.js";import"./@fortawesome/react-fontawesome-0b111e8e.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";import"./MoonLoader-795b8e10.js";let U=new $;const he=()=>{var d,x;const k=D({email:S().email().required(),password:S().required()}).required(),{dispatch:E}=N.useContext(O),{dispatch:n}=N.useContext(H),[r,a]=L.useState(!1),[i,F]=L.useState(!1),M=I(),R=new URLSearchParams(M.search).get("redirect_uri"),B=G(),{register:c,handleSubmit:P,setError:m,formState:{errors:t}}=_({resolver:q(k)}),Z=async p=>{var h,u,f,g,w,b,j,C;try{a(!0);const s=await U.login(p.email,p.password,"admin");if(!s.error)E({type:"LOGIN",payload:s}),A(n,"Succesfully Logged In",4e3,"success"),B(R??"/admin/dashboard");else if(a(!1),s.validation){const v=Object.keys(s.validation);for(let l=0;l<v.length;l++){const y=v[l];m(y,{type:"manual",message:s.validation[y]})}}}catch(s){a(!1),A(n,(u=(h=s==null?void 0:s.response)==null?void 0:h.data)!=null&&u.message?(g=(f=s==null?void 0:s.response)==null?void 0:f.data)==null?void 0:g.message:s==null?void 0:s.message,4e3,"error"),console.log("Error",s),m("email",{type:"manual",message:(b=(w=s==null?void 0:s.response)==null?void 0:w.data)!=null&&b.message?(C=(j=s==null?void 0:s.response)==null?void 0:j.data)==null?void 0:C.message:s==null?void 0:s.message})}};return e.jsxs("main",{className:"min-h-screen bg-cover",style:{backgroundImage:`url(${T})`},children:[e.jsx("nav",{className:"flex items-center justify-between px-6 py-2 min-h-[50px] border-b border-b-[#C6C6C6] bg-white",children:e.jsx(o,{to:"/",className:"text-xl font-semibold",children:"Baas Brand"})}),e.jsx("div",{className:"flex flex-col justify-center items-center min-h-full",children:e.jsxs("div",{className:"w-[50%] flex flex-col items-center p-4 rounded-lg shadow-md border border-[#a8a8a8]  my-12",children:[e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"25",height:"24",viewBox:"0 0 25 24",fill:"none",children:[e.jsx("path",{d:"M12.5 2C10.0147 2 8 4.01472 8 6.5C8 8.98528 10.0147 11 12.5 11C14.9853 11 17 8.98528 17 6.5C17 4.01472 14.9853 2 12.5 2Z",fill:"#4F46E5"}),e.jsx("path",{d:"M12.5004 12.5C8.3271 12.5 5.27345 15.2936 4.4402 19.0013C4.19057 20.112 5.10014 21 6.09882 21H18.902C19.9007 21 20.8102 20.112 20.5606 19.0013C19.7274 15.2936 16.6737 12.5 12.5004 12.5Z",fill:"#4F46E5"})]}),e.jsx("div",{className:"text-xl font-semibold text-[#262626] my-2",children:"Welcome Back"}),e.jsxs("div",{className:"flex items-center text-sm",children:[e.jsx("span",{className:"text-[#525252] mr-1",children:"Don’t have account? "})," ",e.jsx(o,{to:"/admin/signup",className:"text-[#4F46E5]",children:"Sign up here"})]}),e.jsx("div",{className:"oauth flex flex-col gap-4 max-w-md w-full px-6 text-[#344054] grow"}),e.jsxs("form",{className:"min-w-[70%]",onSubmit:P(Z),children:[e.jsxs("div",{className:"flex flex-col text-sm mb-6",children:[e.jsx("label",{htmlFor:"",children:"Email"}),e.jsx("input",{className:"py-2 px-3 rounded-md border border-[#c6c6c6] text-[#525252] bg-transparent",type:"text",placeholder:"<EMAIL>",...c("email")}),e.jsx("p",{className:"text-xs italic text-red-500",children:(d=t==null?void 0:t.email)==null?void 0:d.message})]}),e.jsxs("div",{className:"flex flex-col text-sm",children:[e.jsx("label",{htmlFor:"",children:"Password"}),e.jsxs("div",{className:"flex items-center py-1 px-2 rounded-md border border-[#c6c6c6] text-[#525252] bg-transparent",children:[e.jsx("input",{className:"w-[95%] p-1 bg-transparent border-none outline-none shadow-[0] focus:border-none focus:outline-none focus:shadow-none focus-visible::outline-none",type:i?"text":"password",placeholder:"********",...c("password"),style:{boxShadow:"0 0 transparent"}}),e.jsx("span",{className:"w-[5%] cursor-pointer",onClick:()=>F(!i),children:i?e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",children:e.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M9.99998 3.33337C13.5326 3.33335 16.9489 5.50937 19.0735 9.61715L19.2715 10L19.0735 10.3828C16.9489 14.4906 13.5326 16.6667 10 16.6667C6.46737 16.6667 3.05113 14.4907 0.926472 10.3829L0.728455 10.0001L0.926472 9.61724C3.05113 5.50946 6.46736 3.3334 9.99998 3.33337ZM7.08333 10C7.08333 8.38921 8.38917 7.08337 10 7.08337C11.6108 7.08337 12.9167 8.38921 12.9167 10C12.9167 11.6109 11.6108 12.9167 10 12.9167C8.38917 12.9167 7.08333 11.6109 7.08333 10Z",fill:"#A8A8A8"})}):e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",children:[e.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M3.28033 2.21967C2.98744 1.92678 2.51256 1.92678 2.21967 2.21967C1.92678 2.51256 1.92678 2.98744 2.21967 3.28033L5.38733 6.44799C4.04329 7.533 2.8302 8.97021 1.81768 10.7471C1.37472 11.5245 1.37667 12.4782 1.81881 13.2539C3.74678 16.6364 6.40456 18.789 9.29444 19.6169C12.0009 20.3923 14.8469 19.9857 17.3701 18.4308L20.7197 21.7803C21.0126 22.0732 21.4874 22.0732 21.7803 21.7803C22.0732 21.4874 22.0732 21.0126 21.7803 20.7197L3.28033 2.21967ZM14.2475 15.3082L13.1559 14.2166C12.81 14.3975 12.4167 14.4995 11.9991 14.4995C10.6184 14.4995 9.49911 13.3802 9.49911 11.9995C9.49911 11.5819 9.60116 11.1886 9.78207 10.8427L8.69048 9.75114C8.25449 10.3917 7.99911 11.1662 7.99911 11.9995C7.99911 14.2087 9.78998 15.9995 11.9991 15.9995C12.8324 15.9995 13.6069 15.7441 14.2475 15.3082Z",fill:"#A8A8A8"}),e.jsx("path",{d:"M19.7234 16.5416C20.5189 15.7335 21.2556 14.7869 21.9145 13.7052C22.5512 12.66 22.5512 11.34 21.9145 10.2948C19.3961 6.16075 15.7432 4.00003 11.9999 4C10.6454 3.99999 9.30281 4.28286 8.02148 4.83974L19.7234 16.5416Z",fill:"#A8A8A8"})]})})]}),e.jsx("p",{className:"text-xs italic text-red-500",children:(x=t==null?void 0:t.password)==null?void 0:x.message})]}),e.jsxs("div",{className:"flex justify-between text-sm my-2",children:[e.jsxs("div",{className:"flex items-center text-[#525252]",children:[e.jsx("input",{className:"mr-2",type:"checkbox"}),"Remember me"]}),e.jsx(o,{to:"/admin/forgot",className:"text-[#4F46E5]",children:"Forgot password"})]}),e.jsx(K,{type:"submit",className:"w-full flex items-center justify-center rounded-md bg-[#4F46E5] py-2 tracking-wide text-white outline-none focus:outline-none my-12",loading:r,disabled:r,children:e.jsx("span",{children:"Sign in"})})]})]})})]})};export{he as default};

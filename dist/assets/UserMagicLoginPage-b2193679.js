import{j as e}from"./@react-google-maps/api-4794cf1a.js";import{R as o,h as b,b as f}from"./vendor-dd4ba10b.js";import{u as w}from"./react-hook-form-a6ecef1c.js";import{o as j}from"./yup-f7f8305f.js";import{c as y,a as N}from"./yup-79911193.js";import{A as v,G as S,M as A,s as k}from"./index-3efdd896.js";import"./@hookform/resolvers-61c06af0.js";import"./react-confirm-alert-5d5c0db6.js";import"./html2pdf.js-19c9759c.js";import"./@headlessui/react-7b0d4887.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-1762f471.js";import"./@fortawesome/react-fontawesome-0b111e8e.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";const z=()=>{var i,l;const m=y({email:N().email().required()}).required();o.useContext(v);const{dispatch:n}=o.useContext(S),[c,s]=o.useState(!1),a=b();f();const{register:p,handleSubmit:u,setError:d,formState:{errors:r}}=w({resolver:j(m)}),x=async g=>{let h=new A;try{s(!0);const t=await h.magicLoginAttempt(g.email,a==null?void 0:a.role);t.error||(s(!1),console.log(t),k(n,"Please check your mail to complete login attempt"))}catch(t){s(!1),console.log("Error",t),d("email",{type:"manual",message:t.message})}};return e.jsxs("div",{className:"w-full max-w-xs mx-auto",children:[e.jsxs("form",{onSubmit:u(x),className:"bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4 mt-8 ",children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"email",children:"Email"}),e.jsx("input",{type:"email",placeholder:"Email",...p("email"),className:`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(i=r.email)!=null&&i.message?"border-red-500":""}`}),e.jsx("p",{className:"text-red-500 text-xs italic",children:(l=r.email)==null?void 0:l.message})]}),e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("button",{type:"submit",className:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:[c?"Attempting Log In...":"Sign In"," "]})})]}),e.jsxs("p",{className:"text-center text-gray-500 text-xs",children:["© ",new Date().getFullYear()," manaknightdigital inc. All rights reserved."]})]})};export{z as default};

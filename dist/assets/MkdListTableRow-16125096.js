import{j as r}from"./@react-google-maps/api-4794cf1a.js";import{b as C}from"./vendor-dd4ba10b.js";const F=({i:h,row:p,columns:g,actions:e,actionPostion:w,actionId:d="id",handleTableCellChange:m,selectedIds:v=[],handleSelectRow:N,setDeleteId:b,table:k,tableRole:y})=>{const O=C();return r.jsx(r.Fragment,{children:r.jsx("tr",{children:g.map((s,t)=>{var u,a,i,n,f,j,l,c;return s.accessor.indexOf("image")>-1?r.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:r.jsx("img",{src:p[s.accessor],className:"h-[3.rem] w-[9.375rem]",alt:""})},t):s.accessor.indexOf("pdf")>-1||s.accessor.indexOf("doc")>-1||s.accessor.indexOf("file")>-1||s.accessor.indexOf("video")>-1?r.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:r.jsxs("a",{className:"text-blue-500",target:"_blank",href:p[s.accessor],rel:"noreferrer",children:[" ","View"]})},t):s.accessor===""?[(u=e==null?void 0:e.select)==null?void 0:u.show,(a=e==null?void 0:e.view)==null?void 0:a.show,(i=e==null?void 0:e.edit)==null?void 0:i.show,(n=e==null?void 0:e.delete)==null?void 0:n.show].includes(!0)?r.jsxs("td",{className:"flex !w-full gap-2 whitespace-nowrap px-6 py-4",children:[((f=e==null?void 0:e.select)==null?void 0:f.show)&&r.jsx("span",{children:r.jsx("input",{className:"mr-1",type:"checkbox",name:"select_item",checked:v.includes(p[d]),onChange:()=>N(p[d])})}),w==="ontable"&&r.jsxs(r.Fragment,{children:[((j=e==null?void 0:e.edit)==null?void 0:j.show)&&r.jsx("button",{className:"cursor-pointer text-xs font-medium text-indigo-600 hover:underline",onClick:()=>{var x;(x=e==null?void 0:e.edit)!=null&&x.action&&e.edit.action([p[d]])},children:r.jsx("span",{children:"Edit"})}),((l=e==null?void 0:e.view)==null?void 0:l.show)&&r.jsx("button",{className:"cursor-pointer px-1 text-xs font-medium text-blue-500 hover:underline",onClick:()=>{var x;(x=e==null?void 0:e.view)!=null&&x.action&&e.view.action([p[d]]),O(`/${y}/view-${k}/`+p[d],{state:p})},children:r.jsx("span",{children:"View"})}),((c=e==null?void 0:e.delete)==null?void 0:c.show)&&r.jsx("button",{className:"cursor-pointer px-1 text-xs font-medium text-red-500 hover:underline",onClick:()=>{b(p[d])},children:r.jsx("span",{children:"Delete"})})]})]},t):null:s.mappingExist?r.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:r.jsx("select",{onChange:x=>m(p[d],x.target.value,h,s.accessor),children:Object.keys(s.mappings).map((x,_)=>r.jsx("option",{value:x,selected:x===p[s.accessor],children:s.mappings[x]},_))})},t):!s.mappingExist&&s.accessor!=="id"&&s.accessor!=="create_at"&&s.accessor!=="update_at"&&s.accessor!=="user_id"?r.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:r.jsx("input",{className:"text-ellipsis border-0",type:"text",value:p[s.accessor],onChange:x=>m(p[d],x.target.value,h,s.accessor)})},t):r.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:p[s.accessor]},t)})})})};export{F as default};

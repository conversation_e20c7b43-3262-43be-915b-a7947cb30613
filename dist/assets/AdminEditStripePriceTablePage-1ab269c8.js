import{j as t}from"./@react-google-maps/api-4794cf1a.js";import{R as n,b as G,r as a,h as $}from"./vendor-dd4ba10b.js";import{u as B}from"./react-hook-form-a6ecef1c.js";import{o as H}from"./yup-f7f8305f.js";import{c as q,a as r}from"./yup-79911193.js";import{M as K,A as V,G as z,t as J,s as Q}from"./index-3efdd896.js";import"./react-quill-25360d36.js";import{M as l}from"./MkdInput-bb15886f.js";import{I as W}from"./InteractiveButton-6ddb3b9d.js";import{S as X}from"./index-3b0c955b.js";import"./@hookform/resolvers-61c06af0.js";import"./react-confirm-alert-5d5c0db6.js";import"./html2pdf.js-19c9759c.js";import"./@headlessui/react-7b0d4887.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-1762f471.js";import"./@fortawesome/react-fontawesome-0b111e8e.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";import"./@craftjs/core-3d3a3b40.js";import"./MoonLoader-795b8e10.js";let g=new K;const ke=u=>{var S,N;const{dispatch:I}=n.useContext(V),w=q({name:r(),product_id:r(),stripe_id:r(),is_usage_metered:r(),usage_limit:r(),object:r(),amount:r(),trial_days:r(),type:r(),status:r()}).required(),{dispatch:y}=n.useContext(z),[x,Y]=n.useState({}),[h,f]=n.useState(!1),[E,_]=n.useState(!1),T=G(),[Z,v]=a.useState(""),[ee,A]=a.useState(0),[te,P]=a.useState(""),[se,D]=a.useState(""),[ae,k]=a.useState(0),[oe,R]=a.useState(""),[ie,U]=a.useState(0),[re,L]=a.useState(0),[le,M]=a.useState(""),[me,O]=a.useState(""),{register:o,handleSubmit:F,setError:j,setValue:i,formState:{errors:s}}=B({resolver:H(w)}),d=$();a.useEffect(function(){(async function(){try{_(!0),g.setTable("stripe_price");const e=await g.callRestAPI({id:u.activeId?u.activeId:Number(d==null?void 0:d.id)},"GET");e.error||(i("name",e.model.name),i("product_id",e.model.product_id),i("stripe_id",e.model.stripe_id),i("is_usage_metered",e.model.is_usage_metered),i("usage_limit",e.model.usage_limit),i("object",e.model.object),i("amount",e.model.amount),i("trial_days",e.model.trial_days),i("type",e.model.type),i("status",e.model.status),v(e.model.name),A(e.model.product_id),P(e.model.stripe_id),D(e.model.is_usage_metered),k(e.model.usage_limit),R(e.model.object),U(e.model.amount),L(e.model.trial_days),M(e.model.type),O(e.model.status),_(!1))}catch(e){_(!1),console.log("error",e),J(I,e.message)}})()},[]);const C=async e=>{f(!0);try{g.setTable("stripe_price");for(let p in x){let c=new FormData;c.append("file",x[p].file);let b=await g.uploadImage(c);e[p]=b.url}const m=await g.callRestAPI({id:u.activeId?u.activeId:Number(d==null?void 0:d.id),name:e.name,product_id:e.product_id,stripe_id:e.stripe_id,is_usage_metered:e.is_usage_metered,usage_limit:e.usage_limit,object:e.object,amount:e.amount,trial_days:e.trial_days,type:e.type,status:e.status},"PUT");if(!m.error)Q(y,"Updated"),T("/admin/stripe_price"),y({type:"REFRESH_DATA",payload:{refreshData:!0}}),u.setSidebar(!1);else if(m.validation){const p=Object.keys(m.validation);for(let c=0;c<p.length;c++){const b=p[c];j(b,{type:"manual",message:m.validation[b]})}}f(!1)}catch(m){f(!1),console.log("Error",m),j("name",{type:"manual",message:m.message})}};return n.useEffect(()=>{y({type:"SETPATH",payload:{path:"stripe_price"}})},[]),t.jsxs("div",{className:" shadow-md rounded   mx-auto p-5",children:[t.jsx("h4",{className:"text-2xl font-medium",children:"Edit Stripe Price"}),E?t.jsx(X,{}):t.jsxs("form",{className:" w-full max-w-lg",onSubmit:F(C),children:[t.jsx(l,{type:"text",page:"edit",name:"name",errors:s,label:"Name",placeholder:"Name",register:o,className:""}),t.jsx(l,{type:"number",page:"edit",name:"product_id",errors:s,label:"Product Id",placeholder:"Product Id",register:o,className:""}),t.jsx(l,{type:"text",page:"edit",name:"stripe_id",errors:s,label:"Stripe Id",placeholder:"Stripe Id",register:o,className:""}),t.jsx(l,{type:"number",page:"edit",name:"is_usage_metered",errors:s,label:"Is Usage Metered",placeholder:"Is Usage Metered",register:o,className:""}),t.jsx(l,{type:"number",page:"edit",name:"usage_limit",errors:s,label:"Usage Limit",placeholder:"Usage Limit",register:o,className:""}),t.jsxs("div",{className:"mb-4  ",children:[t.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"object",children:"Object"}),t.jsx("textarea",{placeholder:"Object",...o("object"),className:`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(S=s.object)!=null&&S.message?"border-red-500":""}`,row:50}),t.jsx("p",{className:"text-red-500 text-xs italic",children:(N=s.object)==null?void 0:N.message})]}),t.jsx(l,{type:"number",page:"edit",name:"amount",errors:s,label:"Amount",placeholder:"Amount",register:o,className:""}),t.jsx(l,{type:"number",page:"edit",name:"trial_days",errors:s,label:"Trial Days",placeholder:"Trial Days",register:o,className:""}),t.jsx(l,{type:"text",page:"edit",name:"type",errors:s,label:"Type",placeholder:"Type",register:o,className:""}),t.jsx(l,{type:"number",page:"edit",name:"status",errors:s,label:"Status",placeholder:"Status",register:o,className:""}),t.jsx(W,{type:"submit",className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",loading:h,disable:h,children:"Submit"})]})]})};export{ke as default};

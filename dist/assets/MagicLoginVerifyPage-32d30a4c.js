import{j as e}from"./@react-google-maps/api-4794cf1a.js";import{R as r,b as l,i as m}from"./vendor-dd4ba10b.js";import{A as p,G as u,M as d}from"./index-3efdd896.js";import"./react-confirm-alert-5d5c0db6.js";import"./html2pdf.js-19c9759c.js";import"./@headlessui/react-7b0d4887.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-1762f471.js";import"./@fortawesome/react-fontawesome-0b111e8e.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";const G=()=>{const{dispatch:o}=r.useContext(p);r.useContext(u);const t=l(),[i,f]=m(),n=async()=>{let c=new d;try{let s=i.get("token")??null;const a=await c.magicLoginVerify(s);a.error?t("/user/login"):(o({type:"LOGIN",payload:a}),t(`/${a.role}/dashboard`))}catch{t("/user/login")}};return r.useEffect(()=>{(async()=>await n())()}),e.jsx(e.Fragment,{children:e.jsx("div",{className:"flex min-h-screen justify-center items-center min-w-full",children:e.jsx("svg",{className:"w-24 h-24 animate-spin",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{fillRule:"evenodd",d:"M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z",clipRule:"evenodd"})})})})};export{G as default};

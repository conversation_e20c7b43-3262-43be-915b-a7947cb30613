import{j as e}from"./@react-google-maps/api-4794cf1a.js";import{R as s,b as g,r as u}from"./vendor-dd4ba10b.js";import{M as x,A as E,G as w,L as r,b as D,a as c,i as b,j as A}from"./index-3efdd896.js";import"./react-confirm-alert-5d5c0db6.js";import"./html2pdf.js-19c9759c.js";import"./@headlessui/react-7b0d4887.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-1762f471.js";import"./@fortawesome/react-fontawesome-0b111e8e.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";new x;const j=[{header:"Id",accessor:"id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Oauth",accessor:"oauth",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Role",accessor:"role",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"First Name",accessor:"first_name",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Last Name",accessor:"last_name",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Email",accessor:"email",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Password",accessor:"password",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Type",accessor:"type",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Verify",accessor:"verify",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Packages Id",accessor:"packages_id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Phone",accessor:"phone",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Photo",accessor:"photo",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Refer",accessor:"refer",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Stripe Uid",accessor:"stripe_uid",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Paypal Uid",accessor:"paypal_uid",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Two Factor Authentication",accessor:"two_factor_authentication",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Status",accessor:"status",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Create At",accessor:"create_at",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Update At",accessor:"update_at",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Action",accessor:""}],G=()=>{s.useContext(E),s.useContext(w),g();const[n,a]=s.useState(!1),[o,t]=s.useState(!1),[f,m]=s.useState(),S=u.useRef(null),[_,h]=s.useState([]),d=(i,l,p=[])=>{switch(i){case"add":a(l);break;case"edit":t(l),h(p),m(p[0]);break}};return e.jsxs(e.Fragment,{children:[e.jsx(e.Fragment,{children:e.jsx("div",{className:"overflow-x-auto  rounded bg-white p-5 shadow",children:e.jsx(r,{children:e.jsx(D,{columns:j,tableRole:"admin",table:"user",actionId:"id",actions:{view:{show:!0,action:null,multiple:!1},edit:{show:!0,multiple:!1,action:i=>d("edit",!0,i)},delete:{show:!0,action:null,multiple:!1},select:{show:!0,action:null,multiple:!1},add:{show:!0,action:()=>d("add",!0),multiple:!1,children:"Add New",showChildren:!0},export:{show:!1,action:null,multiple:!0}},actionPostion:"ontable",refreshRef:S})})})}),e.jsx(r,{children:e.jsx(c,{isModalActive:n,closeModalFn:()=>a(!1),children:e.jsx(b,{setSidebar:a})})}),o&&e.jsx(r,{children:e.jsx(c,{isModalActive:o,closeModalFn:()=>t(!1),children:e.jsx(A,{activeId:f,setSidebar:t})})})]})};export{G as default};

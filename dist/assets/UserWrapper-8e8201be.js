import{j as e}from"./@react-google-maps/api-4794cf1a.js";import{r}from"./vendor-dd4ba10b.js";import{U as l}from"./index-154578dc.js";import{T as a}from"./index-292ff41b.js";import"./index-e6a343d0.js";import"./html2pdf.js-19c9759c.js";const m=({children:s})=>e.jsx("div",{id:"user_wrapper",className:"flex w-full max-w-full flex-col bg-white",children:e.jsxs("div",{className:"flex min-h-screen w-full max-w-full ",children:[e.jsx(l,{}),e.jsxs("div",{className:"mb-20 w-full overflow-hidden",children:[e.jsx(a,{}),e.jsx(r.Suspense,{fallback:e.jsx("div",{className:"flex h-screen w-full items-center justify-center"}),children:e.jsx("div",{className:"w-full overflow-y-auto overflow-x-hidden pt-[calc(3.5rem+2rem)]",children:s})})]})]})}),p=r.memo(m);export{p as default};

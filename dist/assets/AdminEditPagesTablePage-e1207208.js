import{j as t}from"./@react-google-maps/api-4794cf1a.js";import{R as r,b as P,r as p,h as R}from"./vendor-dd4ba10b.js";import{u as D}from"./react-hook-form-a6ecef1c.js";import{o as C}from"./yup-f7f8305f.js";import{c as L,a as w}from"./yup-79911193.js";import{M,A as G,G as O,t as $,s as B}from"./index-3efdd896.js";import"./react-quill-25360d36.js";import{M as H}from"./MkdInput-bb15886f.js";import{I as U}from"./InteractiveButton-6ddb3b9d.js";import{S as q}from"./index-3b0c955b.js";import"./@hookform/resolvers-61c06af0.js";import"./react-confirm-alert-5d5c0db6.js";import"./html2pdf.js-19c9759c.js";import"./@headlessui/react-7b0d4887.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-1762f471.js";import"./@fortawesome/react-fontawesome-0b111e8e.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";import"./@craftjs/core-3d3a3b40.js";import"./MoonLoader-795b8e10.js";let m=new M;const ge=i=>{var S,j;const{dispatch:N}=r.useContext(G),E=L({name:w(),features:w()}).required(),{dispatch:c}=r.useContext(O),[x,K]=r.useState({}),[g,d]=r.useState(!1),[k,u]=r.useState(!1),v=P(),[V,I]=p.useState(""),[_,T]=p.useState(""),{register:h,handleSubmit:A,setError:b,setValue:y,formState:{errors:f}}=D({resolver:C(E)}),s=R();p.useEffect(function(){(async function(){try{u(!0),m.setTable("packages");const e=await m.callRestAPI({id:i.activeId?i.activeId:Number(s==null?void 0:s.id)},"GET");e.error||(y("name",e.model.name),y("features",e.model.features),I(e.model.name),T(e.model.features),u(!1))}catch(e){u(!1),console.log("error",e),$(N,e.message)}})()},[]);const F=async e=>{d(!0);try{m.setTable("packages");for(let l in x){let o=new FormData;o.append("file",x[l].file);let n=await m.uploadImage(o);e[l]=n.url}const a=await m.callRestAPI({id:i.activeId?i.activeId:Number(s==null?void 0:s.id),name:e.name,features:e.features},"PUT");if(!a.error)B(c,"Updated"),v("/admin/packages"),c({type:"REFRESH_DATA",payload:{refreshData:!0}}),i.setSidebar(!1);else if(a.validation){const l=Object.keys(a.validation);for(let o=0;o<l.length;o++){const n=l[o];b(n,{type:"manual",message:a.validation[n]})}}d(!1)}catch(a){d(!1),console.log("Error",a),b("name",{type:"manual",message:a.message})}};return r.useEffect(()=>{c({type:"SETPATH",payload:{path:"packages"}})},[]),t.jsxs("div",{className:" shadow-md rounded   mx-auto p-5",children:[t.jsx("h4",{className:"text-2xl font-medium",children:"Edit Packages"}),k?t.jsx(q,{}):t.jsxs("form",{className:" w-full max-w-lg",onSubmit:A(F),children:[t.jsx(H,{type:"text",page:"edit",name:"name",errors:f,label:"Name",placeholder:"Name",register:h,className:""}),t.jsxs("div",{className:"mb-4  ",children:[t.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"features",children:"Features"}),t.jsx("textarea",{placeholder:"Features",...h("features"),className:`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(S=f.features)!=null&&S.message?"border-red-500":""}`,row:50}),t.jsx("p",{className:"text-red-500 text-xs italic",children:(j=f.features)==null?void 0:j.message})]}),t.jsx(U,{type:"submit",className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",loading:g,disable:g,children:"Submit"})]})]})};export{ge as default};

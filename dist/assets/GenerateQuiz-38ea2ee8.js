import{j as e}from"./@react-google-maps/api-4794cf1a.js";import{R as u,b as P,r as s,L as m}from"./vendor-dd4ba10b.js";import{A as I,G as S,M as y,s as c,t as D}from"./index-3efdd896.js";import{D as W}from"./document-download-151d604a.js";import{M as L}from"./monitor-8a3dd572.js";import"./react-confirm-alert-5d5c0db6.js";import"./html2pdf.js-19c9759c.js";import"./@headlessui/react-7b0d4887.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-1762f471.js";import"./@fortawesome/react-fontawesome-0b111e8e.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";const J="data:image/png;base64,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",Y=({onClose:n})=>{u.useContext(I),u.useContext(S);const d=P();s.useState(!1);const i=()=>{n(),d("/user/profile",{state:{activeTab:"Current plan",showUpgradeOptions:!0}})};return e.jsx("div",{className:"fixed inset-0 md:bg-black/50 flex items-center justify-center z-50",children:e.jsxs("div",{className:"bg-white rounded-md h-screen p-4 md:p-0 md:h-[321px] flex flex-col justify-center items-center relative",style:{width:"770px"},children:[e.jsx("div",{className:"pr-[28px] pt-[13px] absolute top-0 right-0 hidden md:block",children:e.jsx("button",{onClick:n,className:"text-[#8F8F8F]",children:e.jsx("svg",{width:"14",height:"14",viewBox:"0 0 14 14",fill:"none",children:e.jsx("path",{d:"M1 1L13 13M1 13L13 1",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round"})})})}),e.jsxs("div",{className:"px-[28px] pb-[13px] -mt-2",children:[e.jsx("h2",{className:"text-center font-inter text-[20px] leading-[28px] mb-4 text-[#111928]",children:"You discovered a premium feature!"}),e.jsx("p",{className:"text-center text-[16px] leading-[1.4] text-[#111928] mb-8 mx-auto",style:{maxWidth:"460px"},children:"Upgrade your plan to use this feature. You can learn more about our subscription packages by clicking on the 'Upgrade' button."}),e.jsxs("div",{className:"flex justify-center items-center gap-[10px]",children:[e.jsx("button",{onClick:n,className:"h-[50px] w-full md:w-[202px] px-[24px] rounded-[6px] border border-[#E5E5E5] text-[16px] font-medium text-[#054FB1] hover:bg-gray-50",children:"CANCEL"}),e.jsx("button",{onClick:i,className:"h-[50px] w-full md:w-[202px] px-[24px] rounded-[6px] bg-[#054FB1] text-[16px] font-medium text-white hover:bg-[#054FB1]/90",children:"UPGRADE"})]})]})]})})},Ae=()=>{const{dispatch:n,state:d}=u.useContext(I),{dispatch:i}=u.useContext(S),z=P(),x=d==null?void 0:d.user,[C]=s.useState("Customer Service"),[k,h]=s.useState(!1),[G,v]=s.useState(!1),[f,b]=s.useState(!1),[E]=s.useState(()=>{const t=localStorage.getItem(`allThemes_${x}`);return t?JSON.parse(t):[]}),[A,q]=s.useState(()=>localStorage.getItem(`mergedPdfUrl_${x}`)||""),[j,p]=s.useState(!1),[g,F]=s.useState(null),[V,N]=s.useState(0),[H,M]=s.useState(null),[O]=s.useState(()=>{const t=localStorage.getItem(`completedThemes_${x}`);return t?JSON.parse(t):[]}),B=[{id:1,name:"Single line text inputs"},{id:2,name:"Multiple Choice questions"},{id:3,name:"True False questions"}];s.useEffect(()=>{(async()=>{var l,a;const r=new y;try{const o=await r.getCustomerStripeSubscription();console.log("Subscription response:",o);const Q=((l=o.customer)==null?void 0:l.email)==="<EMAIL>"||o.customer&&o.customer.subId!==null&&o.customer.planId!==null;M((a=o.customer)==null?void 0:a.planId),v(Q);const w=await r.callRawAPI("/v3/api/custom/jordan/user/user-info",{},"GET");!w.error&&w.data&&N(w.data.quizzes)}catch(o){console.error("Premium status check error:",o),v(!1)}})()},[]);const T=async()=>{if(!G){h(!0);return}if(!g){p(!0);return}try{b(!0);const t=new y;if(!A){c(i,"Merged PDF URL not found",4e3,"error");return}const r=await t.callRawAPI("/v3/api/custom/theme/generate-quiz",{pdf_url:A,quiz_type:g.id.toString()},"POST");if(console.log("Generate Quiz Response:",r),r.error)c(i,"Failed to generate quiz",4e3,"error");else{console.log("Quiz Data:",r.data);const l=r.data.quiz_id||r.id;console.log("Storing Quiz ID:",l),localStorage.setItem("generatedQuiz",JSON.stringify(r.data)),localStorage.setItem("quizType",g.id),localStorage.setItem("quizId",l),N(a=>a+1),z("/user/onboarding/quiz")}}catch(t){console.error("Error:",t),c(i,t.message||"Failed to generate quiz",4e3,"error"),D(n,t.message)}finally{b(!1)}},U=()=>{h(!1)},R=async()=>{try{if(!A){c(i,"PDF URL not found",4e3,"error");return}const r=await(await fetch(A)).blob(),l=window.URL.createObjectURL(r),a=document.createElement("a");a.href=l,a.download="merged_themes_report.pdf",document.body.appendChild(a),a.click(),document.body.removeChild(a),window.URL.revokeObjectURL(l)}catch(t){console.error("PDF download error:",t),c(i,"Failed to download PDF",4e3,"error")}};return e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"w-full text-7xl min-h-screen h-full text-gray-700 bg-[#E2E6EB] p-[1rem]",children:e.jsxs("div",{className:"w-[95%] md:w-[90%] mx-auto md:mx-0",children:[e.jsx("h1",{className:"text-[20px] text-[#111928] font-semibold mb-6",children:"Generate themes document"}),e.jsxs("div",{className:"flex items-center justify-between mb-8 relative bg-white h-[98px] p-[1rem]",children:[e.jsx("div",{className:"absolute left-8 right-8 top-1/4 md:top-1/3 h-0.5 bg-gray-200 w-[100% - 2rem]"}),e.jsxs("div",{className:"flex flex-col items-start z-10 w-[28%]",children:[e.jsx("div",{className:"w-10 h-10 border border-[#9C9C9C] bg-white rounded-full flex items-center justify-center",children:e.jsx("div",{className:"w-6 h-6 bg-[#389C14] rounded-full flex items-center  justify-center",children:e.jsx("svg",{className:"w-4 h-4 text-white",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"})})})}),e.jsx(m,{to:"/user/onboarding",className:"text-xs md:text-sm hover:text-[#054FB1] transition-colors",children:"Upload your resume & full history docs"})]}),e.jsxs("div",{className:"flex flex-col items-center z-10 w-[24%]",children:[e.jsx("div",{className:"w-10 h-10 border border-[#9C9C9C] bg-white rounded-full flex items-center justify-center",children:e.jsx("div",{className:"w-6 h-6 bg-[#389C14] rounded-full flex items-center  justify-center",children:e.jsx("svg",{className:"w-4 h-4 text-white",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"})})})}),e.jsx(m,{to:"/user/onboarding/form",className:"text-xs md:text-sm hover:text-[#054FB1] transition-colors",children:"Fill out questions form"})]}),e.jsxs("div",{className:"flex flex-col items-end z-10 w-[24%]",children:[e.jsx("div",{className:"w-8 h-8 bg-[#054FB1] rounded-full flex items-center justify-center text-gray-500 mb-2",children:e.jsx("img",{src:L,className:"w-[20px] h-[20px]",alt:"upload icon"})}),e.jsxs(m,{to:"/user/onboarding/theme",className:"text-xs md:text-sm text-[#054FB1]",children:["Generate Themes (",O.length,"/",E.length,")"]})]})]}),e.jsx("div",{className:"text-right font-bold text-[14px] md:text-[16px] text-[#373A4B]",children:"50% completed"}),e.jsxs("div",{className:" mt-[2rem] mx-auto p-8 bg-white",children:[e.jsxs("div",{className:"flex flex-col-reverse gap-[1rem] md:gap-0 md:flex-row md:justify-between md:items-center mb-8",children:[e.jsxs("div",{className:"text-lg",children:[e.jsx("span",{className:"font-medium",children:"Theme title: "}),e.jsx("span",{children:C})]}),e.jsxs("div",{className:"flex items-center gap-2 cursor-pointer",onClick:R,children:[e.jsx("img",{src:W,alt:"document download"}),e.jsx("span",{className:"text-[#111928] font-medium text-[12px]",children:"PDF Preview"})]})]}),e.jsxs("div",{className:"border rounded-lg p-6 mb-8",children:[e.jsx("h2",{className:"text-lg font-medium mb-4",children:"Tell me a time when you made a MISTAKE how did you fix it? (Eaves Cleaning Mistake)"}),e.jsxs("div",{className:"mb-4",children:[e.jsx("h3",{className:"font-normal mb-2 text-[16px]",children:"Situation:"}),e.jsxs("ul",{className:"space-y-3 text-[16px] text-[#111928] leading-[24px] mt-[1rem] list-disc pl-5",children:[e.jsx("li",{children:"In the Fall my business, Tiger Building Services, does a lot of eavestrough cleaning."}),e.jsx("li",{children:"Back in 2019 I was working with an employee in my truck. We were working nicely to hit my daily revenue target."}),e.jsx("li",{children:"We got to the last job of the day; we were tired and running out of sunlight. But I really wanted to squeeze it in."}),e.jsx("li",{children:"We have procedures to follow in order to work safely and effectively. My goal is to be as low impact as possible."}),e.jsx("li",{children:"They were livid. Swearing and completely unhappy with how we were doing the work. I take ownership of my mistakes and realized I screwed up by using blowers instead of hand bombing it."})]})]})]}),e.jsx("div",{className:"space-y-4",children:e.jsxs("div",{className:"flex flex-col-reverse md:flex-row justify-end gap-[1rem] md:pl-[3rem]",children:[e.jsx("button",{className:"px-6 py-2 border border-blue-600 text-[#054FB1] rounded hover:bg-blue-50 transition-colors text-[16px] font-medium md:w-[313px] w-full h-[50px]",children:e.jsx(m,{to:"/user/onboarding/allquizzes",children:"VIEW ALL GENERATED QUIZZES"})}),e.jsxs("div",{className:"relative",children:[e.jsx("button",{onClick:()=>!f&&p(!j),className:"px-6 py-2 bg-[#054FB1] text-white rounded hover:bg-blue-700 transition-colors font-medium text-[16px] h-[50px] flex items-center w-full md:w-autojustify-center gap-[0.75rem]",disabled:f,children:f?e.jsxs(e.Fragment,{children:[e.jsxs("svg",{className:"animate-spin h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[e.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),e.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"GENERATING..."]}):e.jsxs(e.Fragment,{children:[e.jsx("img",{src:J,alt:"gem image"})," GENERATE QUIZ"]})}),j&&e.jsx("div",{className:"absolute right-0 bottom-[calc(100%+8px)] w-64 bg-white border rounded-lg shadow-lg z-50",children:B.map(t=>e.jsx("div",{onClick:()=>{F(t),p(!1),T()},className:"w-full px-4 py-3 text-left cursor-pointer hover:bg-gray-50 text-[14px] first:rounded-t-lg last:rounded-b-lg border-b border-gray-100 last:border-b-0",children:t.name},t.id))})]})]})})]})]})}),k&&e.jsx(Y,{onClose:()=>h(!1),onUpgrade:U})]})};export{Ae as default};

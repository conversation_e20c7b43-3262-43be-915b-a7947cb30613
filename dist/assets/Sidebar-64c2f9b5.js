import{j as e}from"./@react-google-maps/api-4794cf1a.js";import{R as a,r as i,L as y}from"./vendor-dd4ba10b.js";import{M as N,G as k,A as L,t as u}from"./index-3efdd896.js";import{L as E}from"./logo-a4db6296.js";import"./react-confirm-alert-5d5c0db6.js";import"./html2pdf.js-19c9759c.js";import"./@headlessui/react-7b0d4887.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-1762f471.js";import"./@fortawesome/react-fontawesome-0b111e8e.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";let d=new N;const M=()=>{const{state:{isOpen:m},dispatch:p}=a.useContext(k),{state:l,dispatch:c}=a.useContext(L),r=l==null?void 0:l.user;a.useState(!1),a.useState(!1);const[w,b]=i.useState([]),[o,j]=i.useState(null),[x,h]=i.useState(()=>{const t=localStorage.getItem(`completedThemes_${r}`);return t?JSON.parse(t):[]}),[,g]=i.useState({});i.useEffect(()=>{const t=()=>{const n=localStorage.getItem(`completedThemes_${r}`);n&&(h(JSON.parse(n)),g({}))},s=()=>{const n=localStorage.getItem(`completedThemes_${r}`);n&&(h(JSON.parse(n)),g({}))};return window.addEventListener("storage",t),document.addEventListener("themeCompleted",s),t(),()=>{window.removeEventListener("storage",t),document.removeEventListener("themeCompleted",s)}},[r]),a.useEffect(()=>{(async()=>{try{d.setTable("themes");const s=await d.callRestAPI({},"GETALL");s.list&&(b(s.list),localStorage.setItem(`allThemes_${r}`,JSON.stringify(s.list)))}catch(s){console.error("Error fetching themes:",s),u(c,s.message)}})()},[r]);const v=t=>{j(t),localStorage.setItem(`selectedTheme_${r}`,JSON.stringify(t)),window.dispatchEvent(new Event("themesUpdated"))};let f=t=>{p({type:"OPEN_SIDEBAR",payload:{isOpen:t}})};return a.useEffect(()=>{async function t(){try{const s=await d.getProfile();c({type:"UPDATE_PROFILE",payload:s})}catch(s){console.log("Error",s),u(c,s.response.data.message?s.response.data.message:s.message)}}t()},[]),e.jsxs(e.Fragment,{children:[!m&&e.jsx("div",{className:"z-50 hidden lg:flex h-screen py-4 fixed",children:e.jsx("div",{className:"px-4",children:e.jsx("div",{className:"flex items-center gap-6 text-white",children:e.jsx("button",{onClick:()=>f(!0),className:"bg-[#054FB1] w-[20px] h-[20px] hover:bg-[#0441A3]",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M7.5 5L12.5 10L7.5 15",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})})})})}),m&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"z-50 flex flex-col bg-white py-4 text-[#A8A8A8] fixed h-screen w-[318px] min-w-[318px] max-w-[318px] overflow-y-auto [&::-webkit-scrollbar]:w-[2px] [&::-webkit-scrollbar-thumb]:bg-gray-100 hover:[&::-webkit-scrollbar-thumb]:bg-gray-200 [&::-webkit-scrollbar-track]:bg-transparent",children:[e.jsx("div",{className:"text-[#393939] flex w-full",children:e.jsx("div",{className:"flex gap-2 w-full px-[2rem]",children:e.jsxs(y,{to:"/user/dashboard",className:"flex items-center gap-2",children:[e.jsx("img",{src:E,alt:"Logo",className:"h-[29px] w-[22px]"}),e.jsx("p",{className:"text-[20px] font-semibold leading-[28px] text-[#020617]",children:"Rescue Career Academy"})]})})}),e.jsx("div",{className:"px-4 py-2 border-b border-gray-100",children:e.jsxs("div",{className:"flex items-center gap-6 text-white",children:[e.jsx("button",{onClick:()=>f(!1),className:"bg-[#054FB1] w-[20px] h-[20px] mt-[1rem] hover:bg-[#0441A3]",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M12.5 5L7.5 10L12.5 15",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})}),e.jsx("span",{className:"font-medium text-[16px] mt-[1rem] text-[#373A4B]",children:"Create my answers"})]})}),e.jsx("div",{className:"px-4 py-2",children:w.map(t=>e.jsxs("div",{className:"flex items-center justify-between py-2 cursor-pointer",onClick:()=>v(t),children:[e.jsx("span",{className:"text-[#373A4B] text-base",children:t.name}),e.jsx("div",{className:`w-5 h-5 rounded-full flex items-center justify-center transition-colors
                    ${x.includes(t.id)?"bg-green-500":(o==null?void 0:o.id)===t.id?"bg-[#054FB1]":"bg-gray-200"}`,children:(x.includes(t.id)||(o==null?void 0:o.id)===t.id)&&e.jsx("svg",{width:"12",height:"12",viewBox:"0 0 12 12",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"text-white",children:e.jsx("path",{d:"M10 3L4.5 8.5L2 6",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})})]},t.id))})]}),e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden",onClick:()=>p({type:"OPEN_SIDEBAR",payload:{isOpen:!1}})})]})]})};export{M as Sidebar,M as default};

import{j as e}from"./@react-google-maps/api-4794cf1a.js";import{h as w,R as p,r as i}from"./vendor-dd4ba10b.js";import{A as g,G as b,M as j,s as n,t as z}from"./index-3efdd896.js";import{B as N}from"./index.esm-c1b51ffb.js";import"./react-confirm-alert-5d5c0db6.js";import"./html2pdf.js-19c9759c.js";import"./@headlessui/react-7b0d4887.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-1762f471.js";import"./@fortawesome/react-fontawesome-0b111e8e.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";import"./react-icons-0b96c072.js";const I=()=>{const{id:o}=w(),{dispatch:c}=p.useContext(g),{dispatch:s}=p.useContext(b),[x,h]=i.useState(null),[u,f]=i.useState(!0),[d,y]=i.useState(null);return i.useEffect(()=>{s({type:"SETPATH",payload:{path:"quiz"}}),async function(){try{const t=new j;t.setTable("quiz");const r=await t.callRestAPI({id:Number(o)},"GET");if(console.log("Quiz Response:",r),!r.error&&r.model)try{let a;const l=JSON.parse(r.model.quiz);if(Array.isArray(l))a=l;else if(l.quiz_data)a=l.quiz_data.map(m=>({quiz:m.question,user_answer:m.user_answer||"",correct_answer:m.correct_answer||""}));else throw new Error("Unexpected quiz data format");h(a),y(r.model.quiz_type)}catch(a){console.error("Error parsing quiz data:",a),n(s,"Invalid quiz data format",4e3,"error")}else n(s,"Failed to fetch quiz",4e3,"error")}catch(t){console.error("Error fetching quiz:",t),n(s,t.message||"Failed to fetch quiz",4e3,"error"),z(c,t.message)}finally{f(!1)}}()},[o,c,s]),e.jsx(e.Fragment,{children:e.jsx("div",{className:"w-full text-7xl min-h-screen h-full text-gray-700 bg-[#E2E6EB] p-[1rem] md:pl-[3rem]",children:e.jsxs("div",{className:"w-[95%] md:w-[90%] mx-auto md:mx-0",children:[e.jsx("h1",{className:"text-[16px] md:text-[20px] text-[#111928] font-semibold mb-6",children:"Document title: Fire fighter exams 2025"}),e.jsxs("p",{className:"text-[#373A4B] text-[14px] md:text-[16px] font-bold text-right",children:["Quiz ",o," details"]}),e.jsx("div",{className:"bg-white border border-gray-200 rounded-lg overflow-hidden mt-[1rem] p-[2rem] pb-[4rem]",children:e.jsx("div",{className:"overflow-x-auto",style:{msOverflowStyle:"none",scrollbarWidth:"none",WebkitOverflowScrolling:"touch"},children:e.jsx("div",{style:{width:"100%",minWidth:"600px"},children:u?e.jsx("div",{className:"flex justify-center items-center py-8",children:e.jsx(N,{className:"animate-spin text-[#054FB1] text-4xl"})}):e.jsxs("table",{className:"w-full",children:[e.jsx("thead",{children:e.jsxs("tr",{className:"bg-[#E4E4E4]",children:[e.jsx("th",{className:"text-left py-3 px-4 text-sm font-semibold text-gray-700 min-w-[20rem] max-w-[20rem]",children:"Question asked"}),e.jsx("th",{className:"text-left py-3 px-4 text-sm font-semibold text-gray-700 min-w-[12rem] max-w-[20rem]",children:"Answer provided"}),e.jsx("th",{className:"text-left py-3 px-4 text-sm font-semibold text-gray-700 min-w-[12rem] max-w-[20rem]",children:"Correct answer"}),d!==1&&e.jsx("th",{className:"text-left py-3 px-4 text-sm font-semibold text-gray-700",children:"Status"})]})}),e.jsx("tbody",{children:x&&x.map((t,r)=>e.jsxs("tr",{className:`${r%2===0?"bg-white hover:bg-[#efefefb8]":"bg-[#EFEFEF]"}`,children:[e.jsx("td",{className:"py-3 px-4 text-sm text-gray-900",children:t.quiz}),e.jsx("td",{className:"py-3 px-4 text-sm text-gray-900",children:t.user_answer}),e.jsx("td",{className:"py-3 px-4 text-sm text-gray-900",children:t.correct_answer}),d!==1&&e.jsx("td",{className:"py-3 px-4 text-sm",children:e.jsx("span",{className:`px-2 py-1 rounded ${t.user_answer===t.correct_answer?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:t.user_answer===t.correct_answer?"Correct":"Incorrect"})})]},r))})]})})})})]})})})};export{I as default};

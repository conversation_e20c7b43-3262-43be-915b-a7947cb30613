import{j as t}from"./@react-google-maps/api-4794cf1a.js";import{R as N,b as k,r as c}from"./vendor-dd4ba10b.js";import{A as F,G as U,M as A,s as p,t as B}from"./index-3efdd896.js";import"./react-confirm-alert-5d5c0db6.js";import"./html2pdf.js-19c9759c.js";import"./@headlessui/react-7b0d4887.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-1762f471.js";import"./@fortawesome/react-fontawesome-0b111e8e.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";const ee=()=>{const{state:P,dispatch:S}=N.useContext(F),{dispatch:d}=N.useContext(U),v=k(),[r,f]=c.useState(0),[x,m]=c.useState({}),[j,_]=c.useState(()=>localStorage.getItem("quizType")||"1"),[g,R]=c.useState(()=>{const e=localStorage.getItem("generatedQuiz");return e?JSON.parse(e):null}),[z,y]=c.useState(!1),n=(g==null?void 0:g.quiz_data)||[],o=n[r],h=x[r]||"",Q=()=>{r>0&&f(e=>e-1)},E=()=>{r<n.length-1&&(f(e=>e+1),x[r+1]||m(e=>({...e,[r+1]:""})))},q=async()=>{var e;try{y(!0);const s=new A,u=localStorage.getItem("mergedPdfUrl"),w=localStorage.getItem("quizId"),b=localStorage.getItem("generatedQuiz");console.log("Stored Quiz ID:",w),console.log("Generated Quiz Data:",b);let i=w;if(!i&&b)try{const a=JSON.parse(b);i=a.id||((e=a.data)==null?void 0:e.id),console.log("Retrieved Quiz ID from generatedQuiz:",i)}catch(a){console.error("Error parsing generatedQuiz:",a)}if(!i){p(d,"Quiz ID not found. Please regenerate the quiz.",4e3,"error");return}s.setTable("quiz");const C=n.map((a,D)=>({quiz:a.question,correct_answer:a.correct_answer||"",user_answer:x[D]||""}));console.log("Final Quiz ID being used:",i);const I={id:parseInt(i),quiz:JSON.stringify(C),update_at:new Date().toISOString().slice(0,19).replace("T"," ")};console.log("Quiz Update Payload:",I);const l=await s.callRestAPI(I,"PUT");if(!l||l.error){p(d,(l==null?void 0:l.message)||"Failed to save quiz",4e3,"error");return}localStorage.removeItem("generatedQuiz"),localStorage.removeItem("quizType"),localStorage.removeItem("quizId"),p(d,"Quiz submitted successfully!",4e3,"success"),v("/user/onboarding/allquizzes")}catch(s){console.error("Error submitting quiz:",s),p(d,s.message||"Failed to submit quiz",4e3,"error"),B(S,s.message)}finally{y(!1)}},T=()=>{if(!o)return null;switch(j){case"1":return t.jsx("input",{type:"text",placeholder:"Type your response here",value:h,onChange:e=>m(s=>({...s,[r]:e.target.value})),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"});case"2":return t.jsx("div",{className:"space-y-2",children:o.options.map((e,s)=>t.jsxs("div",{className:"flex items-center",children:[t.jsx("input",{type:"radio",name:"mcq",id:`option-${s}`,checked:h===e,onChange:()=>m(u=>({...u,[r]:e})),className:"mr-2"}),t.jsx("label",{htmlFor:`option-${s}`,className:"text-[16px]",children:e})]},s))});case"3":return t.jsx("div",{className:"space-x-4",children:o.options.map((e,s)=>t.jsxs("label",{className:"inline-flex items-center mr-4 text-[16px]",children:[t.jsx("input",{type:"radio",name:"tf",checked:h===e,onChange:()=>m(u=>({...u,[r]:e})),className:"mr-2"}),e]},s))});default:return null}};return t.jsx(t.Fragment,{children:t.jsx("div",{className:"w-full text-7xl h-screen text-gray-700 bg-[#1E1E1E] md:bg-[#E2E6EB] p-[1rem] md:pl-[3rem] overflow-y-auto fixed md:static top-0 left-0",children:t.jsxs("div",{className:"w-[95%] md:w-[90%] mx-auto md:mx-0 pb-[120px] md:pb-0",children:[t.jsx("h1",{className:"text-[20px] mt-[1rem] md:mt-0 text-white md:text-[#111928] font-semibold mb-6",children:"Document title: Fire fighter 2024"}),t.jsx("div",{className:"bg-[#1E1E1E] rounded-lg mb-6 py-[2rem] md:py-[5rem] relative min-h-[calc(100vh-200px)] md:min-h-0 flex md:block items-center",children:t.jsxs("div",{className:"bg-white rounded-lg p-4 md:p-8 mb-6 xl:w-[665px] md:w-[90%] w-[95%] mx-auto",children:[t.jsx("div",{className:"flex justify-between items-start mb-4",children:t.jsxs("h2",{className:"text-[20px] font-semibold text-[#111928] mt-[1rem] md:mt-[3rem]",children:["Question #",r+1]})}),t.jsx("p",{className:"text-[#373A4B] text-[16px] leading-[24px] mb-6",children:(o==null?void 0:o.question)||"Loading question..."}),T()]})}),t.jsxs("div",{className:"fixed bottom-[20px] left-0 right-0 p-4 bg-[#1E1E1E] md:bg-transparent md:p-0 md:static flex flex-col-reverse md:flex-row gap-[1rem] md:gap-0 justify-between items-center",children:[t.jsx("button",{onClick:Q,disabled:r===0,className:"px-6 py-2 border border-blue-600 text-[#054FB1] bg-white rounded hover:bg-blue-50 transition-colors text-[16px] font-medium w-full md:w-[201px] h-[50px] disabled:opacity-50",children:"PREVIOUS"}),t.jsx("button",{onClick:r===n.length-1?q:E,disabled:z,className:"px-6 py-2 bg-[#054FB1] text-white rounded hover:bg-blue-700 transition-colors text-[16px] font-medium w-full md:w-[201px] h-[50px]",children:z?"SUBMITTING...":r===n.length-1?"SUBMIT QUIZ":"NEXT"})]})]})})})};export{ee as default};

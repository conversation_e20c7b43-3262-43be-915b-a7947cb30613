import{j as e}from"./@react-google-maps/api-4794cf1a.js";import{R as I,b as $,u as L,r as i,L as b}from"./vendor-dd4ba10b.js";import{A as U,G as M,M as R,s as p,t as A}from"./index-3efdd896.js";import{D as J}from"./document-download-151d604a.js";import{M as P}from"./monitor-8a3dd572.js";import"./jspdf-90209ad1.js";import"./react-confirm-alert-5d5c0db6.js";import"./html2pdf.js-19c9759c.js";import"./@headlessui/react-7b0d4887.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-1762f471.js";import"./@fortawesome/react-fontawesome-0b111e8e.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";const ne=()=>{const{dispatch:E,state:u}=I.useContext(U),{dispatch:c}=I.useContext(M),m=$(),j=L(),{themeTitle:x,initialQuestion:k,userResponses:F,theme_id:f}=j.state||{},s=u==null?void 0:u.user,n=localStorage.getItem("theme_documents_id");console.log("themedocs",n);const[l]=i.useState(()=>{const t=localStorage.getItem(`allThemes_${s}`);return t?JSON.parse(t):[]});i.useState(()=>{const t=localStorage.getItem(`selectedTheme_${s}`);return t?JSON.parse(t):null});const[w,z]=i.useState(()=>{const t=localStorage.getItem(`generatedThemes_${s}`);return t?JSON.parse(t):[]}),[g,v]=i.useState(!1),[N]=i.useState(()=>{const t=localStorage.getItem(`completedThemes_${s}`);return t?JSON.parse(t):[]}),T=async()=>{const t=JSON.parse(localStorage.getItem(`completedThemes_${s}`)||"[]"),r=l.filter(o=>!t.includes(o.id));if(r.length===0){try{const o=new R,B=w.map(d=>({id:d.id,name:d.name,url:d.pdfUrl})),C=await o.callRawAPI("/v3/api/custom/theme/merge",{pdfLinks:w.map(d=>d.pdfUrl),generated_themes:JSON.stringify(B),theme_documents_id:n},"POST");C.error?p(c,"Failed to merge documents",4e3,"error"):(localStorage.setItem(`mergedPdfUrl_${s}`,C.data),localStorage.removeItem(`generatedThemes_${s}`),m("/user/onboarding/generatequiz"))}catch(o){console.error("Merge error:",o),p(c,o.message||"Failed to merge documents",4e3,"error"),A(E,o.message)}return}const a=l.findIndex(o=>o.id===f),S=l.slice(a+1).find(o=>!t.includes(o.id))||r[0];localStorage.setItem(`selectedTheme_${s}`,JSON.stringify(S)),m("/user/onboarding/themecontd",{state:{theme_id:S.ds_theme_id,theme_documents_id:n}})},_=()=>{const t=JSON.parse(localStorage.getItem(`selectedTheme_${s}`)),a=JSON.parse(localStorage.getItem(`completedThemes_${s}`)||"[]").filter(y=>y!==f);localStorage.setItem(`completedThemes_${s}`,JSON.stringify(a)),document.dispatchEvent(new Event("themeCompleted")),m("/user/onboarding/themecontd",{state:{theme_id:t.ds_theme_id,theme_documents_id:n,isRegenerating:!0}})},D=l.findIndex(t=>t.id===f),h=l.slice(D+1).find(t=>!N.includes(t.id))||l.find(t=>!N.includes(t.id)),O=async()=>{try{const{pdfUrl:t}=j.state||{};if(!t){p(c,"PDF URL not found",4e3,"error");return}const r=document.createElement("a");r.href=t,r.target="_blank";const a=x?`${x.toLowerCase().replace(/\s+/g,"_")}_preview.pdf`:"theme_preview.pdf";r.download=a,document.body.appendChild(r),r.click(),document.body.removeChild(r)}catch(t){console.error("PDF download error:",t),p(c,"Failed to download PDF",4e3,"error")}};return e.jsx(e.Fragment,{children:e.jsx("div",{className:"w-full text-7xl min-h-screen h-full text-gray-700 bg-[#E2E6EB] p-[1rem] md:pl-[3rem]",children:e.jsxs("div",{className:"w-[95%] md:w-[90%] mx-auto md:mx-0",children:[e.jsx("h1",{className:"text-[20px] text-[#111928] font-semibold mb-6",children:"Generate themes document"}),e.jsxs("div",{className:"flex items-center justify-between mb-8 relative bg-white h-[98px] p-[1rem]",children:[e.jsx("div",{className:"absolute left-8 right-8 top-1/4 md:top-1/3 h-0.5 bg-gray-200 w-[100% - 2rem]"}),e.jsxs("div",{className:"flex flex-col items-start z-10 w-[28%]",children:[e.jsx("div",{className:"w-10 h-10 border border-[#9C9C9C] bg-white rounded-full flex items-center justify-center",children:e.jsx("div",{className:"w-6 h-6 bg-[#389C14] rounded-full flex items-center  justify-center",children:e.jsx("svg",{className:"w-4 h-4 text-white",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"})})})}),e.jsx(b,{to:"/user/onboarding",className:"text-xs md:text-sm hover:text-[#054FB1] transition-colors",children:"Upload your resume & full history docs"})]}),e.jsxs("div",{className:"flex flex-col items-center z-10 w-[24%]",children:[e.jsx("div",{className:"w-10 h-10 border border-[#9C9C9C] bg-white rounded-full flex items-center justify-center",children:e.jsx("div",{className:"w-6 h-6 bg-[#389C14] rounded-full flex items-center  justify-center",children:e.jsx("svg",{className:"w-4 h-4 text-white",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"})})})}),e.jsx(b,{to:"/user/onboarding/form",className:"text-xs md:text-sm hover:text-[#054FB1] transition-colors",children:"Fill out questions form"})]}),e.jsxs("div",{className:"flex flex-col items-end z-10 w-[24%]",children:[e.jsx("div",{className:"w-8 h-8 bg-[#054FB1] rounded-full flex items-center justify-center text-gray-500 mb-2",children:e.jsx("img",{src:P,className:"w-[20px] h-[20px]",alt:"upload icon"})}),e.jsxs(b,{to:"/user/onboarding/theme",className:"text-xs md:text-sm text-[#054FB1]",children:["Generate Themes (",N.length,"/",l.length,")"]})]})]}),e.jsx("div",{className:"text-right font-bold text-[14px] md:text-[16px] text-[#373A4B]",children:"50% completed"}),e.jsxs("div",{className:" mt-[2rem] mx-auto p-8 bg-white",children:[e.jsxs("div",{className:"flex flex-col-reverse gap-[1rem] md:gap-0 md:flex-row md:justify-between md:items-center mb-8",children:[e.jsxs("div",{className:"text-lg",children:[e.jsx("span",{className:"font-medium",children:"Theme title: "}),e.jsx("span",{children:x})]}),e.jsxs("button",{onClick:O,className:"flex items-center gap-2 cursor-pointer hover:opacity-80",children:[e.jsx("img",{src:J,alt:"document download"}),e.jsx("span",{className:"text-[#111928] font-medium text-[12px]",children:"PDF Preview"})]})]}),e.jsxs("div",{className:"border rounded-lg p-6 mb-8",children:[e.jsx("h2",{className:"text-lg font-medium mb-4",children:k}),e.jsxs("div",{className:"mb-4",children:[e.jsx("h3",{className:"font-normal mb-2 text-[16px]",children:"Responses:"}),e.jsx("ul",{className:"space-y-3 text-[16px] text-[#111928] leading-[24px] mt-[1rem] list-disc pl-5",children:F.map((t,r)=>e.jsx("li",{children:t},r))})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex justify-between items-start",children:[e.jsxs("div",{className:"flex flex-col gap-1",children:[e.jsx("p",{className:"text-[#111928] font-semibold text-[16px] leading-[28px]",children:"Not satisfied with the response?"}),e.jsxs("p",{className:"text-[#373A4B] leading-[24px] text-[16px]",children:[x," ",e.jsx("span",{className:"md:hidden text-blue-700 underline cursor-pointer",onClick:_,children:"Click here"})]})]}),e.jsx("button",{onClick:T,className:"px-6 py-2 bg-[#054FB1] text-white rounded hover:bg-blue-700 transition-colors font-medium text-[16px] h-[50px] hidden md:flex justify-center items-center",children:h?`CONTINUE TO ${h.name}`:"SUBMIT AND GENERATE FINAL DOCUMENT"})]}),e.jsx("button",{onClick:T,className:"px-6 py-2 bg-[#054FB1] text-white rounded hover:bg-blue-700 transition-colors font-medium text-sm md:text-[16px] h-[50px] md:hidden",children:h?`CONTINUE TO ${h.name}`:"SUBMIT AND GENERATE FINAL DOCUMENT"}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("button",{onClick:_,className:"px-6 py-2 border border-[#054FB1] text-[#054FB1] text-[16px] font-medium h-[50px] rounded hover:bg-blue-50 transition-colors hidden md:flex justify-center items-center",children:"CLICK HERE"}),e.jsxs("div",{className:"relative w-full md:w-auto",children:[e.jsxs("button",{onClick:()=>v(!g),className:"px-6 py-2 border rounded flex items-center gap-2 text-[16px] h-[48px] w-[100%] md:w-[184px] text-[#637381]",children:["Switch theme",e.jsx("svg",{className:`w-4 h-4 transform transition-transform ${g?"rotate-180":""}`,viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z",clipRule:"evenodd"})})]}),g&&e.jsx("div",{className:"absolute right-0 bottom-[calc(100%+8px)] w-full md:w-64 bg-white border rounded-lg shadow-lg z-50 max-h-[200px] overflow-y-auto",children:l.map(t=>e.jsxs("button",{onClick:()=>{localStorage.setItem(`selectedTheme_${s}`,JSON.stringify(t)),m("/user/onboarding/themecontd",{state:{theme_id:t.ds_theme_id,theme_documents_id:n}}),v(!1)},className:"w-full px-4 py-2 text-left hover:bg-gray-50 flex items-center justify-between border-b border-gray-100 last:border-b-0",children:[e.jsx("span",{className:"text-[#373A4B] text-[14px]",children:t.name}),JSON.parse(localStorage.getItem(`completedThemes_${s}`)||"[]").includes(t.id)&&e.jsx("svg",{className:"w-4 h-4 text-green-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})]},t.id))})]})]})]})]})]})})})};export{ne as default};

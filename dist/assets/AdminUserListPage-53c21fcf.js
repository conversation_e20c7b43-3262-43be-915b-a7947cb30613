import{j as e}from"./@react-google-maps/api-4794cf1a.js";import{R as r,b as ce}from"./vendor-dd4ba10b.js";import{M as de,G as ue,A as pe,a as L,o as me,E as xe,t as O,g as he}from"./index-3efdd896.js";import{o as ge}from"./yup-f7f8305f.js";import{u as fe}from"./react-hook-form-a6ecef1c.js";import{c as je,a as d}from"./yup-79911193.js";import{P as be}from"./index-fe50f63a.js";import{A as ye}from"./AddButton-bf2721f5.js";import{S as ve}from"./index-3b0c955b.js";import{a as we}from"./index.esm-c1b51ffb.js";import{A as Ne}from"./index.esm-8010d0dc.js";import{R as Se}from"./index.esm-3f4ea327.js";import"./react-confirm-alert-5d5c0db6.js";import"./html2pdf.js-19c9759c.js";import"./@headlessui/react-7b0d4887.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-1762f471.js";import"./@fortawesome/react-fontawesome-0b111e8e.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";import"./@hookform/resolvers-61c06af0.js";import"./react-icons-0b96c072.js";let u=new de;const p=[{header:"Email",accessor:"email"},{header:"Role",accessor:"role"},{header:"Status",accessor:"status",mapping:["Inactive","Active","Suspend"]},{header:"Action",accessor:""}],Xe=()=>{const{dispatch:I}=r.useContext(ue),{dispatch:S}=r.useContext(pe),[C,A]=r.useState([]),[o,E]=r.useState(10),[M,q]=r.useState(0),[Ce,z]=r.useState(0),[m,B]=r.useState(0),[U,G]=r.useState(!1),[_,H]=r.useState(!1),[F,P]=r.useState(!1),[k,R]=r.useState(!1),[l,x]=r.useState([]),[K,h]=r.useState([]),[Y,J]=r.useState("eq"),[g,f]=r.useState(!0),[Q,j]=r.useState(!1),[V,b]=r.useState(!1),[W,X]=r.useState();ce();const y=r.useRef(null),Z=je({id:d(),email:d(),role:d(),status:d()}),{register:Ae,handleSubmit:ee,formState:{errors:Ee}}=fe({resolver:ge(Z)});function te(){c(m-1,o)}function se(){c(m+1,o)}const ae=(t,a,s)=>{const i=a==="eq"&&isNaN(s)?`${s}`:s,n=`${t},${a},${i}`;h(v=>[...v.filter(w=>!w.includes(t)),n])},re=()=>{c(0,o,{},K)},ie=t=>{c(0,o,{},t)};async function c(t,a,s={},i=[]){f(!0);try{u.setTable("user");const n=await u.callRestAPI({payload:{...s},page:t,limit:a,filter:i},"PAGINATE");n&&f(!1);const{list:v,total:T,limit:w,num_pages:$,page:N}=n;A(v),E(w),q($),B(N),z(T),G(N>1),H(N+1<=$)}catch(n){f(!1),console.log("ERROR",n),O(S,n.message)}}const ne=t=>{const a=p.filter(s=>s.accessor).map(s=>{const i=he(t[s.accessor]);return i?`${s.accessor},cs,${i}`:null}).filter(Boolean);c(0,o,{},a)};r.useEffect(()=>{I({type:"SETPATH",payload:{path:"users"}});const a=setTimeout(async()=>{await c(1,o)},700);return()=>{clearTimeout(a)}},[]);const D=t=>{y.current&&!y.current.contains(t.target)&&P(!1)};r.useEffect(()=>(document.addEventListener("mousedown",D),()=>{document.removeEventListener("mousedown",D)}),[]);const oe=()=>{x([]),h([]),c(1,o)},le=async t=>{try{A(a=>a.filter(s=>s.id!==t)),u.setTable("user"),await u.callRestAPI({id:t},"DELETE")}catch(a){console.log("ERROR",a),O(S,a.message)}};return e.jsxs("div",{className:"px-8",children:[e.jsxs("div",{className:"flex items-center justify-between py-3",children:[e.jsx("form",{className:"relative rounded bg-white",onSubmit:ee(ne),children:e.jsx("div",{className:"flex items-center gap-4 text-gray-700 text-nowrap",children:e.jsxs("div",{className:"relative",ref:y,children:[e.jsxs("div",{className:"flex cursor-pointer items-center justify-between gap-3 rounded-md border border-gray-200 px-3 py-1",onClick:()=>P(!F),children:[e.jsx(we,{}),e.jsx("span",{children:"Filters"}),l.length>0&&e.jsx("span",{className:"flex h-6 w-6 items-center justify-center rounded-full bg-gray-800 text-start text-white",children:l.length})]}),F&&e.jsx("div",{className:"absolute z-10 mt-4 w-[500px] min-w-[90%] top-fill left-0 filter-form-holder bg-white border border-gray-200 rounded-md shadow-lg",children:e.jsxs("div",{className:"p-4",children:[l==null?void 0:l.map((t,a)=>e.jsxs("div",{className:"mb-2 flex w-full items-center justify-between gap-3 text-gray-600",children:[e.jsx("div",{className:" mb-3 w-1/3 rounded-md border border-gray-300 px-3 py-2 leading-tight text-gray-700 outline-none",children:t}),e.jsxs("select",{className:"mb-3 w-[40%] rounded-md border px-3 py-2 leading-tight text-gray-700 outline-none",onChange:s=>{J(s.target.value)},children:[e.jsx("option",{value:"eq",selected:!0,children:"equals"}),e.jsx("option",{value:"cs",children:"contains"}),e.jsx("option",{value:"sw",children:"start with"}),e.jsx("option",{value:"ew",children:"ends with"}),e.jsx("option",{value:"lt",children:"lower than"}),e.jsx("option",{value:"le",children:"lower or equal"}),e.jsx("option",{value:"ge",children:"greater or equal"}),e.jsx("option",{value:"gt",children:"greater than"}),e.jsx("option",{value:"bt",children:"between"}),e.jsx("option",{value:"in",children:"in"}),e.jsx("option",{value:"is",children:"is null"})]}),e.jsx("input",{type:"text",placeholder:"Enter value...",className:" mb-3 w-1/3 rounded-md border px-3 py-2 leading-tight text-gray-700 outline-none",onChange:s=>ae(t,Y,s.target.value)}),e.jsx("div",{className:"w-1/12 mt-[-10px]",children:e.jsx(Se,{className:" cursor-pointer text-xl",onClick:()=>{x(s=>s.filter(i=>i!==t)),h(s=>{const i=s.filter(n=>!n.includes(t));return ie(i),i})}})})]},a)),e.jsxs("div",{className:"search-buttons relative flex items-center justify-between font-semibold",children:[e.jsxs("div",{className:"mr-2 flex w-auto cursor-pointer items-center gap-2 rounded bg-gray-100 px-4 py-2.5 font-medium leading-tight text-gray-600 transition duration-150 ease-in-out ",onClick:()=>{R(!k)},children:[e.jsx(Ne,{}),"Add filter"]}),k&&e.jsx("div",{className:"absolute top-11 z-10 bg-white px-5 py-3 text-gray-600 shadow-md",children:e.jsx("ul",{className:"flex flex-col gap-2 text-gray-500",children:p.slice(0,-1).map(t=>e.jsx("li",{className:`${l.includes(t.accessor)?"cursor-not-allowed text-gray-400":"cursor-pointer"}`,onClick:()=>{l.includes(t.header)||x(a=>[...a,t.header]),R(!1)},children:t.header},t.header))})}),l.length>0&&e.jsx("div",{onClick:oe,className:"inline-block cursor-pointer  rounded px-6  py-2.5 font-medium leading-tight text-gray-600  transition duration-150 ease-in-out",children:"Clear all filter"})]}),e.jsx("button",{type:"button",onClick:re,className:"mt-4 inline-block cursor-pointer rounded bg-blue-500 px-6 py-2.5 font-medium leading-tight text-white transition duration-150 ease-in-out",children:"Apply Filters"})]})})]})})}),e.jsx(ye,{onClick:()=>b(!0)})]}),g?e.jsx(ve,{}):e.jsxs("div",{className:"overflow-x-auto border-b border-gray-200 shadow ",children:[e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsx("tr",{children:p.map((t,a)=>e.jsxs("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500",children:[t.header,e.jsx("span",{children:t.isSorted?t.isSortedDesc?" ▼":" ▲":""})]},a))})}),e.jsx("tbody",{className:"divide-y divide-gray-200 bg-white",children:C.map((t,a)=>e.jsx("tr",{children:p.map((s,i)=>s.accessor==""?e.jsxs("td",{className:"whitespace-nowrap px-6 py-4",children:[e.jsxs("button",{className:"text-[#4F46E5]",onClick:()=>{X(t.id),j(!0)},children:[" ","Edit"]}),e.jsx("button",{className:"text-red-500 ml-2",onClick:()=>le(t.id),children:"Delete"})]},i):s.mapping&&s.accessor==="status"?e.jsx("td",{className:"px-6 py-5 whitespace-nowrap inline-block text-sm",children:t[s.accessor]===1?e.jsx("span",{className:"bg-[#D1FAE5] rounded-md py-1 px-3 text-[#065F46]",children:s.mapping[t[s.accessor]]}):e.jsx("span",{className:"bg-[#F4F4F4] rounded-md py-1 px-3 text-[#393939]",children:s.mapping[t[s.accessor]]})},i):s.mapping?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:s.mapping[t[s.accessor]]},i):e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:t[s.accessor]},i))},a))})]}),g&&e.jsx(e.Fragment,{children:e.jsx("p",{className:" px-10 py-3 text-xl capitalize ",children:"Loading..."})}),!g&&C.length===0&&e.jsx(e.Fragment,{children:e.jsx("p",{className:" px-10 py-3 text-xl capitalize ",children:"You Don't have any User"})})]}),e.jsx(be,{currentPage:m,pageCount:M,pageSize:o,canPreviousPage:U,canNextPage:_,updatePageSize:t=>{E(t),c(1,t)},previousPage:te,nextPage:se}),e.jsx(L,{isModalActive:V,closeModalFn:()=>b(!1),children:e.jsx(me,{setSidebar:b})}),e.jsx(L,{isModalActive:Q,closeModalFn:()=>j(!1),children:e.jsx(xe,{activeId:W,setSidebar:j})})]})};export{Xe as default};

import{j as s}from"./@react-google-maps/api-4794cf1a.js";import{r as L,R as E,L as S}from"./vendor-dd4ba10b.js";import{u as k}from"./react-hook-form-a6ecef1c.js";import{o as R}from"./yup-f7f8305f.js";import{c as F,a as A}from"./yup-79911193.js";import{G as C,M as I,s as q,t as G}from"./index-3efdd896.js";import{I as M}from"./InteractiveButton-6ddb3b9d.js";import{L as P}from"./logo-a4db6296.js";import{L as _}from"./AI image-fab52c2e.js";import"./@hookform/resolvers-61c06af0.js";import"./react-confirm-alert-5d5c0db6.js";import"./html2pdf.js-19c9759c.js";import"./@headlessui/react-7b0d4887.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-1762f471.js";import"./@fortawesome/react-fontawesome-0b111e8e.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";import"./MoonLoader-795b8e10.js";const le=()=>{const[l,a]=L.useState(!1),b=F({email:A().email().required()}).required(),{register:j,handleSubmit:w,setError:i,formState:{errors:t}}=k({resolver:R(b)}),{dispatch:o}=E.useContext(C),v=async y=>{var r,n,c,d,p,x,g,u;let N=new I;try{a(!0);const e=await N.forgot(y.email);if(!e.error)q(o,"Reset Code Sent");else if(e.validation){const h=Object.keys(e.validation);for(let m=0;m<h.length;m++){const f=h[m];i(f,{type:"manual",message:e.validation[f]})}}a(!1)}catch(e){a(!1),console.log("Error",e),i("email",{type:"manual",message:(n=(r=e==null?void 0:e.response)==null?void 0:r.data)!=null&&n.message?(d=(c=e==null?void 0:e.response)==null?void 0:c.data)==null?void 0:d.message:e==null?void 0:e.message}),G(o,(x=(p=e==null?void 0:e.response)==null?void 0:p.data)!=null&&x.message?(u=(g=e==null?void 0:e.response)==null?void 0:g.data)==null?void 0:u.message:e==null?void 0:e.message)}};return s.jsx("div",{className:"flex flex-col items-center justify-center min-h-screen bg-[#FAFAFA] md:p-4",children:s.jsxs("div",{className:"flex flex-row w-full md:w-[90%] h-screen md:h-[80vh] lg:h-[70vh] xl:h-[80vh]",children:[s.jsx("div",{className:"w-full lg:w-[50%] flex items-center justify-center bg-white h-screen md:h-[80vh] lg:h-[70vh] xl:h-[80vh] rounded-lg shadow px-[2rem] md:px-[7rem] lg:px-[4rem] xl:px-[7rem] py-[2rem] rounded-[24px]]",children:s.jsxs("div",{className:"w-full max-w-md lg:max-w-none",children:[s.jsx("div",{className:"flex justify-center mb-6",children:s.jsx("img",{src:P,alt:"Logo",className:"w-[55px] h-[73px]"})}),s.jsxs("form",{onSubmit:w(v),className:"space-y-4",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm mb-1",children:"Email"}),s.jsx("input",{type:"email",placeholder:"Email",...j("email"),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none"}),(t==null?void 0:t.email)&&s.jsx("p",{className:"mt-1 text-xs text-red-500",children:t.email.message})]}),s.jsx(M,{type:"submit",loading:l,disabled:l,className:"w-full py-2 px-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 mt-6",children:"Reset Password"}),s.jsx("div",{className:"text-center mt-4",children:s.jsx(S,{to:"/user/login",className:"text-blue-600 text-sm",children:"Remember your password? Login"})})]})]})}),s.jsx("img",{src:_,alt:"login image",className:"w-[50%] hidden lg:flex"})]})})};export{le as default};

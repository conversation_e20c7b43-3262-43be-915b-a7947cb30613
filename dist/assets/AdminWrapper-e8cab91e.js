import{j as e}from"./@react-google-maps/api-4794cf1a.js";import{r}from"./vendor-dd4ba10b.js";import{a as s}from"./html2pdf.js-19c9759c.js";import{T as a}from"./index-292ff41b.js";import"./index-e6a343d0.js";const i=r.lazy(()=>s(()=>import("./AdminHeader-7312fd26.js"),["assets/AdminHeader-7312fd26.js","assets/@react-google-maps/api-4794cf1a.js","assets/vendor-dd4ba10b.js","assets/index.esm-c8f76a7c.js","assets/react-icons-0b96c072.js","assets/index-3efdd896.js","assets/react-confirm-alert-5d5c0db6.js","assets/html2pdf.js-19c9759c.js","assets/@headlessui/react-7b0d4887.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-1762f471.js","assets/@fortawesome/react-fontawesome-0b111e8e.js","assets/@fortawesome/fontawesome-svg-core-254ba2e2.js","assets/@fortawesome/free-solid-svg-icons-82da594a.js","assets/@fortawesome/free-regular-svg-icons-a38012c9.js","assets/@fortawesome/free-brands-svg-icons-2c021b6b.js","assets/index-5300e751.css","assets/index.esm-f1328d83.js"])),m=({children:l})=>e.jsx("div",{id:"admin_wrapper",className:"flex w-full max-w-full flex-col bg-white",children:e.jsxs("div",{className:"flex min-h-screen w-full max-w-full ",children:[e.jsx(i,{}),e.jsxs("div",{className:"mb-20 w-full overflow-hidden",children:[e.jsx(a,{}),e.jsx(r.Suspense,{fallback:e.jsx("div",{className:"flex h-screen w-full items-center justify-center"}),children:e.jsx("div",{className:"w-full overflow-y-auto overflow-x-hidden pt-[calc(3.5rem+2rem)]",children:l})})]})]})}),n=r.memo(m);export{n as default};

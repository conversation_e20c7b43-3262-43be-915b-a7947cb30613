import{j as e}from"./@react-google-maps/api-4794cf1a.js";import{R as a,h as d}from"./vendor-dd4ba10b.js";import"./yup-79911193.js";import{M as x,G as o,t as f}from"./index-3efdd896.js";import{S as u}from"./index-3b0c955b.js";import"./react-confirm-alert-5d5c0db6.js";import"./html2pdf.js-19c9759c.js";import"./@headlessui/react-7b0d4887.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-1762f471.js";import"./@fortawesome/react-fontawesome-0b111e8e.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";let m=new x;const V=()=>{a.useContext(o);const{dispatch:l}=a.useContext(o),[s,c]=a.useState({}),[n,r]=a.useState(!0),i=d();return a.useEffect(function(){(async function(){try{r(!0),m.setTable("packages");const t=await m.callRestAPI({id:Number(i==null?void 0:i.id),join:""},"GET");t.error||(c(t.model),r(!1))}catch(t){r(!1),console.log("error",t),f(l,t.message)}})()},[]),e.jsx("div",{className:" shadow-md rounded  mx-auto p-5",children:n?e.jsx(u,{}):e.jsxs(e.Fragment,{children:[e.jsx("h4",{className:"text-2xl font-medium",children:"View Packages"}),e.jsx("div",{className:"mb-4 mt-4",children:e.jsxs("div",{className:"flex mb-4",children:[e.jsx("div",{className:"flex-1",children:"Name"}),e.jsx("div",{className:"flex-1",children:s==null?void 0:s.name})]})}),e.jsx("div",{className:"mb-4 mt-4",children:e.jsxs("div",{className:"flex mb-4",children:[e.jsx("div",{className:"flex-1",children:"Features"}),e.jsx("div",{className:"flex-1",children:s==null?void 0:s.features})]})})]})})};export{V as default};

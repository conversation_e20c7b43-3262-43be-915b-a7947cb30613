import{j as s}from"./@react-google-maps/api-4794cf1a.js";import"./vendor-dd4ba10b.js";const m=({currentPage:t,pageCount:n,pageSize:a,canPreviousPage:l,canNextPage:d,updatePageSize:r,previousPage:i,nextPage:o})=>s.jsx(s.<PERSON>agment,{children:s.jsxs("div",{className:"flex justify-between ",children:[s.jsxs("div",{className:"mt-2",children:[s.jsxs("span",{children:["Page"," ",s.jsxs("strong",{children:[+t," of ",n]})," "]}),s.jsx("select",{className:"mt-2 h-8 max-h-8 !py-0 rounded-md",value:a,onChange:e=>{r(Number(e.target.value))},children:[5,10,20,30,40,50].map(e=>s.jsxs("option",{value:e,children:["Show ",e]},e))})]}),s.jsxs("div",{className:"mt-2",children:[s.jsx("button",{onClick:i,disabled:!l,className:"h-10 w-10 font-bold text-[25px]",children:"←"})," ",s.jsx("button",{onClick:o,disabled:!d,className:"h-10 w-10 font-bold text-[25px]",children:"→"})," "]})]})});export{m as default};

import{j as e}from"./@react-google-maps/api-4794cf1a.js";import{r}from"./vendor-dd4ba10b.js";import{a as l}from"./html2pdf.js-19c9759c.js";import"./index-e6a343d0.js";const t=r.lazy(()=>l(()=>import("./PublicHeader-a7d86023.js"),["assets/PublicHeader-a7d86023.js","assets/@react-google-maps/api-4794cf1a.js","assets/vendor-dd4ba10b.js","assets/index-3efdd896.js","assets/react-confirm-alert-5d5c0db6.js","assets/html2pdf.js-19c9759c.js","assets/@headlessui/react-7b0d4887.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-1762f471.js","assets/@fortawesome/react-fontawesome-0b111e8e.js","assets/@fortawesome/fontawesome-svg-core-254ba2e2.js","assets/@fortawesome/free-solid-svg-icons-82da594a.js","assets/@fortawesome/free-regular-svg-icons-a38012c9.js","assets/@fortawesome/free-brands-svg-icons-2c021b6b.js","assets/index-5300e751.css"])),a=({children:s})=>e.jsxs("div",{className:"flex w-full flex-col",children:[e.jsx(t,{}),e.jsx("div",{className:"min-h-screen grow",children:e.jsx(r.Suspense,{fallback:e.jsx("div",{className:"flex h-screen w-full items-center justify-center"}),children:s})})]}),n=r.memo(a);export{n as default};

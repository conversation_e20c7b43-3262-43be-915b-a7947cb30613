import{j as t}from"./@react-google-maps/api-4794cf1a.js";import{R as a,b as c}from"./vendor-dd4ba10b.js";import{X as u}from"./@uppy/xhr-upload-54a0071a.js";import{u as f,D as h}from"./@uppy/react-c6d54ea9.js";import{M as x,A as b,G as g,s as E}from"./index-3efdd896.js";import{a as j}from"./@uppy/core-e491152c.js";import"./@uppy/aws-s3-61b9ec5d.js";import"./@craftjs/core-3d3a3b40.js";import"./@uppy/aws-s3-multipart-d8008cbd.js";import"./@fortawesome/react-fontawesome-0b111e8e.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@uppy/dashboard-7f6fae24.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/compressor-dfa3f345.js";import"./@uppy/drag-drop-98694d6f.js";import"./@uppy/progress-bar-e3c7768f.js";import"./@uppy/file-input-a14c1f6c.js";import"./react-confirm-alert-5d5c0db6.js";import"./html2pdf.js-19c9759c.js";import"./@headlessui/react-7b0d4887.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-1762f471.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";let i=new x;const L=({setSidebar:p})=>{const{dispatch:d}=a.useContext(b),m=c(),{dispatch:r}=a.useContext(g),l=f(()=>{let e=new j;return e.use(u,{id:"XHRUpload",method:"post",formData:!0,limit:0,fieldName:"file",allowedMetaFields:["caption","size"],headers:i.getHeader(),endpoint:i.uploadPhoto()}),e.on("file-added",o=>{e.setFileMeta(o.id,{size:o.size,caption:""})}),e.on("upload-success",async(o,s)=>{s.status,s.body,console.log("response",s),E(r,"Uploaded"),m("/admin/photo")}),e.on("upload-error",(o,s,n)=>{n.status==401&&tokenExpireError(d,"TOKEN_EXPIRED")}),e});return a.useEffect(()=>{r({type:"SETPATH",payload:{path:"photo"}})},[]),t.jsxs("div",{className:"relative p-4 flex-auto",children:[t.jsxs("div",{className:"flex items-center pb-3 gap-4 border-b border-b-[#E0E0E0] justify-between",children:[t.jsx("div",{className:"flex items-center gap-3",children:t.jsx("span",{className:"text-lg font-semibold",children:"Add Photo"})}),t.jsx("div",{className:"flex items-center gap-4",children:t.jsx("button",{className:"flex items-center py-2 px-3 border border-[#C6C6C6] rounded-md shadow-sm hover:bg-[#F4F4F4]",onClick:()=>p(!1),children:"Cancel"})})]}),t.jsx(h,{uppy:l})]})};export{L as default};

import{j as e}from"./@react-google-maps/api-4794cf1a.js";import{h as a,r as s,R as r}from"./vendor-dd4ba10b.js";import{M as x,A as c,G as l}from"./index-3efdd896.js";import"./react-confirm-alert-5d5c0db6.js";import"./html2pdf.js-19c9759c.js";import"./@headlessui/react-7b0d4887.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-1762f471.js";import"./@fortawesome/react-fontawesome-0b111e8e.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";new x;const g=()=>{a(),s.useContext(c);const{state:d,dispatch:t}=s.useContext(l);return r.useEffect(()=>{t({type:"SETPATH",payload:{path:"dashboard"}})},[]),e.jsx("div",{className:" shadow-md rounded  mx-auto p-5",children:e.jsxs("div",{className:"mt-10",children:[e.jsxs("div",{className:"mb-10",children:[e.jsx("h3",{className:"text-[18px]",children:"Engagement Overview:"}),e.jsxs("div",{className:"flex gap-[10%] flex-wrap md:flex-nowrap py-7",children:[e.jsxs("div",{className:"w-[300px] p-3 h-[120px] border border-[black]",children:[e.jsx("h5",{className:"text-[16px]",children:"Total active users"}),e.jsx("h5",{className:"text-[18px] mt-5 text-center",children:"158"})]}),e.jsxs("div",{className:"w-[300px] p-3 h-[120px] border border-[black]",children:[e.jsx("h5",{className:"text-[16px]",children:"Total active subscriptions "}),e.jsx("h5",{className:"text-[18px] mt-5 text-center",children:"250"})]}),e.jsxs("div",{className:"w-[300px] p-3 h-[120px] border border-[black]",children:[e.jsx("h5",{className:"text-[16px]",children:"Total pdfs generated"}),e.jsx("h5",{className:"text-[18px] mt-5 text-center",children:"30"})]})]}),e.jsxs("div",{className:"w-[300px] p-3 h-[120px] border border-[black]",children:[e.jsx("h5",{className:"text-[16px]",children:"Total quizzes generated"}),e.jsx("h5",{className:"text-[18px] mt-5 text-center",children:"30"})]})]}),e.jsxs("div",{className:"mb-10",children:[e.jsx("h3",{className:"text-[18px]",children:"Total revenue:"}),e.jsxs("div",{className:"flex gap-[10%] flex-wrap md:flex-nowrap py-7",children:[e.jsxs("div",{className:"w-[300px] p-3 h-[120px] border border-[black]",children:[e.jsx("h5",{className:"text-[16px]",children:"Monthly Recurring Revenue (MRR): "}),e.jsx("h5",{className:"text-[18px] mt-5 text-center",children:"$50,000"})]}),e.jsxs("div",{className:"w-[300px] p-3 h-[120px] border border-[black]",children:[e.jsx("h5",{className:"text-[16px]",children:"Annual Recurring Revenue (ARR):"}),e.jsx("h5",{className:"text-[18px] mt-5 text-center",children:"$150,000"})]})]})]}),e.jsxs("div",{className:"mb-10",children:[e.jsx("h3",{className:"text-[18px]",children:"Recurring Subscriptions"}),e.jsxs("div",{className:"flex gap-[10%] flex-wrap md:flex-nowrap py-7",children:[e.jsxs("div",{className:"w-[300px] p-3 h-[120px] border border-[black]",children:[e.jsx("h5",{className:"text-[16px]",children:"Active Subscriptions: "}),e.jsx("h5",{className:"text-[18px] mt-5 text-center",children:"500"})]}),e.jsxs("div",{className:"w-[300px] p-3 h-[120px] border border-[black]",children:[e.jsx("h5",{className:"text-[16px]",children:"Revenue from Active Subscriptions: "}),e.jsx("h5",{className:"text-[18px] mt-5 text-center",children:"$150,000"})]})]})]})]})})};export{g as default};

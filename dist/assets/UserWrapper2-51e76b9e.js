import{j as r}from"./@react-google-maps/api-4794cf1a.js";import{r as e}from"./vendor-dd4ba10b.js";import"./index-154578dc.js";import{a as o}from"./html2pdf.js-19c9759c.js";import{T as i}from"./index-292ff41b.js";import{G as p}from"./index-3efdd896.js";import"./index-e6a343d0.js";import"./react-confirm-alert-5d5c0db6.js";import"./@headlessui/react-7b0d4887.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-1762f471.js";import"./@fortawesome/react-fontawesome-0b111e8e.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";const a=e.lazy(()=>o(()=>import("./Sidebar-64c2f9b5.js"),["assets/Sidebar-64c2f9b5.js","assets/@react-google-maps/api-4794cf1a.js","assets/vendor-dd4ba10b.js","assets/index-3efdd896.js","assets/react-confirm-alert-5d5c0db6.js","assets/html2pdf.js-19c9759c.js","assets/@headlessui/react-7b0d4887.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-1762f471.js","assets/@fortawesome/react-fontawesome-0b111e8e.js","assets/@fortawesome/fontawesome-svg-core-254ba2e2.js","assets/@fortawesome/free-solid-svg-icons-82da594a.js","assets/@fortawesome/free-regular-svg-icons-a38012c9.js","assets/@fortawesome/free-brands-svg-icons-2c021b6b.js","assets/index-5300e751.css","assets/logo-a4db6296.js"])),l=({children:t})=>{const{state:{isOpen:s}}=e.useContext(p);return r.jsxs("div",{id:"user_wrapper",className:"flex w-full max-w-full bg-white",children:[r.jsx(a,{}),r.jsxs("div",{className:`flex-1 w-full overflow-y-auto transition-all duration-300 ${s?"pl-[318px]":"pl-0 sm:pl-[20px]"}`,children:[r.jsx(i,{}),r.jsx(e.Suspense,{fallback:r.jsx("div",{className:"flex h-screen w-full items-center justify-center"}),children:r.jsx("div",{className:"w-full pt-[calc(3.5rem+2rem)]",children:t})})]})]})},T=e.memo(l);export{T as default};

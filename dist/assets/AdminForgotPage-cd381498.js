import{j as t}from"./@react-google-maps/api-4794cf1a.js";import{r as S,R as F,L}from"./vendor-dd4ba10b.js";import{u as R}from"./react-hook-form-a6ecef1c.js";import{o as B}from"./yup-f7f8305f.js";import{c as C,a as $}from"./yup-79911193.js";import{G as q,M as A,s as D,t as G}from"./index-3efdd896.js";import{I}from"./InteractiveButton-6ddb3b9d.js";import"./@hookform/resolvers-61c06af0.js";import"./react-confirm-alert-5d5c0db6.js";import"./html2pdf.js-19c9759c.js";import"./@headlessui/react-7b0d4887.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-1762f471.js";import"./@fortawesome/react-fontawesome-0b111e8e.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";import"./MoonLoader-795b8e10.js";const ae=()=>{var n,r;const[i,a]=S.useState(!1),y=C({email:$().email().required()}).required(),{register:j,handleSubmit:N,setError:l,formState:{errors:s}}=R({resolver:B(y)}),{dispatch:m}=F.useContext(q),k=async v=>{var d,c,p,u,x,g,f,h;let E=new A;try{a(!0);const e=await E.forgot(v.email,admin);if(!e.error)D(m,"Reset Code Sent");else if(e.validation){const b=Object.keys(e.validation);for(let o=0;o<b.length;o++){const w=b[o];l(w,{type:"manual",message:e.validation[w]})}}a(!1)}catch(e){a(!1),console.log("Error",e),l("email",{type:"manual",message:(c=(d=e==null?void 0:e.response)==null?void 0:d.data)!=null&&c.message?(u=(p=e==null?void 0:e.response)==null?void 0:p.data)==null?void 0:u.message:e==null?void 0:e.message}),G(m,(g=(x=e==null?void 0:e.response)==null?void 0:x.data)!=null&&g.message?(h=(f=e==null?void 0:e.response)==null?void 0:f.data)==null?void 0:h.message:e==null?void 0:e.message)}};return t.jsx(t.Fragment,{children:t.jsxs("div",{className:"w-full max-w-xs mx-auto",children:[t.jsxs("form",{onSubmit:N(k),className:"bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4 mt-8 ",children:[t.jsxs("div",{className:"mb-4",children:[t.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"email",children:"Email"}),t.jsx("input",{type:"email",placeholder:"Email",...j("email"),className:`"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${s&&((n=s.email)!=null&&n.message)?"border-red-500":""}`}),t.jsx("p",{className:"text-red-500 text-xs italic",children:s&&((r=s.email)==null?void 0:r.message)})]}),t.jsxs("div",{className:"flex items-center justify-between",children:[t.jsx(I,{className:"bg-primaryBlue disabled:cursor-not-allowed text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",type:"submit",loading:i,disabled:i,children:"Forgot Password"}),t.jsx(L,{className:"inline-block align-baseline font-bold text-sm text-primaryBlue",to:"/admin/login",children:"Login?"})]})]}),t.jsxs("p",{className:"text-center text-gray-500 text-xs",children:["© ",new Date().getFullYear()," manaknightdigital inc. All rights reserved."]})]})})};export{ae as default};

import{j as t}from"./@react-google-maps/api-4794cf1a.js";import{R as d,b as T}from"./vendor-dd4ba10b.js";import{L as p,p as L}from"./index-3efdd896.js";import{S as $}from"./index-e6a343d0.js";import k from"./MkdListTableRow-16125096.js";import z from"./MkdListTableHead-39b57446.js";import{M as C}from"./index-01b5280f.js";import"./react-confirm-alert-5d5c0db6.js";import"./html2pdf.js-19c9759c.js";import"./@headlessui/react-7b0d4887.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-1762f471.js";import"./@fortawesome/react-fontawesome-0b111e8e.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";const re=({table:h,tableTitle:H,onSort:E,loading:u,columns:x,actions:n,actionPostion:g,tableRole:_,deleteItem:M,deleteLoading:N,actionId:j="id",showDeleteModal:S,currentTableData:a,setShowDeleteModal:w,handleTableCellChange:I,setSelectedItems:o})=>{const[y,b]=d.useState(null),[Y,m]=d.useState(!1),[R,c]=d.useState(!1),[r,i]=d.useState([]);function A(e){var v;m(!0);const s=r;if((v=n==null?void 0:n.select)!=null&&v.multiple)if(s.includes(e)){const l=s.filter(f=>f!==e);i(()=>[...l]),o(l)}else{const l=[...s,e];i(()=>[...l]),o(l)}else if(s.includes(e)){const l=s.filter(f=>f!==e);i(()=>[...l]),o(l)}else{const l=[e];i(()=>[...l]),o(l)}console.log(e)}const F=()=>{if(c(e=>!e),R)i([]),o([]);else{const e=a.map(s=>s[j]);i(e),o(e)}};T();const O=async e=>{console.log("id >>",e),w(!0),b(e)};return d.useEffect(()=>{r.length<=0&&(m(!1),c(!1)),r.length===a.length&&(c(!0),m(!0)),r.length<a.length&&r.length>0&&c(!1)},[r,a]),t.jsxs(t.Fragment,{children:[t.jsx("div",{className:`${u?"":"overflow-x-auto"} border-b border-gray-200 shadow`,children:u?t.jsx("div",{className:"flex max-h-fit min-h-fit min-w-fit max-w-full items-center justify-center  py-5",children:t.jsx($,{size:40,color:"#4F46E5"})}):t.jsx(t.Fragment,{children:t.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[t.jsx("thead",{className:"bg-gray-50",children:t.jsx(p,{children:t.jsx(z,{onSort:E,columns:x,actions:n,actionPostion:g,areAllRowsSelected:R,handleSelectAll:F})})}),t.jsx("tbody",{className:"divide-y divide-gray-200 bg-white",children:a.map((e,s)=>t.jsx(p,{children:t.jsx(k,{i:s,row:e,columns:x,actions:n,actionPostion:g,actionId:j,handleTableCellChange:I,handleSelectRow:A,selectedIds:r,setDeleteId:O},s)},s))})]})})}),t.jsx(p,{children:t.jsx(C,{open:S,actionHandler:()=>{M(y)},closeModalFunction:()=>{b(null),w(!1)},title:`Delete ${L(h)} `,message:`You are about to delete ${L(h)} ${y}, note that this action is irreversible`,acceptText:"DELETE",rejectText:"CANCEL",loading:N})})]})};export{re as default};

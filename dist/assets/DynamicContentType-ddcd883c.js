import{j as a}from"./@react-google-maps/api-4794cf1a.js";import{R as h}from"./vendor-dd4ba10b.js";import{M as v,q as f}from"./index-3efdd896.js";import"./react-confirm-alert-5d5c0db6.js";import"./html2pdf.js-19c9759c.js";import"./@headlessui/react-7b0d4887.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-1762f471.js";import"./@fortawesome/react-fontawesome-0b111e8e.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";const b=new v,x="https://via.placeholder.com/150?text=%20",U=({contentType:c,contentValue:o,setContentValue:d})=>{const[u,i]=h.useState(x),m=async y=>{const p=new FormData;p.append("file",y.target.files[0]);try{const e=await b.uploadImage(p);i(e.url),d(e.url)}catch(e){console.error(e)}};switch(c){case"text":return a.jsx(a.Fragment,{children:a.jsx("textarea",{className:"shadow appearance-none border  rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline",rows:15,placeholder:"Content",onChange:y=>d(y.target.value),defaultValue:o})});case"image":return a.jsxs(a.Fragment,{children:[a.jsx("img",{src:f(o)?u:o,alt:"preview",height:150,width:150}),a.jsx("input",{type:"file",onChange:m,className:"shadow appearance-none border block  rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline"})]});case"number":return a.jsx("input",{type:"number",className:"shadow appearance-none border block  rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline",onChange:y=>d(y.target.value),defaultValue:o});case"team-list":return a.jsx(N,{setContentValue:d,contentValue:o});case"image-list":return a.jsx(k,{setContentValue:d,contentValue:o});case"captioned-image-list":return a.jsx(j,{setContentValue:d,contentValue:o});case"kvp":return a.jsx(I,{setContentValue:d,contentValue:o})}},k=({contentValue:c,setContentValue:o})=>{let d=[{key:"",value_type:"image",value:null}];f(c)||(d=JSON.parse(c));const[u,i]=h.useState(d),m=async p=>{const e=p.target.getAttribute("listkey"),t=new FormData;t.append("file",p.target.files[0]);try{const s=await b.uploadImage(t);i(n=>n.map((r,g)=>(g==e&&(r.value=s.url),r))),o(JSON.stringify(u))}catch(s){console.error(s)}},y=p=>{const e=p.target.getAttribute("listkey");i(t=>t.map((n,l)=>(l==e&&(n.key=p.target.value),n))),o(JSON.stringify(u))};return a.jsxs("div",{className:"block",children:[u.map((p,e)=>a.jsxs("div",{children:[a.jsx("img",{src:p.value!==null?p.value:x,alt:"preview",height:150,width:150}),a.jsxs("div",{className:"flex",children:[a.jsx("input",{className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline",type:"text",placeholder:"key",listkey:e,onChange:y,defaultValue:p.key}),a.jsx("input",{listkey:e,type:"file",accept:"image/*",onChange:m,className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline"})]})]},e*.23)),a.jsx("button",{type:"button",className:"bg-blue-400 hover:bg-blue-700 text-white font-bold py-1 px-2 my-4 rounded focus:outline-none focus:shadow-outline",onClick:p=>i(e=>[...e,{key:"",value_type:"image",value:null}]),children:"+"})]})},j=({setContentValue:c,contentValue:o})=>{let d=[{key:"",value_type:"image",value:null,caption:""}];f(o)||(d=JSON.parse(o));const[u,i]=h.useState(d),m=async e=>{const t=e.target.getAttribute("listkey"),s=new FormData;s.append("file",e.target.files[0]);try{const n=await b.uploadImage(s);i(l=>l.map((g,w)=>(w==t&&(g.value=n.url),g))),c(JSON.stringify(u))}catch(n){console.error(n)}},y=e=>{const t=e.target.getAttribute("listkey");i(s=>s.map((l,r)=>(r==t&&(l.key=e.target.value),l))),c(JSON.stringify(u))},p=e=>{const t=e.target.getAttribute("listkey");i(s=>s.map((l,r)=>(r==t&&(l.caption=e.target.value),l))),c(JSON.stringify(u))};return a.jsxs("div",{className:"block",children:[u.map((e,t)=>a.jsxs("div",{children:[a.jsx("img",{src:e.value!==null?e.value:x,alt:"preview",height:150,width:150}),a.jsxs("div",{className:"flex",children:[a.jsx("input",{className:"shadow appearance-none border rounded w-full py-2 px-3 mr-2 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline",type:"text",placeholder:"Key",listkey:t,onChange:y,defaultValue:e.key}),a.jsx("input",{listkey:t,type:"file",accept:"image/*",onChange:m,className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline"})]}),a.jsx("input",{className:"shadow block appearance-none border rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline",type:"text",placeholder:"Caption",listkey:t,onChange:p,defaultValue:e.caption})]},t*.23)),a.jsx("button",{type:"button",className:"bg-blue-400 hover:bg-blue-700 text-white font-bold py-1 px-2 my-4 rounded focus:outline-none focus:shadow-outline",onClick:e=>i(t=>[...t,{key:"",value_type:"image",value:null,caption:""}]),children:"+"})]})},N=({setContentValue:c,contentValue:o})=>{let d=[{name:"",image:null,title:""}];f(o)||(d=JSON.parse(o));const[u,i]=h.useState(d),m=async e=>{const t=e.target.getAttribute("listkey"),s=new FormData;s.append("file",e.target.files[0]);try{const n=await b.uploadImage(s);i(l=>l.map((g,w)=>(w==t&&(g.image=n.url),g))),c(JSON.stringify(u))}catch(n){console.error(n)}},y=e=>{const t=e.target.getAttribute("listkey");i(s=>s.map((l,r)=>(r==t&&(l.name=e.target.value),l))),c(JSON.stringify(u))},p=e=>{const t=e.target.getAttribute("listkey");i(s=>s.map((l,r)=>(r==t&&(l.title=e.target.value),l))),c(JSON.stringify(u))};return a.jsxs("div",{className:"block my-4",children:[u.map((e,t)=>a.jsxs("div",{className:"my-4",children:[a.jsx("input",{className:"shadow block appearance-none border rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline",type:"text",placeholder:"Title",listkey:t,onChange:p,defaultValue:e.title}),a.jsx("input",{className:"shadow appearance-none border rounded w-full py-2 px-3 mr-2 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline",type:"text",placeholder:"Name",listkey:t,onChange:y,defaultValue:e.name}),a.jsx("img",{src:e.image!==null?e.image:x,alt:"preview",height:150,width:150}),a.jsx("input",{listkey:t,type:"file",accept:"image/*",onChange:m,className:"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline"})]},t*.23)),a.jsx("button",{type:"button",className:"bg-blue-400 hover:bg-blue-700 text-white font-bold py-1 px-2 my-4 rounded focus:outline-none focus:shadow-outline",onClick:e=>i(t=>[...t,{name:"",image:null,title:""}]),children:"+"})]})},I=({setContentValue:c,contentValue:o})=>{let d=[{key:"",value_type:"text",value:""}];f(o)||(d=JSON.parse(o));const[u,i]=h.useState(d),m=[{key:"text",value:"Text"},{key:"number",value:"Number"},{key:"json",value:"JSON Object"},{key:"url",value:"URL"}],y=t=>{const s=t.target.getAttribute("listkey");i(n=>n.map((r,g)=>(g==s&&(r.key=t.target.value),r))),c(JSON.stringify(u))},p=t=>{const s=t.target.getAttribute("listkey");i(n=>n.map((r,g)=>(g==s&&(r.value=t.target.value),r))),c(JSON.stringify(u))},e=t=>{const s=t.target.getAttribute("listkey");i(n=>n.map((r,g)=>(g==s&&(r.value_type=t.target.value),r))),c(JSON.stringify(u))};return a.jsxs("div",{className:"block",children:[u.map((t,s)=>a.jsxs("div",{className:"my-4",children:[a.jsx("input",{className:"shadow appearance-none border rounded w-full py-2 px-3 mr-2 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline",type:"text",placeholder:"Key",listkey:s,onChange:y,defaultValue:t.key}),a.jsx("select",{className:"shadow block border rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline",listkey:s,onChange:e,defaultValue:t.value_type,children:m.map((n,l)=>a.jsx("option",{value:n.key,children:n.value},l*122))}),a.jsx("input",{className:"shadow block appearance-none border rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline",type:"text",required:!0,placeholder:"Value",listkey:s,onChange:p,defaultValue:t.value})]},s*.23)),a.jsx("button",{type:"button",className:"bg-blue-400 hover:bg-blue-700 text-white font-bold py-1 px-2 my-4 rounded focus:outline-none focus:shadow-outline",onClick:t=>i(s=>[...s,{key:"",value_type:"text",value:""}]),children:"+"})]})};export{U as default};

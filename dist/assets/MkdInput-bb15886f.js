import{j as e}from"./@react-google-maps/api-4794cf1a.js";import"./vendor-dd4ba10b.js";import{S as k}from"./index-3efdd896.js";const y=({type:s="text",page:j,cols:b="30",rows:m="50",name:o,label:$,errors:r,register:t,className:n,placeholder:a,options:u=[],mapping:c=null,disabled:d=!1})=>{var x,p,h,g,f,w;return e.jsx(e.Fragment,{children:e.jsxs("div",{className:`mb-4 ${j==="list"?"w-full pl-2 pr-2 md:w-1/2":""}`,children:[e.jsx("label",{className:"mb-2 block cursor-pointer text-sm font-bold text-gray-700",htmlFor:o,children:k($,{casetype:"capitalize",separator:"space"})}),s==="textarea"?e.jsx("textarea",{className:`focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${n} ${(x=r[o])!=null&&x.message?"border-red-500":""}`,disabled:d,id:o,cols:b,name:o,placeholder:a,rows:m,...t(o)}):s==="radio"||s==="checkbox"||s==="color"?e.jsx("input",{disabled:d,type:s,id:o,name:o,placeholder:a,...t(o),className:`focus:shadow-outline cursor-pointer appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${n} ${(p=r[o])!=null&&p.message?"border-red-500":""} ${s==="color"?"min-h-[3.125rem] min-w-[6.25rem]":""}`}):s==="dropdown"||s==="select"?e.jsxs("select",{type:s,id:o,disabled:d,placeholder:a,...t(o),className:`focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${n} ${(h=r[o])!=null&&h.message?"border-red-500":""}`,children:[e.jsx("option",{}),u.map((l,i)=>e.jsx("option",{value:l,children:l},i+1))]}):s==="mapping"?e.jsx(e.Fragment,{children:c?e.jsxs("select",{type:s,id:o,disabled:d,placeholder:a,...t(o),className:`focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${n} ${(g=r[o])!=null&&g.message?"border-red-500":""}`,children:[e.jsx("option",{}),u.map((l,i)=>e.jsx("option",{value:l,children:c[l]},i+1))]}):"Please Pass the mapping e.g {key:value}"}):e.jsx("input",{type:s,id:o,disabled:d,placeholder:a,...t(o),className:`focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${n} ${(f=r[o])!=null&&f.message?"border-red-500":""}`}),e.jsx("p",{className:"text-field-error italic text-red-500",children:(w=r[o])==null?void 0:w.message})]})})};export{y as M};

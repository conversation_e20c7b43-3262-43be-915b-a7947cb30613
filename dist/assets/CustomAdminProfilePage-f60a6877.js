import{j as e}from"./@react-google-maps/api-4794cf1a.js";import{R as I,r as l}from"./vendor-dd4ba10b.js";import{u as ie}from"./react-hook-form-a6ecef1c.js";import{o as le}from"./yup-f7f8305f.js";import{c as oe,a as ne}from"./yup-79911193.js";import{M as re,A as ce,G as de,t as Y,s as A}from"./index-3efdd896.js";import{I as W}from"./InteractiveButton-6ddb3b9d.js";import me from"./ModalPrompt-0604e64d.js";import{S as V}from"./index-3b0c955b.js";import{G as K}from"./react-icons-0b96c072.js";import"./@hookform/resolvers-61c06af0.js";import"./react-confirm-alert-5d5c0db6.js";import"./html2pdf.js-19c9759c.js";import"./@headlessui/react-7b0d4887.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-1762f471.js";import"./@fortawesome/react-fontawesome-0b111e8e.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";import"./MoonLoader-795b8e10.js";import"./index-e6a343d0.js";function ee(x){return K({tag:"svg",attr:{viewBox:"0 0 640 512"},child:[{tag:"path",attr:{d:"M537.6 226.6c4.1-10.7 6.4-22.4 6.4-34.6 0-53-43-96-96-96-19.7 0-38.1 6-53.3 16.2C367 64.2 315.3 32 256 32c-88.4 0-160 71.6-160 160 0 2.7.1 5.4.2 8.1C40.2 219.8 0 273.2 0 336c0 79.5 64.5 144 144 144h368c70.7 0 128-57.3 128-128 0-61.9-44-113.6-102.4-125.4zM393.4 288H328v112c0 8.8-7.2 16-16 16h-48c-8.8 0-16-7.2-16-16V288h-65.4c-14.3 0-21.4-17.2-11.3-27.3l105.4-105.4c6.2-6.2 16.4-6.2 22.6 0l105.4 105.4c10.1 10.1 2.9 27.3-11.3 27.3z"}}]})(x)}function xe(x){return K({tag:"svg",attr:{viewBox:"0 0 640 512"},child:[{tag:"path",attr:{d:"M320 400c-75.85 0-137.25-58.71-142.9-133.11L72.2 185.82c-13.79 17.3-26.48 35.59-36.72 55.59a32.35 32.35 0 0 0 0 29.19C89.71 376.41 197.07 448 320 448c26.91 0 52.87-4 77.89-10.46L346 397.39a144.13 144.13 0 0 1-26 2.61zm313.82 58.1l-110.55-85.44a331.25 331.25 0 0 0 81.25-102.07 32.35 32.35 0 0 0 0-29.19C550.29 135.59 442.93 64 320 64a308.15 308.15 0 0 0-147.32 37.7L45.46 3.37A16 16 0 0 0 23 6.18L3.37 31.45A16 16 0 0 0 6.18 53.9l588.36 454.73a16 16 0 0 0 22.46-2.81l19.64-25.27a16 16 0 0 0-2.82-22.45zm-183.72-142l-39.3-30.38A94.75 94.75 0 0 0 416 256a94.76 94.76 0 0 0-121.31-92.21A47.65 47.65 0 0 1 304 192a46.64 46.64 0 0 1-1.54 10l-73.61-56.89A142.31 142.31 0 0 1 320 112a143.92 143.92 0 0 1 144 144c0 21.63-5.29 41.79-13.9 60.11z"}}]})(x)}function pe(x){return K({tag:"svg",attr:{viewBox:"0 0 576 512"},child:[{tag:"path",attr:{d:"M572.52 241.4C518.29 135.59 410.93 64 288 64S57.68 135.64 3.48 241.41a32.35 32.35 0 0 0 0 29.19C57.71 376.41 165.07 448 288 448s230.32-71.64 284.52-177.41a32.35 32.35 0 0 0 0-29.19zM288 400a144 144 0 1 1 144-144 143.93 143.93 0 0 1-144 144zm0-240a95.31 95.31 0 0 0-25.31 3.79 47.85 47.85 0 0 1-66.9 66.9A95.78 95.78 0 1 0 288 160z"}}]})(x)}let D=new re;const Ie=()=>{var J,Q;const x=oe({email:ne().email().required()}).required(),{dispatch:k}=I.useContext(ce),[f,v]=l.useState(""),[u,P]=I.useState({}),[S,g]=l.useState(!1),[n,N]=l.useState(!1),[y,L]=l.useState(""),[z,j]=l.useState(!1),[_,w]=l.useState(!1),[r,M]=l.useState("Profile"),[t,T]=l.useState({}),[F,O]=l.useState(!0),[C,B]=l.useState(!1),{dispatch:p}=I.useContext(de),{register:U,handleSubmit:i,setError:b,setValue:G,formState:{errors:H}}=ie({resolver:le(x)}),se=()=>{B(!C)},te=(s,o,$=!1)=>{j(!0);let h=u;$?h[s]?h[s]=[...h[s],{file:o.files[0],tempFile:{url:URL.createObjectURL(o.files[0]),name:o.files[0].name,type:o.files[0].type}}]:h[s]=[{file:o.files[0],tempFile:{url:URL.createObjectURL(o.files[0]),name:o.files[0].name,type:o.files[0].type}}]:h[s]={file:o.files[0],tempURL:URL.createObjectURL(o.files[0])},P({...h})};async function Z(){O(!0);try{const s=await D.getProfile();s.error||(T(s),G("email",s==null?void 0:s.email),G("first_name",s==null?void 0:s.first_name),G("last_name",s==null?void 0:s.last_name),v(s==null?void 0:s.email),L(s==null?void 0:s.photo),k({type:"UPDATE_PROFILE",payload:s}),O(!1))}catch(s){Y(k,s.response.data.message?s.response.data.message:s.message)}}const q=async s=>{var o,$,h;if(t.email===s.email&&t.first_name===s.first_name&&t.last_name===s.last_name&&!z&&!s.password)return E(),A(p,"No Changes Available",1e3);T(s);try{if(w(!0),u&&u.photo&&((o=u.photo)!=null&&o.file)){let a=new FormData;a.append("file",($=u.photo)==null?void 0:$.file);let d=await D.uploadImage(a);s.photo=d.url,A(p,"Profile Photo Updated",1e3)}const c=await D.updateProfile({first_name:s.first_name||(t==null?void 0:t.first_name),last_name:s.last_name||(t==null?void 0:t.last_name),photo:s.photo||y});if(!c.error)A(p,"Profile Updated",4e3),E(),Z();else{if(c.validation){const a=Object.keys(c.validation);for(let d=0;d<a.length;d++){const m=a[d];b(m,{type:"manual",message:c.validation[m]})}}E()}if(f!==s.email){const a=await D.updateEmail(s.email);if(!a.error)A(p,"Email Updated",1e3);else if(a.validation){const d=Object.keys(a.validation);for(let m=0;m<d.length;m++){const R=d[m];b(R,{type:"manual",message:a.validation[R]})}}E()}if(((h=s.password)==null?void 0:h.length)>0){const a=await D.updatePassword(s.password);if(!a.error)A(p,"Password Updated",2e3);else if(a.validation){const d=Object.keys(a.validation);for(let m=0;m<d.length;m++){const R=d[m];b(R,{type:"manual",message:a.validation[R]})}}}s.photo="",await Z(),w(!1)}catch(c){w(!1),b("email",{type:"manual",message:c.response.data.message?c.response.data.message:c.message}),Y(k,c.response.data.message?c.response.data.message:c.message)}},ae=async()=>{P({}),L(""),j(!0)};I.useEffect(()=>{p({type:"SETPATH",payload:{path:"profile"}}),Z()},[]);const X=()=>{N(!0)},E=()=>{g(!1),N(!1),L(t==null?void 0:t.photo),P({}),j(!1)};return e.jsxs("div",{className:"w-full",children:[e.jsx("div",{className:"flex items-center border-b border-b-[#E0E0E0] px-8 py-3 text-[#8D8D8D]",children:e.jsxs("div",{className:"flex items-center space-x-6",children:[e.jsx("div",{className:`cursor-pointer rounded-lg px-3 py-1 ${r==="Profile"?"bg-[#f4f4f4] text-[#525252]":""}`,onClick:()=>M("Profile"),children:"Profile"}),e.jsx("div",{className:`cursor-pointer rounded-lg px-3 py-1 ${r==="Security"?"bg-[#f4f4f4] text-[#525252]":""}`,onClick:()=>M("Security"),children:"Security"})]})}),e.jsxs("main",{children:[r==="Profile"&&e.jsx("div",{className:"rounded bg-white",children:e.jsxs("form",{onSubmit:i(q),children:[e.jsx("p",{className:"text-xs italic text-red-500",children:(J=H.photo)==null?void 0:J.message}),e.jsxs("div",{className:"mx-10 mt-4 max-w-lg",children:[e.jsxs("div",{className:"mb-3 flex items-center justify-between",children:[e.jsx("p",{className:"mb-3	text-lg	font-semibold text-gray-900",children:"Personal Details"}),F?e.jsx("div",{className:"h-10",children:e.jsx(V,{count:1,counts:[1]})}):e.jsx("p",{className:"cursor-pointer	text-base	font-semibold text-indigo-600",onClick:X,children:"Edit"})]}),e.jsx("div",{className:"mb-3 flex items-center justify-between",children:e.jsxs("div",{className:"flex items-start gap-x-24",children:[e.jsx("p",{className:"text-base	font-medium	text-gray-600",children:"Profile Picture"}),F?e.jsx("div",{className:"flex items-center",children:e.jsx("div",{className:"h-[120px] w-[120px] flex-1 overflow-hidden rounded-2xl",children:e.jsx(V,{count:4,counts:[1]})})}):t!=null&&t.photo?e.jsx("div",{className:"relative flex h-[100px] w-[100px] items-center rounded-lg py-2",children:e.jsx("img",{className:"h-[100px] w-[100px] rounded-lg object-cover",src:t==null?void 0:t.photo,alt:""})}):e.jsx("div",{className:"flex items-center gap-4 py-2",children:e.jsx("div",{className:"flex h-[100px] w-[100px] items-center justify-center rounded-lg border bg-slate-300 object-cover",children:e.jsx("span",{className:"text-xs",children:"No Image"})})})]})}),e.jsx("div",{className:"mb-3 flex items-center justify-between",children:e.jsxs("div",{className:"flex items-center gap-x-[7.5rem]",children:[e.jsx("p",{className:"text-base	font-medium	text-gray-600",children:"First Name"}),e.jsx("p",{className:"text-base	font-medium	text-gray-900",children:t==null?void 0:t.first_name})]})}),e.jsx("div",{className:"mb-3 flex items-center justify-between",children:e.jsxs("div",{className:"flex items-center gap-x-[7.5rem]",children:[e.jsx("p",{className:"text-base	font-medium	text-gray-600",children:"Last Name"}),e.jsx("p",{className:"text-base	font-medium	text-gray-900",children:t==null?void 0:t.last_name})]})}),e.jsx("div",{className:"mb-6 flex items-center justify-between text-left",children:e.jsxs("div",{className:"flex items-center gap-x-40",children:[e.jsx("p",{className:"text-base	font-medium text-gray-600",children:"Email"}),e.jsx("p",{className:"text-base	font-medium	text-gray-900",children:f})]})})]})]})}),r==="Security"&&e.jsx("div",{className:"rounded bg-white px-10 py-6",children:e.jsx("form",{onSubmit:i(q),className:"max-w-lg",children:e.jsxs("div",{className:"",children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",children:"Password"}),e.jsxs("div",{className:"relative w-full md:w-3/4 lg:w-2/3",children:[e.jsx("input",{...U("password"),name:"password",className:"focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none",id:"password",placeholder:"********",type:C?"text":"password"}),e.jsx("div",{className:"absolute inset-y-0 right-0 flex items-center pr-3 cursor-pointer",onClick:se,children:C?e.jsx(xe,{}):e.jsx(pe,{})})]}),e.jsx("p",{className:"text-xs italic text-red-500",children:(Q=H.password)==null?void 0:Q.message})]}),e.jsx("div",{className:"flex items-center justify-between",children:e.jsx(W,{className:"focus:shadow-outline rounded bg-indigo-600 px-4 py-2 font-bold text-white hover:bg-indigo-600 focus:outline-none disabled:cursor-not-allowed",type:"submit",loading:_,disabled:_,children:"Update"})})]})})}),S&&e.jsx(me,{closeModalFunction:E,title:"Are you sure?",message:"Are you sure you want to delete profile picture? ",acceptText:"DELETE",rejectText:"CANCEL"}),n&&e.jsx(he,{title:"Edit Personal Details",isOpen:X,onClose:E,handleSubmit:i,onSubmit:q,register:U,submitLoading:_,errors:H,oldPhoto:y,fileObj:u,onDeleteProfile:ae,previewImage:te,oldEmail:f})]})]})},he=x=>{var F,O,C,B,p,U;const{title:k,isOpen:f,onClose:v,handleSubmit:u,onSubmit:P,register:S,submitLoading:g,errors:n,oldPhoto:N,fileObj:y,onDeleteProfile:L,previewImage:z,oldEmail:j}=x,[_,w]=l.useState(!1),[r,M]=l.useState({email:""}),t=I.useRef(null);l.useEffect(()=>{M({...r,email:j}),w(!1)},[j]);const T=i=>b=>{i==="email"&&M({...r,[i]:b.target.value})};return l.useEffect(()=>{const i=b=>{t.current&&!t.current.contains(b.target)&&v()};return f?document.addEventListener("mousedown",i):document.removeEventListener("mousedown",i),()=>{document.removeEventListener("mousedown",i)}},[f,v]),e.jsxs("div",{className:`fixed inset-0 z-[100] ${f?"block":"hidden"}`,children:[e.jsx("div",{className:`fixed inset-0 bg-gray-800 bg-opacity-75 ${f?"block":"hidden"}`}),e.jsx("div",{className:"fixed inset-0 z-20 overflow-y-auto",children:e.jsxs("div",{className:"flex min-h-screen items-end justify-center px-4 pb-20 pt-4 text-center sm:block sm:p-0",children:[e.jsx("div",{className:"fixed inset-0 transition-opacity",children:e.jsx("div",{className:"absolute inset-0 bg-gray-500 opacity-75"})}),e.jsx("span",{className:"hidden sm:inline-block sm:h-screen sm:align-middle","aria-hidden":"true",children:"​"}),e.jsxs("div",{ref:t,className:"inline-block transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left align-bottom shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6 sm:align-middle",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"text-xl font-semibold leading-6 text-gray-900",children:k}),e.jsx("button",{className:"text-gray-500 hover:text-gray-700 focus:outline-none",onClick:v,children:e.jsx("svg",{className:"h-6 w-6",fill:"none",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{d:"M6 18L18 6M6 6l12 12"})})})]}),e.jsxs("form",{onSubmit:u(P),className:"max-w-lg",children:[e.jsxs("div",{className:"py-5",children:[e.jsx("label",{htmlFor:"first_name",className:"mb-1 block text-sm font-medium text-gray-700",children:"Upload Profile"}),N?e.jsx("div",{children:e.jsxs("div",{className:"hover-container relative flex h-[100px] w-[100px] items-center rounded-lg",children:[e.jsx("img",{className:"h-[100px] w-[100px] rounded-lg object-cover",src:((F=y.photo)==null?void 0:F.tempURL)||N,alt:""}),e.jsxs("button",{className:"file-wrapper absolute bottom-0 left-0 block",disabled:g,children:[e.jsx("div",{className:"!z-40 !cursor-pointer rounded-bl-lg rounded-tr-xl !bg-[#4F46E5] p-1",children:e.jsx(ee,{className:"text-[1.3rem] text-white"})}),e.jsx("input",{id:"photo",type:"file",placeholder:"Photo",name:"photo",onChange:i=>z("photo",i.target),className:"absolute left-0 top-0 flex h-full w-full !cursor-pointer opacity-0"})]}),e.jsx("div",{className:"visible-btn absolute right-0 top-0 hidden",children:e.jsx(W,{onClick:L,loading:g,disabled:g,className:"!h-[1.4rem] cursor-pointer !rounded-none !rounded-bl-xl !rounded-tr-lg !bg-red-500 text-sm font-semibold",children:"X"})})]})}):e.jsx("div",{className:"flex h-[100px] w-[100px] items-center justify-center rounded-lg border object-cover",children:e.jsx("div",{className:"relative",children:(O=y.photo)!=null&&O.tempURL?e.jsx("img",{className:"h-[100px] w-[100px] rounded-lg object-cover",src:((C=y.photo)==null?void 0:C.tempURL)||N,alt:""}):e.jsxs("button",{disabled:g,children:[e.jsx("div",{children:e.jsx(ee,{className:"text-[35px] text-blue-950"})}),e.jsx("input",{id:"photo",type:"file",placeholder:"Photo",name:"photo",onChange:i=>z("photo",i.target),className:"absolute left-0 top-0 flex h-full w-full !cursor-pointer opacity-0"})]})})}),e.jsxs("div",{className:"mt-3",children:[e.jsx("label",{htmlFor:"first_name",className:"mb-1 block text-sm font-medium text-gray-700",children:"First Name"}),e.jsx("input",{className:"focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none",id:"first_name",type:"text",placeholder:"Enter First Name",name:"first_name",...S("first_name")}),e.jsx("p",{className:"text-xs italic text-red-500",children:(B=n==null?void 0:n.id)==null?void 0:B.message})]}),e.jsxs("div",{className:"mt-3",children:[e.jsx("label",{htmlFor:"last_name",className:"mb-1 block text-sm font-medium text-gray-700",children:"Last Name"}),e.jsx("input",{className:"focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none",id:"last_name",type:"text",placeholder:"Enter last Name",name:"last_name",...S("last_name")}),e.jsx("p",{className:"text-xs italic text-red-500",children:(p=n==null?void 0:n.id)==null?void 0:p.message})]}),_&&j!==r.email?e.jsxs("div",{className:"mt-3 flex",children:[e.jsx("div",{className:"mr-2",children:e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",children:e.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M10.0003 1.66663C5.39795 1.66663 1.66699 5.39759 1.66699 9.99996C1.66699 14.6023 5.39795 18.3333 10.0003 18.3333C14.6027 18.3333 18.3337 14.6023 18.3337 9.99996C18.3337 5.39759 14.6027 1.66663 10.0003 1.66663ZM8.33366 9.16663C8.33366 8.82145 8.61348 8.54163 8.95866 8.54163H10.0003C10.3455 8.54163 10.6253 8.82145 10.6253 9.16663L10.6253 13.5416C10.6253 13.8868 10.3455 14.1666 10.0003 14.1666C9.65515 14.1666 9.37533 13.8868 9.37533 13.5416L9.37532 9.79163H8.95866C8.61348 9.79163 8.33366 9.5118 8.33366 9.16663ZM10.0003 6.04163C9.65515 6.04163 9.37533 6.32145 9.37533 6.66663C9.37533 7.0118 9.65515 7.29163 10.0003 7.29163C10.3455 7.29163 10.6253 7.0118 10.6253 6.66663C10.6253 6.32145 10.3455 6.04163 10.0003 6.04163Z",fill:"#4F46E5"})})}),e.jsxs("div",{children:[e.jsxs("p",{className:"mb-1	text-sm	font-medium text-gray-600",children:["We've sent an email to: ",r==null?void 0:r.email]}),e.jsx("p",{className:"mb-2	text-sm	font-semibold text-gray-900"}),e.jsx("p",{className:"mb-2	text-sm	font-medium text-gray-600",children:"In order to complete the email update click the confirmation link."}),e.jsx("p",{className:"mb-2	text-sm	font-medium text-gray-600",children:"(the link expires in 24 hours)"})]})]}):e.jsxs("div",{className:"mt-3",children:[e.jsx("label",{htmlFor:"email",className:"mb-1 block text-sm font-medium text-gray-700",children:"Email"}),e.jsx("input",{className:"focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none",id:"email",type:"text",placeholder:"Enter Email",name:"email",...S("email"),onChange:T("email")}),e.jsx("p",{className:"text-xs italic text-red-500",children:(U=n==null?void 0:n.id)==null?void 0:U.message})]})]}),e.jsxs("div",{className:"mt-4 flex justify-between gap-10",children:[e.jsx("button",{className:"mr-2 w-full rounded-md border border-gray-300 px-4 py-1.5 text-gray-700	",onClick:v,children:"Cancel"}),e.jsx(W,{className:"focus:shadow-outline !h-[2.5rem] w-full rounded-md bg-indigo-500 px-4 py-2 font-bold text-white hover:bg-indigo-600 focus:outline-none disabled:cursor-not-allowed",type:"submit",loading:g,disabled:g,onClick:()=>w(!0),children:"Save"})]})]})]})]})})]})};export{he as EditInfoModal,Ie as default};

import{j as e}from"./@react-google-maps/api-4794cf1a.js";import{R as k,b as I,r as l,h as G}from"./vendor-dd4ba10b.js";import{u as B}from"./react-hook-form-a6ecef1c.js";import{o as D}from"./yup-f7f8305f.js";import{c as M,a as n}from"./yup-79911193.js";import{M as O,A as U,G as _,t as S,s as H}from"./index-3efdd896.js";import"./@hookform/resolvers-61c06af0.js";import"./react-confirm-alert-5d5c0db6.js";import"./html2pdf.js-19c9759c.js";import"./@headlessui/react-7b0d4887.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-1762f471.js";import"./@fortawesome/react-fontawesome-0b111e8e.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";let m=new O;const ie=({activeId:C,setSidebar:c})=>{var b,f,j,y,N,w;const F=M({subject:n().required(),html:n().required(),tag:n().required()}).required(),{dispatch:d}=k.useContext(U),{dispatch:u}=k.useContext(_),T=I(),[A,P]=l.useState(0),[x,R]=l.useState(""),[p,h]=l.useState(!1),{register:o,handleSubmit:$,setError:g,setValue:r,formState:{errors:a}}=B({resolver:D(F)});G(),l.useEffect(function(){u({type:"SETPATH",payload:{path:"email"}}),async function(){try{m.setTable("email");const t=await m.callRestAPI({id:C},"GET");t.error||(r("subject",t.model.subject),r("html",t.model.html),r("tag",t.model.tag),R(t.model.slug),P(t.model.id))}catch(t){console.log("error",t),S(d,t.message)}}()},[]);const q=async t=>{h(!0);try{const s=await m.callRestAPI({id:A,slug:x,subject:t.subject,html:t.html,tag:t.tag},"PUT");if(!s.error)H(u,"Updated"),T("/admin/email");else if(s.validation){const E=Object.keys(s.validation);for(let i=0;i<E.length;i++){const v=E[i];g(v,{type:"manual",message:s.validation[v]})}}}catch(s){console.log("Error",s),g("html",{type:"manual",message:s.message}),S(d,s.message)}h(!1)};return e.jsxs("div",{className:"mx-auto rounded",children:[e.jsxs("div",{className:"flex items-center p-3 gap-4 border-b border-b-[#E0E0E0] justify-between",children:[e.jsx("div",{className:"flex items-center gap-3",children:e.jsx("span",{className:"text-lg font-semibold",children:"Edit Email"})}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("button",{className:"flex items-center py-2 px-3 border border-[#C6C6C6] rounded-md shadow-sm hover:bg-[#F4F4F4]",onClick:()=>c(!1),children:"Cancel"}),e.jsx("button",{className:"flex items-center py-2 px-3 text-white bg-[#4F46E5] rounded-md shadow-sm",onClick:async()=>{await $(q)(),c(!1)},disabled:p,children:p?"Saving...":"Save"})]})]}),e.jsxs("form",{className:"w-full p-4 text-left",children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"slug",children:"Email Type"}),e.jsx("input",{type:"text",placeholder:"Email Type",value:x,readOnly:!0,className:"shadow appearance-none border  rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline}"})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"email",children:"Subject"}),e.jsx("input",{type:"text",placeholder:"subject",...o("subject"),className:`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(b=a.subject)!=null&&b.message?"border-red-500":""}`}),e.jsx("p",{className:"text-red-500 text-xs italic",children:(f=a.subject)==null?void 0:f.message})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"tag",children:"Tags"}),e.jsx("input",{type:"text",placeholder:"tag",...o("tag"),className:`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(j=a.tag)!=null&&j.message?"border-red-500":""}`}),e.jsx("p",{className:"text-red-500 text-xs italic",children:(y=a.tag)==null?void 0:y.message})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"html",children:"Email Body"}),e.jsx("textarea",{placeholder:"Email Body",className:`shadow appearance-none border  rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline ${(N=a.html)!=null&&N.message?"border-red-500":""}`,...o("html"),rows:15}),e.jsx("p",{className:"text-red-500 text-xs italic",children:(w=a.html)==null?void 0:w.message})]})]})]})};export{ie as default};

import{j as d}from"./@react-google-maps/api-4794cf1a.js";import{r as m}from"./vendor-dd4ba10b.js";const u="_button_k8i1c_2",l={button:u},x=({onClick:t,children:n="Add New",showPlus:o=!0,className:r,showChildren:i=!0})=>{const[s,e]=m.useState(!1),a=()=>{t&&t(),e(!0)};return d.jsxs("button",{onAnimationEnd:()=>e(!1),onClick:a,className:`${s&&"animate-wiggle"} ${l.button} relative flex h-[2.125rem] w-fit min-w-fit items-center justify-center overflow-hidden rounded-md border border-primaryBlue bg-indigo-600 px-[.6125rem] py-[.5625rem] text-sm font-medium leading-none text-white shadow-md shadow-indigo-600  ${r}`,children:[o?"+":null," ",i?n:null]})};export{x as A};

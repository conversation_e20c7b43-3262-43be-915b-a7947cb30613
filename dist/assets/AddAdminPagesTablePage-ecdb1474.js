import{j as e}from"./@react-google-maps/api-4794cf1a.js";import{R as s,b as A}from"./vendor-dd4ba10b.js";import{u as v}from"./react-hook-form-a6ecef1c.js";import{o as E}from"./yup-f7f8305f.js";import{c as k,a as g}from"./yup-79911193.js";import{G as F,A as R,M as D,s as I,t as P}from"./index-3efdd896.js";import"./react-quill-25360d36.js";import{M as T}from"./MkdInput-bb15886f.js";import{I as C}from"./InteractiveButton-6ddb3b9d.js";import"./index-3b0c955b.js";import"./@hookform/resolvers-61c06af0.js";import"./react-confirm-alert-5d5c0db6.js";import"./html2pdf.js-19c9759c.js";import"./@headlessui/react-7b0d4887.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-1762f471.js";import"./@fortawesome/react-fontawesome-0b111e8e.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";import"./@craftjs/core-3d3a3b40.js";import"./MoonLoader-795b8e10.js";const ie=({setSidebar:b})=>{var x,h;const{dispatch:i}=s.useContext(F),y=k({name:g(),features:g()}).required(),{dispatch:j}=s.useContext(R),[p,M]=s.useState({}),[u,l]=s.useState(!1),w=A(),{register:c,handleSubmit:S,setError:f,setValue:O,formState:{errors:m}}=v({resolver:E(y)});s.useState([]);const N=async n=>{let d=new D;l(!0);try{for(let o in p){let a=new FormData;a.append("file",p[o].file);let r=await d.uploadImage(a);n[o]=r.url}d.setTable("stripe_price");const t=await d.callRestAPI({name:n.name,features:n.features},"POST");if(!t.error)I(i,"Added"),w("/admin/pages"),b(!1),i({type:"REFRESH_DATA",payload:{refreshData:!0}});else if(t.validation){const o=Object.keys(t.validation);for(let a=0;a<o.length;a++){const r=o[a];f(r,{type:"manual",message:t.validation[r]})}}l(!1)}catch(t){l(!1),console.log("Error",t),f("name",{type:"manual",message:t.message}),P(j,t.message)}};return s.useEffect(()=>{i({type:"SETPATH",payload:{path:"pages"}})},[]),e.jsxs("div",{className:" shadow-md rounded  mx-auto p-5",children:[e.jsx("h4",{className:"text-2xl font-medium",children:"Add Prices"}),e.jsxs("form",{className:" w-full max-w-lg",onSubmit:S(N),children:[e.jsx(T,{type:"text",page:"add",name:"name",errors:m,label:"Name",placeholder:"Name",register:c,className:""}),e.jsxs("div",{className:"mb-4  ",children:[e.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"features",children:"Features"}),e.jsx("textarea",{placeholder:"Features",...c("features"),className:`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(x=m.features)!=null&&x.message?"border-red-500":""}`,row:50}),e.jsx("p",{className:"text-red-500 text-xs italic",children:(h=m.features)==null?void 0:h.message})]}),e.jsx(C,{type:"submit",loading:u,disabled:u,className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:"Submit"})]})]})};export{ie as default};

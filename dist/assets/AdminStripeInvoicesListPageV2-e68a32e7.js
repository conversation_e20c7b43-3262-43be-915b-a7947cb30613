import{j as e}from"./@react-google-maps/api-4794cf1a.js";import{R as a,b as Z}from"./vendor-dd4ba10b.js";import{M as ee,G as te,A as se,t as ae,g as re}from"./index-3efdd896.js";import{o as ne}from"./yup-f7f8305f.js";import{u as ie}from"./react-hook-form-a6ecef1c.js";import{c as oe,a as ce}from"./yup-79911193.js";import{P as le}from"./index-fe50f63a.js";import{a as de,b as ue}from"./index.esm-c1b51ffb.js";import{A as pe,a as me}from"./index.esm-8010d0dc.js";import{R as xe}from"./index.esm-3f4ea327.js";import{S as he}from"./index-3b0c955b.js";import"./react-confirm-alert-5d5c0db6.js";import"./html2pdf.js-19c9759c.js";import"./@headlessui/react-7b0d4887.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-1762f471.js";import"./@fortawesome/react-fontawesome-0b111e8e.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";import"./@hookform/resolvers-61c06af0.js";import"./react-icons-0b96c072.js";const f=[{header:"Status",accessor:"status"},{header:"Currency",accessor:"currency"},{header:"Amount due",accessor:"amount_due",type:"currency"},{header:"Amount paid",accessor:"amount_paid",type:"currency"},{header:"Amount remaining",accessor:"amount_remaining",type:"currency"},{header:"Created at",accessor:"created_at",type:"timestamp"}],Ve=()=>{const _=new ee,{dispatch:y}=a.useContext(te),{dispatch:E}=a.useContext(se);a.useState("");const[R,$]=a.useState([]),[o,j]=a.useState(10),[w,D]=a.useState(0),[ge,L]=a.useState(0),[c,O]=a.useState(0),[T,q]=a.useState(!1),[z,I]=a.useState(!1),[b,v]=a.useState(!1),[N,S]=a.useState(!1),[i,u]=a.useState([]),[C,p]=a.useState([]),[B,V]=a.useState(""),[P,G]=a.useState("eq"),[M,k]=a.useState(!1);Z();const m=a.useRef(null),H=oe({customer_email:ce()}),{register:fe,handleSubmit:K,formState:{errors:ye}}=ie({resolver:ne(H)}),A=(t,r,s)=>{const n=r==="eq"&&isNaN(s)?`"${s}"`:s,x=`${t},${r},${n}`;p(h=>[...h.filter(d=>!d.includes(t)),x]),V(s)};function U(t){(async function(){j(t),await l(1,t)})()}function J(){(async function(){await l(c-1>1?c-1:1,o)})()}function Q(){(async function(){await l(c+1<=w?c+1:1,o)})()}async function l(t,r,s){k(!0);try{const{list:n,total:x,limit:h,num_pages:g,page:d,error:X,message:Y}=await _.getStripeInvoicesV2({page:t,limit:r},`filter=${s.toString()}`);if(X){showToast(y,Y,5e3);return}$(n),j(+h),D(+g),O(+d),L(+x),q(+d>1),I(+d+1<=+g)}catch(n){console.log("ERROR",n),ae(E,n.message)}k(!1)}const W=t=>{const r=re(t.customer_email);l(1,o,{customer_email:r,product_name})};a.useEffect(()=>{y({type:"SETPATH",payload:{path:"invoices"}});const r=setTimeout(async()=>{await l(1,o,C)},700);return()=>{clearTimeout(r)}},[B,C,P]);const F=t=>{m.current&&!m.current.contains(t.target)&&v(!1)};return a.useEffect(()=>(document.addEventListener("mousedown",F),()=>{document.removeEventListener("mousedown",F)}),[]),e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"flex items-center justify-between py-3",children:e.jsx("form",{className:"relative rounded bg-white",onSubmit:K(W),children:e.jsxs("div",{className:"flex items-center gap-4 text-gray-700 text-nowrap",children:[e.jsxs("div",{className:"relative",ref:m,children:[e.jsxs("div",{className:"flex cursor-pointer items-center justify-between gap-3 rounded-md border border-gray-200 px-3 py-1",onClick:()=>v(!b),children:[e.jsx(de,{}),e.jsx("span",{children:"Filters"}),i.length>0&&e.jsx("span",{className:"flex h-6 w-6 items-center justify-center rounded-full bg-gray-800 text-start text-white",children:i.length})]}),b&&e.jsx("div",{className:"absolute z-10 mt-4 w-[500px] min-w-[90%] top-fill left-0 filter-form-holder bg-white border border-gray-200 rounded-md shadow-lg",children:e.jsxs("div",{className:"p-4",children:[i==null?void 0:i.map((t,r)=>e.jsxs("div",{className:"mb-2 flex w-full items-center justify-between gap-3 text-gray-600",children:[e.jsx("div",{className:" mb-3 w-1/3 rounded-md border border-gray-300 px-3 py-2 leading-tight text-gray-700 outline-none",children:t}),e.jsxs("select",{className:"mb-3 w-[40%] rounded-md border px-3 py-2 leading-tight text-gray-700 outline-none",onChange:s=>{G(s.target.value)},children:[e.jsx("option",{value:"eq",selected:!0,children:"equals"}),e.jsx("option",{value:"cs",children:"contains"}),e.jsx("option",{value:"sw",children:"start with"}),e.jsx("option",{value:"ew",children:"ends with"}),e.jsx("option",{value:"lt",children:"lower than"}),e.jsx("option",{value:"le",children:"lower or equal"}),e.jsx("option",{value:"ge",children:"greater or equal"}),e.jsx("option",{value:"gt",children:"greater than"}),e.jsx("option",{value:"bt",children:"between"}),e.jsx("option",{value:"in",children:"in"}),e.jsx("option",{value:"is",children:"is null"})]}),e.jsx("input",{type:"text",placeholder:"Enter value...",className:" mb-3 w-1/3 rounded-md border px-3 py-2 leading-tight text-gray-700 outline-none",onChange:s=>A(t,P,s.target.value)}),e.jsx("div",{className:"w-1/12 mt-[-10px]",children:e.jsx(xe,{className:" cursor-pointer text-xl",onClick:()=>{u(s=>s.filter(n=>n!==t)),p(s=>s.filter(n=>!n.includes(t)))}})})]},r)),e.jsxs("div",{className:"search-buttons relative flex items-center justify-between font-semibold",children:[e.jsxs("div",{className:"mr-2 flex w-auto cursor-pointer items-center gap-2 rounded bg-gray-100 px-4 py-2.5 font-medium leading-tight text-gray-600 transition duration-150 ease-in-out ",onClick:()=>{S(!N)},children:[e.jsx(pe,{}),"Add filter"]}),N&&e.jsx("div",{className:"absolute top-11 z-10 bg-white px-5 py-3 text-gray-600 shadow-md",children:e.jsx("ul",{className:"flex flex-col gap-2 text-gray-500",children:f.slice(0,-1).map(t=>e.jsx("li",{className:`${i.includes(t.header)?"cursor-not-allowed text-gray-400":"cursor-pointer"}`,onClick:()=>{i.includes(t.header)||u(r=>[...r,t.accessor]),S(!1)},children:t.header},t.header))})}),i.length>0&&e.jsx("div",{onClick:()=>{u([]),p([])},className:"inline-block cursor-pointer  rounded px-6  py-2.5 font-medium leading-tight text-gray-600  transition duration-150 ease-in-out",children:"Clear all filter"})]})]})})]}),e.jsxs("div",{className:" flex cursor-pointer items-center justify-between gap-3 rounded-md border border-gray-200 px-2 py-1 focus-within:border-gray-400",children:[e.jsx(ue,{className:"text-xl text-gray-200"}),e.jsx("input",{type:"text",placeholder:"search...",className:"border-none p-0 placeholder:text-left  focus:outline-none",style:{boxShadow:"0 0 transparent"},onInput:t=>{var r;return A("name","cs",(r=t.target)==null?void 0:r.value)}}),e.jsx(me,{className:"text-lg text-gray-200"})]})]})})}),M?e.jsx(he,{}):e.jsx("div",{className:"overflow-x-auto rounded-md border border-gray-200 shadow ",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsx("tr",{children:f.map((t,r)=>e.jsxs("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500",children:[t.header,e.jsx("span",{children:t.isSorted?t.isSortedDesc?" ▼":" ▲":""})]},r))})}),e.jsx("tbody",{className:"divide-y divide-gray-200 bg-white",children:R.map((t,r)=>e.jsx("tr",{children:f.map((s,n)=>s.accessor==""?e.jsx("td",{className:"whitespace-nowrap px-6 py-4"},n):s.mapping?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:s.mapping[t[s.accessor]]},n):s.type==="timestamp"?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:new Date(t[s.accessor]*1e3).toLocaleString("en-US")},n):s.type==="currency"?e.jsxs("td",{className:"whitespace-nowrap px-6 py-4",children:["$",Number(t[s.accessor]/100).toFixed(2)]},n):s.type==="metadata"?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:t[s.pre_accessor][s.accessor]??"n/a"},n):e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:t[s.accessor]},n))},r))})]})}),e.jsx(le,{currentPage:c,pageCount:w,pageSize:o,canPreviousPage:T,canNextPage:z,updatePageSize:U,previousPage:J,nextPage:Q})]})};export{Ve as default};

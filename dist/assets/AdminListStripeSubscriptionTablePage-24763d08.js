import{j as e}from"./@react-google-maps/api-4794cf1a.js";import{R as s,b as x,r as b}from"./vendor-dd4ba10b.js";import{M as g,A as w,G as E,L as r,b as A,a as p,c as j,d as D}from"./index-3efdd896.js";import"./react-confirm-alert-5d5c0db6.js";import"./html2pdf.js-19c9759c.js";import"./@headlessui/react-7b0d4887.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-1762f471.js";import"./@fortawesome/react-fontawesome-0b111e8e.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";new g;const I=[{header:"Id",accessor:"id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Stripe Id",accessor:"stripe_id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Price Id",accessor:"price_id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"User Id",accessor:"user_id",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Object",accessor:"object",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Status",accessor:"status",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Is Lifetime",accessor:"is_lifetime",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Create At",accessor:"create_at",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Update At",accessor:"update_at",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Action",accessor:""}],z=()=>{s.useContext(w);const{dispatch:n}=s.useContext(E);x();const[m,t]=s.useState(!1),[o,a]=s.useState(!1),[f,u]=s.useState(),S=b.useRef(null),[_,h]=s.useState([]),d=(i,l,c=[])=>{switch(i){case"add":t(l);break;case"edit":a(l),h(c),u(c[0]);break}};return s.useEffect(()=>{n({type:"SETPATH",payload:{path:"stripe_subscription"}})},[]),e.jsxs(e.Fragment,{children:[e.jsx(e.Fragment,{children:e.jsx("div",{className:"overflow-x-auto  rounded bg-white p-5 shadow",children:e.jsx(r,{children:e.jsx(A,{columns:I,tableRole:"admin",table:"stripe_subscription",actionId:"id",actions:{view:{show:!0,action:null,multiple:!1},edit:{show:!0,multiple:!1,action:i=>d("edit",!0,i)},delete:{show:!0,action:null,multiple:!1},select:{show:!0,action:null,multiple:!1},add:{show:!0,action:()=>d("add",!0),multiple:!1,children:"Add New",showChildren:!0},export:{show:!1,action:null,multiple:!0}},actionPostion:"ontable",refreshRef:S})})})}),e.jsx(r,{children:e.jsx(p,{isModalActive:m,closeModalFn:()=>t(!1),children:e.jsx(j,{setSidebar:t})})}),o&&e.jsx(r,{children:e.jsx(p,{isModalActive:o,closeModalFn:()=>a(!1),children:e.jsx(D,{activeId:f,setSidebar:a})})})]})};export{z as default};

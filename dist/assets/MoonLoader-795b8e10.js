import{c as o,r as L}from"./vendor-dd4ba10b.js";var w={},c={};Object.defineProperty(c,"__esModule",{value:!0});c.cssValue=c.parseLengthAndUnit=void 0;var U={cm:!0,mm:!0,in:!0,px:!0,pt:!0,pc:!0,em:!0,ex:!0,ch:!0,rem:!0,vw:!0,vh:!0,vmin:!0,vmax:!0,"%":!0};function j(e){if(typeof e=="number")return{value:e,unit:"px"};var t,r=(e.match(/^[0-9.]*/)||"").toString();r.includes(".")?t=parseFloat(r):t=parseInt(r,10);var n=(e.match(/[^0-9]*$/)||"").toString();return U[n]?{value:t,unit:n}:(console.warn("React Spinners: ".concat(e," is not a valid css value. Defaulting to ").concat(t,"px.")),{value:t,unit:"px"})}c.parseLengthAndUnit=j;function V(e){var t=j(e);return"".concat(t.value).concat(t.unit)}c.cssValue=V;var v={};Object.defineProperty(v,"__esModule",{value:!0});v.createAnimation=void 0;var C=function(e,t,r){var n="react-spinners-".concat(e,"-").concat(r);if(typeof window>"u"||!window.document)return n;var a=document.createElement("style");document.head.appendChild(a);var u=a.sheet,d=`
    @keyframes `.concat(n,` {
      `).concat(t,`
    }
  `);return u&&u.insertRule(d,0),n};v.createAnimation=C;var i=o&&o.__assign||function(){return i=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++){t=arguments[r];for(var a in t)Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a])}return e},i.apply(this,arguments)},D=o&&o.__createBinding||(Object.create?function(e,t,r,n){n===void 0&&(n=r);var a=Object.getOwnPropertyDescriptor(t,r);(!a||("get"in a?!t.__esModule:a.writable||a.configurable))&&(a={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,n,a)}:function(e,t,r,n){n===void 0&&(n=r),e[n]=t[r]}),F=o&&o.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),R=o&&o.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(e!=null)for(var r in e)r!=="default"&&Object.prototype.hasOwnProperty.call(e,r)&&D(t,e,r);return F(t,e),t},B=o&&o.__rest||function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,n=Object.getOwnPropertySymbols(e);a<n.length;a++)t.indexOf(n[a])<0&&Object.prototype.propertyIsEnumerable.call(e,n[a])&&(r[n[a]]=e[n[a]]);return r};Object.defineProperty(w,"__esModule",{value:!0});var p=R(L),m=c,I=v,O=(0,I.createAnimation)("MoonLoader","100% {transform: rotate(360deg)}","moon");function $(e){var t=e.loading,r=t===void 0?!0:t,n=e.color,a=n===void 0?"#000000":n,u=e.speedMultiplier,d=u===void 0?1:u,_=e.cssOverride,M=_===void 0?{}:_,b=e.size,x=b===void 0?60:b,S=B(e,["loading","color","speedMultiplier","cssOverride","size"]),g=(0,m.parseLengthAndUnit)(x),l=g.value,f=g.unit,s=l/7,P=i({display:"inherit",position:"relative",width:"".concat("".concat(l+s*2).concat(f)),height:"".concat("".concat(l+s*2).concat(f)),animation:"".concat(O," ").concat(.6/d,"s 0s infinite linear"),animationFillMode:"forwards"},M),y=function(h){return{width:(0,m.cssValue)(h),height:(0,m.cssValue)(h),borderRadius:"100%"}},A=i(i({},y(s)),{backgroundColor:"".concat(a),opacity:"0.8",position:"absolute",top:"".concat("".concat(l/2-s/2).concat(f)),animation:"".concat(O," ").concat(.6/d,"s 0s infinite linear"),animationFillMode:"forwards"}),E=i(i({},y(l)),{border:"".concat(s,"px solid ").concat(a),opacity:"0.1",boxSizing:"content-box",position:"absolute"});return r?p.createElement("span",i({style:P},S),p.createElement("span",{style:A}),p.createElement("span",{style:E})):null}var N=w.default=$;export{N as _};

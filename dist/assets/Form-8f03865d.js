import{j as e}from"./@react-google-maps/api-4794cf1a.js";import{R as p,b as T,u as M,r as y,L as g}from"./vendor-dd4ba10b.js";import{A as E,G as B,M as W,s as n,t as D}from"./index-3efdd896.js";import{M as R}from"./monitor-8a3dd572.js";import{F as I}from"./formicon-d938e1f3.js";import"./react-confirm-alert-5d5c0db6.js";import"./html2pdf.js-19c9759c.js";import"./@headlessui/react-7b0d4887.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-1762f471.js";import"./@fortawesome/react-fontawesome-0b111e8e.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";const Z=()=>{const{dispatch:k,state:x}=p.useContext(E),{dispatch:r}=p.useContext(B),w=T(),j=M(),N=new W,{resumeUrl:d,historyUrl:c,documentTitle:m}=j.state||{},u=x==null?void 0:x.user,h={highPerformanceTeams:{title:"High Performance Teams",questions:[{id:"sportsStories",text:"Any memorable stories from your time playing sports? Be Specific.",isOptional:!1},{id:"teamChallenges",text:"What kind of challenges did you and your sports teammates overcome?",isOptional:!0},{id:"bestTeam",text:"What is the best performing work team you have ever been on?",isOptional:!1},{id:"bestCrew",text:"Think about the best crew you have ever been on at work... who was on it and why did it work so well?",isOptional:!0},{id:"hardestEducation",text:"What was the hardest part of your education?",isOptional:!1},{id:"teamMistake",text:"Have you ever been on a team where there was a massive mistake to overcome?",isOptional:!0},{id:"workMistake",text:"Can you remember a time when you got in trouble at work for making a mistake?",isOptional:!1},{id:"volunteerStories",text:"Are there any memorable stories from your time at church, while volunteering, or with family?",isOptional:!0},{id:"likedEveryone",text:"When's the last time you walked into a place and liked every single person there? Why?",isOptional:!0},{id:"taskReliance",text:"Have you ever had to deeply rely on someone to get an important task or project completed?",isOptional:!1},{id:"helpedChallenge",text:"Was there ever a major setback or challenge that you helped someone get through?",isOptional:!1},{id:"uncomfortableTask",text:"When's the last time someone delegated a task to you that you felt uncomfortable about completing?",isOptional:!1},{id:"trainingDifficulty",text:"Have you ever had to train someone that wasn't understanding the important concepts of the role?",isOptional:!1},{id:"significantTeam",text:"Describe a time you were part of a team that achieved something significant. What was your role?",isOptional:!1},{id:"teamworkObstacle",text:"Share an experience where teamwork helped you overcome a major obstacle.",isOptional:!0},{id:"deadlinePressure",text:"Have you ever worked with a team under intense pressure to meet a deadline?",isOptional:!1},{id:"poorTeamDynamics",text:"Describe a situation where poor team dynamics affected the outcome.",isOptional:!1},{id:"motivatingOthers",text:"Tell us about a time when you motivated others to work together toward a common goal.",isOptional:!0},{id:"unclearRoles",text:"Have you ever been part of a team where roles and responsibilities weren't clear?",isOptional:!1},{id:"rulesBending",text:"Have you ever been tempted to do some things that went against the rules or best practices?",isOptional:!0}]},situationalAwareness:{title:"Situational Awareness",questions:[{id:"stressedOut",text:"Share a time when you felt really stressed out about all the things you had to accomplish piling up.",isOptional:!1},{id:"preventAccident",text:"Share a time when being aware of your surroundings prevented an accident or problem.",isOptional:!1},{id:"quickThinking",text:"Describe a situation where quick thinking and awareness helped you adapt to unexpected changes.",isOptional:!0},{id:"noticeAction",text:"Tell us about a time when you noticed something others didn't and took action.",isOptional:!0},{id:"multipleRisks",text:"Have you ever had to assess multiple risks at once? What did you do?",isOptional:!0},{id:"missedImportant",text:"Recall a moment when you failed to notice something important. What was the impact, and what did you learn?",isOptional:!1},{id:"protectOthers",text:"Describe a time you relied on situational awareness to protect others.",isOptional:!0}]},problemSolving:{title:"Good Problem Solving",questions:[{id:"complexProblem",text:"Share a time when you solved a complex problem under pressure.",isOptional:!1},{id:"creativeThinking",text:"Describe a situation where your creative thinking led to a solution nobody else considered.",isOptional:!0},{id:"outsideExpertise",text:"Have you ever been tasked with fixing something outside of your expertise? What was your approach?",isOptional:!1},{id:"limitedResources",text:"Tell us about a time when you had limited resources and still managed to resolve an issue.",isOptional:!0},{id:"multipleInput",text:"Recall a problem that required input from multiple people. How did you coordinate the solution?",isOptional:!1},{id:"dataAnalysis",text:"Describe a moment when you analyzed data or information to solve a problem effectively.",isOptional:!0},{id:"complicatedSolution",text:"Have you ever had to communicate a complicated solution to solve a big problem?",isOptional:!1},{id:"trustedAdvisor",text:"Have you ever been a trusted advisor or has anyone ever confided in you deep personal information?",isOptional:!1},{id:"calmArgument",text:"Have you ever had to calm down an argument between two or more people?",isOptional:!1},{id:"angryCalming",text:"When's the last time you got really mad at someone and had to figure out how to calm down?",isOptional:!1}]},customerService:{title:"Customer Service",questions:[{id:"aboveAndBeyond",text:"Share an experience where you went above and beyond to help a customer or client.",isOptional:!1},{id:"angryCustomer",text:"Describe a time when you had to manage an upset or angry customer. What did you do?",isOptional:!1},{id:"clearCommunication",text:"Tell us about a situation where clear communication improved a customer's experience.",isOptional:!0},{id:"balanceRules",text:"Recall a time when you had to balance customer satisfaction with organizational rules.",isOptional:!0},{id:"tightDeadline",text:"Have you ever solved a problem for a customer that had a tight deadline? How did you handle it?",isOptional:!0},{id:"diverseBackground",text:"Share an example of when you helped someone from a different background feel comfortable or understood.",isOptional:!1},{id:"disabledExperience",text:"What kind of direct experience do you have with people who are terminally ill or disabled? Did you solve a problem for them?",isOptional:!1},{id:"languageBarrier",text:"Have you ever had to translate instructions or help someone that didn't speak English? How did you handle it?",isOptional:!1}]},buildingConstruction:{title:"Building Construction and Mechanical Aptitude",questions:[{id:"complexBuild",text:"Have you ever fixed or built something complex? What tools or techniques did you use?",isOptional:!1},{id:"mechanicalKnowledge",text:"Share an example of a time when mechanical knowledge helped you solve a problem.",isOptional:!1},{id:"buildingLayout",text:"Tell us about a situation where understanding a building's layout or construction was critical.",isOptional:!1},{id:"mechanicalIssue",text:"Have you ever identified a mechanical issue before it became a larger problem? How?",isOptional:!0},{id:"challengingConditions",text:"Describe a time you worked with machinery or tools in challenging conditions.",isOptional:!0},{id:"teachingMechanical",text:"Recall a moment when you taught someone else about mechanical or construction concepts.",isOptional:!0},{id:"workShortcuts",text:"What kind of shortcuts do you use at work to help you make jobs more efficient or safer?",isOptional:!1}]},emergencyMedical:{title:"Emergency Medical Experience",questions:[{id:"medicalAssistance",text:"Share a time when you provided medical assistance in an emergency.",isOptional:!1},{id:"calmMedical",text:"Describe a situation where you stayed calm and helped someone in a high-stress medical situation.",isOptional:!1},{id:"prioritizeCare",text:"Tell us about a time when you had to prioritize care for multiple individuals.",isOptional:!0},{id:"firstAidSkills",text:"Have you ever had to use first aid skills to stabilize someone? What steps did you take?",isOptional:!0},{id:"healthEducation",text:"Recall a moment when you educated someone about health or safety.",isOptional:!0},{id:"medicalKnowledge",text:"Share an example of when your medical knowledge directly impacted a positive outcome.",isOptional:!1},{id:"correctMedical",text:"Have you ever had to correct someone about the way they were handling a medical situation?",isOptional:!1}]},mentalPhysicalHealth:{title:"Mental and Physical Health",questions:[{id:"personalChallenge",text:"Describe a time when you overcame a personal challenge through resilience and determination.",isOptional:!1},{id:"helpedCope",text:"Share an example of when you helped someone else cope with stress or hardship.",isOptional:!1},{id:"suicidalSituation",text:"Have you ever had to deal with someone that was suicidal or looking to harm themselves physically?",isOptional:!1},{id:"physicalFitness",text:"Tell us about a situation where maintaining physical fitness made a significant difference in your performance.",isOptional:!1},{id:"healthImprovement",text:"Have you ever made a change to improve your mental or physical health? What was the result?",isOptional:!0},{id:"balanceStress",text:"Recall a moment when you balanced multiple stressful responsibilities without compromising your well-being.",isOptional:!0},{id:"supportTeamHealth",text:"Share an experience where you supported a team member's mental or physical health during a tough time.",isOptional:!0}]}},[l,S]=y.useState(()=>{if(u){const i=localStorage.getItem(`formAnswers_${u}`);if(i)return JSON.parse(i)}const t={};return Object.values(h).forEach(i=>{i.questions.forEach(a=>{t[a.id]=""})}),t}),[b,C]=y.useState(()=>{const t={};return Object.keys(l).forEach(i=>{t[i]=l[i].length}),t}),[f,v]=y.useState(!1),A=(t,i)=>{const a={...l,[t]:i};S(a),C(s=>({...s,[t]:i.length})),u&&localStorage.setItem(`formAnswers_${u}`,JSON.stringify(a))},H=async()=>{try{if(v(!0),Object.values(h).flatMap(o=>o.questions).filter(o=>!o.isOptional).filter(o=>b[o.id]<100).length>0){n(r,"All mandatory questions must have at least 100 characters",4e3,"error");return}if(!d||!c||!m){n(r,"Missing required document information",4e3,"error");return}const a=Object.values(h).flatMap(o=>o.questions.map(O=>({question:O.text,answer:l[O.id]||""}))),s=await N.callRawAPI("/v3/api/custom/theme-document/upload",{answer:JSON.stringify(a),resume_url:d,history_doc_url:c,title:m},"POST");if(!s.error)localStorage.setItem("theme_documents_id",s.data.toString()),n(r,"Successfully submitted answers",4e3,"success"),w("/user/onboarding/theme");else if(s.validation){const o=Object.values(s.validation).join(", ");n(r,o,4e3,"error")}}catch(t){console.error("Submission error:",t),n(r,t.message||"An error occurred while submitting the form",4e3,"error"),D(k,t.message)}finally{v(!1)}};return p.useEffect(()=>{(!d||!c||!m)&&(n(r,"Missing required document information. Please upload documents first.",4e3,"error"),w("/user/onboarding"))},[d,c,m]),p.useEffect(()=>{window.scrollTo(0,0)},[]),e.jsx(e.Fragment,{children:e.jsx("div",{className:"w-full text-7xl min-h-screen h-full text-gray-700 bg-[#E2E6EB] md:p-[1rem] md:pl-[3rem] p-0",children:e.jsxs("div",{className:"w-[95%] md:w-[90%] mx-auto md:mx-0",children:[e.jsx("h1",{className:"text-[20px] text-[#111928] font-semibold mb-6",children:"Generate themes document"}),e.jsxs("div",{className:"space-y-8",children:[e.jsxs("div",{className:"flex items-center justify-between mb-8 relative bg-white h-[98px] p-[1rem]",children:[e.jsx("div",{className:"absolute left-8 right-8 top-1/4 md:top-1/3 h-0.5 bg-gray-200 w-[100% - 2rem]"}),e.jsxs("div",{className:"flex flex-col items-start z-10 w-[33%]",children:[e.jsx("div",{className:"w-10 h-10 border border-[#9C9C9C] bg-white rounded-full flex items-center justify-center",children:e.jsx("div",{className:"w-6 h-6 bg-[#389C14] rounded-full flex items-center  justify-center",children:e.jsx("svg",{className:"w-4 h-4 text-white",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"})})})}),e.jsx(g,{to:"/user/onboarding",className:"text-sm hover:text-[#054FB1] transition-colors",children:"Upload your resume & full history docs"})]}),e.jsxs("div",{className:"flex flex-col items-center z-10 w-[33%]",children:[e.jsx("div",{className:"w-8 h-8 bg-[#054FB1] rounded-full flex items-center justify-center text-white mb-2",children:e.jsx("img",{src:I,className:"w-[16px] h-[16px]",alt:"upload icon"})}),e.jsx(g,{to:"/user/onboarding/form",className:"text-sm text-[#054FB1]",children:"Fill out questions form"})]}),e.jsxs("div",{className:"flex flex-col items-end z-10 w-[33%]",children:[e.jsx("div",{className:"w-8 h-8 bg-[#9C9C9C] rounded-full flex items-center justify-center text-gray-500 mb-2",children:e.jsx("img",{src:R,className:"w-[20px] h-[20px]",alt:"upload icon"})}),e.jsx(g,{to:"/user/onboarding/theme",className:"text-sm hover:text-[#054FB1] transition-colors",children:"Generate Themes (0/20)"})]})]}),e.jsx("div",{className:"text-[12px] md:text-[16px] font-bold text-[#373A4B] text-right",children:"25% completed"}),e.jsx("div",{className:"space-y-6",children:Object.entries(h).map(([t,i])=>e.jsxs("div",{className:"bg-white p-[1rem] rounded-lg",children:[e.jsx("h2",{className:"text-xl font-bold mb-4 text-[#111928]",children:i.title}),i.questions.map(a=>e.jsxs("div",{className:"mb-6",children:[e.jsxs("label",{className:"block text-sm font-medium mb-2 text-[#111928] text-[16px]",children:[e.jsxs("span",{className:"text-gray-600",children:[!a.isOptional&&e.jsx("span",{className:"text-red-600 mr-1",children:"*"}),a.text]}),a.isOptional&&e.jsx("span",{className:"ml-2 text-gray-500",children:"(Optional)"})]}),e.jsx("textarea",{className:"w-full h-32 p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 [&::-webkit-scrollbar]:w-1 [&::-webkit-scrollbar-thumb]:bg-gray-300 [&::-webkit-scrollbar-thumb]:rounded-lg [&::-webkit-scrollbar-track]:bg-gray-100",placeholder:"Type your answer here...",value:l[a.id],onChange:s=>A(a.id,s.target.value)}),e.jsxs("div",{className:"flex justify-between mt-1 text-xs text-gray-500",children:[e.jsx("span",{className:"text-[#373A4B99] text-[14px]",children:a.isOptional?"":"Minimum 100 characters"}),e.jsxs("span",{className:"text-[#373A4B99] text-[14px]",children:[b[a.id]," characters"]})]})]},a.id))]},t))}),e.jsx("div",{className:"flex",children:e.jsx("button",{onClick:H,disabled:f,className:`bg-[#054FB1] text-white text-[16px] leading-[24px] px-6 py-2 rounded-md hover:bg-blue-700 transition-colors w-[231px] h-[50px] ml-auto ${f?"opacity-50 cursor-not-allowed":""}`,children:f?"SUBMITTING...":"CONTINUE"})})]})]})})})};export{Z as default};

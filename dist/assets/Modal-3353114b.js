import{j as e}from"./@react-google-maps/api-4794cf1a.js";import{r as d}from"./vendor-dd4ba10b.js";import{b as i}from"./index.esm-f1328d83.js";import"./react-icons-0b96c072.js";const m=({children:t,title:r,modalCloseClick:s,modalHeader:l,classes:o,page:a=""})=>e.jsx("div",{style:{zIndex:100000002,transform:"translate(-50%, -50%)"},className:"modal-holder fixed left-[50%] top-[50%] flex h-[100vh] w-full items-center justify-center overflow-auto bg-[#00000099]",children:e.jsxs("div",{className:`${a==="ManagePermissionAddRole"?"w-fit":"w-[80%]"} rounded-lg bg-white py-5 shadow ${o==null?void 0:o.modalDialog} `,children:[l&&e.jsxs("div",{className:"flex justify-between border-b px-5 pb-2",children:[e.jsx("h5",{className:"text-center text-lg font-bold uppercase",children:r}),e.jsx("div",{className:"modal-close cursor-pointer",onClick:s,children:e.jsx(i,{className:"text-xl"})})]}),e.jsx("div",{className:"mt-4 px-5",children:t})]})}),f=d.memo(m);export{f as Modal};

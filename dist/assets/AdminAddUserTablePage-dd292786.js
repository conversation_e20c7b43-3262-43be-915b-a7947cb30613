import{j as e}from"./@react-google-maps/api-4794cf1a.js";import{R as i,b as P}from"./vendor-dd4ba10b.js";import{u as k}from"./react-hook-form-a6ecef1c.js";import{o as v}from"./yup-f7f8305f.js";import{c as A,a as o}from"./yup-79911193.js";import{G as E,A as R,M as T,s as F,t as I}from"./index-3efdd896.js";import"./react-quill-25360d36.js";import{M as l}from"./MkdInput-bb15886f.js";import{I as D}from"./InteractiveButton-6ddb3b9d.js";import"./index-3b0c955b.js";import"./@hookform/resolvers-61c06af0.js";import"./react-confirm-alert-5d5c0db6.js";import"./html2pdf.js-19c9759c.js";import"./@headlessui/react-7b0d4887.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-1762f471.js";import"./@fortawesome/react-fontawesome-0b111e8e.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";import"./@craftjs/core-3d3a3b40.js";import"./MoonLoader-795b8e10.js";const re=({setSidebar:g})=>{var y,b;const{dispatch:n}=i.useContext(E),j=A({oauth:o(),role:o(),first_name:o(),last_name:o(),email:o(),password:o(),type:o(),verify:o(),packages_id:o(),phone:o(),photo:o(),refer:o(),stripe_uid:o(),paypal_uid:o(),two_factor_authentication:o(),status:o()}).required(),{dispatch:N}=i.useContext(R),[h,O]=i.useState({}),[f,c]=i.useState(!1),w=P(),{register:s,handleSubmit:S,setError:x,setValue:U,formState:{errors:a}}=k({resolver:v(j)});i.useState([]);const _=async t=>{let u=new T;c(!0);try{for(let m in h){let p=new FormData;p.append("file",h[m].file);let d=await u.uploadImage(p);t[m]=d.url}u.setTable("user");const r=await u.callRestAPI({oauth:t.oauth,role:t.role,first_name:t.first_name,last_name:t.last_name,email:t.email,password:t.password,type:t.type,verify:t.verify,packages_id:t.packages_id,phone:t.phone,photo:t.photo,refer:t.refer,stripe_uid:t.stripe_uid,paypal_uid:t.paypal_uid,two_factor_authentication:t.two_factor_authentication,status:t.status},"POST");if(!r.error)F(n,"Added"),w("/admin/user"),g(!1),n({type:"REFRESH_DATA",payload:{refreshData:!0}});else if(r.validation){const m=Object.keys(r.validation);for(let p=0;p<m.length;p++){const d=m[p];x(d,{type:"manual",message:r.validation[d]})}}c(!1)}catch(r){c(!1),console.log("Error",r),x("oauth",{type:"manual",message:r.message}),I(N,r.message)}};return i.useEffect(()=>{n({type:"SETPATH",payload:{path:"user"}})},[]),e.jsxs("div",{className:" shadow-md rounded  mx-auto p-5",children:[e.jsx("h4",{className:"text-2xl font-medium",children:"Add User"}),e.jsxs("form",{className:" w-full max-w-lg",onSubmit:S(_),children:[e.jsx(l,{type:"text",page:"add",name:"oauth",errors:a,label:"Oauth",placeholder:"Oauth",register:s,className:""}),e.jsx(l,{type:"text",page:"add",name:"role",errors:a,label:"Role",placeholder:"Role",register:s,className:""}),e.jsx(l,{type:"text",page:"add",name:"first_name",errors:a,label:"First Name",placeholder:"First Name",register:s,className:""}),e.jsx(l,{type:"text",page:"add",name:"last_name",errors:a,label:"Last Name",placeholder:"Last Name",register:s,className:""}),e.jsx(l,{type:"text",page:"add",name:"email",errors:a,label:"Email",placeholder:"Email",register:s,className:""}),e.jsx(l,{type:"text",page:"add",name:"password",errors:a,label:"Password",placeholder:"Password",register:s,className:""}),e.jsx(l,{type:"number",page:"add",name:"type",errors:a,label:"Type",placeholder:"Type",register:s,className:""}),e.jsx(l,{type:"number",page:"add",name:"verify",errors:a,label:"Verify",placeholder:"Verify",register:s,className:""}),e.jsx(l,{type:"number",page:"add",name:"packages_id",errors:a,label:"Packages Id",placeholder:"Packages Id",register:s,className:""}),e.jsx(l,{type:"text",page:"add",name:"phone",errors:a,label:"Phone",placeholder:"Phone",register:s,className:""}),e.jsxs("div",{className:"mb-4  ",children:[e.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"photo",children:"Photo"}),e.jsx("textarea",{placeholder:"Photo",...s("photo"),className:`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(y=a.photo)!=null&&y.message?"border-red-500":""}`,row:50}),e.jsx("p",{className:"text-red-500 text-xs italic",children:(b=a.photo)==null?void 0:b.message})]}),e.jsx(l,{type:"text",page:"add",name:"refer",errors:a,label:"Refer",placeholder:"Refer",register:s,className:""}),e.jsx(l,{type:"text",page:"add",name:"stripe_uid",errors:a,label:"Stripe Uid",placeholder:"Stripe Uid",register:s,className:""}),e.jsx(l,{type:"text",page:"add",name:"paypal_uid",errors:a,label:"Paypal Uid",placeholder:"Paypal Uid",register:s,className:""}),e.jsx(l,{type:"number",page:"add",name:"two_factor_authentication",errors:a,label:"Two Factor Authentication",placeholder:"Two Factor Authentication",register:s,className:""}),e.jsx(l,{type:"number",page:"add",name:"status",errors:a,label:"Status",placeholder:"Status",register:s,className:""}),e.jsx(D,{type:"submit",loading:f,disabled:f,className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:"Submit"})]})]})};export{re as default};

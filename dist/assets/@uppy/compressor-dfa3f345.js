import{g as B}from"../vendor-dd4ba10b.js";function f(e){const t=e.lastIndexOf(".");return t===-1||t===e.length-1?{name:e,extension:void 0}:{name:e.slice(0,t),extension:e.slice(t+1)}}var c=function(t){if(typeof t!="number"||Number.isNaN(t))throw new TypeError(`Expected a number, got ${typeof t}`);const a=t<0;let n=Math.abs(t);if(a&&(n=-n),n===0)return"0 B";const r=["B","KB","MB","GB","TB","PB","EB","ZB","YB"],s=Math.min(Math.floor(Math.log(n)/Math.log(1024)),r.length-1),o=Number(n/1024**s),i=r[s];return`${o>=10||o%1===0?Math.round(o):o.toFixed(1)} ${i}`};const h=B(c);export{f as g,h as p};

import{j as e}from"./@react-google-maps/api-4794cf1a.js";import{r as s,R as o}from"./vendor-dd4ba10b.js";import{a}from"./html2pdf.js-19c9759c.js";const n=s.lazy(()=>a(()=>import("./Loader-fd5778b4.js"),["assets/Loader-fd5778b4.js","assets/@react-google-maps/api-4794cf1a.js","assets/vendor-dd4ba10b.js","assets/html2pdf.js-19c9759c.js"])),u=()=>{const[t,r]=o.useState(!0);return console.log(t),o.useEffect(()=>{setTimeout(()=>{r(!1)},5e3)},[]),e.jsx(e.Fragment,{children:t?e.jsx(n,{}):e.jsx("div",{className:"w-full flex justify-center items-center text-7xl h-screen text-gray-700 ",children:"Not Found"})})};export{u as default};

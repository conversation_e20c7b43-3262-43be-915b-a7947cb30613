import{j as e}from"./@react-google-maps/api-4794cf1a.js";import{R as Z,u as ge,b as pe,r as o,L as I}from"./vendor-dd4ba10b.js";import{A as fe,G as we,M as ee,s as l,t as B}from"./index-3efdd896.js";import{G as ve}from"./react-icons-0b96c072.js";import{M as be}from"./monitor-8a3dd572.js";import"./react-confirm-alert-5d5c0db6.js";import"./html2pdf.js-19c9759c.js";import"./@headlessui/react-7b0d4887.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-1762f471.js";import"./@fortawesome/react-fontawesome-0b111e8e.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";function je(u){return ve({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{d:"M13.5 4.06c0-1.336-1.616-2.005-2.56-1.06l-4.5 4.5H4.508c-1.141 0-2.318.664-2.66 1.905A9.76 9.76 0 001.5 12c0 .898.121 1.768.35 2.595.341 1.24 1.518 1.905 2.659 1.905h1.93l4.5 4.5c.945.945 2.561.276 2.561-1.06V4.06zM18.584 5.106a.75.75 0 011.06 0c3.808 3.807 3.808 9.98 0 13.788a.75.75 0 11-1.06-1.06 8.25 8.25 0 000-11.668.75.75 0 010-1.06z"}},{tag:"path",attr:{d:"M15.932 7.757a.75.75 0 011.061 0 6 6 0 010 8.486.75.75 0 01-1.06-1.061 4.5 4.5 0 000-6.364.75.75 0 010-1.06z"}}]})(u)}const ze=()=>{var q,Q,Y,X;const{dispatch:u,state:y}=Z.useContext(fe),{dispatch:i}=Z.useContext(we),a=ge(),N=pe(),R=new ee,[d,g]=o.useState(""),[A,p]=o.useState(!1),[w,v]=o.useState([]),[te,F]=o.useState(null),[M,L]=o.useState(null),[$,G]=o.useState(!1),[S,b]=o.useState(!1),[k,j]=o.useState(!1),[se,re]=o.useState([]),[C,z]=o.useState(null),[_,V]=o.useState(!1),[oe,O]=o.useState(""),[ne,ae]=o.useState(""),[ye,ie]=o.useState([]),[T,le]=o.useState(!1),[D]=o.useState(()=>{const t=localStorage.getItem("allThemes");return t?JSON.parse(t):[]}),m=y==null?void 0:y.user,[x,J]=o.useState(!1),[U]=o.useState(()=>{const t=localStorage.getItem(`completedThemes_${m}`);return t?JSON.parse(t):[]}),{theme_id:P,theme_documents_id:E,isRegenerating:Ne}=a.state||{};o.useEffect(()=>{if(!P||!E){l(i,"Missing required theme information",4e3,"error"),N("/user/onboarding/theme");return}const t=localStorage.getItem("selectedTheme");if(t){const s=JSON.parse(t);O(s.name)}W()},[]),o.useEffect(()=>{const t=()=>{const r=window.speechSynthesis.getVoices().filter(n=>n.lang.includes("en"));re(r),r.length>0&&z(r[0])};window.speechSynthesis.onvoiceschanged!==void 0&&(window.speechSynthesis.onvoiceschanged=t),t()},[]),o.useEffect(()=>{var t;if((t=a.state)!=null&&t.theme_id){v([]),g(""),p(!0);const s=localStorage.getItem("selectedTheme");if(s){const r=JSON.parse(s);O(r.name)}W()}},[(q=a.state)==null?void 0:q.theme_id]);const W=async()=>{var t,s;try{p(!0);const r=await R.callRawAPI("/v3/api/custom/jordan/ai-chat/get-answer",{theme_id:(t=a.state)==null?void 0:t.theme_id,theme_documents_id:(s=a.state)==null?void 0:s.theme_documents_id},"POST");r.error||(v([{role:"ai",content:r.data.message}]),ae(r.data.message),r.data.conversation_id&&F(r.data.conversation_id),r.data.chat_id&&L(r.data.chat_id),G(r.data.end||!1))}catch(r){console.error("Chat initiation error:",r),l(i,r.message||"Failed to start conversation",4e3,"error"),B(u,r.message)}finally{p(!1)}},K=async t=>{var s,r;if(t.key==="Enter"&&!t.shiftKey&&d.trim()){t.preventDefault();try{p(!0);const n=d;g(""),ie(h=>[...h,n]),v(h=>[...h,{role:"user",content:n}]);const c=await R.callRawAPI("/v3/api/custom/jordan/ai-chat/get-answer",{theme_id:String((s=a.state)==null?void 0:s.theme_id),theme_documents_id:String((r=a.state)==null?void 0:r.theme_documents_id),conversation_id:String(te),chat_id:String(M),answer:n},"POST");c.error||(v(h=>[...h,{role:"ai",content:c.data.message}]),c.data.conversation_id&&F(c.data.conversation_id),c.data.chat_id&&L(c.data.chat_id),G(c.data.end||!1))}catch(n){console.error("Message send error:",n),l(i,n.message||"Failed to send message",4e3,"error"),B(u,n.message)}finally{p(!1)}}},ce=()=>{if(!("webkitSpeechRecognition"in window)){l(i,"Voice recognition is not supported in your browser",4e3,"error");return}const t=new window.webkitSpeechRecognition;t.continuous=!0,t.interimResults=!0,t.lang="en-US",t.onstart=()=>{b(!0)},t.onresult=s=>{const r=Array.from(s.results).map(n=>n[0]).map(n=>n.transcript).join("");g(r)},t.onerror=s=>{console.error("Speech recognition error:",s.error),b(!1),l(i,"Error recording voice",4e3,"error")},t.onend=()=>{b(!1)},t.start(),window.recognition=t},de=()=>{window.recognition&&window.recognition.stop(),b(!1)},me=t=>{if(!window.speechSynthesis){l(i,"Text-to-speech is not supported in your browser",4e3,"error");return}window.speechSynthesis.cancel();const s=new SpeechSynthesisUtterance(t);C&&(s.voice=C),s.lang="en-US",s.rate=1,s.pitch=1,s.onstart=()=>{j(!0)},s.onend=()=>{j(!1)},s.onerror=()=>{j(!1),l(i,"Error reading message",4e3,"error")},window.speechSynthesis.speak(s)},he=()=>{window.speechSynthesis.cancel(),j(!1)},H=async()=>{try{J(!0);const t=new ee,s=JSON.parse(localStorage.getItem(`selectedTheme_${m}`)),r=await t.callRawAPI("/v3/api/custom/theme/generate",{chat_id:String(M),theme_documents_id:String(E),theme_id:String(P)},"POST");if(r.error)l(i,"Failed to generate theme",4e3,"error");else{const n=JSON.parse(localStorage.getItem(`completedThemes_${m}`)||"[]");if(!n.includes(s.id)){const f=[...n,s.id];localStorage.setItem(`completedThemes_${m}`,JSON.stringify(f))}const h=[...JSON.parse(localStorage.getItem(`generatedThemes_${m}`)||"[]"),{id:s.id,name:s.name,pdfUrl:r.data}];localStorage.setItem(`generatedThemes_${m}`,JSON.stringify(h)),document.dispatchEvent(new Event("themeCompleted")),N("/user/onboarding/themepreview",{state:{themeTitle:s.name,initialQuestion:ne,userResponses:w.filter(f=>f.role==="user").map(f=>f.content),theme_id:s.id,pdfUrl:r.data}})}}catch(t){console.error("Generation error:",t),l(i,t.message||"Failed to generate theme",4e3,"error"),B(u,t.message)}finally{J(!1)}},xe=t=>{localStorage.setItem(`selectedTheme_${m}`,JSON.stringify(t)),N("/user/onboarding/themecontd",{state:{theme_id:t.ds_theme_id,theme_documents_id:E}})},ue=async()=>{d.trim()&&(await K({key:"Enter",shiftKey:!1,preventDefault:()=>{}}),g(""))};return e.jsx(e.Fragment,{children:e.jsx("div",{className:"w-full text-7xl min-h-screen h-full text-gray-700 bg-[#E2E6EB] p-[1rem] md:pl-[3rem]",children:e.jsxs("div",{className:"w-[95%] md:w-[90%] mx-auto md:mx-0",children:[e.jsx("h1",{className:"text-[20px] text-[#111928] font-semibold mb-6",children:"Generate themes document"}),e.jsxs("div",{className:"flex items-center justify-between mb-8 relative bg-white h-[98px] p-[1rem]",children:[e.jsx("div",{className:"absolute left-8 right-8 top-1/4 md:top-1/3 h-0.5 bg-gray-200 w-[100% - 2rem]"}),e.jsxs("div",{className:"flex flex-col items-start z-10 w-[28%]",children:[e.jsx("div",{className:"w-10 h-10 border border-[#9C9C9C] bg-white rounded-full flex items-center justify-center",children:e.jsx("div",{className:"w-6 h-6 bg-[#389C14] rounded-full flex items-center  justify-center",children:e.jsx("svg",{className:"w-4 h-4 text-white",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"})})})}),e.jsx(I,{to:"/user/onboarding",className:"text-xs md:text-sm hover:text-[#054FB1] transition-colors",children:"Upload your resume & full history docs"})]}),e.jsxs("div",{className:"flex flex-col items-center z-10 w-[24%]",children:[e.jsx("div",{className:"w-10 h-10 border border-[#9C9C9C] bg-white rounded-full flex items-center justify-center",children:e.jsx("div",{className:"w-6 h-6 bg-[#389C14] rounded-full flex items-center  justify-center",children:e.jsx("svg",{className:"w-4 h-4 text-white",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"})})})}),e.jsx(I,{to:"/user/onboarding/form",className:"text-xs md:text-sm hover:text-[#054FB1] transition-colors",children:"Fill out questions form"})]}),e.jsxs("div",{className:"flex flex-col items-end z-10 w-[24%]",children:[e.jsx("div",{className:"w-8 h-8 bg-[#054FB1] rounded-full flex items-center justify-center text-gray-500 mb-2",children:e.jsx("img",{src:be,className:"w-[20px] h-[20px]",alt:"upload icon"})}),e.jsxs(I,{to:"/user/onboarding/theme",className:"text-xs md:text-sm text-[#054FB1]",children:["Generate Themes (",U.length,"/",D.length,")"]})]})]}),e.jsx("div",{className:"text-right font-bold text-xs md:text-[16px] text-[#373A4B]",children:"50% completed"}),e.jsxs("div",{className:" bg-white p-8 flex flex-col mt-[2rem]",children:[e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsxs("div",{className:"flex flex-col md:flex-row md:items-center gap-2 text-[16px]",children:[e.jsx("span",{className:"text-[#111928] font-medium",children:"Theme title:"}),e.jsx("span",{className:"text-[#111928] text-[14px] sm:text-[16px]",children:oe})]}),e.jsxs("div",{className:"flex items-center gap-2 border border-[#DFE4EA] rounded-[6px] p-2 relative",children:[e.jsx("button",{onClick:()=>{if(k)he();else if(w.length>0){const t=[...w].reverse().find(s=>s.role==="ai");t&&me(t.content)}},className:`p-2 hover:bg-gray-100 rounded transition-colors ${k?"text-[#054FB1]":"text-[#373A4B]"}`,children:e.jsx(je,{className:`text-[24px] ${k?"animate-pulse":""}`})}),e.jsx("button",{onClick:()=>V(!_),className:"p-2 hover:bg-gray-100 rounded",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:`transform transition-transform ${_?"rotate-180":""}`,children:e.jsx("polyline",{points:"6 9 12 15 18 9"})})}),_&&e.jsx("div",{className:"absolute top-full right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-[200px] overflow-y-auto w-[200px]",children:se.map((t,s)=>e.jsx("button",{onClick:()=>{z(t),V(!1)},className:`w-full px-4 py-2 text-left hover:bg-gray-100 text-sm ${C===t?"bg-gray-50 text-[#054FB1]":"text-gray-700"}`,children:t.name},s))})]})]}),((Q=a.state)==null?void 0:Q.isRegenerating)&&e.jsx("p",{className:"font-bold text-[#111928] text-[16px] mb-[2rem]",children:"Share your feedback 😊"}),e.jsxs("div",{className:"space-y-6 flex-grow",children:[w.map((t,s)=>e.jsxs("div",{className:`${t.role==="ai"?"bg-gray-100 rounded-lg p-4 inline-block w-full md:max-w-[75%]":"flex flex-col bg-[#2F3B4B] text-white rounded-lg p-4 w-fit max-w-[90%] md:max-w-[75%] ml-auto"}`,children:[e.jsx("div",{className:`font-medium mb-2 text-[14px] font-semibold ${t.role==="ai"?"text-[#111928]":"text-white"}`,children:t.role==="ai"?"RCA AI":"You"}),e.jsx("div",{children:e.jsx("p",{className:`text-[14px] mt-[1rem] leadig-[22px] ${t.role==="ai"?"text-[#111928]":"text-white"}`,children:t.content})})]},s)),A&&e.jsxs("div",{className:"bg-gray-100 rounded-lg p-4 inline-block max-w-[75%]",children:[e.jsx("div",{className:"font-medium mb-2 text-[14px] font-semibold text-[#111928]",children:"RCA AI"}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"0ms"}}),e.jsx("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"150ms"}}),e.jsx("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"300ms"}})]})]})]}),e.jsx("div",{className:"mt-[5rem]",children:$?(Y=a.state)!=null&&Y.isRegenerating?e.jsx("button",{onClick:H,disabled:x,className:`flex items-center justify-center gap-2 px-6 py-2 bg-[#054FB1] text-white rounded hover:bg-blue-700 transition-colors font-medium text-[16px] h-[50px] ${x?"opacity-75 cursor-not-allowed":""}`,children:x?e.jsxs(e.Fragment,{children:[e.jsxs("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[e.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),e.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"GENERATING..."]}):"SUBMIT FEEDBACK AND REGENERATE"}):e.jsx("button",{onClick:H,disabled:x,className:`flex items-center justify-center gap-2 bg-[#054FB1] text-white text-[16px] leading-[24px] px-6 py-2 rounded-md hover:bg-blue-700 transition-colors w-[231px] h-[50px] ${x?"opacity-75 cursor-not-allowed":""}`,children:x?e.jsxs(e.Fragment,{children:[e.jsxs("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[e.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),e.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"GENERATING..."]}):"GENERATE THEME"}):e.jsxs("div",{className:"flex flex-col md:flex-row items-center gap-4",children:[e.jsxs("div",{className:"flex items-center gap-4 w-full md:w-[75%]",children:[e.jsx("input",{type:"text",value:d,onChange:t=>g(t.target.value),onKeyPress:K,placeholder:"Type your response here",disabled:A,className:"flex-1 px-4 py-2 outline-none text-sm rounded-lg border-[#054FB1] border-[1px] border-solid h-[48px]"}),e.jsx("button",{onClick:S?de:d.trim()?ue:ce,className:`p-2 rounded-lg h-[48px] w-[48px] flex-shrink-0 flex items-center justify-center transition-colors ${S?"bg-red-500 text-white hover:bg-red-600":d.trim()?"bg-[#054FB1] text-white hover:bg-blue-600":"bg-gray-100 text-[#054FB1] hover:bg-gray-200"}`,children:S?e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("rect",{x:"6",y:"6",width:"12",height:"12"})}):d.trim()?e.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[e.jsx("line",{x1:"22",y1:"2",x2:"11",y2:"13"}),e.jsx("polygon",{points:"22 2 15 22 11 13 2 9 22 2"})]}):e.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[e.jsx("path",{d:"M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z"}),e.jsx("path",{d:"M19 10v2a7 7 0 0 1-14 0v-2"}),e.jsx("line",{x1:"12",y1:"19",x2:"12",y2:"23"}),e.jsx("line",{x1:"8",y1:"23",x2:"16",y2:"23"})]})})]}),!((X=a.state)!=null&&X.isRegenerating)&&!$&&e.jsxs("div",{className:"w-full md:w-[25%] relative",children:[e.jsxs("button",{onClick:()=>le(!T),className:"w-full flex items-center justify-between text-[16px] text-[#637381] border rounded-lg px-4 py-2 h-[48px]",children:["Switch theme",e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:`transform transition-transform ${T?"rotate-180":""}`,children:e.jsx("polyline",{points:"6 9 12 15 18 9"})})]}),T&&e.jsx("div",{className:"absolute right-0 bottom-[calc(100%+8px)] w-64 bg-white border rounded-lg shadow-lg z-50 max-h-[200px] overflow-y-auto",children:D.map(t=>e.jsxs("button",{onClick:()=>xe(t),className:"w-full px-4 py-2 text-left hover:bg-gray-50 flex items-center justify-between border-b border-gray-100 last:border-b-0",children:[e.jsx("span",{className:"text-[#373A4B] text-[14px]",children:t.name}),U.includes(t.id)&&e.jsx("svg",{className:"w-4 h-4 text-green-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})]},t.id))})]})]})})]})]})})})};export{ze as default};

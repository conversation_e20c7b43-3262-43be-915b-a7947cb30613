import{j as e}from"./@react-google-maps/api-4794cf1a.js";import{R as o,b as k}from"./vendor-dd4ba10b.js";import{u as v}from"./react-hook-form-a6ecef1c.js";import{o as A}from"./yup-f7f8305f.js";import{c as E,a as h}from"./yup-79911193.js";import{G as F,A as D,M as R,s as I,t as P}from"./index-3efdd896.js";import"./react-quill-25360d36.js";import{M as C}from"./MkdInput-bb15886f.js";import{I as M}from"./InteractiveButton-6ddb3b9d.js";import"./index-3b0c955b.js";import"./@hookform/resolvers-61c06af0.js";import"./react-confirm-alert-5d5c0db6.js";import"./html2pdf.js-19c9759c.js";import"./@headlessui/react-7b0d4887.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-1762f471.js";import"./@fortawesome/react-fontawesome-0b111e8e.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";import"./@craftjs/core-3d3a3b40.js";import"./MoonLoader-795b8e10.js";const ie=({setSidebar:b})=>{var x,g;const{dispatch:m}=o.useContext(F),y=E({name:h(),features:h()}).required(),{dispatch:j}=o.useContext(D),[d,T]=o.useState({}),[p,u]=o.useState(!1),w=k(),{register:c,handleSubmit:S,setError:f,setValue:O,formState:{errors:l}}=v({resolver:A(y)});o.useState([]);const N=async n=>{u(!0);try{let t=new R;for(let r in d){let s=new FormData;s.append("file",d[r].file);let i=await t.uploadImage(s);n[r]=i.url}const a=await t.addStripeProduct({name:n.name,features:n.features});if(!(a!=null&&a.error))I(m,"Added"),w("/admin/packages"),b(!1),m({type:"REFRESH_DATA",payload:{refreshData:!0}});else if(a.validation){const r=Object.keys(a.validation);for(let s=0;s<r.length;s++){const i=r[s];f(i,{type:"manual",message:a.validation[i]})}}}catch(t){console.log("Error",t),f("name",{type:"manual",message:t.message}),P(j,t.message)}u(!1)};return o.useEffect(()=>{m({type:"SETPATH",payload:{path:"packages"}})},[]),e.jsxs("div",{className:" shadow-md rounded  mx-auto p-5",children:[e.jsx("h4",{className:"text-2xl font-medium",children:"Add Packages"}),e.jsxs("form",{className:" w-full max-w-lg",onSubmit:S(N),children:[e.jsx(C,{type:"text",page:"add",name:"name",errors:l,label:"Name",placeholder:"Name",register:c,className:""}),e.jsxs("div",{className:"mb-4  ",children:[e.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"features",children:"Features"}),e.jsx("textarea",{placeholder:"Features",...c("features"),className:`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(x=l.features)!=null&&x.message?"border-red-500":""}`,row:50}),e.jsx("p",{className:"text-red-500 text-xs italic",children:(g=l.features)==null?void 0:g.message})]}),e.jsx(M,{type:"submit",loading:p,disabled:p,className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:"Submit"})]})]})};export{ie as default};

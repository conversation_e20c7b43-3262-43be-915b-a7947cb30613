import{j as e}from"./@react-google-maps/api-4794cf1a.js";import"./vendor-dd4ba10b.js";const x=({closeModalFunction:t,message:r,title:s,messageClasses:l,titleClasses:i,buttonText:n="OK"})=>e.jsx("aside",{className:"fixed top-0 right-0 bottom-0 left-0 flex justify-center items-center ",style:{backgroundColor:"rgba(0, 0, 0, 0.5)",zIndex:"1000"},children:e.jsxs("section",{className:"bg-white min-w-[25rem] w-[25rem]  rounded-[.5rem] py-6 px-6 flex flex-col gap-6",children:[e.jsxs("div",{className:"flex justify-between ",children:[e.jsx("div",{}),e.jsx("button",{onClick:t})]}),s?e.jsx("div",{className:` ${i}`,children:s}):null,r?e.jsx("div",{className:`text-[1.5rem] leading-[1.5rem] text-[#667085] font-normal ${l}`,children:r}):null,e.jsx("div",{className:"w-full flex justify-center items-center font-medium text-[base] leading-[1.5rem]",children:e.jsx("button",{className:"border  border-[#DC5A5D] bg-[#DC5A5D] rounded-[.5rem] w-[10.375rem] h-[2.75rem] flex items-center justify-center text-white",style:{width:"10.375rem",height:"2.75rem"},onClick:t,children:n})})]})});export{x as default};

import{j as e}from"./@react-google-maps/api-4794cf1a.js";import{R as x,b as B,r as u,L as g}from"./vendor-dd4ba10b.js";import{A as v,G as U,M as D,s as c}from"./index-3efdd896.js";import{M as F}from"./monitor-8a3dd572.js";import{F as O}from"./formicon-d938e1f3.js";import"./react-confirm-alert-5d5c0db6.js";import"./html2pdf.js-19c9759c.js";import"./@headlessui/react-7b0d4887.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-1762f471.js";import"./@fortawesome/react-fontawesome-0b111e8e.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";const S="data:image/png;base64,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",G=()=>{const{state:i}=x.useContext(v);x.useContext(v);const{dispatch:o}=x.useContext(U),w=B(),h=new D,d=i==null?void 0:i.user,[n,C]=u.useState(()=>localStorage.getItem(`documentTitle_${d}`)||""),[r,f]=u.useState(()=>{const s=localStorage.getItem(`resumeUrl_${d}`);return s?{name:"Previously uploaded resume",url:s}:null}),[l,b]=u.useState(()=>{const s=localStorage.getItem(`historyUrl_${d}`);return s?{name:"Previously uploaded history",url:s}:null}),[p,A]=u.useState(!1),[R,I]=u.useState(""),y=s=>{s.preventDefault()},N=s=>a=>{a.preventDefault();const t=a.dataTransfer.files[0];s({name:t.name,file:t})},j=s=>a=>{const t=a.target.files[0];s({name:t.name,file:t})};x.useEffect(()=>{o({type:"SETPATH",payload:{path:"user"}})},[]),x.useEffect(()=>{n&&d&&localStorage.setItem(`documentTitle_${d}`,n)},[n,d]);const E=async()=>{try{if(A(!0),I(""),!n||!r||!l){c(o,"Please fill in all required fields",4e3,"error");return}const s=i==null?void 0:i.user;if(!s){c(o,"User ID not found. Please try logging in again.",4e3,"error");return}let a=r.url,t=l.url;if(!r.url&&r.file){const m=await h.upload(r.file);if(!m.url){c(o,"Resume upload failed",4e3,"error");return}a=m.url,localStorage.setItem(`resumeUrl_${s}`,a)}if(!l.url&&l.file){const m=await h.upload(l.file);if(!m.url){c(o,"History document upload failed",4e3,"error");return}t=m.url,localStorage.setItem(`historyUrl_${s}`,t)}c(o,"Documents uploaded successfully!",4e3,"success"),w("/user/onboarding/form",{state:{resumeUrl:a,historyUrl:t,documentTitle:n}})}catch(s){console.error("Upload error:",s),c(o,s.message||"An error occurred during upload",4e3,"error")}finally{A(!1)}};return e.jsx(e.Fragment,{children:e.jsx("div",{className:"md:pl-[3rem] w-full text-7xl min-h-screen h-full text-gray-700 bg-[#E2E6EB] p-[1rem]",children:e.jsxs("div",{className:"w-[95%] md:w-[90%] mx-auto md:mx-0",children:[e.jsx("h1",{className:"text-[20px] text-[#111928] font-semibold mb-6",children:"Generate themes document"}),e.jsxs("div",{className:"flex items-center justify-between mb-8 relative bg-white h-[98px] p-[1rem]",children:[e.jsx("div",{className:"absolute left-8 right-8 top-1/4 md:top-1/3 h-0.5 bg-gray-200 w-[100% - 2rem]"}),e.jsxs("div",{className:"flex flex-col items-start z-10 w-[33%]",children:[e.jsx("div",{className:"w-10 h-10 border border-[#9C9C9C] bg-white rounded-full flex items-center justify-center",children:e.jsx("div",{className:"w-6 h-6 bg-[#389C14] rounded-full flex items-center justify-center",children:e.jsx("svg",{className:"w-4 h-4 text-white",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"})})})}),e.jsx(g,{to:"/user/onboarding",className:"text-sm hover:text-[#054FB1] transition-colors",children:"Upload your resume & full history docs"})]}),e.jsxs("div",{className:"flex flex-col items-center z-10 w-[33%]",children:[e.jsx("div",{className:"w-8 h-8 bg-[#054FB1] rounded-full flex items-center justify-center text-white mb-2",children:e.jsx("img",{src:O,className:"w-[16px] h-[16px]",alt:"upload icon"})}),e.jsx(g,{to:"/user/onboarding/form",className:"text-sm text-[#054FB1]",children:"Fill out questions form"})]}),e.jsxs("div",{className:"flex flex-col items-end z-10 w-[33%]",children:[e.jsx("div",{className:"w-8 h-8 bg-[#9C9C9C] rounded-full flex items-center justify-center text-gray-500 mb-2",children:e.jsx("img",{src:F,className:"w-[20px] h-[20px]",alt:"upload icon"})}),e.jsx(g,{to:"/user/onboarding/theme",className:"text-sm hover:text-[#054FB1] transition-colors",children:"Generate Themes (0/20)"})]})]}),e.jsxs("div",{className:"mb-8",children:[e.jsxs("div",{className:"flex justify-between items-center mb-[-1rem]",children:[e.jsx("label",{className:"font-medium text-[16px] leading-[24px] text-[#111928] mt-[2rem]",children:"Document Title"}),e.jsx("span",{className:"text-[12px] md:text-[16px] font-bold text-[#373A4B]",children:"0% completed"})]}),e.jsx("input",{type:"text",className:"w-[211px] h-[46px] p-2 border border-gray-300 rounded-md",placeholder:"Enter document name",value:n,onChange:s=>C(s.target.value)})]}),e.jsxs("div",{className:"mb-8 bg-white p-[2rem]",children:[e.jsx("h2",{className:"font-semibold mb-2 text-[16px]",children:"Resume Upload"}),e.jsx("p",{className:"text-sm text-gray-600 mb-4",children:"Upload your professional resume highlighting your skills and experience"}),e.jsx("div",{className:"border-2 border-dashed border-blue-200 rounded-lg p-8 bg-blue-50",onDragOver:y,onDrop:N(f),children:e.jsxs("div",{className:"flex flex-col items-center",children:[e.jsx("img",{src:S,alt:"upload icon"}),e.jsxs("p",{className:"text-sm text-gray-600 mb-2",children:[r?r.url?"File already uploaded":r.name:"Drag & drop files or ",e.jsxs("label",{className:"text-blue-600 cursor-pointer underline",children:["Browse",e.jsx("input",{type:"file",className:"hidden",onChange:j(f),accept:".pdf,.doc,.docx"})]})]}),e.jsx("p",{className:"text-xs text-gray-500",children:"Supported formats: PDF, Word"})]})})]}),e.jsxs("div",{className:"mb-8 bg-white p-[2rem]",children:[e.jsx("h2",{className:"font-semibold mb-2 text-[16px]",children:"Full History Upload"}),e.jsx("p",{className:"text-sm text-gray-600 mb-4",children:"Upload your complete work and educational history document"}),e.jsx("div",{className:"border-2 border-dashed border-blue-200 rounded-lg p-8 bg-blue-50",onDragOver:y,onDrop:N(b),children:e.jsxs("div",{className:"flex flex-col items-center",children:[e.jsx("img",{src:S,alt:"upload icon"}),e.jsxs("p",{className:"text-sm text-gray-600 mb-2",children:[l?l.url?"File already uploaded":l.name:"Drag & drop files or ",e.jsxs("label",{className:"text-blue-600 cursor-pointer underline",children:["Browse",e.jsx("input",{type:"file",className:"hidden",onChange:j(b),accept:".pdf,.doc,.docx"})]})]}),e.jsx("p",{className:"text-xs text-gray-500",children:"Supported formats: PDF, Word"})]})})]}),e.jsx("div",{className:"flex justify-end",children:e.jsx("button",{onClick:E,disabled:p,className:`bg-[#054FB1] text-white text-[16px] leading-[24px] px-6 py-2 rounded-md hover:bg-blue-700 transition-colors w-[231px] h-[50px] ${p?"opacity-50 cursor-not-allowed":""}`,children:p?"UPLOADING...":"CONTINUE"})})]})})})};export{G as default};

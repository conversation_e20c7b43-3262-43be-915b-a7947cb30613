import{j as e}from"./@react-google-maps/api-4794cf1a.js";import{R as r,b as C}from"./vendor-dd4ba10b.js";import{u as S}from"./react-hook-form-a6ecef1c.js";import{o as A}from"./yup-f7f8305f.js";import{c as R,a as i}from"./yup-79911193.js";import{A as F,G as U,M as $,s as q,t as P}from"./index-3efdd896.js";import"./@hookform/resolvers-61c06af0.js";import"./react-confirm-alert-5d5c0db6.js";import"./html2pdf.js-19c9759c.js";import"./@headlessui/react-7b0d4887.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-1762f471.js";import"./@fortawesome/react-fontawesome-0b111e8e.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";const Y=({setSidebar:n})=>{var f,h,b,g;const v=R({email:i().email().required(),password:i().required(),role:i()}).required(),{dispatch:d}=r.useContext(F),{dispatch:y}=r.useContext(U),[m,c]=r.useState(!1),N=C(),{register:l,handleSubmit:p,setError:u,formState:{errors:t}}=S({resolver:A(v)}),E=[{name:"role",value:"user"},{name:"role",value:"admin"}],x=async s=>{let k=new $;c(!0);try{const a=await k.register(s.email,s.password,s.role);if(!a.error)q(d,"Added"),N("/admin/users");else if(a.validation){const w=Object.keys(a.validation);for(let o=0;o<w.length;o++){const j=w[o];u(j,{type:"manual",message:a.validation[j]})}}}catch(a){console.log("Error",a),u("email",{type:"manual",message:a.message}),P(d,a.message)}c(!1)};return r.useEffect(()=>{y({type:"SETPATH",payload:{path:"users"}})},[]),e.jsxs("div",{className:"mx-auto  rounded",children:[e.jsxs("div",{className:"flex items-center justify-between gap-4 border-b border-b-[#E0E0E0] p-3",children:[e.jsx("div",{className:"flex items-center gap-3",children:e.jsx("span",{className:"text-lg font-semibold",children:"Add User"})}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("button",{className:"flex items-center rounded-md border border-[#C6C6C6] px-3 py-2 shadow-sm hover:bg-[#f4f4f4]",onClick:()=>n(!1),children:"Cancel"}),e.jsx("button",{className:"flex items-center rounded-md bg-[#4F46E5] px-3 py-2 text-white shadow-sm",onClick:async()=>{await p(x)(),n(!1)},disabled:m,children:m?"Saving...":"Save"})]})]}),e.jsxs("form",{className:" w-full p-4 text-left",onSubmit:p(x),children:[e.jsxs("div",{className:"mb-4 ",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"email",children:"Email"}),e.jsx("input",{type:"email",placeholder:"Email",...l("email"),className:`focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(f=t.email)!=null&&f.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(h=t.email)==null?void 0:h.message})]}),e.jsxs("div",{className:"mb-5",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",children:"Role"}),e.jsx("select",{name:"role",id:"role",className:"focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none",...l("role"),children:E.map(s=>e.jsx("option",{name:s.name,value:s.value,defaultValue:s.value==="client",children:s.value},s.value))})]}),e.jsxs("div",{className:"mb-5",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"password",children:"Password"}),e.jsx("input",{type:"password",placeholder:"******************",...l("password"),className:`focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(b=t.password)!=null&&b.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(g=t.password)==null?void 0:g.message})]})]})]})};export{Y as default};

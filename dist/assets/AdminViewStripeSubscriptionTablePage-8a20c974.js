import{j as s}from"./@react-google-maps/api-4794cf1a.js";import{R as a,h as x}from"./vendor-dd4ba10b.js";import"./yup-79911193.js";import{M as o,G as l,t as f}from"./index-3efdd896.js";import{S as j}from"./index-3b0c955b.js";import"./react-confirm-alert-5d5c0db6.js";import"./html2pdf.js-19c9759c.js";import"./@headlessui/react-7b0d4887.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-1762f471.js";import"./@fortawesome/react-fontawesome-0b111e8e.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";let c=new o;const G=()=>{a.useContext(l);const{dispatch:m}=a.useContext(l),[e,d]=a.useState({}),[n,r]=a.useState(!0),t=x();return a.useEffect(function(){(async function(){try{r(!0),c.setTable("stripe_subscription");const i=await c.callRestAPI({id:Number(t==null?void 0:t.id),join:""},"GET");i.error||(d(i.model),r(!1))}catch(i){r(!1),console.log("error",i),f(m,i.message)}})()},[]),s.jsx("div",{className:" shadow-md rounded  mx-auto p-5",children:n?s.jsx(j,{}):s.jsxs(s.Fragment,{children:[s.jsx("h4",{className:"text-2xl font-medium",children:"View Stripe Subscription"}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Stripe Id"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.stripe_id})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Price Id"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.price_id})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"User Id"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.user_id})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Object"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.object})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Status"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.status})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Is Lifetime"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.is_lifetime})]})})]})})};export{G as default};

import{j as e}from"./@react-google-maps/api-4794cf1a.js";import{u as d,b as x}from"./vendor-dd4ba10b.js";import{D as p}from"./document-download-151d604a.js";const N=()=>{const o=d(),r=x(),{allThemes:c}=o.state||{},m=c.map(s=>{var i;const t=JSON.parse(localStorage.getItem(`theme_${s.id}_conversation`)||"[]"),a=[];for(let n=0;n<t.length;n++){const l=t[n];l.role==="assistant"&&l.content&&a.push({question:l.content,answer:((i=t[n+1])==null?void 0:i.role)==="user"?t[n+1].content:""})}return{...s,responses:a}});return e.jsx("div",{className:"w-full min-h-screen h-full text-gray-700 bg-[#E2E6EB] p-[1rem]",children:e.jsx("div",{className:"w-[90%]",children:e.jsxs("div",{className:"mt-[2rem] mx-auto p-8 bg-white",children:[e.jsxs("div",{className:"flex justify-between items-center mb-8",children:[e.jsx("div",{className:"text-lg",children:e.jsx("span",{className:"font-medium",children:"Complete Guide: All Themes and Frameworks"})}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("img",{src:p,alt:"document download"}),e.jsx("span",{className:"text-[#111928] font-medium text-[12px]",children:"PDF Preview"})]})]}),e.jsx("div",{className:"border rounded-lg p-6",children:m.map((s,t)=>e.jsxs("div",{className:"mb-8 last:mb-0",children:[e.jsx("h2",{className:"text-lg font-medium mb-4 text-[#111928]",children:s.name}),e.jsx("div",{className:"space-y-6",children:s.responses.map((a,i)=>e.jsxs("div",{className:"space-y-3",children:[e.jsx("p",{className:"text-[16px] text-[#111928] font-medium",children:a.question}),e.jsx("p",{className:"text-[16px] text-[#111928] leading-[24px] pl-5",children:a.answer})]},i))})]},s.id))}),e.jsx("div",{className:"mt-6 flex justify-end",children:e.jsx("button",{onClick:()=>r("/user/onboarding/generatequiz"),className:"px-6 py-2 bg-[#054FB1] text-white rounded hover:bg-blue-700 transition-colors font-medium text-[16px] h-[50px]",children:"CONTINUE TO QUIZ"})})]})})})};export{N as default};

import{j as e}from"./@react-google-maps/api-4794cf1a.js";import{L as x}from"./logo-a4db6296.js";import{u,a as g,C as p}from"./@stripe/react-stripe-js-1762f471.js";import{r as h}from"./vendor-dd4ba10b.js";import"./@fortawesome/react-fontawesome-0b111e8e.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";function w({onClose:n,onSubmit:r,price:o,loading:i}){const s=u(),t=g(),[l,d]=h.useState(""),c=async a=>{a.preventDefault(),r&&s&&t&&await r({stripe:s,elements:t,postalCode:l})},m={style:{base:{fontSize:"16px",color:"#424770","::placeholder":{color:"#aab7c4"},padding:"12px"},invalid:{color:"#9e2146"}},hidePostalCode:!0};return e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4",children:e.jsxs("div",{className:"relative w-full max-w-4xl bg-white rounded-2xl shadow-2xl overflow-hidden",children:[e.jsx("button",{onClick:n,className:"absolute right-6 top-6 p-2 hover:bg-gray-100 rounded-full transition-colors duration-200",children:e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"#666",children:e.jsx("path",{d:"M18 6L6 18M6 6l12 12",strokeWidth:"2",strokeLinecap:"round"})})}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-12 p-12",children:[e.jsxs("div",{className:"flex flex-col",children:[e.jsx("img",{src:x,alt:"Logo",className:"h-[73px] w-[54.75px] mb-12"}),e.jsx("h1",{className:"text-4xl font-semibold mb-6 leading-tight text-gray-900",children:"Make a One-Time Payment"}),e.jsx("p",{className:"text-lg leading-relaxed text-gray-600",children:"Enjoy lifetime access to unlimited prompts and PDF generation. Pay once and unlock endless possibilities to boost your creativity and productivity!"}),e.jsx("div",{className:"md:hidden mt-8 p-4 bg-gray-50 rounded-lg",children:e.jsxs("div",{className:"text-center",children:[e.jsxs("span",{className:"text-2xl font-bold text-gray-900",children:["$",o]}),e.jsx("span",{className:"ml-2 text-gray-600",children:"USD"})]})})]}),e.jsxs("div",{className:"flex flex-col",children:[e.jsx("div",{className:"hidden md:block mb-8 p-4 bg-gray-50 rounded-lg",children:e.jsxs("div",{className:"text-center",children:[e.jsxs("span",{className:"text-2xl font-bold text-gray-900",children:["$",o]}),e.jsx("span",{className:"ml-2 text-gray-600",children:"USD"})]})}),e.jsxs("form",{className:"rounded-xl border border-gray-200 p-8 bg-white shadow-sm",onSubmit:c,children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Card Information"}),e.jsx("div",{className:"relative mb-4",children:e.jsx(p,{options:m,className:"p-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})}),e.jsxs("div",{className:"mt-4",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Postal Code"}),e.jsx("input",{type:"text",value:l,onChange:a=>d(a.target.value.toUpperCase()),placeholder:"Enter postal code",className:"w-full p-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",required:!0})]})]}),e.jsx("button",{type:"submit",disabled:!s||!t||i||!l,className:"w-full rounded-lg bg-blue-600 py-3 px-4 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors duration-200",children:i?e.jsxs("span",{className:"flex items-center justify-center",children:[e.jsxs("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[e.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),e.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Processing..."]}):"Pay Now"})]}),e.jsx("div",{className:"mt-6 text-center text-sm text-gray-500",children:"Your payment is secure and encrypted"})]})]})]})})}export{w as default};

import{j as s}from"./@react-google-maps/api-4794cf1a.js";import{R as x,b as G,h as q,r as f}from"./vendor-dd4ba10b.js";import{u as B}from"./react-hook-form-a6ecef1c.js";import{o as D}from"./yup-f7f8305f.js";import{c as M,a as h}from"./yup-79911193.js";import{M as _,A as H,G as I,t as C,s as b}from"./index-3efdd896.js";import"./@hookform/resolvers-61c06af0.js";import"./react-confirm-alert-5d5c0db6.js";import"./html2pdf.js-19c9759c.js";import"./@headlessui/react-7b0d4887.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-1762f471.js";import"./@fortawesome/react-fontawesome-0b111e8e.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";let r=new _;const ne=({activeId:i,setSidebar:g})=>{var N,k,S,A;const P=M({email:h().email().required(),password:h(),role:h()}).required(),{dispatch:y}=x.useContext(H),{dispatch:d}=x.useContext(I),R=G();q();const[T,U]=f.useState(""),[K,F]=f.useState(0),[w,j]=f.useState(!1),{register:m,handleSubmit:v,setError:c,setValue:p,formState:{errors:u}}=B({resolver:D(P)}),O=["user","admin"],$=[{key:"0",value:"Inactive"},{key:"2",value:"Suspend"},{key:"1",value:"Active"}],E=async e=>{j(!0);try{if(T!==e.email){const a=await r.updateEmailByAdmin(e.email,i);if(!a.error)b(d,"Email Updated",1e3);else if(a.validation){const l=Object.keys(a.validation);for(let t=0;t<l.length;t++){const n=l[t];c(n,{type:"manual",message:a.validation[n]})}}}if(e.password.length>0){const a=await r.updatePasswordByAdmin(e.password,i);if(!a.error)b(d,"Password Updated",2e3);else if(a.validation){const l=Object.keys(a.validation);for(let t=0;t<l.length;t++){const n=l[t];c(n,{type:"manual",message:a.validation[n]})}}}r.setTable("user");const o=await r.callRestAPI({activeId:i,email:e.email,role:e.role,status:e.status},"PUT");if(!o.error)b(d,"Added",4e3),R("/admin/users");else if(o.validation){const a=Object.keys(o.validation);for(let l=0;l<a.length;l++){const t=a[l];c(t,{type:"manual",message:o.validation[t]})}}}catch(o){console.log("Error",o),c("email",{type:"manual",message:o.message}),C(y,o.message)}j(!1)};return x.useEffect(()=>{d({type:"SETPATH",payload:{path:"users"}}),async function(){try{r.setTable("user");const e=await r.callRestAPI({id:i},"GET");e.error||(p("email",e.model.email),p("role",e.model.role),p("status",e.model.status),U(e.model.email),F(e.model.id))}catch(e){console.log("Error",e),C(y,e.message)}}()},[i]),s.jsxs("div",{className:"mx-auto rounded",children:[s.jsxs("div",{className:"flex items-center justify-between gap-4 border-b border-b-[#E0E0E0] p-3",children:[s.jsx("div",{className:"flex items-center gap-3",children:s.jsx("span",{className:"text-lg font-semibold",children:"Edit User"})}),s.jsxs("div",{className:"flex items-center gap-4",children:[s.jsx("button",{className:"flex items-center rounded-md border border-[#C6C6C6] px-3 py-2 shadow-sm hover:bg-[#f4f4f4]",onClick:()=>g(!1),children:"Cancel"}),s.jsx("button",{className:"flex items-center rounded-md bg-[#4F46E5] px-3 py-2 text-white shadow-sm",onClick:async()=>{await v(E)(),g(!1)},disabled:w,children:w?"Saving...":"Save"})]})]}),s.jsxs("form",{className:" w-full p-4 text-left",onSubmit:v(E),children:[s.jsxs("div",{className:"mb-4",children:[s.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"email",children:"Email"}),s.jsx("input",{type:"email",...m("email"),className:`focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(N=u.email)!=null&&N.message?"border-red-500":""}`}),s.jsx("p",{className:"text-xs italic text-red-500",children:(k=u.email)==null?void 0:k.message})]}),s.jsxs("div",{className:"mb-4",children:[s.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",children:"Role"}),s.jsx("select",{className:"focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none",...m("role"),children:O.map(e=>s.jsx("option",{name:"role",value:e,children:e},e))})]}),s.jsxs("div",{className:"mb-4",children:[s.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",children:"Status"}),s.jsx("select",{className:"focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none",...m("status"),children:$.map(e=>s.jsx("option",{name:"status",value:e.key,children:e.value},e.key))})]}),s.jsxs("div",{className:"mb-4",children:[s.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"password",children:"Password"}),s.jsx("input",{type:"password",placeholder:"******************",...m("password"),className:`focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(S=u.password)!=null&&S.message?"border-red-500":""}`}),s.jsx("p",{className:"text-xs italic text-red-500",children:(A=u.password)==null?void 0:A.message})]})]})]})};export{ne as default};

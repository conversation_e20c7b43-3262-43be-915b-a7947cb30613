import{j as e}from"./@react-google-maps/api-4794cf1a.js";import{R as b,b as S,r,L as h}from"./vendor-dd4ba10b.js";import{A as T,G as k,M as E,t as j,s as u}from"./index-3efdd896.js";import{M as L}from"./monitor-8a3dd572.js";import"./react-confirm-alert-5d5c0db6.js";import"./html2pdf.js-19c9759c.js";import"./@headlessui/react-7b0d4887.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-1762f471.js";import"./@fortawesome/react-fontawesome-0b111e8e.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";const H=()=>{const{dispatch:f,state:d}=b.useContext(T),{dispatch:c}=b.useContext(k),v=S(),p=new E,o=d==null?void 0:d.user;r.useState(!1),r.useState(0);const[m,g]=r.useState(!1),[x,N]=r.useState([]),[s,i]=r.useState(null),[a,n]=r.useState(!1),[y]=r.useState(()=>{const t=localStorage.getItem(`completedThemes_${o}`);return t?JSON.parse(t):[]});r.useEffect(()=>{(async()=>{try{p.setTable("themes");const l=await p.callRestAPI({},"GETALL");l.list&&N(l.list)}catch(l){console.error("Error fetching themes:",l),j(f,l.message)}})()},[]),r.useEffect(()=>{const t=localStorage.getItem(`selectedTheme_${o}`);t&&i(JSON.parse(t));const l=()=>{const w=localStorage.getItem(`selectedTheme_${o}`);w&&i(JSON.parse(w))};return window.addEventListener("themesUpdated",l),()=>{window.removeEventListener("themesUpdated",l)}},[o]);const C=async()=>{try{if(g(!0),!s){u(c,"Please select a theme",4e3,"error");return}const t=localStorage.getItem("theme_documents_id");if(!t){u(c,"Missing theme document information",4e3,"error");return}v("/user/onboarding/themecontd",{state:{theme_id:s.ds_theme_id,theme_documents_id:parseInt(t)}})}catch(t){console.error("Navigation error:",t),u(c,t.message||"An error occurred",4e3,"error"),j(f,t.message)}finally{g(!1)}};return e.jsx(e.Fragment,{children:e.jsx("div",{className:"w-full text-7xl min-h-screen h-full text-gray-700 bg-[#E2E6EB] p-[1rem] md:pl-[3rem]",children:e.jsxs("div",{className:"w-[95%] md:w-[90%] mx-auto md:mx-0",children:[e.jsx("h1",{className:"text-[20px] text-[#111928] font-semibold mb-6",children:"Generate themes document"}),e.jsxs("div",{className:"space-y-8",children:[e.jsxs("div",{className:"flex items-center justify-between mb-8 relative bg-white h-[98px] p-[1rem]",children:[e.jsx("div",{className:"absolute left-8 right-8 top-1/4 md:top-1/3 h-0.5 bg-gray-200 w-[100% - 2rem]"}),e.jsxs("div",{className:"flex flex-col items-start z-10 w-[33%]",children:[e.jsx("div",{className:"w-10 h-10 border border-[#9C9C9C] bg-white rounded-full flex items-center justify-center",children:e.jsx("div",{className:"w-6 h-6 bg-[#389C14] rounded-full flex items-center  justify-center",children:e.jsx("svg",{className:"w-4 h-4 text-white",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"})})})}),e.jsx(h,{to:"/user/onboarding",className:"text-xs md:text-sm hover:text-[#054FB1] transition-colors",children:"Upload your resume & full history docs"})]}),e.jsxs("div",{className:"flex flex-col items-center z-10 w-[33%]",children:[e.jsx("div",{className:"w-10 h-10 border border-[#9C9C9C] bg-white rounded-full  flex items-center justify-center ",children:e.jsx("div",{className:"w-6 h-6 bg-[#389C14] rounded-full flex items-center  justify-center",children:e.jsx("svg",{className:"w-4 h-4 text-white",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"})})})}),e.jsx(h,{to:"/user/onboarding/form",className:"text-xs md:text-sm hover:text-[#054FB1] transition-colors",children:"Fill out questions form"})]}),e.jsxs("div",{className:"flex flex-col items-end z-10 w-[33%]",children:[e.jsx("div",{className:"w-8 h-8 bg-[#054FB1] rounded-full flex items-center justify-center text-gray-500 mb-2",children:e.jsx("img",{src:L,className:"w-[20px] h-[20px]",alt:"upload icon"})}),e.jsxs(h,{to:"/user/onboarding/theme",className:"text-xs md:text-sm text-[#054FB1]",children:["Generate Themes (",y.length,"/",x.length,")"]})]})]}),e.jsx("div",{className:"text-right font-bold text-[12px] md:text-[16px] text-[#373A4B]",children:"50% completed"})]}),e.jsxs("div",{className:"bg-white p-8 rounded-lg mt-[1rem]",children:[e.jsx("div",{className:"relative w-full aspect-video bg-black overflow-hidden",children:e.jsx("iframe",{width:"100%",height:"100%",src:"https://www.youtube.com/embed/8Hf-XrPZ0mk",title:"Getting Started Guide",frameBorder:"0",allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture",allowFullScreen:!0})}),e.jsxs("div",{className:"flex flex-col md:flex-row items-center justify-between mb-4 mt-[2rem]",children:[e.jsxs("div",{className:"flex flex-col md:flex-row w-full md:items-center gap-2 text-[16px] font-medium leading-[24px]",children:[e.jsx("span",{className:"font-medium",children:"Theme Title:"}),e.jsx("span",{children:s==null?void 0:s.name})]}),e.jsxs("div",{className:"relative w-full md:hidden",children:[e.jsxs("button",{onClick:()=>n(!a),className:"px-4 py-2 text-[#637381] border rounded-md flex items-center justify-between text-[16px] w-full mt-[1rem] h-[48px]",children:["Switch theme",e.jsx("svg",{className:`w-4 h-4 transform transition-transform ${a?"rotate-180":""}`,viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z",clipRule:"evenodd"})})]}),a&&e.jsx("div",{className:"absolute left-0 right-0 bottom-[calc(100%+8px)] bg-white border rounded-lg shadow-lg z-50 max-h-[200px] overflow-y-auto",children:x.map(t=>e.jsxs("button",{onClick:()=>{i(t),localStorage.setItem(`selectedTheme_${o}`,JSON.stringify(t)),n(!1)},className:"w-full px-4 py-2 text-left hover:bg-gray-50 flex items-center justify-between border-b border-gray-100 last:border-b-0",children:[e.jsx("span",{className:"text-[#373A4B] text-[14px]",children:t.name}),(s==null?void 0:s.id)===t.id&&e.jsx("svg",{className:"w-4 h-4 text-green-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})]},t.id))})]}),e.jsx("button",{onClick:C,disabled:m||!s,className:`w-full md:w-[371px] h-[50px] bg-[#054FB1] text-white rounded-[6px] hover:bg-blue-700 transition-colors font-[Inter] font-medium text-[16px] leading-[24px] text-center mt-[1rem] md:mt-0
                  ${!s||m?"opacity-50 cursor-not-allowed":""}`,children:m?"SAVING...":"CONTINUE WITH SELECTED THEME"})]}),e.jsxs("div",{className:"hidden md:flex justify-end relative",children:[e.jsxs("button",{onClick:()=>n(!a),className:"px-4 py-2 text-[#637381] border rounded-md flex items-center gap-2 text-[16px] h-[48px] w-[184px]",children:["Switch theme",e.jsx("svg",{className:`w-4 h-4 transform transition-transform ${a?"rotate-180":""}`,viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z",clipRule:"evenodd"})})]}),a&&e.jsx("div",{className:"absolute right-0 bottom-[calc(100%+8px)] w-64 bg-white border rounded-lg shadow-lg z-50 max-h-[200px] overflow-y-auto",children:x.map(t=>e.jsxs("button",{onClick:()=>{i(t),localStorage.setItem(`selectedTheme_${o}`,JSON.stringify(t)),n(!1)},className:"w-full px-4 py-2 text-left hover:bg-gray-50 flex items-center justify-between border-b border-gray-100 last:border-b-0",children:[e.jsx("span",{className:"text-[#373A4B] text-[14px]",children:t.name}),(s==null?void 0:s.id)===t.id&&e.jsx("svg",{className:"w-4 h-4 text-green-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})]},t.id))})]})]})]})})})};export{H as default};

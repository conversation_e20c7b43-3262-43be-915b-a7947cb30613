import{j as s}from"./@react-google-maps/api-4794cf1a.js";import{R as l,h as t}from"./vendor-dd4ba10b.js";import"./yup-79911193.js";import{M as h,G as m,t as j}from"./index-3efdd896.js";import{S as N}from"./index-3b0c955b.js";import"./react-confirm-alert-5d5c0db6.js";import"./html2pdf.js-19c9759c.js";import"./@headlessui/react-7b0d4887.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-1762f471.js";import"./@fortawesome/react-fontawesome-0b111e8e.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";let r=new h;const U=()=>{l.useContext(m);const{dispatch:d}=l.useContext(m),[e,x]=l.useState({}),[n,c]=l.useState(!0),i=t();return l.useEffect(function(){(async function(){try{c(!0),r.setTable("user");const a=await r.callRestAPI({id:Number(i==null?void 0:i.id),join:""},"GET");a.error||(x(a.model),c(!1))}catch(a){c(!1),console.log("error",a),j(d,a.message)}})()},[]),s.jsx("div",{className:" shadow-md rounded  mx-auto p-5",children:n?s.jsx(N,{}):s.jsxs(s.Fragment,{children:[s.jsx("h4",{className:"text-2xl font-medium",children:"View User"}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Oauth"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.oauth})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Role"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.role})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"First Name"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.first_name})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Last Name"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.last_name})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Email"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.email})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Password"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.password})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Type"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.type})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Verify"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.verify})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Packages Id"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.packages_id})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Phone"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.phone})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Photo"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.photo})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Refer"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.refer})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Stripe Uid"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.stripe_uid})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Paypal Uid"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.paypal_uid})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Two Factor Authentication"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.two_factor_authentication})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Status"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.status})]})})]})})};export{U as default};

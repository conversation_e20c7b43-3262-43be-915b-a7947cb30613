import{_ as ve,z as $o,a as Qo,u as Jc}from"./html2pdf.js-19c9759c.js";var zt=function(){return typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:this}();function Go(){zt.console&&typeof zt.console.log=="function"&&zt.console.log.apply(zt.console,arguments)}var me={log:Go,warn:function(i){zt.console&&(typeof zt.console.warn=="function"?zt.console.warn.apply(zt.console,arguments):Go.call(null,arguments))},error:function(i){zt.console&&(typeof zt.console.error=="function"?zt.console.error.apply(zt.console,arguments):Go(i))}};function Jo(i,e,r){var a=new XMLHttpRequest;a.open("GET",i),a.responseType="blob",a.onload=function(){qr(a.response,e,r)},a.onerror=function(){me.error("could not download file")},a.send()}function Ms(i){var e=new XMLHttpRequest;e.open("HEAD",i,!1);try{e.send()}catch{}return e.status>=200&&e.status<=299}function Ga(i){try{i.dispatchEvent(new MouseEvent("click"))}catch{var e=document.createEvent("MouseEvents");e.initMouseEvent("click",!0,!0,window,0,0,0,80,20,!1,!1,!1,!1,0,null),i.dispatchEvent(e)}}var ea,ts,qr=zt.saveAs||((typeof window>"u"?"undefined":ve(window))!=="object"||window!==zt?function(){}:typeof HTMLAnchorElement<"u"&&"download"in HTMLAnchorElement.prototype?function(i,e,r){var a=zt.URL||zt.webkitURL,u=document.createElement("a");e=e||i.name||"download",u.download=e,u.rel="noopener",typeof i=="string"?(u.href=i,u.origin!==location.origin?Ms(u.href)?Jo(i,e,r):Ga(u,u.target="_blank"):Ga(u)):(u.href=a.createObjectURL(i),setTimeout(function(){a.revokeObjectURL(u.href)},4e4),setTimeout(function(){Ga(u)},0))}:"msSaveOrOpenBlob"in navigator?function(i,e,r){if(e=e||i.name||"download",typeof i=="string")if(Ms(i))Jo(i,e,r);else{var a=document.createElement("a");a.href=i,a.target="_blank",setTimeout(function(){Ga(a)})}else navigator.msSaveOrOpenBlob(function(u,o){return o===void 0?o={autoBom:!1}:ve(o)!=="object"&&(me.warn("Deprecated: Expected third argument to be a object"),o={autoBom:!o}),o.autoBom&&/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(u.type)?new Blob([String.fromCharCode(65279),u],{type:u.type}):u}(i,r),e)}:function(i,e,r,a){if((a=a||open("","_blank"))&&(a.document.title=a.document.body.innerText="downloading..."),typeof i=="string")return Jo(i,e,r);var u=i.type==="application/octet-stream",o=/constructor/i.test(zt.HTMLElement)||zt.safari,h=/CriOS\/[\d]+/.test(navigator.userAgent);if((h||u&&o)&&(typeof FileReader>"u"?"undefined":ve(FileReader))==="object"){var l=new FileReader;l.onloadend=function(){var x=l.result;x=h?x:x.replace(/^data:[^;]*;/,"data:attachment/file;"),a?a.location.href=x:location=x,a=null},l.readAsDataURL(i)}else{var f=zt.URL||zt.webkitURL,v=f.createObjectURL(i);a?a.location=v:location.href=v,a=null,setTimeout(function(){f.revokeObjectURL(v)},4e4)}});/**
 * A class to parse color values
 * <AUTHOR> Stefanov <<EMAIL>>
 * {@link   http://www.phpied.com/rgb-color-parser-in-javascript/}
 * @license Use it if you like it
 */function $s(i){var e;i=i||"",this.ok=!1,i.charAt(0)=="#"&&(i=i.substr(1,6)),i={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"00ffff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000000",blanchedalmond:"ffebcd",blue:"0000ff",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"00ffff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgreen:"006400",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dodgerblue:"1e90ff",feldspar:"d19275",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"ff00ff",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",green:"008000",greenyellow:"adff2f",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgrey:"d3d3d3",lightgreen:"90ee90",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslateblue:"8470ff",lightslategray:"778899",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"00ff00",limegreen:"32cd32",linen:"faf0e6",magenta:"ff00ff",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370d8",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"d87093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",red:"ff0000",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",violetred:"d02090",wheat:"f5deb3",white:"ffffff",whitesmoke:"f5f5f5",yellow:"ffff00",yellowgreen:"9acd32"}[i=(i=i.replace(/ /g,"")).toLowerCase()]||i;for(var r=[{re:/^rgb\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3})\)$/,example:["rgb(123, 234, 45)","rgb(255,234,245)"],process:function(l){return[parseInt(l[1]),parseInt(l[2]),parseInt(l[3])]}},{re:/^(\w{2})(\w{2})(\w{2})$/,example:["#00ff00","336699"],process:function(l){return[parseInt(l[1],16),parseInt(l[2],16),parseInt(l[3],16)]}},{re:/^(\w{1})(\w{1})(\w{1})$/,example:["#fb0","f0f"],process:function(l){return[parseInt(l[1]+l[1],16),parseInt(l[2]+l[2],16),parseInt(l[3]+l[3],16)]}}],a=0;a<r.length;a++){var u=r[a].re,o=r[a].process,h=u.exec(i);h&&(e=o(h),this.r=e[0],this.g=e[1],this.b=e[2],this.ok=!0)}this.r=this.r<0||isNaN(this.r)?0:this.r>255?255:this.r,this.g=this.g<0||isNaN(this.g)?0:this.g>255?255:this.g,this.b=this.b<0||isNaN(this.b)?0:this.b>255?255:this.b,this.toRGB=function(){return"rgb("+this.r+", "+this.g+", "+this.b+")"},this.toHex=function(){var l=this.r.toString(16),f=this.g.toString(16),v=this.b.toString(16);return l.length==1&&(l="0"+l),f.length==1&&(f="0"+f),v.length==1&&(v="0"+v),"#"+l+f+v}}/**
 * @license
 * Joseph Myers does not specify a particular license for his work.
 *
 * Author: Joseph Myers
 * Accessed from: http://www.myersdaily.org/joseph/javascript/md5.js
 *
 * Modified by: Owen Leong
 */function Yo(i,e){var r=i[0],a=i[1],u=i[2],o=i[3];r=Xe(r,a,u,o,e[0],7,-680876936),o=Xe(o,r,a,u,e[1],12,-389564586),u=Xe(u,o,r,a,e[2],17,606105819),a=Xe(a,u,o,r,e[3],22,-**********),r=Xe(r,a,u,o,e[4],7,-176418897),o=Xe(o,r,a,u,e[5],12,**********),u=Xe(u,o,r,a,e[6],17,-**********),a=Xe(a,u,o,r,e[7],22,-45705983),r=Xe(r,a,u,o,e[8],7,**********),o=Xe(o,r,a,u,e[9],12,-**********),u=Xe(u,o,r,a,e[10],17,-42063),a=Xe(a,u,o,r,e[11],22,-**********),r=Xe(r,a,u,o,e[12],7,**********),o=Xe(o,r,a,u,e[13],12,-40341101),u=Xe(u,o,r,a,e[14],17,-**********),r=Ke(r,a=Xe(a,u,o,r,e[15],22,**********),u,o,e[1],5,-165796510),o=Ke(o,r,a,u,e[6],9,-**********),u=Ke(u,o,r,a,e[11],14,643717713),a=Ke(a,u,o,r,e[0],20,-373897302),r=Ke(r,a,u,o,e[5],5,-701558691),o=Ke(o,r,a,u,e[10],9,38016083),u=Ke(u,o,r,a,e[15],14,-660478335),a=Ke(a,u,o,r,e[4],20,-405537848),r=Ke(r,a,u,o,e[9],5,568446438),o=Ke(o,r,a,u,e[14],9,-1019803690),u=Ke(u,o,r,a,e[3],14,-187363961),a=Ke(a,u,o,r,e[8],20,1163531501),r=Ke(r,a,u,o,e[13],5,-1444681467),o=Ke(o,r,a,u,e[2],9,-51403784),u=Ke(u,o,r,a,e[7],14,1735328473),r=Ze(r,a=Ke(a,u,o,r,e[12],20,-1926607734),u,o,e[5],4,-378558),o=Ze(o,r,a,u,e[8],11,-2022574463),u=Ze(u,o,r,a,e[11],16,1839030562),a=Ze(a,u,o,r,e[14],23,-35309556),r=Ze(r,a,u,o,e[1],4,-1530992060),o=Ze(o,r,a,u,e[4],11,1272893353),u=Ze(u,o,r,a,e[7],16,-155497632),a=Ze(a,u,o,r,e[10],23,-1094730640),r=Ze(r,a,u,o,e[13],4,681279174),o=Ze(o,r,a,u,e[0],11,-358537222),u=Ze(u,o,r,a,e[3],16,-722521979),a=Ze(a,u,o,r,e[6],23,76029189),r=Ze(r,a,u,o,e[9],4,-640364487),o=Ze(o,r,a,u,e[12],11,-421815835),u=Ze(u,o,r,a,e[15],16,530742520),r=$e(r,a=Ze(a,u,o,r,e[2],23,-995338651),u,o,e[0],6,-198630844),o=$e(o,r,a,u,e[7],10,1126891415),u=$e(u,o,r,a,e[14],15,-1416354905),a=$e(a,u,o,r,e[5],21,-57434055),r=$e(r,a,u,o,e[12],6,1700485571),o=$e(o,r,a,u,e[3],10,-1894986606),u=$e(u,o,r,a,e[10],15,-1051523),a=$e(a,u,o,r,e[1],21,-2054922799),r=$e(r,a,u,o,e[8],6,1873313359),o=$e(o,r,a,u,e[15],10,-30611744),u=$e(u,o,r,a,e[6],15,-1560198380),a=$e(a,u,o,r,e[13],21,1309151649),r=$e(r,a,u,o,e[4],6,-145523070),o=$e(o,r,a,u,e[11],10,-1120210379),u=$e(u,o,r,a,e[2],15,718787259),a=$e(a,u,o,r,e[9],21,-343485551),i[0]=Nr(r,i[0]),i[1]=Nr(a,i[1]),i[2]=Nr(u,i[2]),i[3]=Nr(o,i[3])}function no(i,e,r,a,u,o){return e=Nr(Nr(e,i),Nr(a,o)),Nr(e<<u|e>>>32-u,r)}function Xe(i,e,r,a,u,o,h){return no(e&r|~e&a,i,e,u,o,h)}function Ke(i,e,r,a,u,o,h){return no(e&a|r&~a,i,e,u,o,h)}function Ze(i,e,r,a,u,o,h){return no(e^r^a,i,e,u,o,h)}function $e(i,e,r,a,u,o,h){return no(r^(e|~a),i,e,u,o,h)}function Qs(i){var e,r=i.length,a=[1732584193,-271733879,-1732584194,271733878];for(e=64;e<=i.length;e+=64)Yo(a,Yc(i.substring(e-64,e)));i=i.substring(e-64);var u=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];for(e=0;e<i.length;e++)u[e>>2]|=i.charCodeAt(e)<<(e%4<<3);if(u[e>>2]|=128<<(e%4<<3),e>55)for(Yo(a,u),e=0;e<16;e++)u[e]=0;return u[14]=8*r,Yo(a,u),a}function Yc(i){var e,r=[];for(e=0;e<64;e+=4)r[e>>2]=i.charCodeAt(e)+(i.charCodeAt(e+1)<<8)+(i.charCodeAt(e+2)<<16)+(i.charCodeAt(e+3)<<24);return r}ea=zt.atob.bind(zt),ts=zt.btoa.bind(zt);var Es="0123456789abcdef".split("");function Xc(i){for(var e="",r=0;r<4;r++)e+=Es[i>>8*r+4&15]+Es[i>>8*r&15];return e}function Kc(i){return String.fromCharCode((255&i)>>0,(65280&i)>>8,(16711680&i)>>16,(**********&i)>>24)}function es(i){return Qs(i).map(Kc).join("")}var Zc=function(i){for(var e=0;e<i.length;e++)i[e]=Xc(i[e]);return i.join("")}(Qs("hello"))!="5d41402abc4b2a76b9719d911017c592";function Nr(i,e){if(Zc){var r=(65535&i)+(65535&e);return(i>>16)+(e>>16)+(r>>16)<<16|65535&r}return i+e&**********}/**
 * @license
 * FPDF is released under a permissive license: there is no usage restriction.
 * You may embed it freely in your application (commercial or not), with or
 * without modifications.
 *
 * Reference: http://www.fpdf.org/en/script/script37.php
 */function ns(i,e){var r,a,u,o;if(i!==r){for(var h=(u=i,o=1+(256/i.length>>0),new Array(o+1).join(u)),l=[],f=0;f<256;f++)l[f]=f;var v=0;for(f=0;f<256;f++){var x=l[f];v=(v+x+h.charCodeAt(f))%256,l[f]=l[v],l[v]=x}r=i,a=l}else l=a;var A=e.length,_=0,p=0,B="";for(f=0;f<A;f++)p=(p+(x=l[_=(_+1)%256]))%256,l[_]=l[p],l[p]=x,h=l[(l[_]+l[p])%256],B+=String.fromCharCode(e.charCodeAt(f)^h);return B}/**
 * @license
 * Licensed under the MIT License.
 * http://opensource.org/licenses/mit-license
 * Author: Owen Leong (@owenl131)
 * Date: 15 Oct 2020
 * References:
 * https://www.cs.cmu.edu/~dst/Adobe/Gallery/anon21jul01-pdf-encryption.txt
 * https://github.com/foliojs/pdfkit/blob/master/lib/security.js
 * http://www.fpdf.org/en/script/script37.php
 */var qs={print:4,modify:8,copy:16,"annot-forms":32};function bi(i,e,r,a){this.v=1,this.r=2;var u=192;i.forEach(function(l){if(qs.perm!==void 0)throw new Error("Invalid permission: "+l);u+=qs[l]}),this.padding="(¿N^NuAd\0NVÿú\b..\0¶Ðh>/\f©þdSiz";var o=(e+this.padding).substr(0,32),h=(r+this.padding).substr(0,32);this.O=this.processOwnerPassword(o,h),this.P=-(1+(255^u)),this.encryptionKey=es(o+this.O+this.lsbFirstWord(this.P)+this.hexToBytes(a)).substr(0,5),this.U=ns(this.encryptionKey,this.padding)}function yi(i){if(/[^\u0000-\u00ff]/.test(i))throw new Error("Invalid PDF Name Object: "+i+", Only accept ASCII characters.");for(var e="",r=i.length,a=0;a<r;a++){var u=i.charCodeAt(a);u<33||u===35||u===37||u===40||u===41||u===47||u===60||u===62||u===91||u===93||u===123||u===125||u>126?e+="#"+("0"+u.toString(16)).slice(-2):e+=i[a]}return e}function Ds(i){if(ve(i)!=="object")throw new Error("Invalid Context passed to initialize PubSub (jsPDF-module)");var e={};this.subscribe=function(r,a,u){if(u=u||!1,typeof r!="string"||typeof a!="function"||typeof u!="boolean")throw new Error("Invalid arguments passed to PubSub.subscribe (jsPDF-module)");e.hasOwnProperty(r)||(e[r]={});var o=Math.random().toString(35);return e[r][o]=[a,!!u],o},this.unsubscribe=function(r){for(var a in e)if(e[a][r])return delete e[a][r],Object.keys(e[a]).length===0&&delete e[a],!0;return!1},this.publish=function(r){if(e.hasOwnProperty(r)){var a=Array.prototype.slice.call(arguments,1),u=[];for(var o in e[r]){var h=e[r][o];try{h[0].apply(i,a)}catch(l){zt.console&&me.error("jsPDF PubSub Error",l.message,l)}h[1]&&u.push(o)}u.length&&u.forEach(this.unsubscribe)}},this.getTopics=function(){return e}}function to(i){if(!(this instanceof to))return new to(i);var e="opacity,stroke-opacity".split(",");for(var r in i)i.hasOwnProperty(r)&&e.indexOf(r)>=0&&(this[r]=i[r]);this.id="",this.objectNumber=-1}function tc(i,e){this.gState=i,this.matrix=e,this.id="",this.objectNumber=-1}function Dr(i,e,r,a,u){if(!(this instanceof Dr))return new Dr(i,e,r,a,u);this.type=i==="axial"?2:3,this.coords=e,this.colors=r,tc.call(this,a,u)}function wi(i,e,r,a,u){if(!(this instanceof wi))return new wi(i,e,r,a,u);this.boundingBox=i,this.xStep=e,this.yStep=r,this.stream="",this.cloneIndex=0,tc.call(this,a,u)}function Ut(i){var e,r=typeof arguments[0]=="string"?arguments[0]:"p",a=arguments[1],u=arguments[2],o=arguments[3],h=[],l=1,f=16,v="S",x=null;ve(i=i||{})==="object"&&(r=i.orientation,a=i.unit||a,u=i.format||u,o=i.compress||i.compressPdf||o,(x=i.encryption||null)!==null&&(x.userPassword=x.userPassword||"",x.ownerPassword=x.ownerPassword||"",x.userPermissions=x.userPermissions||[]),l=typeof i.userUnit=="number"?Math.abs(i.userUnit):1,i.precision!==void 0&&(e=i.precision),i.floatPrecision!==void 0&&(f=i.floatPrecision),v=i.defaultPathOperation||"S"),h=i.filters||(o===!0?["FlateEncode"]:h),a=a||"mm",r=(""+(r||"P")).toLowerCase();var A=i.putOnlyUsedFonts||!1,_={},p={internal:{},__private__:{}};p.__private__.PubSub=Ds;var B="1.3",F=p.__private__.getPdfVersion=function(){return B};p.__private__.setPdfVersion=function(s){B=s};var q={a0:[2383.94,3370.39],a1:[1683.78,2383.94],a2:[1190.55,1683.78],a3:[841.89,1190.55],a4:[595.28,841.89],a5:[419.53,595.28],a6:[297.64,419.53],a7:[209.76,297.64],a8:[147.4,209.76],a9:[104.88,147.4],a10:[73.7,104.88],b0:[2834.65,4008.19],b1:[2004.09,2834.65],b2:[1417.32,2004.09],b3:[1000.63,1417.32],b4:[708.66,1000.63],b5:[498.9,708.66],b6:[354.33,498.9],b7:[249.45,354.33],b8:[175.75,249.45],b9:[124.72,175.75],b10:[87.87,124.72],c0:[2599.37,3676.54],c1:[1836.85,2599.37],c2:[1298.27,1836.85],c3:[918.43,1298.27],c4:[649.13,918.43],c5:[459.21,649.13],c6:[323.15,459.21],c7:[229.61,323.15],c8:[161.57,229.61],c9:[113.39,161.57],c10:[79.37,113.39],dl:[311.81,623.62],letter:[612,792],"government-letter":[576,756],legal:[612,1008],"junior-legal":[576,360],ledger:[1224,792],tabloid:[792,1224],"credit-card":[153,243]};p.__private__.getPageFormats=function(){return q};var S=p.__private__.getPageFormat=function(s){return q[s]};u=u||"a4";var M={COMPAT:"compat",ADVANCED:"advanced"},Z=M.COMPAT;function st(){this.saveGraphicsState(),O(new Tt(It,0,0,-It,0,ur()*It).toString()+" cm"),this.setFontSize(this.getFontSize()/It),v="n",Z=M.ADVANCED}function dt(){this.restoreGraphicsState(),v="S",Z=M.COMPAT}var Nt=p.__private__.combineFontStyleAndFontWeight=function(s,m){if(s=="bold"&&m=="normal"||s=="bold"&&m==400||s=="normal"&&m=="italic"||s=="bold"&&m=="italic")throw new Error("Invalid Combination of fontweight and fontstyle");return m&&(s=m==400||m==="normal"?s==="italic"?"italic":"normal":m!=700&&m!=="bold"||s!=="normal"?(m==700?"bold":m)+""+s:"bold"),s};p.advancedAPI=function(s){var m=Z===M.COMPAT;return m&&st.call(this),typeof s!="function"||(s(this),m&&dt.call(this)),this},p.compatAPI=function(s){var m=Z===M.ADVANCED;return m&&dt.call(this),typeof s!="function"||(s(this),m&&st.call(this)),this},p.isAdvancedAPI=function(){return Z===M.ADVANCED};var rt,G=function(s){if(Z!==M.ADVANCED)throw new Error(s+" is only available in 'advanced' API mode. You need to call advancedAPI() first.")},vt=p.roundToPrecision=p.__private__.roundToPrecision=function(s,m){var j=e||m;if(isNaN(s)||isNaN(j))throw new Error("Invalid argument passed to jsPDF.roundToPrecision");return s.toFixed(j).replace(/0+$/,"")};rt=p.hpf=p.__private__.hpf=typeof f=="number"?function(s){if(isNaN(s))throw new Error("Invalid argument passed to jsPDF.hpf");return vt(s,f)}:f==="smart"?function(s){if(isNaN(s))throw new Error("Invalid argument passed to jsPDF.hpf");return vt(s,s>-1&&s<1?16:5)}:function(s){if(isNaN(s))throw new Error("Invalid argument passed to jsPDF.hpf");return vt(s,16)};var bt=p.f2=p.__private__.f2=function(s){if(isNaN(s))throw new Error("Invalid argument passed to jsPDF.f2");return vt(s,2)},k=p.__private__.f3=function(s){if(isNaN(s))throw new Error("Invalid argument passed to jsPDF.f3");return vt(s,3)},I=p.scale=p.__private__.scale=function(s){if(isNaN(s))throw new Error("Invalid argument passed to jsPDF.scale");return Z===M.COMPAT?s*It:Z===M.ADVANCED?s:void 0},H=function(s){return Z===M.COMPAT?ur()-s:Z===M.ADVANCED?s:void 0},R=function(s){return I(H(s))};p.__private__.setPrecision=p.setPrecision=function(s){typeof parseInt(s,10)=="number"&&(e=parseInt(s,10))};var ct,ot="00000000000000000000000000000000",mt=p.__private__.getFileId=function(){return ot},tt=p.__private__.setFileId=function(s){return ot=s!==void 0&&/^[a-fA-F0-9]{32}$/.test(s)?s.toUpperCase():ot.split("").map(function(){return"ABCDEF0123456789".charAt(Math.floor(16*Math.random()))}).join(""),x!==null&&(Je=new bi(x.userPermissions,x.userPassword,x.ownerPassword,ot)),ot};p.setFileId=function(s){return tt(s),this},p.getFileId=function(){return mt()};var pt=p.__private__.convertDateToPDFDate=function(s){var m=s.getTimezoneOffset(),j=m<0?"+":"-",D=Math.floor(Math.abs(m/60)),Y=Math.abs(m%60),it=[j,E(D),"'",E(Y),"'"].join("");return["D:",s.getFullYear(),E(s.getMonth()+1),E(s.getDate()),E(s.getHours()),E(s.getMinutes()),E(s.getSeconds()),it].join("")},ft=p.__private__.convertPDFDateToDate=function(s){var m=parseInt(s.substr(2,4),10),j=parseInt(s.substr(6,2),10)-1,D=parseInt(s.substr(8,2),10),Y=parseInt(s.substr(10,2),10),it=parseInt(s.substr(12,2),10),yt=parseInt(s.substr(14,2),10);return new Date(m,j,D,Y,it,yt,0)},Et=p.__private__.setCreationDate=function(s){var m;if(s===void 0&&(s=new Date),s instanceof Date)m=pt(s);else{if(!/^D:(20[0-2][0-9]|203[0-7]|19[7-9][0-9])(0[0-9]|1[0-2])([0-2][0-9]|3[0-1])(0[0-9]|1[0-9]|2[0-3])(0[0-9]|[1-5][0-9])(0[0-9]|[1-5][0-9])(\+0[0-9]|\+1[0-4]|-0[0-9]|-1[0-1])'(0[0-9]|[1-5][0-9])'?$/.test(s))throw new Error("Invalid argument passed to jsPDF.setCreationDate");m=s}return ct=m},w=p.__private__.getCreationDate=function(s){var m=ct;return s==="jsDate"&&(m=ft(ct)),m};p.setCreationDate=function(s){return Et(s),this},p.getCreationDate=function(s){return w(s)};var C,E=p.__private__.padd2=function(s){return("0"+parseInt(s)).slice(-2)},W=p.__private__.padd2Hex=function(s){return("00"+(s=s.toString())).substr(s.length)},J=0,$=[],et=[],Q=0,At=[],Lt=[],Ot=!1,Ct=et,Wt=function(){J=0,Q=0,et=[],$=[],At=[],Zn=Oe(),_n=Oe()};p.__private__.setCustomOutputDestination=function(s){Ot=!0,Ct=s};var at=function(s){Ot||(Ct=s)};p.__private__.resetCustomOutputDestination=function(){Ot=!1,Ct=et};var O=p.__private__.out=function(s){return s=s.toString(),Q+=s.length+1,Ct.push(s),Ct},Kt=p.__private__.write=function(s){return O(arguments.length===1?s.toString():Array.prototype.join.call(arguments," "))},Mt=p.__private__.getArrayBuffer=function(s){for(var m=s.length,j=new ArrayBuffer(m),D=new Uint8Array(j);m--;)D[m]=s.charCodeAt(m);return j},wt=[["Helvetica","helvetica","normal","WinAnsiEncoding"],["Helvetica-Bold","helvetica","bold","WinAnsiEncoding"],["Helvetica-Oblique","helvetica","italic","WinAnsiEncoding"],["Helvetica-BoldOblique","helvetica","bolditalic","WinAnsiEncoding"],["Courier","courier","normal","WinAnsiEncoding"],["Courier-Bold","courier","bold","WinAnsiEncoding"],["Courier-Oblique","courier","italic","WinAnsiEncoding"],["Courier-BoldOblique","courier","bolditalic","WinAnsiEncoding"],["Times-Roman","times","normal","WinAnsiEncoding"],["Times-Bold","times","bold","WinAnsiEncoding"],["Times-Italic","times","italic","WinAnsiEncoding"],["Times-BoldItalic","times","bolditalic","WinAnsiEncoding"],["ZapfDingbats","zapfdingbats","normal",null],["Symbol","symbol","normal",null]];p.__private__.getStandardFonts=function(){return wt};var xt=i.fontSize||16;p.__private__.setFontSize=p.setFontSize=function(s){return xt=Z===M.ADVANCED?s/It:s,this};var kt,Pt=p.__private__.getFontSize=p.getFontSize=function(){return Z===M.COMPAT?xt:xt*It},qt=i.R2L||!1;p.__private__.setR2L=p.setR2L=function(s){return qt=s,this},p.__private__.getR2L=p.getR2L=function(){return qt};var Gt,Qt=p.__private__.setZoomMode=function(s){var m=[void 0,null,"fullwidth","fullheight","fullpage","original"];if(/^(?:\d+\.\d*|\d*\.\d+|\d+)%$/.test(s))kt=s;else if(isNaN(s)){if(m.indexOf(s)===-1)throw new Error('zoom must be Integer (e.g. 2), a percentage Value (e.g. 300%) or fullwidth, fullheight, fullpage, original. "'+s+'" is not recognized.');kt=s}else kt=parseInt(s,10)};p.__private__.getZoomMode=function(){return kt};var te,ie=p.__private__.setPageMode=function(s){if([void 0,null,"UseNone","UseOutlines","UseThumbs","FullScreen"].indexOf(s)==-1)throw new Error('Page mode must be one of UseNone, UseOutlines, UseThumbs, or FullScreen. "'+s+'" is not recognized.');Gt=s};p.__private__.getPageMode=function(){return Gt};var fe=p.__private__.setLayoutMode=function(s){if([void 0,null,"continuous","single","twoleft","tworight","two"].indexOf(s)==-1)throw new Error('Layout mode must be one of continuous, single, twoleft, tworight. "'+s+'" is not recognized.');te=s};p.__private__.getLayoutMode=function(){return te},p.__private__.setDisplayMode=p.setDisplayMode=function(s,m,j){return Qt(s),fe(m),ie(j),this};var Ht={title:"",subject:"",author:"",keywords:"",creator:""};p.__private__.getDocumentProperty=function(s){if(Object.keys(Ht).indexOf(s)===-1)throw new Error("Invalid argument passed to jsPDF.getDocumentProperty");return Ht[s]},p.__private__.getDocumentProperties=function(){return Ht},p.__private__.setDocumentProperties=p.setProperties=p.setDocumentProperties=function(s){for(var m in Ht)Ht.hasOwnProperty(m)&&s[m]&&(Ht[m]=s[m]);return this},p.__private__.setDocumentProperty=function(s,m){if(Object.keys(Ht).indexOf(s)===-1)throw new Error("Invalid arguments passed to jsPDF.setDocumentProperty");return Ht[s]=m};var ee,It,Ge,oe,An,pe={},we={},qn=[],ce={},xr={},Ne={},xn={},Kn=null,Ae=0,Jt=[],ue=new Ds(p),Sr=i.hotfixes||[],He={},Dn={},Rn=[],Tt=function s(m,j,D,Y,it,yt){if(!(this instanceof s))return new s(m,j,D,Y,it,yt);isNaN(m)&&(m=1),isNaN(j)&&(j=0),isNaN(D)&&(D=0),isNaN(Y)&&(Y=1),isNaN(it)&&(it=0),isNaN(yt)&&(yt=0),this._matrix=[m,j,D,Y,it,yt]};Object.defineProperty(Tt.prototype,"sx",{get:function(){return this._matrix[0]},set:function(s){this._matrix[0]=s}}),Object.defineProperty(Tt.prototype,"shy",{get:function(){return this._matrix[1]},set:function(s){this._matrix[1]=s}}),Object.defineProperty(Tt.prototype,"shx",{get:function(){return this._matrix[2]},set:function(s){this._matrix[2]=s}}),Object.defineProperty(Tt.prototype,"sy",{get:function(){return this._matrix[3]},set:function(s){this._matrix[3]=s}}),Object.defineProperty(Tt.prototype,"tx",{get:function(){return this._matrix[4]},set:function(s){this._matrix[4]=s}}),Object.defineProperty(Tt.prototype,"ty",{get:function(){return this._matrix[5]},set:function(s){this._matrix[5]=s}}),Object.defineProperty(Tt.prototype,"a",{get:function(){return this._matrix[0]},set:function(s){this._matrix[0]=s}}),Object.defineProperty(Tt.prototype,"b",{get:function(){return this._matrix[1]},set:function(s){this._matrix[1]=s}}),Object.defineProperty(Tt.prototype,"c",{get:function(){return this._matrix[2]},set:function(s){this._matrix[2]=s}}),Object.defineProperty(Tt.prototype,"d",{get:function(){return this._matrix[3]},set:function(s){this._matrix[3]=s}}),Object.defineProperty(Tt.prototype,"e",{get:function(){return this._matrix[4]},set:function(s){this._matrix[4]=s}}),Object.defineProperty(Tt.prototype,"f",{get:function(){return this._matrix[5]},set:function(s){this._matrix[5]=s}}),Object.defineProperty(Tt.prototype,"rotation",{get:function(){return Math.atan2(this.shx,this.sx)}}),Object.defineProperty(Tt.prototype,"scaleX",{get:function(){return this.decompose().scale.sx}}),Object.defineProperty(Tt.prototype,"scaleY",{get:function(){return this.decompose().scale.sy}}),Object.defineProperty(Tt.prototype,"isIdentity",{get:function(){return this.sx===1&&this.shy===0&&this.shx===0&&this.sy===1&&this.tx===0&&this.ty===0}}),Tt.prototype.join=function(s){return[this.sx,this.shy,this.shx,this.sy,this.tx,this.ty].map(rt).join(s)},Tt.prototype.multiply=function(s){var m=s.sx*this.sx+s.shy*this.shx,j=s.sx*this.shy+s.shy*this.sy,D=s.shx*this.sx+s.sy*this.shx,Y=s.shx*this.shy+s.sy*this.sy,it=s.tx*this.sx+s.ty*this.shx+this.tx,yt=s.tx*this.shy+s.ty*this.sy+this.ty;return new Tt(m,j,D,Y,it,yt)},Tt.prototype.decompose=function(){var s=this.sx,m=this.shy,j=this.shx,D=this.sy,Y=this.tx,it=this.ty,yt=Math.sqrt(s*s+m*m),Ft=(s/=yt)*j+(m/=yt)*D;j-=s*Ft,D-=m*Ft;var Dt=Math.sqrt(j*j+D*D);return Ft/=Dt,s*(D/=Dt)<m*(j/=Dt)&&(s=-s,m=-m,Ft=-Ft,yt=-yt),{scale:new Tt(yt,0,0,Dt,0,0),translate:new Tt(1,0,0,1,Y,it),rotate:new Tt(s,m,-m,s,0,0),skew:new Tt(1,0,Ft,1,0,0)}},Tt.prototype.toString=function(s){return this.join(" ")},Tt.prototype.inversed=function(){var s=this.sx,m=this.shy,j=this.shx,D=this.sy,Y=this.tx,it=this.ty,yt=1/(s*D-m*j),Ft=D*yt,Dt=-m*yt,Zt=-j*yt,Yt=s*yt;return new Tt(Ft,Dt,Zt,Yt,-Ft*Y-Zt*it,-Dt*Y-Yt*it)},Tt.prototype.applyToPoint=function(s){var m=s.x*this.sx+s.y*this.shx+this.tx,j=s.x*this.shy+s.y*this.sy+this.ty;return new ti(m,j)},Tt.prototype.applyToRectangle=function(s){var m=this.applyToPoint(s),j=this.applyToPoint(new ti(s.x+s.w,s.y+s.h));return new Mi(m.x,m.y,j.x-m.x,j.y-m.y)},Tt.prototype.clone=function(){var s=this.sx,m=this.shy,j=this.shx,D=this.sy,Y=this.tx,it=this.ty;return new Tt(s,m,j,D,Y,it)},p.Matrix=Tt;var Sn=p.matrixMult=function(s,m){return m.multiply(s)},Tn=new Tt(1,0,0,1,0,0);p.unitMatrix=p.identityMatrix=Tn;var nn=function(s,m){if(!xr[s]){var j=(m instanceof Dr?"Sh":"P")+(Object.keys(ce).length+1).toString(10);m.id=j,xr[s]=j,ce[j]=m,ue.publish("addPattern",m)}};p.ShadingPattern=Dr,p.TilingPattern=wi,p.addShadingPattern=function(s,m){return G("addShadingPattern()"),nn(s,m),this},p.beginTilingPattern=function(s){G("beginTilingPattern()"),wa(s.boundingBox[0],s.boundingBox[1],s.boundingBox[2]-s.boundingBox[0],s.boundingBox[3]-s.boundingBox[1],s.matrix)},p.endTilingPattern=function(s,m){G("endTilingPattern()"),m.stream=Lt[C].join(`
`),nn(s,m),ue.publish("endTilingPattern",m),Rn.pop().restore()};var qe=p.__private__.newObject=function(){var s=Oe();return hn(s,!0),s},Oe=p.__private__.newObjectDeferred=function(){return J++,$[J]=function(){return Q},J},hn=function(s,m){return m=typeof m=="boolean"&&m,$[s]=Q,m&&O(s+" 0 obj"),s},zr=p.__private__.newAdditionalObject=function(){var s={objId:Oe(),content:""};return At.push(s),s},Zn=Oe(),_n=Oe(),Pn=p.__private__.decodeColorString=function(s){var m=s.split(" ");if(m.length!==2||m[1]!=="g"&&m[1]!=="G")m.length===5&&(m[4]==="k"||m[4]==="K")&&(m=[(1-m[0])*(1-m[3]),(1-m[1])*(1-m[3]),(1-m[2])*(1-m[3]),"r"]);else{var j=parseFloat(m[0]);m=[j,j,j,"r"]}for(var D="#",Y=0;Y<3;Y++)D+=("0"+Math.floor(255*parseFloat(m[Y])).toString(16)).slice(-2);return D},kn=p.__private__.encodeColorString=function(s){var m;typeof s=="string"&&(s={ch1:s});var j=s.ch1,D=s.ch2,Y=s.ch3,it=s.ch4,yt=s.pdfColorType==="draw"?["G","RG","K"]:["g","rg","k"];if(typeof j=="string"&&j.charAt(0)!=="#"){var Ft=new $s(j);if(Ft.ok)j=Ft.toHex();else if(!/^\d*\.?\d*$/.test(j))throw new Error('Invalid color "'+j+'" passed to jsPDF.encodeColorString.')}if(typeof j=="string"&&/^#[0-9A-Fa-f]{3}$/.test(j)&&(j="#"+j[1]+j[1]+j[2]+j[2]+j[3]+j[3]),typeof j=="string"&&/^#[0-9A-Fa-f]{6}$/.test(j)){var Dt=parseInt(j.substr(1),16);j=Dt>>16&255,D=Dt>>8&255,Y=255&Dt}if(D===void 0||it===void 0&&j===D&&D===Y)if(typeof j=="string")m=j+" "+yt[0];else switch(s.precision){case 2:m=bt(j/255)+" "+yt[0];break;case 3:default:m=k(j/255)+" "+yt[0]}else if(it===void 0||ve(it)==="object"){if(it&&!isNaN(it.a)&&it.a===0)return m=["1.","1.","1.",yt[1]].join(" ");if(typeof j=="string")m=[j,D,Y,yt[1]].join(" ");else switch(s.precision){case 2:m=[bt(j/255),bt(D/255),bt(Y/255),yt[1]].join(" ");break;default:case 3:m=[k(j/255),k(D/255),k(Y/255),yt[1]].join(" ")}}else if(typeof j=="string")m=[j,D,Y,it,yt[2]].join(" ");else switch(s.precision){case 2:m=[bt(j),bt(D),bt(Y),bt(it),yt[2]].join(" ");break;case 3:default:m=[k(j),k(D),k(Y),k(it),yt[2]].join(" ")}return m},zn=p.__private__.getFilters=function(){return h},gn=p.__private__.putStream=function(s){var m=(s=s||{}).data||"",j=s.filters||zn(),D=s.alreadyAppliedFilters||[],Y=s.addLength1||!1,it=m.length,yt=s.objectId,Ft=function(Ye){return Ye};if(x!==null&&yt===void 0)throw new Error("ObjectId must be passed to putStream for file encryption");x!==null&&(Ft=Je.encryptor(yt,0));var Dt={};j===!0&&(j=["FlateEncode"]);var Zt=s.additionalKeyValues||[],Yt=(Dt=Ut.API.processDataByFilters!==void 0?Ut.API.processDataByFilters(m,j):{data:m,reverseChain:[]}).reverseChain+(Array.isArray(D)?D.join(" "):D.toString());if(Dt.data.length!==0&&(Zt.push({key:"Length",value:Dt.data.length}),Y===!0&&Zt.push({key:"Length1",value:it})),Yt.length!=0)if(Yt.split("/").length-1==1)Zt.push({key:"Filter",value:Yt});else{Zt.push({key:"Filter",value:"["+Yt+"]"});for(var re=0;re<Zt.length;re+=1)if(Zt[re].key==="DecodeParms"){for(var Le=[],xe=0;xe<Dt.reverseChain.split("/").length-1;xe+=1)Le.push("null");Le.push(Zt[re].value),Zt[re].value="["+Le.join(" ")+"]"}}O("<<");for(var Be=0;Be<Zt.length;Be++)O("/"+Zt[Be].key+" "+Zt[Be].value);O(">>"),Dt.data.length!==0&&(O("stream"),O(Ft(Dt.data)),O("endstream"))},Un=p.__private__.putPage=function(s){var m=s.number,j=s.data,D=s.objId,Y=s.contentsObjId;hn(D,!0),O("<</Type /Page"),O("/Parent "+s.rootDictionaryObjId+" 0 R"),O("/Resources "+s.resourceDictionaryObjId+" 0 R"),O("/MediaBox ["+parseFloat(rt(s.mediaBox.bottomLeftX))+" "+parseFloat(rt(s.mediaBox.bottomLeftY))+" "+rt(s.mediaBox.topRightX)+" "+rt(s.mediaBox.topRightY)+"]"),s.cropBox!==null&&O("/CropBox ["+rt(s.cropBox.bottomLeftX)+" "+rt(s.cropBox.bottomLeftY)+" "+rt(s.cropBox.topRightX)+" "+rt(s.cropBox.topRightY)+"]"),s.bleedBox!==null&&O("/BleedBox ["+rt(s.bleedBox.bottomLeftX)+" "+rt(s.bleedBox.bottomLeftY)+" "+rt(s.bleedBox.topRightX)+" "+rt(s.bleedBox.topRightY)+"]"),s.trimBox!==null&&O("/TrimBox ["+rt(s.trimBox.bottomLeftX)+" "+rt(s.trimBox.bottomLeftY)+" "+rt(s.trimBox.topRightX)+" "+rt(s.trimBox.topRightY)+"]"),s.artBox!==null&&O("/ArtBox ["+rt(s.artBox.bottomLeftX)+" "+rt(s.artBox.bottomLeftY)+" "+rt(s.artBox.topRightX)+" "+rt(s.artBox.topRightY)+"]"),typeof s.userUnit=="number"&&s.userUnit!==1&&O("/UserUnit "+s.userUnit),ue.publish("putPage",{objId:D,pageContext:Jt[m],pageNumber:m,page:j}),O("/Contents "+Y+" 0 R"),O(">>"),O("endobj");var it=j.join(`
`);return Z===M.ADVANCED&&(it+=`
Q`),hn(Y,!0),gn({data:it,filters:zn(),objectId:Y}),O("endobj"),D},_r=p.__private__.putPages=function(){var s,m,j=[];for(s=1;s<=Ae;s++)Jt[s].objId=Oe(),Jt[s].contentsObjId=Oe();for(s=1;s<=Ae;s++)j.push(Un({number:s,data:Lt[s],objId:Jt[s].objId,contentsObjId:Jt[s].contentsObjId,mediaBox:Jt[s].mediaBox,cropBox:Jt[s].cropBox,bleedBox:Jt[s].bleedBox,trimBox:Jt[s].trimBox,artBox:Jt[s].artBox,userUnit:Jt[s].userUnit,rootDictionaryObjId:Zn,resourceDictionaryObjId:_n}));hn(Zn,!0),O("<</Type /Pages");var D="/Kids [";for(m=0;m<Ae;m++)D+=j[m]+" 0 R ";O(D+"]"),O("/Count "+Ae),O(">>"),O("endobj"),ue.publish("postPutPages")},Ur=function(s){ue.publish("putFont",{font:s,out:O,newObject:qe,putStream:gn}),s.isAlreadyPutted!==!0&&(s.objectNumber=qe(),O("<<"),O("/Type /Font"),O("/BaseFont /"+yi(s.postScriptName)),O("/Subtype /Type1"),typeof s.encoding=="string"&&O("/Encoding /"+s.encoding),O("/FirstChar 32"),O("/LastChar 255"),O(">>"),O("endobj"))},Hr=function(){for(var s in pe)pe.hasOwnProperty(s)&&(A===!1||A===!0&&_.hasOwnProperty(s))&&Ur(pe[s])},Wr=function(s){s.objectNumber=qe();var m=[];m.push({key:"Type",value:"/XObject"}),m.push({key:"Subtype",value:"/Form"}),m.push({key:"BBox",value:"["+[rt(s.x),rt(s.y),rt(s.x+s.width),rt(s.y+s.height)].join(" ")+"]"}),m.push({key:"Matrix",value:"["+s.matrix.toString()+"]"});var j=s.pages[1].join(`
`);gn({data:j,additionalKeyValues:m,objectId:s.objectNumber}),O("endobj")},Vr=function(){for(var s in He)He.hasOwnProperty(s)&&Wr(He[s])},na=function(s,m){var j,D=[],Y=1/(m-1);for(j=0;j<1;j+=Y)D.push(j);if(D.push(1),s[0].offset!=0){var it={offset:0,color:s[0].color};s.unshift(it)}if(s[s.length-1].offset!=1){var yt={offset:1,color:s[s.length-1].color};s.push(yt)}for(var Ft="",Dt=0,Zt=0;Zt<D.length;Zt++){for(j=D[Zt];j>s[Dt+1].offset;)Dt++;var Yt=s[Dt].offset,re=(j-Yt)/(s[Dt+1].offset-Yt),Le=s[Dt].color,xe=s[Dt+1].color;Ft+=W(Math.round((1-re)*Le[0]+re*xe[0]).toString(16))+W(Math.round((1-re)*Le[1]+re*xe[1]).toString(16))+W(Math.round((1-re)*Le[2]+re*xe[2]).toString(16))}return Ft.trim()},ro=function(s,m){m||(m=21);var j=qe(),D=na(s.colors,m),Y=[];Y.push({key:"FunctionType",value:"0"}),Y.push({key:"Domain",value:"[0.0 1.0]"}),Y.push({key:"Size",value:"["+m+"]"}),Y.push({key:"BitsPerSample",value:"8"}),Y.push({key:"Range",value:"[0.0 1.0 0.0 1.0 0.0 1.0]"}),Y.push({key:"Decode",value:"[0.0 1.0 0.0 1.0 0.0 1.0]"}),gn({data:D,additionalKeyValues:Y,alreadyAppliedFilters:["/ASCIIHexDecode"],objectId:j}),O("endobj"),s.objectNumber=qe(),O("<< /ShadingType "+s.type),O("/ColorSpace /DeviceRGB");var it="/Coords ["+rt(parseFloat(s.coords[0]))+" "+rt(parseFloat(s.coords[1]))+" ";s.type===2?it+=rt(parseFloat(s.coords[2]))+" "+rt(parseFloat(s.coords[3])):it+=rt(parseFloat(s.coords[2]))+" "+rt(parseFloat(s.coords[3]))+" "+rt(parseFloat(s.coords[4]))+" "+rt(parseFloat(s.coords[5])),O(it+="]"),s.matrix&&O("/Matrix ["+s.matrix.toString()+"]"),O("/Function "+j+" 0 R"),O("/Extend [true true]"),O(">>"),O("endobj")},io=function(s,m){var j=Oe(),D=qe();m.push({resourcesOid:j,objectOid:D}),s.objectNumber=D;var Y=[];Y.push({key:"Type",value:"/Pattern"}),Y.push({key:"PatternType",value:"1"}),Y.push({key:"PaintType",value:"1"}),Y.push({key:"TilingType",value:"1"}),Y.push({key:"BBox",value:"["+s.boundingBox.map(rt).join(" ")+"]"}),Y.push({key:"XStep",value:rt(s.xStep)}),Y.push({key:"YStep",value:rt(s.yStep)}),Y.push({key:"Resources",value:j+" 0 R"}),s.matrix&&Y.push({key:"Matrix",value:"["+s.matrix.toString()+"]"}),gn({data:s.stream,additionalKeyValues:Y,objectId:s.objectNumber}),O("endobj")},Gr=function(s){var m;for(m in ce)ce.hasOwnProperty(m)&&(ce[m]instanceof Dr?ro(ce[m]):ce[m]instanceof wi&&io(ce[m],s))},ra=function(s){for(var m in s.objectNumber=qe(),O("<<"),s)switch(m){case"opacity":O("/ca "+bt(s[m]));break;case"stroke-opacity":O("/CA "+bt(s[m]))}O(">>"),O("endobj")},ao=function(){var s;for(s in Ne)Ne.hasOwnProperty(s)&&ra(Ne[s])},Si=function(){for(var s in O("/XObject <<"),He)He.hasOwnProperty(s)&&He[s].objectNumber>=0&&O("/"+s+" "+He[s].objectNumber+" 0 R");ue.publish("putXobjectDict"),O(">>")},oo=function(){Je.oid=qe(),O("<<"),O("/Filter /Standard"),O("/V "+Je.v),O("/R "+Je.r),O("/U <"+Je.toHexString(Je.U)+">"),O("/O <"+Je.toHexString(Je.O)+">"),O("/P "+Je.P),O(">>"),O("endobj")},ia=function(){for(var s in O("/Font <<"),pe)pe.hasOwnProperty(s)&&(A===!1||A===!0&&_.hasOwnProperty(s))&&O("/"+s+" "+pe[s].objectNumber+" 0 R");O(">>")},so=function(){if(Object.keys(ce).length>0){for(var s in O("/Shading <<"),ce)ce.hasOwnProperty(s)&&ce[s]instanceof Dr&&ce[s].objectNumber>=0&&O("/"+s+" "+ce[s].objectNumber+" 0 R");ue.publish("putShadingPatternDict"),O(">>")}},Jr=function(s){if(Object.keys(ce).length>0){for(var m in O("/Pattern <<"),ce)ce.hasOwnProperty(m)&&ce[m]instanceof p.TilingPattern&&ce[m].objectNumber>=0&&ce[m].objectNumber<s&&O("/"+m+" "+ce[m].objectNumber+" 0 R");ue.publish("putTilingPatternDict"),O(">>")}},co=function(){if(Object.keys(Ne).length>0){var s;for(s in O("/ExtGState <<"),Ne)Ne.hasOwnProperty(s)&&Ne[s].objectNumber>=0&&O("/"+s+" "+Ne[s].objectNumber+" 0 R");ue.publish("putGStateDict"),O(">>")}},Pe=function(s){hn(s.resourcesOid,!0),O("<<"),O("/ProcSet [/PDF /Text /ImageB /ImageC /ImageI]"),ia(),so(),Jr(s.objectOid),co(),Si(),O(">>"),O("endobj")},aa=function(){var s=[];Hr(),ao(),Vr(),Gr(s),ue.publish("putResources"),s.forEach(Pe),Pe({resourcesOid:_n,objectOid:Number.MAX_SAFE_INTEGER}),ue.publish("postPutResources")},oa=function(){ue.publish("putAdditionalObjects");for(var s=0;s<At.length;s++){var m=At[s];hn(m.objId,!0),O(m.content),O("endobj")}ue.publish("postPutAdditionalObjects")},sa=function(s){we[s.fontName]=we[s.fontName]||{},we[s.fontName][s.fontStyle]=s.id},_i=function(s,m,j,D,Y){var it={id:"F"+(Object.keys(pe).length+1).toString(10),postScriptName:s,fontName:m,fontStyle:j,encoding:D,isStandardFont:Y||!1,metadata:{}};return ue.publish("addFont",{font:it,instance:this}),pe[it.id]=it,sa(it),it.id},uo=function(s){for(var m=0,j=wt.length;m<j;m++){var D=_i.call(this,s[m][0],s[m][1],s[m][2],wt[m][3],!0);A===!1&&(_[D]=!0);var Y=s[m][0].split("-");sa({id:D,fontName:Y[0],fontStyle:Y[1]||""})}ue.publish("addFonts",{fonts:pe,dictionary:we})},In=function(s){return s.foo=function(){try{return s.apply(this,arguments)}catch(D){var m=D.stack||"";~m.indexOf(" at ")&&(m=m.split(" at ")[1]);var j="Error in function "+m.split(`
`)[0].split("<")[0]+": "+D.message;if(!zt.console)throw new Error(j);zt.console.error(j,D),zt.alert&&alert(j)}},s.foo.bar=s,s.foo},Yr=function(s,m){var j,D,Y,it,yt,Ft,Dt,Zt,Yt;if(Y=(m=m||{}).sourceEncoding||"Unicode",yt=m.outputEncoding,(m.autoencode||yt)&&pe[ee].metadata&&pe[ee].metadata[Y]&&pe[ee].metadata[Y].encoding&&(it=pe[ee].metadata[Y].encoding,!yt&&pe[ee].encoding&&(yt=pe[ee].encoding),!yt&&it.codePages&&(yt=it.codePages[0]),typeof yt=="string"&&(yt=it[yt]),yt)){for(Dt=!1,Ft=[],j=0,D=s.length;j<D;j++)(Zt=yt[s.charCodeAt(j)])?Ft.push(String.fromCharCode(Zt)):Ft.push(s[j]),Ft[j].charCodeAt(0)>>8&&(Dt=!0);s=Ft.join("")}for(j=s.length;Dt===void 0&&j!==0;)s.charCodeAt(j-1)>>8&&(Dt=!0),j--;if(!Dt)return s;for(Ft=m.noBOM?[]:[254,255],j=0,D=s.length;j<D;j++){if((Yt=(Zt=s.charCodeAt(j))>>8)>>8)throw new Error("Character at position "+j+" of string '"+s+"' exceeds 16bits. Cannot be encoded into UCS-2 BE");Ft.push(Yt),Ft.push(Zt-(Yt<<8))}return String.fromCharCode.apply(void 0,Ft)},rn=p.__private__.pdfEscape=p.pdfEscape=function(s,m){return Yr(s,m).replace(/\\/g,"\\\\").replace(/\(/g,"\\(").replace(/\)/g,"\\)")},Pi=p.__private__.beginPage=function(s){Lt[++Ae]=[],Jt[Ae]={objId:0,contentsObjId:0,userUnit:Number(l),artBox:null,bleedBox:null,cropBox:null,trimBox:null,mediaBox:{bottomLeftX:0,bottomLeftY:0,topRightX:Number(s[0]),topRightY:Number(s[1])}},ua(Ae),at(Lt[C])},ca=function(s,m){var j,D,Y;switch(r=m||r,typeof s=="string"&&(j=S(s.toLowerCase()),Array.isArray(j)&&(D=j[0],Y=j[1])),Array.isArray(s)&&(D=s[0]*It,Y=s[1]*It),isNaN(D)&&(D=u[0],Y=u[1]),(D>14400||Y>14400)&&(me.warn("A page in a PDF can not be wider or taller than 14400 userUnit. jsPDF limits the width/height to 14400"),D=Math.min(14400,D),Y=Math.min(14400,Y)),u=[D,Y],r.substr(0,1)){case"l":Y>D&&(u=[Y,D]);break;case"p":D>Y&&(u=[Y,D])}Pi(u),ga(ji),O(Fn),Oi!==0&&O(Oi+" J"),Bi!==0&&O(Bi+" j"),ue.publish("addPage",{pageNumber:Ae})},ho=function(s){s>0&&s<=Ae&&(Lt.splice(s,1),Jt.splice(s,1),Ae--,C>Ae&&(C=Ae),this.setPage(C))},ua=function(s){s>0&&s<=Ae&&(C=s)},lo=p.__private__.getNumberOfPages=p.getNumberOfPages=function(){return Lt.length-1},ha=function(s,m,j){var D,Y=void 0;return j=j||{},s=s!==void 0?s:pe[ee].fontName,m=m!==void 0?m:pe[ee].fontStyle,D=s.toLowerCase(),we[D]!==void 0&&we[D][m]!==void 0?Y=we[D][m]:we[s]!==void 0&&we[s][m]!==void 0?Y=we[s][m]:j.disableWarning===!1&&me.warn("Unable to look up font label for font '"+s+"', '"+m+"'. Refer to getFontList() for available fonts."),Y||j.noFallback||(Y=we.times[m])==null&&(Y=we.times.normal),Y},fo=p.__private__.putInfo=function(){var s=qe(),m=function(D){return D};for(var j in x!==null&&(m=Je.encryptor(s,0)),O("<<"),O("/Producer ("+rn(m("jsPDF "+Ut.version))+")"),Ht)Ht.hasOwnProperty(j)&&Ht[j]&&O("/"+j.substr(0,1).toUpperCase()+j.substr(1)+" ("+rn(m(Ht[j]))+")");O("/CreationDate ("+rn(m(ct))+")"),O(">>"),O("endobj")},ki=p.__private__.putCatalog=function(s){var m=(s=s||{}).rootDictionaryObjId||Zn;switch(qe(),O("<<"),O("/Type /Catalog"),O("/Pages "+m+" 0 R"),kt||(kt="fullwidth"),kt){case"fullwidth":O("/OpenAction [3 0 R /FitH null]");break;case"fullheight":O("/OpenAction [3 0 R /FitV null]");break;case"fullpage":O("/OpenAction [3 0 R /Fit]");break;case"original":O("/OpenAction [3 0 R /XYZ null null 1]");break;default:var j=""+kt;j.substr(j.length-1)==="%"&&(kt=parseInt(kt)/100),typeof kt=="number"&&O("/OpenAction [3 0 R /XYZ null null "+bt(kt)+"]")}switch(te||(te="continuous"),te){case"continuous":O("/PageLayout /OneColumn");break;case"single":O("/PageLayout /SinglePage");break;case"two":case"twoleft":O("/PageLayout /TwoColumnLeft");break;case"tworight":O("/PageLayout /TwoColumnRight")}Gt&&O("/PageMode /"+Gt),ue.publish("putCatalog"),O(">>"),O("endobj")},po=p.__private__.putTrailer=function(){O("trailer"),O("<<"),O("/Size "+(J+1)),O("/Root "+J+" 0 R"),O("/Info "+(J-1)+" 0 R"),x!==null&&O("/Encrypt "+Je.oid+" 0 R"),O("/ID [ <"+ot+"> <"+ot+"> ]"),O(">>")},go=p.__private__.putHeader=function(){O("%PDF-"+B),O("%ºß¬à")},mo=p.__private__.putXRef=function(){var s="0000000000";O("xref"),O("0 "+(J+1)),O("0000000000 65535 f ");for(var m=1;m<=J;m++)typeof $[m]=="function"?O((s+$[m]()).slice(-10)+" 00000 n "):$[m]!==void 0?O((s+$[m]).slice(-10)+" 00000 n "):O("0000000000 00000 n ")},$n=p.__private__.buildDocument=function(){Wt(),at(et),ue.publish("buildDocument"),go(),_r(),oa(),aa(),x!==null&&oo(),fo(),ki();var s=Q;return mo(),po(),O("startxref"),O(""+s),O("%%EOF"),at(Lt[C]),et.join(`
`)},Xr=p.__private__.getBlob=function(s){return new Blob([Mt(s)],{type:"application/pdf"})},Kr=p.output=p.__private__.output=In(function(s,m){switch(typeof(m=m||{})=="string"?m={filename:m}:m.filename=m.filename||"generated.pdf",s){case void 0:return $n();case"save":p.save(m.filename);break;case"arraybuffer":return Mt($n());case"blob":return Xr($n());case"bloburi":case"bloburl":if(zt.URL!==void 0&&typeof zt.URL.createObjectURL=="function")return zt.URL&&zt.URL.createObjectURL(Xr($n()))||void 0;me.warn("bloburl is not supported by your system, because URL.createObjectURL is not supported by your browser.");break;case"datauristring":case"dataurlstring":var j="",D=$n();try{j=ts(D)}catch{j=ts(unescape(encodeURIComponent(D)))}return"data:application/pdf;filename="+m.filename+";base64,"+j;case"pdfobjectnewwindow":if(Object.prototype.toString.call(zt)==="[object Window]"){var Y="https://cdnjs.cloudflare.com/ajax/libs/pdfobject/2.1.1/pdfobject.min.js",it=' integrity="sha512-4ze/a9/4jqu+tX9dfOqJYSvyYd5M6qum/3HpCLr+/Jqf0whc37VUbkpNGHR7/8pSnCFw47T1fmIpwBV7UySh3g==" crossorigin="anonymous"';m.pdfObjectUrl&&(Y=m.pdfObjectUrl,it="");var yt='<html><style>html, body { padding: 0; margin: 0; } iframe { width: 100%; height: 100%; border: 0;}  </style><body><script src="'+Y+'"'+it+'><\/script><script >PDFObject.embed("'+this.output("dataurlstring")+'", '+JSON.stringify(m)+");<\/script></body></html>",Ft=zt.open();return Ft!==null&&Ft.document.write(yt),Ft}throw new Error("The option pdfobjectnewwindow just works in a browser-environment.");case"pdfjsnewwindow":if(Object.prototype.toString.call(zt)==="[object Window]"){var Dt='<html><style>html, body { padding: 0; margin: 0; } iframe { width: 100%; height: 100%; border: 0;}  </style><body><iframe id="pdfViewer" src="'+(m.pdfJsUrl||"examples/PDF.js/web/viewer.html")+"?file=&downloadName="+m.filename+'" width="500px" height="400px" /></body></html>',Zt=zt.open();if(Zt!==null){Zt.document.write(Dt);var Yt=this;Zt.document.documentElement.querySelector("#pdfViewer").onload=function(){Zt.document.title=m.filename,Zt.document.documentElement.querySelector("#pdfViewer").contentWindow.PDFViewerApplication.open(Yt.output("bloburl"))}}return Zt}throw new Error("The option pdfjsnewwindow just works in a browser-environment.");case"dataurlnewwindow":if(Object.prototype.toString.call(zt)!=="[object Window]")throw new Error("The option dataurlnewwindow just works in a browser-environment.");var re='<html><style>html, body { padding: 0; margin: 0; } iframe { width: 100%; height: 100%; border: 0;}  </style><body><iframe src="'+this.output("datauristring",m)+'"></iframe></body></html>',Le=zt.open();if(Le!==null&&(Le.document.write(re),Le.document.title=m.filename),Le||typeof safari>"u")return Le;break;case"datauri":case"dataurl":return zt.document.location.href=this.output("datauristring",m);default:return null}}),la=function(s){return Array.isArray(Sr)===!0&&Sr.indexOf(s)>-1};switch(a){case"pt":It=1;break;case"mm":It=72/25.4;break;case"cm":It=72/2.54;break;case"in":It=72;break;case"px":It=la("px_scaling")==1?.75:96/72;break;case"pc":case"em":It=12;break;case"ex":It=6;break;default:if(typeof a!="number")throw new Error("Invalid unit: "+a);It=a}var Je=null;Et(),tt();var vo=function(s){return x!==null?Je.encryptor(s,0):function(m){return m}},fa=p.__private__.getPageInfo=p.getPageInfo=function(s){if(isNaN(s)||s%1!=0)throw new Error("Invalid argument passed to jsPDF.getPageInfo");return{objId:Jt[s].objId,pageNumber:s,pageContext:Jt[s]}},Vt=p.__private__.getPageInfoByObjId=function(s){if(isNaN(s)||s%1!=0)throw new Error("Invalid argument passed to jsPDF.getPageInfoByObjId");for(var m in Jt)if(Jt[m].objId===s)break;return fa(m)},bo=p.__private__.getCurrentPageInfo=p.getCurrentPageInfo=function(){return{objId:Jt[C].objId,pageNumber:C,pageContext:Jt[C]}};p.addPage=function(){return ca.apply(this,arguments),this},p.setPage=function(){return ua.apply(this,arguments),at.call(this,Lt[C]),this},p.insertPage=function(s){return this.addPage(),this.movePage(C,s),this},p.movePage=function(s,m){var j,D;if(s>m){j=Lt[s],D=Jt[s];for(var Y=s;Y>m;Y--)Lt[Y]=Lt[Y-1],Jt[Y]=Jt[Y-1];Lt[m]=j,Jt[m]=D,this.setPage(m)}else if(s<m){j=Lt[s],D=Jt[s];for(var it=s;it<m;it++)Lt[it]=Lt[it+1],Jt[it]=Jt[it+1];Lt[m]=j,Jt[m]=D,this.setPage(m)}return this},p.deletePage=function(){return ho.apply(this,arguments),this},p.__private__.text=p.text=function(s,m,j,D,Y){var it,yt,Ft,Dt,Zt,Yt,re,Le,xe,Be=(D=D||{}).scope||this;if(typeof s=="number"&&typeof m=="number"&&(typeof j=="string"||Array.isArray(j))){var Ye=j;j=m,m=s,s=Ye}if(arguments[3]instanceof Tt?(G("The transform parameter of text() with a Matrix value"),xe=Y):(Ft=arguments[4],Dt=arguments[5],ve(re=arguments[3])==="object"&&re!==null||(typeof Ft=="string"&&(Dt=Ft,Ft=null),typeof re=="string"&&(Dt=re,re=null),typeof re=="number"&&(Ft=re,re=null),D={flags:re,angle:Ft,align:Dt})),isNaN(m)||isNaN(j)||s==null)throw new Error("Invalid arguments passed to jsPDF.text");if(s.length===0)return Be;var Re="",jn=!1,ln=typeof D.lineHeightFactor=="number"?D.lineHeightFactor:kr,Vn=Be.internal.scaleFactor;function La(be){return be=be.split("	").join(Array(D.TabLen||9).join(" ")),rn(be,re)}function Ri(be){for(var ye,Ie=be.concat(),De=[],rr=Ie.length;rr--;)typeof(ye=Ie.shift())=="string"?De.push(ye):Array.isArray(be)&&(ye.length===1||ye[1]===void 0&&ye[2]===void 0)?De.push(ye[0]):De.push([ye[0],ye[1],ye[2]]);return De}function Ti(be,ye){var Ie;if(typeof be=="string")Ie=ye(be)[0];else if(Array.isArray(be)){for(var De,rr,Yi=be.concat(),li=[],_a=Yi.length;_a--;)typeof(De=Yi.shift())=="string"?li.push(ye(De)[0]):Array.isArray(De)&&typeof De[0]=="string"&&(rr=ye(De[0],De[1],De[2]),li.push([rr[0],rr[1],rr[2]]));Ie=li}return Ie}var ni=!1,zi=!0;if(typeof s=="string")ni=!0;else if(Array.isArray(s)){var Ui=s.concat();yt=[];for(var ri,We=Ui.length;We--;)(typeof(ri=Ui.shift())!="string"||Array.isArray(ri)&&typeof ri[0]!="string")&&(zi=!1);ni=zi}if(ni===!1)throw new Error('Type of text must be string or Array. "'+s+'" is not recognized.');typeof s=="string"&&(s=s.match(/[\r?\n]/)?s.split(/\r\n|\r|\n/g):[s]);var ii=xt/Be.internal.scaleFactor,ai=ii*(ln-1);switch(D.baseline){case"bottom":j-=ai;break;case"top":j+=ii-ai;break;case"hanging":j+=ii-2*ai;break;case"middle":j+=ii/2-ai}if((Yt=D.maxWidth||0)>0&&(typeof s=="string"?s=Be.splitTextToSize(s,Yt):Object.prototype.toString.call(s)==="[object Array]"&&(s=s.reduce(function(be,ye){return be.concat(Be.splitTextToSize(ye,Yt))},[]))),it={text:s,x:m,y:j,options:D,mutex:{pdfEscape:rn,activeFontKey:ee,fonts:pe,activeFontSize:xt}},ue.publish("preProcessText",it),s=it.text,Ft=(D=it.options).angle,!(xe instanceof Tt)&&Ft&&typeof Ft=="number"){Ft*=Math.PI/180,D.rotationDirection===0&&(Ft=-Ft),Z===M.ADVANCED&&(Ft=-Ft);var oi=Math.cos(Ft),Hi=Math.sin(Ft);xe=new Tt(oi,Hi,-Hi,oi,0,0)}else Ft&&Ft instanceof Tt&&(xe=Ft);Z!==M.ADVANCED||xe||(xe=Tn),(Zt=D.charSpace||Qr)!==void 0&&(Re+=rt(I(Zt))+` Tc
`,this.setCharSpace(this.getCharSpace()||0)),(Le=D.horizontalScale)!==void 0&&(Re+=rt(100*Le)+` Tz
`),D.lang;var an=-1,ko=D.renderingMode!==void 0?D.renderingMode:D.stroke,Wi=Be.internal.getCurrentPageInfo().pageContext;switch(ko){case 0:case!1:case"fill":an=0;break;case 1:case!0:case"stroke":an=1;break;case 2:case"fillThenStroke":an=2;break;case 3:case"invisible":an=3;break;case 4:case"fillAndAddForClipping":an=4;break;case 5:case"strokeAndAddPathForClipping":an=5;break;case 6:case"fillThenStrokeAndAddToPathForClipping":an=6;break;case 7:case"addToPathForClipping":an=7}var Na=Wi.usedRenderingMode!==void 0?Wi.usedRenderingMode:-1;an!==-1?Re+=an+` Tr
`:Na!==-1&&(Re+=`0 Tr
`),an!==-1&&(Wi.usedRenderingMode=an),Dt=D.align||"left";var mn,si=xt*ln,Aa=Be.internal.pageSize.getWidth(),xa=pe[ee];Zt=D.charSpace||Qr,Yt=D.maxWidth||0,re=Object.assign({autoencode:!0,noBOM:!0},D.flags);var hr=[],jr=function(be){return Be.getStringUnitWidth(be,{font:xa,charSpace:Zt,fontSize:xt,doKerning:!1})*xt/Vn};if(Object.prototype.toString.call(s)==="[object Array]"){var on;yt=Ri(s),Dt!=="left"&&(mn=yt.map(jr));var Qe,lr=0;if(Dt==="right"){m-=mn[0],s=[],We=yt.length;for(var tr=0;tr<We;tr++)tr===0?(Qe=Wn(m),on=Qn(j)):(Qe=I(lr-mn[tr]),on=-si),s.push([yt[tr],Qe,on]),lr=mn[tr]}else if(Dt==="center"){m-=mn[0]/2,s=[],We=yt.length;for(var er=0;er<We;er++)er===0?(Qe=Wn(m),on=Qn(j)):(Qe=I((lr-mn[er])/2),on=-si),s.push([yt[er],Qe,on]),lr=mn[er]}else if(Dt==="left"){s=[],We=yt.length;for(var ci=0;ci<We;ci++)s.push(yt[ci])}else if(Dt==="justify"&&xa.encoding==="Identity-H"){s=[],We=yt.length,Yt=Yt!==0?Yt:Aa;for(var nr=0,ke=0;ke<We;ke++)if(on=ke===0?Qn(j):-si,Qe=ke===0?Wn(m):nr,ke<We-1){var Vi=I((Yt-mn[ke])/(yt[ke].split(" ").length-1)),tn=yt[ke].split(" ");s.push([tn[0]+" ",Qe,on]),nr=0;for(var vn=1;vn<tn.length;vn++){var ui=(jr(tn[vn-1]+" "+tn[vn])-jr(tn[vn]))*Vn+Vi;vn==tn.length-1?s.push([tn[vn],ui,0]):s.push([tn[vn]+" ",ui,0]),nr-=ui}}else s.push([yt[ke],Qe,on]);s.push(["",nr,0])}else{if(Dt!=="justify")throw new Error('Unrecognized alignment option, use "left", "center", "right" or "justify".');for(s=[],We=yt.length,Yt=Yt!==0?Yt:Aa,ke=0;ke<We;ke++)on=ke===0?Qn(j):-si,Qe=ke===0?Wn(m):0,ke<We-1?hr.push(rt(I((Yt-mn[ke])/(yt[ke].split(" ").length-1)))):hr.push(0),s.push([yt[ke],Qe,on])}}var Sa=typeof D.R2L=="boolean"?D.R2L:qt;Sa===!0&&(s=Ti(s,function(be,ye,Ie){return[be.split("").reverse().join(""),ye,Ie]})),it={text:s,x:m,y:j,options:D,mutex:{pdfEscape:rn,activeFontKey:ee,fonts:pe,activeFontSize:xt}},ue.publish("postProcessText",it),s=it.text,jn=it.mutex.isHex||!1;var Gi=pe[ee].encoding;Gi!=="WinAnsiEncoding"&&Gi!=="StandardEncoding"||(s=Ti(s,function(be,ye,Ie){return[La(be),ye,Ie]})),yt=Ri(s),s=[];for(var Cr,Or,fr,Br=0,hi=1,Mr=Array.isArray(yt[0])?hi:Br,dr="",Ji=function(be,ye,Ie){var De="";return Ie instanceof Tt?(Ie=typeof D.angle=="number"?Sn(Ie,new Tt(1,0,0,1,be,ye)):Sn(new Tt(1,0,0,1,be,ye),Ie),Z===M.ADVANCED&&(Ie=Sn(new Tt(1,0,0,-1,0,0),Ie)),De=Ie.join(" ")+` Tm
`):De=rt(be)+" "+rt(ye)+` Td
`,De},bn=0;bn<yt.length;bn++){switch(dr="",Mr){case hi:fr=(jn?"<":"(")+yt[bn][0]+(jn?">":")"),Cr=parseFloat(yt[bn][1]),Or=parseFloat(yt[bn][2]);break;case Br:fr=(jn?"<":"(")+yt[bn]+(jn?">":")"),Cr=Wn(m),Or=Qn(j)}hr!==void 0&&hr[bn]!==void 0&&(dr=hr[bn]+` Tw
`),bn===0?s.push(dr+Ji(Cr,Or,xe)+fr):Mr===Br?s.push(dr+fr):Mr===hi&&s.push(dr+Ji(Cr,Or,xe)+fr)}s=Mr===Br?s.join(` Tj
T* `):s.join(` Tj
`),s+=` Tj
`;var yn=`BT
/`;return yn+=ee+" "+xt+` Tf
`,yn+=rt(xt*ln)+` TL
`,yn+=Ir+`
`,yn+=Re,yn+=s,O(yn+="ET"),_[ee]=!0,Be};var yo=p.__private__.clip=p.clip=function(s){return O(s==="evenodd"?"W*":"W"),this};p.clipEvenOdd=function(){return yo("evenodd")},p.__private__.discardPath=p.discardPath=function(){return O("n"),this};var Hn=p.__private__.isValidStyle=function(s){var m=!1;return[void 0,null,"S","D","F","DF","FD","f","f*","B","B*","n"].indexOf(s)!==-1&&(m=!0),m};p.__private__.setDefaultPathOperation=p.setDefaultPathOperation=function(s){return Hn(s)&&(v=s),this};var da=p.__private__.getStyle=p.getStyle=function(s){var m=v;switch(s){case"D":case"S":m="S";break;case"F":m="f";break;case"FD":case"DF":m="B";break;case"f":case"f*":case"B":case"B*":m=s}return m},pa=p.close=function(){return O("h"),this};p.stroke=function(){return O("S"),this},p.fill=function(s){return Zr("f",s),this},p.fillEvenOdd=function(s){return Zr("f*",s),this},p.fillStroke=function(s){return Zr("B",s),this},p.fillStrokeEvenOdd=function(s){return Zr("B*",s),this};var Zr=function(s,m){ve(m)==="object"?Lo(m,s):O(s)},Ii=function(s){s===null||Z===M.ADVANCED&&s===void 0||(s=da(s),O(s))};function wo(s,m,j,D,Y){var it=new wi(m||this.boundingBox,j||this.xStep,D||this.yStep,this.gState,Y||this.matrix);it.stream=this.stream;var yt=s+"$$"+this.cloneIndex+++"$$";return nn(yt,it),it}var Lo=function(s,m){var j=xr[s.key],D=ce[j];if(D instanceof Dr)O("q"),O(No(m)),D.gState&&p.setGState(D.gState),O(s.matrix.toString()+" cm"),O("/"+j+" sh"),O("Q");else if(D instanceof wi){var Y=new Tt(1,0,0,-1,0,ur());s.matrix&&(Y=Y.multiply(s.matrix||Tn),j=wo.call(D,s.key,s.boundingBox,s.xStep,s.yStep,Y).id),O("q"),O("/Pattern cs"),O("/"+j+" scn"),D.gState&&p.setGState(D.gState),O(m),O("Q")}},No=function(s){switch(s){case"f":case"F":return"W n";case"f*":return"W* n";case"B":return"W S";case"B*":return"W* S";case"S":return"W S";case"n":return"W n"}},Fi=p.moveTo=function(s,m){return O(rt(I(s))+" "+rt(R(m))+" m"),this},Pr=p.lineTo=function(s,m){return O(rt(I(s))+" "+rt(R(m))+" l"),this},sr=p.curveTo=function(s,m,j,D,Y,it){return O([rt(I(s)),rt(R(m)),rt(I(j)),rt(R(D)),rt(I(Y)),rt(R(it)),"c"].join(" ")),this};p.__private__.line=p.line=function(s,m,j,D,Y){if(isNaN(s)||isNaN(m)||isNaN(j)||isNaN(D)||!Hn(Y))throw new Error("Invalid arguments passed to jsPDF.line");return Z===M.COMPAT?this.lines([[j-s,D-m]],s,m,[1,1],Y||"S"):this.lines([[j-s,D-m]],s,m,[1,1]).stroke()},p.__private__.lines=p.lines=function(s,m,j,D,Y,it){var yt,Ft,Dt,Zt,Yt,re,Le,xe,Be,Ye,Re,jn;if(typeof s=="number"&&(jn=j,j=m,m=s,s=jn),D=D||[1,1],it=it||!1,isNaN(m)||isNaN(j)||!Array.isArray(s)||!Array.isArray(D)||!Hn(Y)||typeof it!="boolean")throw new Error("Invalid arguments passed to jsPDF.lines");for(Fi(m,j),yt=D[0],Ft=D[1],Zt=s.length,Ye=m,Re=j,Dt=0;Dt<Zt;Dt++)(Yt=s[Dt]).length===2?(Ye=Yt[0]*yt+Ye,Re=Yt[1]*Ft+Re,Pr(Ye,Re)):(re=Yt[0]*yt+Ye,Le=Yt[1]*Ft+Re,xe=Yt[2]*yt+Ye,Be=Yt[3]*Ft+Re,Ye=Yt[4]*yt+Ye,Re=Yt[5]*Ft+Re,sr(re,Le,xe,Be,Ye,Re));return it&&pa(),Ii(Y),this},p.path=function(s){for(var m=0;m<s.length;m++){var j=s[m],D=j.c;switch(j.op){case"m":Fi(D[0],D[1]);break;case"l":Pr(D[0],D[1]);break;case"c":sr.apply(this,D);break;case"h":pa()}}return this},p.__private__.rect=p.rect=function(s,m,j,D,Y){if(isNaN(s)||isNaN(m)||isNaN(j)||isNaN(D)||!Hn(Y))throw new Error("Invalid arguments passed to jsPDF.rect");return Z===M.COMPAT&&(D=-D),O([rt(I(s)),rt(R(m)),rt(I(j)),rt(I(D)),"re"].join(" ")),Ii(Y),this},p.__private__.triangle=p.triangle=function(s,m,j,D,Y,it,yt){if(isNaN(s)||isNaN(m)||isNaN(j)||isNaN(D)||isNaN(Y)||isNaN(it)||!Hn(yt))throw new Error("Invalid arguments passed to jsPDF.triangle");return this.lines([[j-s,D-m],[Y-j,it-D],[s-Y,m-it]],s,m,[1,1],yt,!0),this},p.__private__.roundedRect=p.roundedRect=function(s,m,j,D,Y,it,yt){if(isNaN(s)||isNaN(m)||isNaN(j)||isNaN(D)||isNaN(Y)||isNaN(it)||!Hn(yt))throw new Error("Invalid arguments passed to jsPDF.roundedRect");var Ft=4/3*(Math.SQRT2-1);return Y=Math.min(Y,.5*j),it=Math.min(it,.5*D),this.lines([[j-2*Y,0],[Y*Ft,0,Y,it-it*Ft,Y,it],[0,D-2*it],[0,it*Ft,-Y*Ft,it,-Y,it],[2*Y-j,0],[-Y*Ft,0,-Y,-it*Ft,-Y,-it],[0,2*it-D],[0,-it*Ft,Y*Ft,-it,Y,-it]],s+Y,m,[1,1],yt,!0),this},p.__private__.ellipse=p.ellipse=function(s,m,j,D,Y){if(isNaN(s)||isNaN(m)||isNaN(j)||isNaN(D)||!Hn(Y))throw new Error("Invalid arguments passed to jsPDF.ellipse");var it=4/3*(Math.SQRT2-1)*j,yt=4/3*(Math.SQRT2-1)*D;return Fi(s+j,m),sr(s+j,m-yt,s+it,m-D,s,m-D),sr(s-it,m-D,s-j,m-yt,s-j,m),sr(s-j,m+yt,s-it,m+D,s,m+D),sr(s+it,m+D,s+j,m+yt,s+j,m),Ii(Y),this},p.__private__.circle=p.circle=function(s,m,j,D){if(isNaN(s)||isNaN(m)||isNaN(j)||!Hn(D))throw new Error("Invalid arguments passed to jsPDF.circle");return this.ellipse(s,m,j,j,D)},p.setFont=function(s,m,j){return j&&(m=Nt(m,j)),ee=ha(s,m,{disableWarning:!1}),this};var Ao=p.__private__.getFont=p.getFont=function(){return pe[ha.apply(p,arguments)]};p.__private__.getFontList=p.getFontList=function(){var s,m,j={};for(s in we)if(we.hasOwnProperty(s))for(m in j[s]=[],we[s])we[s].hasOwnProperty(m)&&j[s].push(m);return j},p.addFont=function(s,m,j,D,Y){var it=["StandardEncoding","MacRomanEncoding","Identity-H","WinAnsiEncoding"];return arguments[3]&&it.indexOf(arguments[3])!==-1?Y=arguments[3]:arguments[3]&&it.indexOf(arguments[3])==-1&&(j=Nt(j,D)),Y=Y||"Identity-H",_i.call(this,s,m,j,Y)};var kr,ji=i.lineWidth||.200025,$r=p.__private__.getLineWidth=p.getLineWidth=function(){return ji},ga=p.__private__.setLineWidth=p.setLineWidth=function(s){return ji=s,O(rt(I(s))+" w"),this};p.__private__.setLineDash=Ut.API.setLineDash=Ut.API.setLineDashPattern=function(s,m){if(s=s||[],m=m||0,isNaN(m)||!Array.isArray(s))throw new Error("Invalid arguments passed to jsPDF.setLineDash");return s=s.map(function(j){return rt(I(j))}).join(" "),m=rt(I(m)),O("["+s+"] "+m+" d"),this};var ma=p.__private__.getLineHeight=p.getLineHeight=function(){return xt*kr};p.__private__.getLineHeight=p.getLineHeight=function(){return xt*kr};var va=p.__private__.setLineHeightFactor=p.setLineHeightFactor=function(s){return typeof(s=s||1.15)=="number"&&(kr=s),this},ba=p.__private__.getLineHeightFactor=p.getLineHeightFactor=function(){return kr};va(i.lineHeight);var Wn=p.__private__.getHorizontalCoordinate=function(s){return I(s)},Qn=p.__private__.getVerticalCoordinate=function(s){return Z===M.ADVANCED?s:Jt[C].mediaBox.topRightY-Jt[C].mediaBox.bottomLeftY-I(s)},xo=p.__private__.getHorizontalCoordinateString=p.getHorizontalCoordinateString=function(s){return rt(Wn(s))},cr=p.__private__.getVerticalCoordinateString=p.getVerticalCoordinateString=function(s){return rt(Qn(s))},Fn=i.strokeColor||"0 G";p.__private__.getStrokeColor=p.getDrawColor=function(){return Pn(Fn)},p.__private__.setStrokeColor=p.setDrawColor=function(s,m,j,D){return Fn=kn({ch1:s,ch2:m,ch3:j,ch4:D,pdfColorType:"draw",precision:2}),O(Fn),this};var Ci=i.fillColor||"0 g";p.__private__.getFillColor=p.getFillColor=function(){return Pn(Ci)},p.__private__.setFillColor=p.setFillColor=function(s,m,j,D){return Ci=kn({ch1:s,ch2:m,ch3:j,ch4:D,pdfColorType:"fill",precision:2}),O(Ci),this};var Ir=i.textColor||"0 g",So=p.__private__.getTextColor=p.getTextColor=function(){return Pn(Ir)};p.__private__.setTextColor=p.setTextColor=function(s,m,j,D){return Ir=kn({ch1:s,ch2:m,ch3:j,ch4:D,pdfColorType:"text",precision:3}),this};var Qr=i.charSpace,_o=p.__private__.getCharSpace=p.getCharSpace=function(){return parseFloat(Qr||0)};p.__private__.setCharSpace=p.setCharSpace=function(s){if(isNaN(s))throw new Error("Invalid argument passed to jsPDF.setCharSpace");return Qr=s,this};var Oi=0;p.CapJoinStyles={0:0,butt:0,but:0,miter:0,1:1,round:1,rounded:1,circle:1,2:2,projecting:2,project:2,square:2,bevel:2},p.__private__.setLineCap=p.setLineCap=function(s){var m=p.CapJoinStyles[s];if(m===void 0)throw new Error("Line cap style of '"+s+"' is not recognized. See or extend .CapJoinStyles property for valid styles");return Oi=m,O(m+" J"),this};var Bi=0;p.__private__.setLineJoin=p.setLineJoin=function(s){var m=p.CapJoinStyles[s];if(m===void 0)throw new Error("Line join style of '"+s+"' is not recognized. See or extend .CapJoinStyles property for valid styles");return Bi=m,O(m+" j"),this},p.__private__.setLineMiterLimit=p.__private__.setMiterLimit=p.setLineMiterLimit=p.setMiterLimit=function(s){if(s=s||0,isNaN(s))throw new Error("Invalid argument passed to jsPDF.setLineMiterLimit");return O(rt(I(s))+" M"),this},p.GState=to,p.setGState=function(s){(s=typeof s=="string"?Ne[xn[s]]:ya(null,s)).equals(Kn)||(O("/"+s.id+" gs"),Kn=s)};var ya=function(s,m){if(!s||!xn[s]){var j=!1;for(var D in Ne)if(Ne.hasOwnProperty(D)&&Ne[D].equals(m)){j=!0;break}if(j)m=Ne[D];else{var Y="GS"+(Object.keys(Ne).length+1).toString(10);Ne[Y]=m,m.id=Y}return s&&(xn[s]=m.id),ue.publish("addGState",m),m}};p.addGState=function(s,m){return ya(s,m),this},p.saveGraphicsState=function(){return O("q"),qn.push({key:ee,size:xt,color:Ir}),this},p.restoreGraphicsState=function(){O("Q");var s=qn.pop();return ee=s.key,xt=s.size,Ir=s.color,Kn=null,this},p.setCurrentTransformationMatrix=function(s){return O(s.toString()+" cm"),this},p.comment=function(s){return O("#"+s),this};var ti=function(s,m){var j=s||0;Object.defineProperty(this,"x",{enumerable:!0,get:function(){return j},set:function(it){isNaN(it)||(j=parseFloat(it))}});var D=m||0;Object.defineProperty(this,"y",{enumerable:!0,get:function(){return D},set:function(it){isNaN(it)||(D=parseFloat(it))}});var Y="pt";return Object.defineProperty(this,"type",{enumerable:!0,get:function(){return Y},set:function(it){Y=it.toString()}}),this},Mi=function(s,m,j,D){ti.call(this,s,m),this.type="rect";var Y=j||0;Object.defineProperty(this,"w",{enumerable:!0,get:function(){return Y},set:function(yt){isNaN(yt)||(Y=parseFloat(yt))}});var it=D||0;return Object.defineProperty(this,"h",{enumerable:!0,get:function(){return it},set:function(yt){isNaN(yt)||(it=parseFloat(yt))}}),this},Ei=function(){this.page=Ae,this.currentPage=C,this.pages=Lt.slice(0),this.pagesContext=Jt.slice(0),this.x=Ge,this.y=oe,this.matrix=An,this.width=Fr(C),this.height=ur(C),this.outputDestination=Ct,this.id="",this.objectNumber=-1};Ei.prototype.restore=function(){Ae=this.page,C=this.currentPage,Jt=this.pagesContext,Lt=this.pages,Ge=this.x,oe=this.y,An=this.matrix,qi(C,this.width),Di(C,this.height),Ct=this.outputDestination};var wa=function(s,m,j,D,Y){Rn.push(new Ei),Ae=C=0,Lt=[],Ge=s,oe=m,An=Y,Pi([j,D])},Po=function(s){if(Dn[s])Rn.pop().restore();else{var m=new Ei,j="Xo"+(Object.keys(He).length+1).toString(10);m.id=j,Dn[s]=j,He[j]=m,ue.publish("addFormObject",m),Rn.pop().restore()}};for(var ei in p.beginFormObject=function(s,m,j,D,Y){return wa(s,m,j,D,Y),this},p.endFormObject=function(s){return Po(s),this},p.doFormObject=function(s,m){var j=He[Dn[s]];return O("q"),O(m.toString()+" cm"),O("/"+j.id+" Do"),O("Q"),this},p.getFormObject=function(s){var m=He[Dn[s]];return{x:m.x,y:m.y,width:m.width,height:m.height,matrix:m.matrix}},p.save=function(s,m){return s=s||"generated.pdf",(m=m||{}).returnPromise=m.returnPromise||!1,m.returnPromise===!1?(qr(Xr($n()),s),typeof qr.unload=="function"&&zt.setTimeout&&setTimeout(qr.unload,911),this):new Promise(function(j,D){try{var Y=qr(Xr($n()),s);typeof qr.unload=="function"&&zt.setTimeout&&setTimeout(qr.unload,911),j(Y)}catch(it){D(it.message)}})},Ut.API)Ut.API.hasOwnProperty(ei)&&(ei==="events"&&Ut.API.events.length?function(s,m){var j,D,Y;for(Y=m.length-1;Y!==-1;Y--)j=m[Y][0],D=m[Y][1],s.subscribe.apply(s,[j].concat(typeof D=="function"?[D]:D))}(ue,Ut.API.events):p[ei]=Ut.API[ei]);var Fr=p.getPageWidth=function(s){return(Jt[s=s||C].mediaBox.topRightX-Jt[s].mediaBox.bottomLeftX)/It},qi=p.setPageWidth=function(s,m){Jt[s].mediaBox.topRightX=m*It+Jt[s].mediaBox.bottomLeftX},ur=p.getPageHeight=function(s){return(Jt[s=s||C].mediaBox.topRightY-Jt[s].mediaBox.bottomLeftY)/It},Di=p.setPageHeight=function(s,m){Jt[s].mediaBox.topRightY=m*It+Jt[s].mediaBox.bottomLeftY};return p.internal={pdfEscape:rn,getStyle:da,getFont:Ao,getFontSize:Pt,getCharSpace:_o,getTextColor:So,getLineHeight:ma,getLineHeightFactor:ba,getLineWidth:$r,write:Kt,getHorizontalCoordinate:Wn,getVerticalCoordinate:Qn,getCoordinateString:xo,getVerticalCoordinateString:cr,collections:{},newObject:qe,newAdditionalObject:zr,newObjectDeferred:Oe,newObjectDeferredBegin:hn,getFilters:zn,putStream:gn,events:ue,scaleFactor:It,pageSize:{getWidth:function(){return Fr(C)},setWidth:function(s){qi(C,s)},getHeight:function(){return ur(C)},setHeight:function(s){Di(C,s)}},encryptionOptions:x,encryption:Je,getEncryptor:vo,output:Kr,getNumberOfPages:lo,pages:Lt,out:O,f2:bt,f3:k,getPageInfo:fa,getPageInfoByObjId:Vt,getCurrentPageInfo:bo,getPDFVersion:F,Point:ti,Rectangle:Mi,Matrix:Tt,hasHotfix:la},Object.defineProperty(p.internal.pageSize,"width",{get:function(){return Fr(C)},set:function(s){qi(C,s)},enumerable:!0,configurable:!0}),Object.defineProperty(p.internal.pageSize,"height",{get:function(){return ur(C)},set:function(s){Di(C,s)},enumerable:!0,configurable:!0}),uo.call(p,wt),ee="F1",ca(u,r),ue.publish("initialized"),p}bi.prototype.lsbFirstWord=function(i){return String.fromCharCode(i>>0&255,i>>8&255,i>>16&255,i>>24&255)},bi.prototype.toHexString=function(i){return i.split("").map(function(e){return("0"+(255&e.charCodeAt(0)).toString(16)).slice(-2)}).join("")},bi.prototype.hexToBytes=function(i){for(var e=[],r=0;r<i.length;r+=2)e.push(String.fromCharCode(parseInt(i.substr(r,2),16)));return e.join("")},bi.prototype.processOwnerPassword=function(i,e){return ns(es(e).substr(0,5),i)},bi.prototype.encryptor=function(i,e){var r=es(this.encryptionKey+String.fromCharCode(255&i,i>>8&255,i>>16&255,255&e,e>>8&255)).substr(0,10);return function(a){return ns(r,a)}},to.prototype.equals=function(i){var e,r="id,objectNumber,equals";if(!i||ve(i)!==ve(this))return!1;var a=0;for(e in this)if(!(r.indexOf(e)>=0)){if(this.hasOwnProperty(e)&&!i.hasOwnProperty(e)||this[e]!==i[e])return!1;a++}for(e in i)i.hasOwnProperty(e)&&r.indexOf(e)<0&&a--;return a===0},Ut.API={events:[]},Ut.version="3.0.0";var _e=Ut.API,os=1,Tr=function(i){return i.replace(/\\/g,"\\\\").replace(/\(/g,"\\(").replace(/\)/g,"\\)")},mi=function(i){return i.replace(/\\\\/g,"\\").replace(/\\\(/g,"(").replace(/\\\)/g,")")},Xt=function(i){return i.toFixed(2)},Lr=function(i){return i.toFixed(5)};_e.__acroform__={};var un=function(i,e){i.prototype=Object.create(e.prototype),i.prototype.constructor=i},Rs=function(i){return i*os},Jn=function(i){var e=new nc,r=Bt.internal.getHeight(i)||0,a=Bt.internal.getWidth(i)||0;return e.BBox=[0,0,Number(Xt(a)),Number(Xt(r))],e},$c=_e.__acroform__.setBit=function(i,e){if(i=i||0,e=e||0,isNaN(i)||isNaN(e))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.setBit");return i|=1<<e},Qc=_e.__acroform__.clearBit=function(i,e){if(i=i||0,e=e||0,isNaN(i)||isNaN(e))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.clearBit");return i&=~(1<<e)},tu=_e.__acroform__.getBit=function(i,e){if(isNaN(i)||isNaN(e))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.getBit");return i&1<<e?1:0},Fe=_e.__acroform__.getBitForPdf=function(i,e){if(isNaN(i)||isNaN(e))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.getBitForPdf");return tu(i,e-1)},je=_e.__acroform__.setBitForPdf=function(i,e){if(isNaN(i)||isNaN(e))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.setBitForPdf");return $c(i,e-1)},Ce=_e.__acroform__.clearBitForPdf=function(i,e){if(isNaN(i)||isNaN(e))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.clearBitForPdf");return Qc(i,e-1)},eu=_e.__acroform__.calculateCoordinates=function(i,e){var r=e.internal.getHorizontalCoordinate,a=e.internal.getVerticalCoordinate,u=i[0],o=i[1],h=i[2],l=i[3],f={};return f.lowerLeft_X=r(u)||0,f.lowerLeft_Y=a(o+l)||0,f.upperRight_X=r(u+h)||0,f.upperRight_Y=a(o)||0,[Number(Xt(f.lowerLeft_X)),Number(Xt(f.lowerLeft_Y)),Number(Xt(f.upperRight_X)),Number(Xt(f.upperRight_Y))]},nu=function(i){if(i.appearanceStreamContent)return i.appearanceStreamContent;if(i.V||i.DV){var e=[],r=i._V||i.DV,a=rs(i,r),u=i.scope.internal.getFont(i.fontName,i.fontStyle).id;e.push("/Tx BMC"),e.push("q"),e.push("BT"),e.push(i.scope.__private__.encodeColorString(i.color)),e.push("/"+u+" "+Xt(a.fontSize)+" Tf"),e.push("1 0 0 1 0 0 Tm"),e.push(a.text),e.push("ET"),e.push("Q"),e.push("EMC");var o=Jn(i);return o.scope=i.scope,o.stream=e.join(`
`),o}},rs=function(i,e){var r=i.fontSize===0?i.maxFontSize:i.fontSize,a={text:"",fontSize:""},u=(e=(e=e.substr(0,1)=="("?e.substr(1):e).substr(e.length-1)==")"?e.substr(0,e.length-1):e).split(" ");u=i.multiline?u.map(function(k){return k.split(`
`)}):u.map(function(k){return[k]});var o=r,h=Bt.internal.getHeight(i)||0;h=h<0?-h:h;var l=Bt.internal.getWidth(i)||0;l=l<0?-l:l;var f=function(k,I,H){if(k+1<u.length){var R=I+" "+u[k+1][0];return Ja(R,i,H).width<=l-4}return!1};o++;t:for(;o>0;){e="",o--;var v,x,A=Ja("3",i,o).height,_=i.multiline?h-o:(h-A)/2,p=_+=2,B=0,F=0,q=0;if(o<=0){e=`(...) Tj
`,e+="% Width of Text: "+Ja(e,i,o=12).width+", FieldWidth:"+l+`
`;break}for(var S="",M=0,Z=0;Z<u.length;Z++)if(u.hasOwnProperty(Z)){var st=!1;if(u[Z].length!==1&&q!==u[Z].length-1){if((A+2)*(M+2)+2>h)continue t;S+=u[Z][q],st=!0,F=Z,Z--}else{S=(S+=u[Z][q]+" ").substr(S.length-1)==" "?S.substr(0,S.length-1):S;var dt=parseInt(Z),Nt=f(dt,S,o),rt=Z>=u.length-1;if(Nt&&!rt){S+=" ",q=0;continue}if(Nt||rt){if(rt)F=dt;else if(i.multiline&&(A+2)*(M+2)+2>h)continue t}else{if(!i.multiline||(A+2)*(M+2)+2>h)continue t;F=dt}}for(var G="",vt=B;vt<=F;vt++){var bt=u[vt];if(i.multiline){if(vt===F){G+=bt[q]+" ",q=(q+1)%bt.length;continue}if(vt===B){G+=bt[bt.length-1]+" ";continue}}G+=bt[0]+" "}switch(G=G.substr(G.length-1)==" "?G.substr(0,G.length-1):G,x=Ja(G,i,o).width,i.textAlign){case"right":v=l-x-2;break;case"center":v=(l-x)/2;break;case"left":default:v=2}e+=Xt(v)+" "+Xt(p)+` Td
`,e+="("+Tr(G)+`) Tj
`,e+=-Xt(v)+` 0 Td
`,p=-(o+2),x=0,B=st?F:F+1,M++,S=""}break}return a.text=e,a.fontSize=o,a},Ja=function(i,e,r){var a=e.scope.internal.getFont(e.fontName,e.fontStyle),u=e.scope.getStringUnitWidth(i,{font:a,fontSize:parseFloat(r),charSpace:0})*parseFloat(r);return{height:e.scope.getStringUnitWidth("3",{font:a,fontSize:parseFloat(r),charSpace:0})*parseFloat(r)*1.5,width:u}},ru={fields:[],xForms:[],acroFormDictionaryRoot:null,printedOut:!1,internal:null,isInitialized:!1},iu=function(i,e){var r={type:"reference",object:i};e.internal.getPageInfo(i.page).pageContext.annotations.find(function(a){return a.type===r.type&&a.object===r.object})===void 0&&e.internal.getPageInfo(i.page).pageContext.annotations.push(r)},au=function(i,e){for(var r in i)if(i.hasOwnProperty(r)){var a=r,u=i[r];e.internal.newObjectDeferredBegin(u.objId,!0),ve(u)==="object"&&typeof u.putStream=="function"&&u.putStream(),delete i[a]}},ou=function(i,e){if(e.scope=i,i.internal!==void 0&&(i.internal.acroformPlugin===void 0||i.internal.acroformPlugin.isInitialized===!1)){if(Mn.FieldNum=0,i.internal.acroformPlugin=JSON.parse(JSON.stringify(ru)),i.internal.acroformPlugin.acroFormDictionaryRoot)throw new Error("Exception while creating AcroformDictionary");os=i.internal.scaleFactor,i.internal.acroformPlugin.acroFormDictionaryRoot=new rc,i.internal.acroformPlugin.acroFormDictionaryRoot.scope=i,i.internal.acroformPlugin.acroFormDictionaryRoot._eventID=i.internal.events.subscribe("postPutResources",function(){(function(r){r.internal.events.unsubscribe(r.internal.acroformPlugin.acroFormDictionaryRoot._eventID),delete r.internal.acroformPlugin.acroFormDictionaryRoot._eventID,r.internal.acroformPlugin.printedOut=!0})(i)}),i.internal.events.subscribe("buildDocument",function(){(function(r){r.internal.acroformPlugin.acroFormDictionaryRoot.objId=void 0;var a=r.internal.acroformPlugin.acroFormDictionaryRoot.Fields;for(var u in a)if(a.hasOwnProperty(u)){var o=a[u];o.objId=void 0,o.hasAnnotation&&iu(o,r)}})(i)}),i.internal.events.subscribe("putCatalog",function(){(function(r){if(r.internal.acroformPlugin.acroFormDictionaryRoot===void 0)throw new Error("putCatalogCallback: Root missing.");r.internal.write("/AcroForm "+r.internal.acroformPlugin.acroFormDictionaryRoot.objId+" 0 R")})(i)}),i.internal.events.subscribe("postPutPages",function(r){(function(a,u){var o=!a;for(var h in a||(u.internal.newObjectDeferredBegin(u.internal.acroformPlugin.acroFormDictionaryRoot.objId,!0),u.internal.acroformPlugin.acroFormDictionaryRoot.putStream()),a=a||u.internal.acroformPlugin.acroFormDictionaryRoot.Kids)if(a.hasOwnProperty(h)){var l=a[h],f=[],v=l.Rect;if(l.Rect&&(l.Rect=eu(l.Rect,u)),u.internal.newObjectDeferredBegin(l.objId,!0),l.DA=Bt.createDefaultAppearanceStream(l),ve(l)==="object"&&typeof l.getKeyValueListForStream=="function"&&(f=l.getKeyValueListForStream()),l.Rect=v,l.hasAppearanceStream&&!l.appearanceStreamContent){var x=nu(l);f.push({key:"AP",value:"<</N "+x+">>"}),u.internal.acroformPlugin.xForms.push(x)}if(l.appearanceStreamContent){var A="";for(var _ in l.appearanceStreamContent)if(l.appearanceStreamContent.hasOwnProperty(_)){var p=l.appearanceStreamContent[_];if(A+="/"+_+" ",A+="<<",Object.keys(p).length>=1||Array.isArray(p)){for(var h in p)if(p.hasOwnProperty(h)){var B=p[h];typeof B=="function"&&(B=B.call(u,l)),A+="/"+h+" "+B+" ",u.internal.acroformPlugin.xForms.indexOf(B)>=0||u.internal.acroformPlugin.xForms.push(B)}}else typeof(B=p)=="function"&&(B=B.call(u,l)),A+="/"+h+" "+B,u.internal.acroformPlugin.xForms.indexOf(B)>=0||u.internal.acroformPlugin.xForms.push(B);A+=">>"}f.push({key:"AP",value:`<<
`+A+">>"})}u.internal.putStream({additionalKeyValues:f,objectId:l.objId}),u.internal.out("endobj")}o&&au(u.internal.acroformPlugin.xForms,u)})(r,i)}),i.internal.acroformPlugin.isInitialized=!0}},ec=_e.__acroform__.arrayToPdfArray=function(i,e,r){var a=function(h){return h};if(Array.isArray(i)){for(var u="[",o=0;o<i.length;o++)switch(o!==0&&(u+=" "),ve(i[o])){case"boolean":case"number":case"object":u+=i[o].toString();break;case"string":i[o].substr(0,1)!=="/"?(e!==void 0&&r&&(a=r.internal.getEncryptor(e)),u+="("+Tr(a(i[o].toString()))+")"):u+=i[o].toString()}return u+="]"}throw new Error("Invalid argument passed to jsPDF.__acroform__.arrayToPdfArray")},Xo=function(i,e,r){var a=function(u){return u};return e!==void 0&&r&&(a=r.internal.getEncryptor(e)),(i=i||"").toString(),i="("+Tr(a(i))+")"},Yn=function(){this._objId=void 0,this._scope=void 0,Object.defineProperty(this,"objId",{get:function(){if(this._objId===void 0){if(this.scope===void 0)return;this._objId=this.scope.internal.newObjectDeferred()}return this._objId},set:function(i){this._objId=i}}),Object.defineProperty(this,"scope",{value:this._scope,writable:!0})};Yn.prototype.toString=function(){return this.objId+" 0 R"},Yn.prototype.putStream=function(){var i=this.getKeyValueListForStream();this.scope.internal.putStream({data:this.stream,additionalKeyValues:i,objectId:this.objId}),this.scope.internal.out("endobj")},Yn.prototype.getKeyValueListForStream=function(){var i=[],e=Object.getOwnPropertyNames(this).filter(function(o){return o!="content"&&o!="appearanceStreamContent"&&o!="scope"&&o!="objId"&&o.substring(0,1)!="_"});for(var r in e)if(Object.getOwnPropertyDescriptor(this,e[r]).configurable===!1){var a=e[r],u=this[a];u&&(Array.isArray(u)?i.push({key:a,value:ec(u,this.objId,this.scope)}):u instanceof Yn?(u.scope=this.scope,i.push({key:a,value:u.objId+" 0 R"})):typeof u!="function"&&i.push({key:a,value:u}))}return i};var nc=function(){Yn.call(this),Object.defineProperty(this,"Type",{value:"/XObject",configurable:!1,writable:!0}),Object.defineProperty(this,"Subtype",{value:"/Form",configurable:!1,writable:!0}),Object.defineProperty(this,"FormType",{value:1,configurable:!1,writable:!0});var i,e=[];Object.defineProperty(this,"BBox",{configurable:!1,get:function(){return e},set:function(r){e=r}}),Object.defineProperty(this,"Resources",{value:"2 0 R",configurable:!1,writable:!0}),Object.defineProperty(this,"stream",{enumerable:!1,configurable:!0,set:function(r){i=r.trim()},get:function(){return i||null}})};un(nc,Yn);var rc=function(){Yn.call(this);var i,e=[];Object.defineProperty(this,"Kids",{enumerable:!1,configurable:!0,get:function(){return e.length>0?e:void 0}}),Object.defineProperty(this,"Fields",{enumerable:!1,configurable:!1,get:function(){return e}}),Object.defineProperty(this,"DA",{enumerable:!1,configurable:!1,get:function(){if(i){var r=function(a){return a};return this.scope&&(r=this.scope.internal.getEncryptor(this.objId)),"("+Tr(r(i))+")"}},set:function(r){i=r}})};un(rc,Yn);var Mn=function i(){Yn.call(this);var e=4;Object.defineProperty(this,"F",{enumerable:!1,configurable:!1,get:function(){return e},set:function(S){if(isNaN(S))throw new Error('Invalid value "'+S+'" for attribute F supplied.');e=S}}),Object.defineProperty(this,"showWhenPrinted",{enumerable:!0,configurable:!0,get:function(){return!!Fe(e,3)},set:function(S){S?this.F=je(e,3):this.F=Ce(e,3)}});var r=0;Object.defineProperty(this,"Ff",{enumerable:!1,configurable:!1,get:function(){return r},set:function(S){if(isNaN(S))throw new Error('Invalid value "'+S+'" for attribute Ff supplied.');r=S}});var a=[];Object.defineProperty(this,"Rect",{enumerable:!1,configurable:!1,get:function(){if(a.length!==0)return a},set:function(S){a=S!==void 0?S:[]}}),Object.defineProperty(this,"x",{enumerable:!0,configurable:!0,get:function(){return!a||isNaN(a[0])?0:a[0]},set:function(S){a[0]=S}}),Object.defineProperty(this,"y",{enumerable:!0,configurable:!0,get:function(){return!a||isNaN(a[1])?0:a[1]},set:function(S){a[1]=S}}),Object.defineProperty(this,"width",{enumerable:!0,configurable:!0,get:function(){return!a||isNaN(a[2])?0:a[2]},set:function(S){a[2]=S}}),Object.defineProperty(this,"height",{enumerable:!0,configurable:!0,get:function(){return!a||isNaN(a[3])?0:a[3]},set:function(S){a[3]=S}});var u="";Object.defineProperty(this,"FT",{enumerable:!0,configurable:!1,get:function(){return u},set:function(S){switch(S){case"/Btn":case"/Tx":case"/Ch":case"/Sig":u=S;break;default:throw new Error('Invalid value "'+S+'" for attribute FT supplied.')}}});var o=null;Object.defineProperty(this,"T",{enumerable:!0,configurable:!1,get:function(){if(!o||o.length<1){if(this instanceof eo)return;o="FieldObject"+i.FieldNum++}var S=function(M){return M};return this.scope&&(S=this.scope.internal.getEncryptor(this.objId)),"("+Tr(S(o))+")"},set:function(S){o=S.toString()}}),Object.defineProperty(this,"fieldName",{configurable:!0,enumerable:!0,get:function(){return o},set:function(S){o=S}});var h="helvetica";Object.defineProperty(this,"fontName",{enumerable:!0,configurable:!0,get:function(){return h},set:function(S){h=S}});var l="normal";Object.defineProperty(this,"fontStyle",{enumerable:!0,configurable:!0,get:function(){return l},set:function(S){l=S}});var f=0;Object.defineProperty(this,"fontSize",{enumerable:!0,configurable:!0,get:function(){return f},set:function(S){f=S}});var v=void 0;Object.defineProperty(this,"maxFontSize",{enumerable:!0,configurable:!0,get:function(){return v===void 0?50/os:v},set:function(S){v=S}});var x="black";Object.defineProperty(this,"color",{enumerable:!0,configurable:!0,get:function(){return x},set:function(S){x=S}});var A="/F1 0 Tf 0 g";Object.defineProperty(this,"DA",{enumerable:!0,configurable:!1,get:function(){if(!(!A||this instanceof eo||this instanceof Rr))return Xo(A,this.objId,this.scope)},set:function(S){S=S.toString(),A=S}});var _=null;Object.defineProperty(this,"DV",{enumerable:!1,configurable:!1,get:function(){if(_)return this instanceof Ue?_:Xo(_,this.objId,this.scope)},set:function(S){S=S.toString(),_=this instanceof Ue?S:S.substr(0,1)==="("?mi(S.substr(1,S.length-2)):mi(S)}}),Object.defineProperty(this,"defaultValue",{enumerable:!0,configurable:!0,get:function(){return this instanceof Ue?mi(_.substr(1,_.length-1)):_},set:function(S){S=S.toString(),_=this instanceof Ue?"/"+S:S}});var p=null;Object.defineProperty(this,"_V",{enumerable:!1,configurable:!1,get:function(){if(p)return p},set:function(S){this.V=S}}),Object.defineProperty(this,"V",{enumerable:!1,configurable:!1,get:function(){if(p)return this instanceof Ue?p:Xo(p,this.objId,this.scope)},set:function(S){S=S.toString(),p=this instanceof Ue?S:S.substr(0,1)==="("?mi(S.substr(1,S.length-2)):mi(S)}}),Object.defineProperty(this,"value",{enumerable:!0,configurable:!0,get:function(){return this instanceof Ue?mi(p.substr(1,p.length-1)):p},set:function(S){S=S.toString(),p=this instanceof Ue?"/"+S:S}}),Object.defineProperty(this,"hasAnnotation",{enumerable:!0,configurable:!0,get:function(){return this.Rect}}),Object.defineProperty(this,"Type",{enumerable:!0,configurable:!1,get:function(){return this.hasAnnotation?"/Annot":null}}),Object.defineProperty(this,"Subtype",{enumerable:!0,configurable:!1,get:function(){return this.hasAnnotation?"/Widget":null}});var B,F=!1;Object.defineProperty(this,"hasAppearanceStream",{enumerable:!0,configurable:!0,get:function(){return F},set:function(S){S=!!S,F=S}}),Object.defineProperty(this,"page",{enumerable:!0,configurable:!0,get:function(){if(B)return B},set:function(S){B=S}}),Object.defineProperty(this,"readOnly",{enumerable:!0,configurable:!0,get:function(){return!!Fe(this.Ff,1)},set:function(S){S?this.Ff=je(this.Ff,1):this.Ff=Ce(this.Ff,1)}}),Object.defineProperty(this,"required",{enumerable:!0,configurable:!0,get:function(){return!!Fe(this.Ff,2)},set:function(S){S?this.Ff=je(this.Ff,2):this.Ff=Ce(this.Ff,2)}}),Object.defineProperty(this,"noExport",{enumerable:!0,configurable:!0,get:function(){return!!Fe(this.Ff,3)},set:function(S){S?this.Ff=je(this.Ff,3):this.Ff=Ce(this.Ff,3)}});var q=null;Object.defineProperty(this,"Q",{enumerable:!0,configurable:!1,get:function(){if(q!==null)return q},set:function(S){if([0,1,2].indexOf(S)===-1)throw new Error('Invalid value "'+S+'" for attribute Q supplied.');q=S}}),Object.defineProperty(this,"textAlign",{get:function(){var S;switch(q){case 0:default:S="left";break;case 1:S="center";break;case 2:S="right"}return S},configurable:!0,enumerable:!0,set:function(S){switch(S){case"right":case 2:q=2;break;case"center":case 1:q=1;break;case"left":case 0:default:q=0}}})};un(Mn,Yn);var Li=function(){Mn.call(this),this.FT="/Ch",this.V="()",this.fontName="zapfdingbats";var i=0;Object.defineProperty(this,"TI",{enumerable:!0,configurable:!1,get:function(){return i},set:function(r){i=r}}),Object.defineProperty(this,"topIndex",{enumerable:!0,configurable:!0,get:function(){return i},set:function(r){i=r}});var e=[];Object.defineProperty(this,"Opt",{enumerable:!0,configurable:!1,get:function(){return ec(e,this.objId,this.scope)},set:function(r){var a,u;u=[],typeof(a=r)=="string"&&(u=function(o,h,l){l||(l=1);for(var f,v=[];f=h.exec(o);)v.push(f[l]);return v}(a,/\((.*?)\)/g)),e=u}}),this.getOptions=function(){return e},this.setOptions=function(r){e=r,this.sort&&e.sort()},this.addOption=function(r){r=(r=r||"").toString(),e.push(r),this.sort&&e.sort()},this.removeOption=function(r,a){for(a=a||!1,r=(r=r||"").toString();e.indexOf(r)!==-1&&(e.splice(e.indexOf(r),1),a!==!1););},Object.defineProperty(this,"combo",{enumerable:!0,configurable:!0,get:function(){return!!Fe(this.Ff,18)},set:function(r){r?this.Ff=je(this.Ff,18):this.Ff=Ce(this.Ff,18)}}),Object.defineProperty(this,"edit",{enumerable:!0,configurable:!0,get:function(){return!!Fe(this.Ff,19)},set:function(r){this.combo===!0&&(r?this.Ff=je(this.Ff,19):this.Ff=Ce(this.Ff,19))}}),Object.defineProperty(this,"sort",{enumerable:!0,configurable:!0,get:function(){return!!Fe(this.Ff,20)},set:function(r){r?(this.Ff=je(this.Ff,20),e.sort()):this.Ff=Ce(this.Ff,20)}}),Object.defineProperty(this,"multiSelect",{enumerable:!0,configurable:!0,get:function(){return!!Fe(this.Ff,22)},set:function(r){r?this.Ff=je(this.Ff,22):this.Ff=Ce(this.Ff,22)}}),Object.defineProperty(this,"doNotSpellCheck",{enumerable:!0,configurable:!0,get:function(){return!!Fe(this.Ff,23)},set:function(r){r?this.Ff=je(this.Ff,23):this.Ff=Ce(this.Ff,23)}}),Object.defineProperty(this,"commitOnSelChange",{enumerable:!0,configurable:!0,get:function(){return!!Fe(this.Ff,27)},set:function(r){r?this.Ff=je(this.Ff,27):this.Ff=Ce(this.Ff,27)}}),this.hasAppearanceStream=!1};un(Li,Mn);var Ni=function(){Li.call(this),this.fontName="helvetica",this.combo=!1};un(Ni,Li);var Ai=function(){Ni.call(this),this.combo=!0};un(Ai,Ni);var Ka=function(){Ai.call(this),this.edit=!0};un(Ka,Ai);var Ue=function(){Mn.call(this),this.FT="/Btn",Object.defineProperty(this,"noToggleToOff",{enumerable:!0,configurable:!0,get:function(){return!!Fe(this.Ff,15)},set:function(r){r?this.Ff=je(this.Ff,15):this.Ff=Ce(this.Ff,15)}}),Object.defineProperty(this,"radio",{enumerable:!0,configurable:!0,get:function(){return!!Fe(this.Ff,16)},set:function(r){r?this.Ff=je(this.Ff,16):this.Ff=Ce(this.Ff,16)}}),Object.defineProperty(this,"pushButton",{enumerable:!0,configurable:!0,get:function(){return!!Fe(this.Ff,17)},set:function(r){r?this.Ff=je(this.Ff,17):this.Ff=Ce(this.Ff,17)}}),Object.defineProperty(this,"radioIsUnison",{enumerable:!0,configurable:!0,get:function(){return!!Fe(this.Ff,26)},set:function(r){r?this.Ff=je(this.Ff,26):this.Ff=Ce(this.Ff,26)}});var i,e={};Object.defineProperty(this,"MK",{enumerable:!1,configurable:!1,get:function(){var r=function(o){return o};if(this.scope&&(r=this.scope.internal.getEncryptor(this.objId)),Object.keys(e).length!==0){var a,u=[];for(a in u.push("<<"),e)u.push("/"+a+" ("+Tr(r(e[a]))+")");return u.push(">>"),u.join(`
`)}},set:function(r){ve(r)==="object"&&(e=r)}}),Object.defineProperty(this,"caption",{enumerable:!0,configurable:!0,get:function(){return e.CA||""},set:function(r){typeof r=="string"&&(e.CA=r)}}),Object.defineProperty(this,"AS",{enumerable:!1,configurable:!1,get:function(){return i},set:function(r){i=r}}),Object.defineProperty(this,"appearanceState",{enumerable:!0,configurable:!0,get:function(){return i.substr(1,i.length-1)},set:function(r){i="/"+r}})};un(Ue,Mn);var Za=function(){Ue.call(this),this.pushButton=!0};un(Za,Ue);var xi=function(){Ue.call(this),this.radio=!0,this.pushButton=!1;var i=[];Object.defineProperty(this,"Kids",{enumerable:!0,configurable:!1,get:function(){return i},set:function(e){i=e!==void 0?e:[]}})};un(xi,Ue);var eo=function(){var i,e;Mn.call(this),Object.defineProperty(this,"Parent",{enumerable:!1,configurable:!1,get:function(){return i},set:function(u){i=u}}),Object.defineProperty(this,"optionName",{enumerable:!1,configurable:!0,get:function(){return e},set:function(u){e=u}});var r,a={};Object.defineProperty(this,"MK",{enumerable:!1,configurable:!1,get:function(){var u=function(l){return l};this.scope&&(u=this.scope.internal.getEncryptor(this.objId));var o,h=[];for(o in h.push("<<"),a)h.push("/"+o+" ("+Tr(u(a[o]))+")");return h.push(">>"),h.join(`
`)},set:function(u){ve(u)==="object"&&(a=u)}}),Object.defineProperty(this,"caption",{enumerable:!0,configurable:!0,get:function(){return a.CA||""},set:function(u){typeof u=="string"&&(a.CA=u)}}),Object.defineProperty(this,"AS",{enumerable:!1,configurable:!1,get:function(){return r},set:function(u){r=u}}),Object.defineProperty(this,"appearanceState",{enumerable:!0,configurable:!0,get:function(){return r.substr(1,r.length-1)},set:function(u){r="/"+u}}),this.caption="l",this.appearanceState="Off",this._AppearanceType=Bt.RadioButton.Circle,this.appearanceStreamContent=this._AppearanceType.createAppearanceStream(this.optionName)};un(eo,Mn),xi.prototype.setAppearance=function(i){if(!("createAppearanceStream"in i)||!("getCA"in i))throw new Error("Couldn't assign Appearance to RadioButton. Appearance was Invalid!");for(var e in this.Kids)if(this.Kids.hasOwnProperty(e)){var r=this.Kids[e];r.appearanceStreamContent=i.createAppearanceStream(r.optionName),r.caption=i.getCA()}},xi.prototype.createOption=function(i){var e=new eo;return e.Parent=this,e.optionName=i,this.Kids.push(e),su.call(this.scope,e),e};var $a=function(){Ue.call(this),this.fontName="zapfdingbats",this.caption="3",this.appearanceState="On",this.value="On",this.textAlign="center",this.appearanceStreamContent=Bt.CheckBox.createAppearanceStream()};un($a,Ue);var Rr=function(){Mn.call(this),this.FT="/Tx",Object.defineProperty(this,"multiline",{enumerable:!0,configurable:!0,get:function(){return!!Fe(this.Ff,13)},set:function(e){e?this.Ff=je(this.Ff,13):this.Ff=Ce(this.Ff,13)}}),Object.defineProperty(this,"fileSelect",{enumerable:!0,configurable:!0,get:function(){return!!Fe(this.Ff,21)},set:function(e){e?this.Ff=je(this.Ff,21):this.Ff=Ce(this.Ff,21)}}),Object.defineProperty(this,"doNotSpellCheck",{enumerable:!0,configurable:!0,get:function(){return!!Fe(this.Ff,23)},set:function(e){e?this.Ff=je(this.Ff,23):this.Ff=Ce(this.Ff,23)}}),Object.defineProperty(this,"doNotScroll",{enumerable:!0,configurable:!0,get:function(){return!!Fe(this.Ff,24)},set:function(e){e?this.Ff=je(this.Ff,24):this.Ff=Ce(this.Ff,24)}}),Object.defineProperty(this,"comb",{enumerable:!0,configurable:!0,get:function(){return!!Fe(this.Ff,25)},set:function(e){e?this.Ff=je(this.Ff,25):this.Ff=Ce(this.Ff,25)}}),Object.defineProperty(this,"richText",{enumerable:!0,configurable:!0,get:function(){return!!Fe(this.Ff,26)},set:function(e){e?this.Ff=je(this.Ff,26):this.Ff=Ce(this.Ff,26)}});var i=null;Object.defineProperty(this,"MaxLen",{enumerable:!0,configurable:!1,get:function(){return i},set:function(e){i=e}}),Object.defineProperty(this,"maxLength",{enumerable:!0,configurable:!0,get:function(){return i},set:function(e){Number.isInteger(e)&&(i=e)}}),Object.defineProperty(this,"hasAppearanceStream",{enumerable:!0,configurable:!0,get:function(){return this.V||this.DV}})};un(Rr,Mn);var Qa=function(){Rr.call(this),Object.defineProperty(this,"password",{enumerable:!0,configurable:!0,get:function(){return!!Fe(this.Ff,14)},set:function(i){i?this.Ff=je(this.Ff,14):this.Ff=Ce(this.Ff,14)}}),this.password=!0};un(Qa,Rr);var Bt={CheckBox:{createAppearanceStream:function(){return{N:{On:Bt.CheckBox.YesNormal},D:{On:Bt.CheckBox.YesPushDown,Off:Bt.CheckBox.OffPushDown}}},YesPushDown:function(i){var e=Jn(i);e.scope=i.scope;var r=[],a=i.scope.internal.getFont(i.fontName,i.fontStyle).id,u=i.scope.__private__.encodeColorString(i.color),o=rs(i,i.caption);return r.push("0.749023 g"),r.push("0 0 "+Xt(Bt.internal.getWidth(i))+" "+Xt(Bt.internal.getHeight(i))+" re"),r.push("f"),r.push("BMC"),r.push("q"),r.push("0 0 1 rg"),r.push("/"+a+" "+Xt(o.fontSize)+" Tf "+u),r.push("BT"),r.push(o.text),r.push("ET"),r.push("Q"),r.push("EMC"),e.stream=r.join(`
`),e},YesNormal:function(i){var e=Jn(i);e.scope=i.scope;var r=i.scope.internal.getFont(i.fontName,i.fontStyle).id,a=i.scope.__private__.encodeColorString(i.color),u=[],o=Bt.internal.getHeight(i),h=Bt.internal.getWidth(i),l=rs(i,i.caption);return u.push("1 g"),u.push("0 0 "+Xt(h)+" "+Xt(o)+" re"),u.push("f"),u.push("q"),u.push("0 0 1 rg"),u.push("0 0 "+Xt(h-1)+" "+Xt(o-1)+" re"),u.push("W"),u.push("n"),u.push("0 g"),u.push("BT"),u.push("/"+r+" "+Xt(l.fontSize)+" Tf "+a),u.push(l.text),u.push("ET"),u.push("Q"),e.stream=u.join(`
`),e},OffPushDown:function(i){var e=Jn(i);e.scope=i.scope;var r=[];return r.push("0.749023 g"),r.push("0 0 "+Xt(Bt.internal.getWidth(i))+" "+Xt(Bt.internal.getHeight(i))+" re"),r.push("f"),e.stream=r.join(`
`),e}},RadioButton:{Circle:{createAppearanceStream:function(i){var e={D:{Off:Bt.RadioButton.Circle.OffPushDown},N:{}};return e.N[i]=Bt.RadioButton.Circle.YesNormal,e.D[i]=Bt.RadioButton.Circle.YesPushDown,e},getCA:function(){return"l"},YesNormal:function(i){var e=Jn(i);e.scope=i.scope;var r=[],a=Bt.internal.getWidth(i)<=Bt.internal.getHeight(i)?Bt.internal.getWidth(i)/4:Bt.internal.getHeight(i)/4;a=Number((.9*a).toFixed(5));var u=Bt.internal.Bezier_C,o=Number((a*u).toFixed(5));return r.push("q"),r.push("1 0 0 1 "+Lr(Bt.internal.getWidth(i)/2)+" "+Lr(Bt.internal.getHeight(i)/2)+" cm"),r.push(a+" 0 m"),r.push(a+" "+o+" "+o+" "+a+" 0 "+a+" c"),r.push("-"+o+" "+a+" -"+a+" "+o+" -"+a+" 0 c"),r.push("-"+a+" -"+o+" -"+o+" -"+a+" 0 -"+a+" c"),r.push(o+" -"+a+" "+a+" -"+o+" "+a+" 0 c"),r.push("f"),r.push("Q"),e.stream=r.join(`
`),e},YesPushDown:function(i){var e=Jn(i);e.scope=i.scope;var r=[],a=Bt.internal.getWidth(i)<=Bt.internal.getHeight(i)?Bt.internal.getWidth(i)/4:Bt.internal.getHeight(i)/4;a=Number((.9*a).toFixed(5));var u=Number((2*a).toFixed(5)),o=Number((u*Bt.internal.Bezier_C).toFixed(5)),h=Number((a*Bt.internal.Bezier_C).toFixed(5));return r.push("0.749023 g"),r.push("q"),r.push("1 0 0 1 "+Lr(Bt.internal.getWidth(i)/2)+" "+Lr(Bt.internal.getHeight(i)/2)+" cm"),r.push(u+" 0 m"),r.push(u+" "+o+" "+o+" "+u+" 0 "+u+" c"),r.push("-"+o+" "+u+" -"+u+" "+o+" -"+u+" 0 c"),r.push("-"+u+" -"+o+" -"+o+" -"+u+" 0 -"+u+" c"),r.push(o+" -"+u+" "+u+" -"+o+" "+u+" 0 c"),r.push("f"),r.push("Q"),r.push("0 g"),r.push("q"),r.push("1 0 0 1 "+Lr(Bt.internal.getWidth(i)/2)+" "+Lr(Bt.internal.getHeight(i)/2)+" cm"),r.push(a+" 0 m"),r.push(a+" "+h+" "+h+" "+a+" 0 "+a+" c"),r.push("-"+h+" "+a+" -"+a+" "+h+" -"+a+" 0 c"),r.push("-"+a+" -"+h+" -"+h+" -"+a+" 0 -"+a+" c"),r.push(h+" -"+a+" "+a+" -"+h+" "+a+" 0 c"),r.push("f"),r.push("Q"),e.stream=r.join(`
`),e},OffPushDown:function(i){var e=Jn(i);e.scope=i.scope;var r=[],a=Bt.internal.getWidth(i)<=Bt.internal.getHeight(i)?Bt.internal.getWidth(i)/4:Bt.internal.getHeight(i)/4;a=Number((.9*a).toFixed(5));var u=Number((2*a).toFixed(5)),o=Number((u*Bt.internal.Bezier_C).toFixed(5));return r.push("0.749023 g"),r.push("q"),r.push("1 0 0 1 "+Lr(Bt.internal.getWidth(i)/2)+" "+Lr(Bt.internal.getHeight(i)/2)+" cm"),r.push(u+" 0 m"),r.push(u+" "+o+" "+o+" "+u+" 0 "+u+" c"),r.push("-"+o+" "+u+" -"+u+" "+o+" -"+u+" 0 c"),r.push("-"+u+" -"+o+" -"+o+" -"+u+" 0 -"+u+" c"),r.push(o+" -"+u+" "+u+" -"+o+" "+u+" 0 c"),r.push("f"),r.push("Q"),e.stream=r.join(`
`),e}},Cross:{createAppearanceStream:function(i){var e={D:{Off:Bt.RadioButton.Cross.OffPushDown},N:{}};return e.N[i]=Bt.RadioButton.Cross.YesNormal,e.D[i]=Bt.RadioButton.Cross.YesPushDown,e},getCA:function(){return"8"},YesNormal:function(i){var e=Jn(i);e.scope=i.scope;var r=[],a=Bt.internal.calculateCross(i);return r.push("q"),r.push("1 1 "+Xt(Bt.internal.getWidth(i)-2)+" "+Xt(Bt.internal.getHeight(i)-2)+" re"),r.push("W"),r.push("n"),r.push(Xt(a.x1.x)+" "+Xt(a.x1.y)+" m"),r.push(Xt(a.x2.x)+" "+Xt(a.x2.y)+" l"),r.push(Xt(a.x4.x)+" "+Xt(a.x4.y)+" m"),r.push(Xt(a.x3.x)+" "+Xt(a.x3.y)+" l"),r.push("s"),r.push("Q"),e.stream=r.join(`
`),e},YesPushDown:function(i){var e=Jn(i);e.scope=i.scope;var r=Bt.internal.calculateCross(i),a=[];return a.push("0.749023 g"),a.push("0 0 "+Xt(Bt.internal.getWidth(i))+" "+Xt(Bt.internal.getHeight(i))+" re"),a.push("f"),a.push("q"),a.push("1 1 "+Xt(Bt.internal.getWidth(i)-2)+" "+Xt(Bt.internal.getHeight(i)-2)+" re"),a.push("W"),a.push("n"),a.push(Xt(r.x1.x)+" "+Xt(r.x1.y)+" m"),a.push(Xt(r.x2.x)+" "+Xt(r.x2.y)+" l"),a.push(Xt(r.x4.x)+" "+Xt(r.x4.y)+" m"),a.push(Xt(r.x3.x)+" "+Xt(r.x3.y)+" l"),a.push("s"),a.push("Q"),e.stream=a.join(`
`),e},OffPushDown:function(i){var e=Jn(i);e.scope=i.scope;var r=[];return r.push("0.749023 g"),r.push("0 0 "+Xt(Bt.internal.getWidth(i))+" "+Xt(Bt.internal.getHeight(i))+" re"),r.push("f"),e.stream=r.join(`
`),e}}},createDefaultAppearanceStream:function(i){var e=i.scope.internal.getFont(i.fontName,i.fontStyle).id,r=i.scope.__private__.encodeColorString(i.color);return"/"+e+" "+i.fontSize+" Tf "+r}};Bt.internal={Bezier_C:.551915024494,calculateCross:function(i){var e=Bt.internal.getWidth(i),r=Bt.internal.getHeight(i),a=Math.min(e,r);return{x1:{x:(e-a)/2,y:(r-a)/2+a},x2:{x:(e-a)/2+a,y:(r-a)/2},x3:{x:(e-a)/2,y:(r-a)/2},x4:{x:(e-a)/2+a,y:(r-a)/2+a}}}},Bt.internal.getWidth=function(i){var e=0;return ve(i)==="object"&&(e=Rs(i.Rect[2])),e},Bt.internal.getHeight=function(i){var e=0;return ve(i)==="object"&&(e=Rs(i.Rect[3])),e};var su=_e.addField=function(i){if(ou(this,i),!(i instanceof Mn))throw new Error("Invalid argument passed to jsPDF.addField.");var e;return(e=i).scope.internal.acroformPlugin.printedOut&&(e.scope.internal.acroformPlugin.printedOut=!1,e.scope.internal.acroformPlugin.acroFormDictionaryRoot=null),e.scope.internal.acroformPlugin.acroFormDictionaryRoot.Fields.push(e),i.page=i.scope.internal.getCurrentPageInfo().pageNumber,this};_e.AcroFormChoiceField=Li,_e.AcroFormListBox=Ni,_e.AcroFormComboBox=Ai,_e.AcroFormEditBox=Ka,_e.AcroFormButton=Ue,_e.AcroFormPushButton=Za,_e.AcroFormRadioButton=xi,_e.AcroFormCheckBox=$a,_e.AcroFormTextField=Rr,_e.AcroFormPasswordField=Qa,_e.AcroFormAppearance=Bt,_e.AcroForm={ChoiceField:Li,ListBox:Ni,ComboBox:Ai,EditBox:Ka,Button:Ue,PushButton:Za,RadioButton:xi,CheckBox:$a,TextField:Rr,PasswordField:Qa,Appearance:Bt},Ut.AcroForm={ChoiceField:Li,ListBox:Ni,ComboBox:Ai,EditBox:Ka,Button:Ue,PushButton:Za,RadioButton:xi,CheckBox:$a,TextField:Rr,PasswordField:Qa,Appearance:Bt};function ic(i){return i.reduce(function(e,r,a){return e[r]=a,e},{})}(function(i){i.__addimage__={};var e="UNKNOWN",r={PNG:[[137,80,78,71]],TIFF:[[77,77,0,42],[73,73,42,0]],JPEG:[[255,216,255,224,void 0,void 0,74,70,73,70,0],[255,216,255,225,void 0,void 0,69,120,105,102,0,0],[255,216,255,219],[255,216,255,238]],JPEG2000:[[0,0,0,12,106,80,32,32]],GIF87a:[[71,73,70,56,55,97]],GIF89a:[[71,73,70,56,57,97]],WEBP:[[82,73,70,70,void 0,void 0,void 0,void 0,87,69,66,80]],BMP:[[66,77],[66,65],[67,73],[67,80],[73,67],[80,84]]},a=i.__addimage__.getImageFileTypeByImageData=function(k,I){var H,R,ct,ot,mt,tt=e;if((I=I||e)==="RGBA"||k.data!==void 0&&k.data instanceof Uint8ClampedArray&&"height"in k&&"width"in k)return"RGBA";if(Nt(k))for(mt in r)for(ct=r[mt],H=0;H<ct.length;H+=1){for(ot=!0,R=0;R<ct[H].length;R+=1)if(ct[H][R]!==void 0&&ct[H][R]!==k[R]){ot=!1;break}if(ot===!0){tt=mt;break}}else for(mt in r)for(ct=r[mt],H=0;H<ct.length;H+=1){for(ot=!0,R=0;R<ct[H].length;R+=1)if(ct[H][R]!==void 0&&ct[H][R]!==k.charCodeAt(R)){ot=!1;break}if(ot===!0){tt=mt;break}}return tt===e&&I!==e&&(tt=I),tt},u=function k(I){for(var H=this.internal.write,R=this.internal.putStream,ct=(0,this.internal.getFilters)();ct.indexOf("FlateEncode")!==-1;)ct.splice(ct.indexOf("FlateEncode"),1);I.objectId=this.internal.newObject();var ot=[];if(ot.push({key:"Type",value:"/XObject"}),ot.push({key:"Subtype",value:"/Image"}),ot.push({key:"Width",value:I.width}),ot.push({key:"Height",value:I.height}),I.colorSpace===q.INDEXED?ot.push({key:"ColorSpace",value:"[/Indexed /DeviceRGB "+(I.palette.length/3-1)+" "+("sMask"in I&&I.sMask!==void 0?I.objectId+2:I.objectId+1)+" 0 R]"}):(ot.push({key:"ColorSpace",value:"/"+I.colorSpace}),I.colorSpace===q.DEVICE_CMYK&&ot.push({key:"Decode",value:"[1 0 1 0 1 0 1 0]"})),ot.push({key:"BitsPerComponent",value:I.bitsPerComponent}),"decodeParameters"in I&&I.decodeParameters!==void 0&&ot.push({key:"DecodeParms",value:"<<"+I.decodeParameters+">>"}),"transparency"in I&&Array.isArray(I.transparency)){for(var mt="",tt=0,pt=I.transparency.length;tt<pt;tt++)mt+=I.transparency[tt]+" "+I.transparency[tt]+" ";ot.push({key:"Mask",value:"["+mt+"]"})}I.sMask!==void 0&&ot.push({key:"SMask",value:I.objectId+1+" 0 R"});var ft=I.filter!==void 0?["/"+I.filter]:void 0;if(R({data:I.data,additionalKeyValues:ot,alreadyAppliedFilters:ft,objectId:I.objectId}),H("endobj"),"sMask"in I&&I.sMask!==void 0){var Et="/Predictor "+I.predictor+" /Colors 1 /BitsPerComponent "+I.bitsPerComponent+" /Columns "+I.width,w={width:I.width,height:I.height,colorSpace:"DeviceGray",bitsPerComponent:I.bitsPerComponent,decodeParameters:Et,data:I.sMask};"filter"in I&&(w.filter=I.filter),k.call(this,w)}if(I.colorSpace===q.INDEXED){var C=this.internal.newObject();R({data:G(new Uint8Array(I.palette)),objectId:C}),H("endobj")}},o=function(){var k=this.internal.collections.addImage_images;for(var I in k)u.call(this,k[I])},h=function(){var k,I=this.internal.collections.addImage_images,H=this.internal.write;for(var R in I)H("/I"+(k=I[R]).index,k.objectId,"0","R")},l=function(){this.internal.collections.addImage_images||(this.internal.collections.addImage_images={},this.internal.events.subscribe("putResources",o),this.internal.events.subscribe("putXobjectDict",h))},f=function(){var k=this.internal.collections.addImage_images;return l.call(this),k},v=function(){return Object.keys(this.internal.collections.addImage_images).length},x=function(k){return typeof i["process"+k.toUpperCase()]=="function"},A=function(k){return ve(k)==="object"&&k.nodeType===1},_=function(k,I){if(k.nodeName==="IMG"&&k.hasAttribute("src")){var H=""+k.getAttribute("src");if(H.indexOf("data:image/")===0)return ea(unescape(H).split("base64,").pop());var R=i.loadFile(H,!0);if(R!==void 0)return R}if(k.nodeName==="CANVAS"){if(k.width===0||k.height===0)throw new Error("Given canvas must have data. Canvas width: "+k.width+", height: "+k.height);var ct;switch(I){case"PNG":ct="image/png";break;case"WEBP":ct="image/webp";break;case"JPEG":case"JPG":default:ct="image/jpeg"}return ea(k.toDataURL(ct,1).split("base64,").pop())}},p=function(k){var I=this.internal.collections.addImage_images;if(I){for(var H in I)if(k===I[H].alias)return I[H]}},B=function(k,I,H){return k||I||(k=-96,I=-96),k<0&&(k=-1*H.width*72/k/this.internal.scaleFactor),I<0&&(I=-1*H.height*72/I/this.internal.scaleFactor),k===0&&(k=I*H.width/H.height),I===0&&(I=k*H.height/H.width),[k,I]},F=function(k,I,H,R,ct,ot){var mt=B.call(this,H,R,ct),tt=this.internal.getCoordinateString,pt=this.internal.getVerticalCoordinateString,ft=f.call(this);if(H=mt[0],R=mt[1],ft[ct.index]=ct,ot){ot*=Math.PI/180;var Et=Math.cos(ot),w=Math.sin(ot),C=function(W){return W.toFixed(4)},E=[C(Et),C(w),C(-1*w),C(Et),0,0,"cm"]}this.internal.write("q"),ot?(this.internal.write([1,"0","0",1,tt(k),pt(I+R),"cm"].join(" ")),this.internal.write(E.join(" ")),this.internal.write([tt(H),"0","0",tt(R),"0","0","cm"].join(" "))):this.internal.write([tt(H),"0","0",tt(R),tt(k),pt(I+R),"cm"].join(" ")),this.isAdvancedAPI()&&this.internal.write([1,0,0,-1,0,0,"cm"].join(" ")),this.internal.write("/I"+ct.index+" Do"),this.internal.write("Q")},q=i.color_spaces={DEVICE_RGB:"DeviceRGB",DEVICE_GRAY:"DeviceGray",DEVICE_CMYK:"DeviceCMYK",CAL_GREY:"CalGray",CAL_RGB:"CalRGB",LAB:"Lab",ICC_BASED:"ICCBased",INDEXED:"Indexed",PATTERN:"Pattern",SEPARATION:"Separation",DEVICE_N:"DeviceN"};i.decode={DCT_DECODE:"DCTDecode",FLATE_DECODE:"FlateDecode",LZW_DECODE:"LZWDecode",JPX_DECODE:"JPXDecode",JBIG2_DECODE:"JBIG2Decode",ASCII85_DECODE:"ASCII85Decode",ASCII_HEX_DECODE:"ASCIIHexDecode",RUN_LENGTH_DECODE:"RunLengthDecode",CCITT_FAX_DECODE:"CCITTFaxDecode"};var S=i.image_compression={NONE:"NONE",FAST:"FAST",MEDIUM:"MEDIUM",SLOW:"SLOW"},M=i.__addimage__.sHashCode=function(k){var I,H,R=0;if(typeof k=="string")for(H=k.length,I=0;I<H;I++)R=(R<<5)-R+k.charCodeAt(I),R|=0;else if(Nt(k))for(H=k.byteLength/2,I=0;I<H;I++)R=(R<<5)-R+k[I],R|=0;return R},Z=i.__addimage__.validateStringAsBase64=function(k){(k=k||"").toString().trim();var I=!0;return k.length===0&&(I=!1),k.length%4!=0&&(I=!1),/^[A-Za-z0-9+/]+$/.test(k.substr(0,k.length-2))===!1&&(I=!1),/^[A-Za-z0-9/][A-Za-z0-9+/]|[A-Za-z0-9+/]=|==$/.test(k.substr(-2))===!1&&(I=!1),I},st=i.__addimage__.extractImageFromDataUrl=function(k){var I=(k=k||"").split("base64,"),H=null;if(I.length===2){var R=/^data:(\w*\/\w*);*(charset=(?!charset=)[\w=-]*)*;*$/.exec(I[0]);Array.isArray(R)&&(H={mimeType:R[1],charset:R[2],data:I[1]})}return H},dt=i.__addimage__.supportsArrayBuffer=function(){return typeof ArrayBuffer<"u"&&typeof Uint8Array<"u"};i.__addimage__.isArrayBuffer=function(k){return dt()&&k instanceof ArrayBuffer};var Nt=i.__addimage__.isArrayBufferView=function(k){return dt()&&typeof Uint32Array<"u"&&(k instanceof Int8Array||k instanceof Uint8Array||typeof Uint8ClampedArray<"u"&&k instanceof Uint8ClampedArray||k instanceof Int16Array||k instanceof Uint16Array||k instanceof Int32Array||k instanceof Uint32Array||k instanceof Float32Array||k instanceof Float64Array)},rt=i.__addimage__.binaryStringToUint8Array=function(k){for(var I=k.length,H=new Uint8Array(I),R=0;R<I;R++)H[R]=k.charCodeAt(R);return H},G=i.__addimage__.arrayBufferToBinaryString=function(k){for(var I="",H=Nt(k)?k:new Uint8Array(k),R=0;R<H.length;R+=8192)I+=String.fromCharCode.apply(null,H.subarray(R,R+8192));return I};i.addImage=function(){var k,I,H,R,ct,ot,mt,tt,pt;if(typeof arguments[1]=="number"?(I=e,H=arguments[1],R=arguments[2],ct=arguments[3],ot=arguments[4],mt=arguments[5],tt=arguments[6],pt=arguments[7]):(I=arguments[1],H=arguments[2],R=arguments[3],ct=arguments[4],ot=arguments[5],mt=arguments[6],tt=arguments[7],pt=arguments[8]),ve(k=arguments[0])==="object"&&!A(k)&&"imageData"in k){var ft=k;k=ft.imageData,I=ft.format||I||e,H=ft.x||H||0,R=ft.y||R||0,ct=ft.w||ft.width||ct,ot=ft.h||ft.height||ot,mt=ft.alias||mt,tt=ft.compression||tt,pt=ft.rotation||ft.angle||pt}var Et=this.internal.getFilters();if(tt===void 0&&Et.indexOf("FlateEncode")!==-1&&(tt="SLOW"),isNaN(H)||isNaN(R))throw new Error("Invalid coordinates passed to jsPDF.addImage");l.call(this);var w=vt.call(this,k,I,mt,tt);return F.call(this,H,R,ct,ot,w,pt),this};var vt=function(k,I,H,R){var ct,ot,mt;if(typeof k=="string"&&a(k)===e){k=unescape(k);var tt=bt(k,!1);(tt!==""||(tt=i.loadFile(k,!0))!==void 0)&&(k=tt)}if(A(k)&&(k=_(k,I)),I=a(k,I),!x(I))throw new Error("addImage does not support files of type '"+I+"', please ensure that a plugin for '"+I+"' support is added.");if(((mt=H)==null||mt.length===0)&&(H=function(pt){return typeof pt=="string"||Nt(pt)?M(pt):Nt(pt.data)?M(pt.data):null}(k)),(ct=p.call(this,H))||(dt()&&(k instanceof Uint8Array||I==="RGBA"||(ot=k,k=rt(k))),ct=this["process"+I.toUpperCase()](k,v.call(this),H,function(pt){return pt&&typeof pt=="string"&&(pt=pt.toUpperCase()),pt in i.image_compression?pt:S.NONE}(R),ot)),!ct)throw new Error("An unknown error occurred whilst processing the image.");return ct},bt=i.__addimage__.convertBase64ToBinaryString=function(k,I){var H;I=typeof I!="boolean"||I;var R,ct="";if(typeof k=="string"){R=(H=st(k))!==null?H.data:k;try{ct=ea(R)}catch(ot){if(I)throw Z(R)?new Error("atob-Error in jsPDF.convertBase64ToBinaryString "+ot.message):new Error("Supplied Data is not a valid base64-String jsPDF.convertBase64ToBinaryString ")}}return ct};i.getImageProperties=function(k){var I,H,R="";if(A(k)&&(k=_(k)),typeof k=="string"&&a(k)===e&&((R=bt(k,!1))===""&&(R=i.loadFile(k)||""),k=R),H=a(k),!x(H))throw new Error("addImage does not support files of type '"+H+"', please ensure that a plugin for '"+H+"' support is added.");if(!dt()||k instanceof Uint8Array||(k=rt(k)),!(I=this["process"+H.toUpperCase()](k)))throw new Error("An unknown error occurred whilst processing the image");return I.fileType=H,I}})(Ut.API),function(i){var e=function(r){if(r!==void 0&&r!="")return!0};Ut.API.events.push(["addPage",function(r){this.internal.getPageInfo(r.pageNumber).pageContext.annotations=[]}]),i.events.push(["putPage",function(r){for(var a,u,o,h=this.internal.getCoordinateString,l=this.internal.getVerticalCoordinateString,f=this.internal.getPageInfoByObjId(r.objId),v=r.pageContext.annotations,x=!1,A=0;A<v.length&&!x;A++)switch((a=v[A]).type){case"link":(e(a.options.url)||e(a.options.pageNumber))&&(x=!0);break;case"reference":case"text":case"freetext":x=!0}if(x!=0){this.internal.write("/Annots [");for(var _=0;_<v.length;_++){a=v[_];var p=this.internal.pdfEscape,B=this.internal.getEncryptor(r.objId);switch(a.type){case"reference":this.internal.write(" "+a.object.objId+" 0 R ");break;case"text":var F=this.internal.newAdditionalObject(),q=this.internal.newAdditionalObject(),S=this.internal.getEncryptor(F.objId),M=a.title||"Note";o="<</Type /Annot /Subtype /Text "+(u="/Rect ["+h(a.bounds.x)+" "+l(a.bounds.y+a.bounds.h)+" "+h(a.bounds.x+a.bounds.w)+" "+l(a.bounds.y)+"] ")+"/Contents ("+p(S(a.contents))+")",o+=" /Popup "+q.objId+" 0 R",o+=" /P "+f.objId+" 0 R",o+=" /T ("+p(S(M))+") >>",F.content=o;var Z=F.objId+" 0 R";o="<</Type /Annot /Subtype /Popup "+(u="/Rect ["+h(a.bounds.x+30)+" "+l(a.bounds.y+a.bounds.h)+" "+h(a.bounds.x+a.bounds.w+30)+" "+l(a.bounds.y)+"] ")+" /Parent "+Z,a.open&&(o+=" /Open true"),o+=" >>",q.content=o,this.internal.write(F.objId,"0 R",q.objId,"0 R");break;case"freetext":u="/Rect ["+h(a.bounds.x)+" "+l(a.bounds.y)+" "+h(a.bounds.x+a.bounds.w)+" "+l(a.bounds.y+a.bounds.h)+"] ";var st=a.color||"#000000";o="<</Type /Annot /Subtype /FreeText "+u+"/Contents ("+p(B(a.contents))+")",o+=" /DS(font: Helvetica,sans-serif 12.0pt; text-align:left; color:#"+st+")",o+=" /Border [0 0 0]",o+=" >>",this.internal.write(o);break;case"link":if(a.options.name){var dt=this.annotations._nameMap[a.options.name];a.options.pageNumber=dt.page,a.options.top=dt.y}else a.options.top||(a.options.top=0);if(u="/Rect ["+a.finalBounds.x+" "+a.finalBounds.y+" "+a.finalBounds.w+" "+a.finalBounds.h+"] ",o="",a.options.url)o="<</Type /Annot /Subtype /Link "+u+"/Border [0 0 0] /A <</S /URI /URI ("+p(B(a.options.url))+") >>";else if(a.options.pageNumber)switch(o="<</Type /Annot /Subtype /Link "+u+"/Border [0 0 0] /Dest ["+this.internal.getPageInfo(a.options.pageNumber).objId+" 0 R",a.options.magFactor=a.options.magFactor||"XYZ",a.options.magFactor){case"Fit":o+=" /Fit]";break;case"FitH":o+=" /FitH "+a.options.top+"]";break;case"FitV":a.options.left=a.options.left||0,o+=" /FitV "+a.options.left+"]";break;case"XYZ":default:var Nt=l(a.options.top);a.options.left=a.options.left||0,a.options.zoom===void 0&&(a.options.zoom=0),o+=" /XYZ "+a.options.left+" "+Nt+" "+a.options.zoom+"]"}o!=""&&(o+=" >>",this.internal.write(o))}}this.internal.write("]")}}]),i.createAnnotation=function(r){var a=this.internal.getCurrentPageInfo();switch(r.type){case"link":this.link(r.bounds.x,r.bounds.y,r.bounds.w,r.bounds.h,r);break;case"text":case"freetext":a.pageContext.annotations.push(r)}},i.link=function(r,a,u,o,h){var l=this.internal.getCurrentPageInfo(),f=this.internal.getCoordinateString,v=this.internal.getVerticalCoordinateString;l.pageContext.annotations.push({finalBounds:{x:f(r),y:v(a),w:f(r+u),h:v(a+o)},options:h,type:"link"})},i.textWithLink=function(r,a,u,o){var h,l,f=this.getTextWidth(r),v=this.internal.getLineHeight()/this.internal.scaleFactor;if(o.maxWidth!==void 0){l=o.maxWidth;var x=this.splitTextToSize(r,l).length;h=Math.ceil(v*x)}else l=f,h=v;return this.text(r,a,u,o),u+=.2*v,o.align==="center"&&(a-=f/2),o.align==="right"&&(a-=f),this.link(a,u-v,l,h,o),f},i.getTextWidth=function(r){var a=this.internal.getFontSize();return this.getStringUnitWidth(r)*a/this.internal.scaleFactor}}(Ut.API),function(i){var e={1569:[65152],1570:[65153,65154],1571:[65155,65156],1572:[65157,65158],1573:[65159,65160],1574:[65161,65162,65163,65164],1575:[65165,65166],1576:[65167,65168,65169,65170],1577:[65171,65172],1578:[65173,65174,65175,65176],1579:[65177,65178,65179,65180],1580:[65181,65182,65183,65184],1581:[65185,65186,65187,65188],1582:[65189,65190,65191,65192],1583:[65193,65194],1584:[65195,65196],1585:[65197,65198],1586:[65199,65200],1587:[65201,65202,65203,65204],1588:[65205,65206,65207,65208],1589:[65209,65210,65211,65212],1590:[65213,65214,65215,65216],1591:[65217,65218,65219,65220],1592:[65221,65222,65223,65224],1593:[65225,65226,65227,65228],1594:[65229,65230,65231,65232],1601:[65233,65234,65235,65236],1602:[65237,65238,65239,65240],1603:[65241,65242,65243,65244],1604:[65245,65246,65247,65248],1605:[65249,65250,65251,65252],1606:[65253,65254,65255,65256],1607:[65257,65258,65259,65260],1608:[65261,65262],1609:[65263,65264,64488,64489],1610:[65265,65266,65267,65268],1649:[64336,64337],1655:[64477],1657:[64358,64359,64360,64361],1658:[64350,64351,64352,64353],1659:[64338,64339,64340,64341],1662:[64342,64343,64344,64345],1663:[64354,64355,64356,64357],1664:[64346,64347,64348,64349],1667:[64374,64375,64376,64377],1668:[64370,64371,64372,64373],1670:[64378,64379,64380,64381],1671:[64382,64383,64384,64385],1672:[64392,64393],1676:[64388,64389],1677:[64386,64387],1678:[64390,64391],1681:[64396,64397],1688:[64394,64395],1700:[64362,64363,64364,64365],1702:[64366,64367,64368,64369],1705:[64398,64399,64400,64401],1709:[64467,64468,64469,64470],1711:[64402,64403,64404,64405],1713:[64410,64411,64412,64413],1715:[64406,64407,64408,64409],1722:[64414,64415],1723:[64416,64417,64418,64419],1726:[64426,64427,64428,64429],1728:[64420,64421],1729:[64422,64423,64424,64425],1733:[64480,64481],1734:[64473,64474],1735:[64471,64472],1736:[64475,64476],1737:[64482,64483],1739:[64478,64479],1740:[64508,64509,64510,64511],1744:[64484,64485,64486,64487],1746:[64430,64431],1747:[64432,64433]},r={65247:{65154:65269,65156:65271,65160:65273,65166:65275},65248:{65154:65270,65156:65272,65160:65274,65166:65276},65165:{65247:{65248:{65258:65010}}},1617:{1612:64606,1613:64607,1614:64608,1615:64609,1616:64610}},a={1612:64606,1613:64607,1614:64608,1615:64609,1616:64610},u=[1570,1571,1573,1575];i.__arabicParser__={};var o=i.__arabicParser__.isInArabicSubstitutionA=function(F){return e[F.charCodeAt(0)]!==void 0},h=i.__arabicParser__.isArabicLetter=function(F){return typeof F=="string"&&/^[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]+$/.test(F)},l=i.__arabicParser__.isArabicEndLetter=function(F){return h(F)&&o(F)&&e[F.charCodeAt(0)].length<=2},f=i.__arabicParser__.isArabicAlfLetter=function(F){return h(F)&&u.indexOf(F.charCodeAt(0))>=0};i.__arabicParser__.arabicLetterHasIsolatedForm=function(F){return h(F)&&o(F)&&e[F.charCodeAt(0)].length>=1};var v=i.__arabicParser__.arabicLetterHasFinalForm=function(F){return h(F)&&o(F)&&e[F.charCodeAt(0)].length>=2};i.__arabicParser__.arabicLetterHasInitialForm=function(F){return h(F)&&o(F)&&e[F.charCodeAt(0)].length>=3};var x=i.__arabicParser__.arabicLetterHasMedialForm=function(F){return h(F)&&o(F)&&e[F.charCodeAt(0)].length==4},A=i.__arabicParser__.resolveLigatures=function(F){var q=0,S=r,M="",Z=0;for(q=0;q<F.length;q+=1)S[F.charCodeAt(q)]!==void 0?(Z++,typeof(S=S[F.charCodeAt(q)])=="number"&&(M+=String.fromCharCode(S),S=r,Z=0),q===F.length-1&&(S=r,M+=F.charAt(q-(Z-1)),q-=Z-1,Z=0)):(S=r,M+=F.charAt(q-Z),q-=Z,Z=0);return M};i.__arabicParser__.isArabicDiacritic=function(F){return F!==void 0&&a[F.charCodeAt(0)]!==void 0};var _=i.__arabicParser__.getCorrectForm=function(F,q,S){return h(F)?o(F)===!1?-1:!v(F)||!h(q)&&!h(S)||!h(S)&&l(q)||l(F)&&!h(q)||l(F)&&f(q)||l(F)&&l(q)?0:x(F)&&h(q)&&!l(q)&&h(S)&&v(S)?3:l(F)||!h(S)?1:2:-1},p=function(F){var q=0,S=0,M=0,Z="",st="",dt="",Nt=(F=F||"").split("\\s+"),rt=[];for(q=0;q<Nt.length;q+=1){for(rt.push(""),S=0;S<Nt[q].length;S+=1)Z=Nt[q][S],st=Nt[q][S-1],dt=Nt[q][S+1],h(Z)?(M=_(Z,st,dt),rt[q]+=M!==-1?String.fromCharCode(e[Z.charCodeAt(0)][M]):Z):rt[q]+=Z;rt[q]=A(rt[q])}return rt.join(" ")},B=i.__arabicParser__.processArabic=i.processArabic=function(){var F,q=typeof arguments[0]=="string"?arguments[0]:arguments[0].text,S=[];if(Array.isArray(q)){var M=0;for(S=[],M=0;M<q.length;M+=1)Array.isArray(q[M])?S.push([p(q[M][0]),q[M][1],q[M][2]]):S.push([p(q[M])]);F=S}else F=p(q);return typeof arguments[0]=="string"?F:(arguments[0].text=F,arguments[0])};i.events.push(["preProcessText",B])}(Ut.API),Ut.API.autoPrint=function(i){var e;switch((i=i||{}).variant=i.variant||"non-conform",i.variant){case"javascript":this.addJS("print({});");break;case"non-conform":default:this.internal.events.subscribe("postPutResources",function(){e=this.internal.newObject(),this.internal.out("<<"),this.internal.out("/S /Named"),this.internal.out("/Type /Action"),this.internal.out("/N /Print"),this.internal.out(">>"),this.internal.out("endobj")}),this.internal.events.subscribe("putCatalog",function(){this.internal.out("/OpenAction "+e+" 0 R")})}return this},function(i){var e=function(){var r=void 0;Object.defineProperty(this,"pdf",{get:function(){return r},set:function(l){r=l}});var a=150;Object.defineProperty(this,"width",{get:function(){return a},set:function(l){a=isNaN(l)||Number.isInteger(l)===!1||l<0?150:l,this.getContext("2d").pageWrapXEnabled&&(this.getContext("2d").pageWrapX=a+1)}});var u=300;Object.defineProperty(this,"height",{get:function(){return u},set:function(l){u=isNaN(l)||Number.isInteger(l)===!1||l<0?300:l,this.getContext("2d").pageWrapYEnabled&&(this.getContext("2d").pageWrapY=u+1)}});var o=[];Object.defineProperty(this,"childNodes",{get:function(){return o},set:function(l){o=l}});var h={};Object.defineProperty(this,"style",{get:function(){return h},set:function(l){h=l}}),Object.defineProperty(this,"parentNode",{})};e.prototype.getContext=function(r,a){var u;if((r=r||"2d")!=="2d")return null;for(u in a)this.pdf.context2d.hasOwnProperty(u)&&(this.pdf.context2d[u]=a[u]);return this.pdf.context2d._canvas=this,this.pdf.context2d},e.prototype.toDataURL=function(){throw new Error("toDataURL is not implemented.")},i.events.push(["initialized",function(){this.canvas=new e,this.canvas.pdf=this}])}(Ut.API),function(i){var e={left:0,top:0,bottom:0,right:0},r=!1,a=function(){this.internal.__cell__===void 0&&(this.internal.__cell__={},this.internal.__cell__.padding=3,this.internal.__cell__.headerFunction=void 0,this.internal.__cell__.margins=Object.assign({},e),this.internal.__cell__.margins.width=this.getPageWidth(),u.call(this))},u=function(){this.internal.__cell__.lastCell=new o,this.internal.__cell__.pages=1},o=function(){var f=arguments[0];Object.defineProperty(this,"x",{enumerable:!0,get:function(){return f},set:function(F){f=F}});var v=arguments[1];Object.defineProperty(this,"y",{enumerable:!0,get:function(){return v},set:function(F){v=F}});var x=arguments[2];Object.defineProperty(this,"width",{enumerable:!0,get:function(){return x},set:function(F){x=F}});var A=arguments[3];Object.defineProperty(this,"height",{enumerable:!0,get:function(){return A},set:function(F){A=F}});var _=arguments[4];Object.defineProperty(this,"text",{enumerable:!0,get:function(){return _},set:function(F){_=F}});var p=arguments[5];Object.defineProperty(this,"lineNumber",{enumerable:!0,get:function(){return p},set:function(F){p=F}});var B=arguments[6];return Object.defineProperty(this,"align",{enumerable:!0,get:function(){return B},set:function(F){B=F}}),this};o.prototype.clone=function(){return new o(this.x,this.y,this.width,this.height,this.text,this.lineNumber,this.align)},o.prototype.toArray=function(){return[this.x,this.y,this.width,this.height,this.text,this.lineNumber,this.align]},i.setHeaderFunction=function(f){return a.call(this),this.internal.__cell__.headerFunction=typeof f=="function"?f:void 0,this},i.getTextDimensions=function(f,v){a.call(this);var x=(v=v||{}).fontSize||this.getFontSize(),A=v.font||this.getFont(),_=v.scaleFactor||this.internal.scaleFactor,p=0,B=0,F=0,q=this;if(!Array.isArray(f)&&typeof f!="string"){if(typeof f!="number")throw new Error("getTextDimensions expects text-parameter to be of type String or type Number or an Array of Strings.");f=String(f)}var S=v.maxWidth;S>0?typeof f=="string"?f=this.splitTextToSize(f,S):Object.prototype.toString.call(f)==="[object Array]"&&(f=f.reduce(function(Z,st){return Z.concat(q.splitTextToSize(st,S))},[])):f=Array.isArray(f)?f:[f];for(var M=0;M<f.length;M++)p<(F=this.getStringUnitWidth(f[M],{font:A})*x)&&(p=F);return p!==0&&(B=f.length),{w:p/=_,h:Math.max((B*x*this.getLineHeightFactor()-x*(this.getLineHeightFactor()-1))/_,0)}},i.cellAddPage=function(){a.call(this),this.addPage();var f=this.internal.__cell__.margins||e;return this.internal.__cell__.lastCell=new o(f.left,f.top,void 0,void 0),this.internal.__cell__.pages+=1,this};var h=i.cell=function(){var f;f=arguments[0]instanceof o?arguments[0]:new o(arguments[0],arguments[1],arguments[2],arguments[3],arguments[4],arguments[5]),a.call(this);var v=this.internal.__cell__.lastCell,x=this.internal.__cell__.padding,A=this.internal.__cell__.margins||e,_=this.internal.__cell__.tableHeaderRow,p=this.internal.__cell__.printHeaders;return v.lineNumber!==void 0&&(v.lineNumber===f.lineNumber?(f.x=(v.x||0)+(v.width||0),f.y=v.y||0):v.y+v.height+f.height+A.bottom>this.getPageHeight()?(this.cellAddPage(),f.y=A.top,p&&_&&(this.printHeaderRow(f.lineNumber,!0),f.y+=_[0].height)):f.y=v.y+v.height||f.y),f.text[0]!==void 0&&(this.rect(f.x,f.y,f.width,f.height,r===!0?"FD":void 0),f.align==="right"?this.text(f.text,f.x+f.width-x,f.y+x,{align:"right",baseline:"top"}):f.align==="center"?this.text(f.text,f.x+f.width/2,f.y+x,{align:"center",baseline:"top",maxWidth:f.width-x-x}):this.text(f.text,f.x+x,f.y+x,{align:"left",baseline:"top",maxWidth:f.width-x-x})),this.internal.__cell__.lastCell=f,this};i.table=function(f,v,x,A,_){if(a.call(this),!x)throw new Error("No data for PDF table.");var p,B,F,q,S=[],M=[],Z=[],st={},dt={},Nt=[],rt=[],G=(_=_||{}).autoSize||!1,vt=_.printHeaders!==!1,bt=_.css&&_.css["font-size"]!==void 0?16*_.css["font-size"]:_.fontSize||12,k=_.margins||Object.assign({width:this.getPageWidth()},e),I=typeof _.padding=="number"?_.padding:3,H=_.headerBackgroundColor||"#c8c8c8",R=_.headerTextColor||"#000";if(u.call(this),this.internal.__cell__.printHeaders=vt,this.internal.__cell__.margins=k,this.internal.__cell__.table_font_size=bt,this.internal.__cell__.padding=I,this.internal.__cell__.headerBackgroundColor=H,this.internal.__cell__.headerTextColor=R,this.setFontSize(bt),A==null)M=S=Object.keys(x[0]),Z=S.map(function(){return"left"});else if(Array.isArray(A)&&ve(A[0])==="object")for(S=A.map(function(ft){return ft.name}),M=A.map(function(ft){return ft.prompt||ft.name||""}),Z=A.map(function(ft){return ft.align||"left"}),p=0;p<A.length;p+=1)dt[A[p].name]=A[p].width*(19.049976/25.4);else Array.isArray(A)&&typeof A[0]=="string"&&(M=S=A,Z=S.map(function(){return"left"}));if(G||Array.isArray(A)&&typeof A[0]=="string")for(p=0;p<S.length;p+=1){for(st[q=S[p]]=x.map(function(ft){return ft[q]}),this.setFont(void 0,"bold"),Nt.push(this.getTextDimensions(M[p],{fontSize:this.internal.__cell__.table_font_size,scaleFactor:this.internal.scaleFactor}).w),B=st[q],this.setFont(void 0,"normal"),F=0;F<B.length;F+=1)Nt.push(this.getTextDimensions(B[F],{fontSize:this.internal.__cell__.table_font_size,scaleFactor:this.internal.scaleFactor}).w);dt[q]=Math.max.apply(null,Nt)+I+I,Nt=[]}if(vt){var ct={};for(p=0;p<S.length;p+=1)ct[S[p]]={},ct[S[p]].text=M[p],ct[S[p]].align=Z[p];var ot=l.call(this,ct,dt);rt=S.map(function(ft){return new o(f,v,dt[ft],ot,ct[ft].text,void 0,ct[ft].align)}),this.setTableHeaderRow(rt),this.printHeaderRow(1,!1)}var mt=A.reduce(function(ft,Et){return ft[Et.name]=Et.align,ft},{});for(p=0;p<x.length;p+=1){"rowStart"in _&&_.rowStart instanceof Function&&_.rowStart({row:p,data:x[p]},this);var tt=l.call(this,x[p],dt);for(F=0;F<S.length;F+=1){var pt=x[p][S[F]];"cellStart"in _&&_.cellStart instanceof Function&&_.cellStart({row:p,col:F,data:pt},this),h.call(this,new o(f,v,dt[S[F]],tt,pt,p+2,mt[S[F]]))}}return this.internal.__cell__.table_x=f,this.internal.__cell__.table_y=v,this};var l=function(f,v){var x=this.internal.__cell__.padding,A=this.internal.__cell__.table_font_size,_=this.internal.scaleFactor;return Object.keys(f).map(function(p){var B=f[p];return this.splitTextToSize(B.hasOwnProperty("text")?B.text:B,v[p]-x-x)},this).map(function(p){return this.getLineHeightFactor()*p.length*A/_+x+x},this).reduce(function(p,B){return Math.max(p,B)},0)};i.setTableHeaderRow=function(f){a.call(this),this.internal.__cell__.tableHeaderRow=f},i.printHeaderRow=function(f,v){if(a.call(this),!this.internal.__cell__.tableHeaderRow)throw new Error("Property tableHeaderRow does not exist.");var x;if(r=!0,typeof this.internal.__cell__.headerFunction=="function"){var A=this.internal.__cell__.headerFunction(this,this.internal.__cell__.pages);this.internal.__cell__.lastCell=new o(A[0],A[1],A[2],A[3],void 0,-1)}this.setFont(void 0,"bold");for(var _=[],p=0;p<this.internal.__cell__.tableHeaderRow.length;p+=1){x=this.internal.__cell__.tableHeaderRow[p].clone(),v&&(x.y=this.internal.__cell__.margins.top||0,_.push(x)),x.lineNumber=f;var B=this.getTextColor();this.setTextColor(this.internal.__cell__.headerTextColor),this.setFillColor(this.internal.__cell__.headerBackgroundColor),h.call(this,x),this.setTextColor(B)}_.length>0&&this.setTableHeaderRow(_),this.setFont(void 0,"normal"),r=!1}}(Ut.API);var ac={italic:["italic","oblique","normal"],oblique:["oblique","italic","normal"],normal:["normal","oblique","italic"]},oc=["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded"],is=ic(oc),sc=[100,200,300,400,500,600,700,800,900],cu=ic(sc);function as(i){var e=i.family.replace(/"|'/g,"").toLowerCase(),r=function(o){return ac[o=o||"normal"]?o:"normal"}(i.style),a=function(o){if(!o)return 400;if(typeof o=="number")return o>=100&&o<=900&&o%100==0?o:400;if(/^\d00$/.test(o))return parseInt(o);switch(o){case"bold":return 700;case"normal":default:return 400}}(i.weight),u=function(o){return typeof is[o=o||"normal"]=="number"?o:"normal"}(i.stretch);return{family:e,style:r,weight:a,stretch:u,src:i.src||[],ref:i.ref||{name:e,style:[u,r,a].join(" ")}}}function Ts(i,e,r,a){var u;for(u=r;u>=0&&u<e.length;u+=a)if(i[e[u]])return i[e[u]];for(u=r;u>=0&&u<e.length;u-=a)if(i[e[u]])return i[e[u]]}var uu={"sans-serif":"helvetica",fixed:"courier",monospace:"courier",terminal:"courier",cursive:"times",fantasy:"times",serif:"times"},zs={caption:"times",icon:"times",menu:"times","message-box":"times","small-caption":"times","status-bar":"times"};function Us(i){return[i.stretch,i.style,i.weight,i.family].join(" ")}function hu(i,e,r){for(var a=(r=r||{}).defaultFontFamily||"times",u=Object.assign({},uu,r.genericFontFamilies||{}),o=null,h=null,l=0;l<e.length;++l)if(u[(o=as(e[l])).family]&&(o.family=u[o.family]),i.hasOwnProperty(o.family)){h=i[o.family];break}if(!(h=h||i[a]))throw new Error("Could not find a font-family for the rule '"+Us(o)+"' and default family '"+a+"'.");if(h=function(f,v){if(v[f])return v[f];var x=is[f],A=x<=is.normal?-1:1,_=Ts(v,oc,x,A);if(!_)throw new Error("Could not find a matching font-stretch value for "+f);return _}(o.stretch,h),h=function(f,v){if(v[f])return v[f];for(var x=ac[f],A=0;A<x.length;++A)if(v[x[A]])return v[x[A]];throw new Error("Could not find a matching font-style for "+f)}(o.style,h),!(h=function(f,v){if(v[f])return v[f];if(f===400&&v[500])return v[500];if(f===500&&v[400])return v[400];var x=cu[f],A=Ts(v,sc,x,f<400?-1:1);if(!A)throw new Error("Could not find a matching font-weight for value "+f);return A}(o.weight,h)))throw new Error("Failed to resolve a font for the rule '"+Us(o)+"'.");return h}function Hs(i){return i.trimLeft()}function lu(i,e){for(var r=0;r<i.length;){if(i.charAt(r)===e)return[i.substring(0,r),i.substring(r+1)];r+=1}return null}function fu(i){var e=i.match(/^(-[a-z_]|[a-z_])[a-z0-9_-]*/i);return e===null?null:[e[0],i.substring(e[0].length)]}var Ya,Ws,Vs,Ko=["times"];(function(i){var e,r,a,u,o,h,l,f,v,x=function(w){return w=w||{},this.isStrokeTransparent=w.isStrokeTransparent||!1,this.strokeOpacity=w.strokeOpacity||1,this.strokeStyle=w.strokeStyle||"#000000",this.fillStyle=w.fillStyle||"#000000",this.isFillTransparent=w.isFillTransparent||!1,this.fillOpacity=w.fillOpacity||1,this.font=w.font||"10px sans-serif",this.textBaseline=w.textBaseline||"alphabetic",this.textAlign=w.textAlign||"left",this.lineWidth=w.lineWidth||1,this.lineJoin=w.lineJoin||"miter",this.lineCap=w.lineCap||"butt",this.path=w.path||[],this.transform=w.transform!==void 0?w.transform.clone():new f,this.globalCompositeOperation=w.globalCompositeOperation||"normal",this.globalAlpha=w.globalAlpha||1,this.clip_path=w.clip_path||[],this.currentPoint=w.currentPoint||new h,this.miterLimit=w.miterLimit||10,this.lastPoint=w.lastPoint||new h,this.lineDashOffset=w.lineDashOffset||0,this.lineDash=w.lineDash||[],this.margin=w.margin||[0,0,0,0],this.prevPageLastElemOffset=w.prevPageLastElemOffset||0,this.ignoreClearRect=typeof w.ignoreClearRect!="boolean"||w.ignoreClearRect,this};i.events.push(["initialized",function(){this.context2d=new A(this),e=this.internal.f2,r=this.internal.getCoordinateString,a=this.internal.getVerticalCoordinateString,u=this.internal.getHorizontalCoordinate,o=this.internal.getVerticalCoordinate,h=this.internal.Point,l=this.internal.Rectangle,f=this.internal.Matrix,v=new x}]);var A=function(w){Object.defineProperty(this,"canvas",{get:function(){return{parentNode:!1,style:!1}}});var C=w;Object.defineProperty(this,"pdf",{get:function(){return C}});var E=!1;Object.defineProperty(this,"pageWrapXEnabled",{get:function(){return E},set:function(at){E=!!at}});var W=!1;Object.defineProperty(this,"pageWrapYEnabled",{get:function(){return W},set:function(at){W=!!at}});var J=0;Object.defineProperty(this,"posX",{get:function(){return J},set:function(at){isNaN(at)||(J=at)}});var $=0;Object.defineProperty(this,"posY",{get:function(){return $},set:function(at){isNaN(at)||($=at)}}),Object.defineProperty(this,"margin",{get:function(){return v.margin},set:function(at){var O;typeof at=="number"?O=[at,at,at,at]:((O=new Array(4))[0]=at[0],O[1]=at.length>=2?at[1]:O[0],O[2]=at.length>=3?at[2]:O[0],O[3]=at.length>=4?at[3]:O[1]),v.margin=O}});var et=!1;Object.defineProperty(this,"autoPaging",{get:function(){return et},set:function(at){et=at}});var Q=0;Object.defineProperty(this,"lastBreak",{get:function(){return Q},set:function(at){Q=at}});var At=[];Object.defineProperty(this,"pageBreaks",{get:function(){return At},set:function(at){At=at}}),Object.defineProperty(this,"ctx",{get:function(){return v},set:function(at){at instanceof x&&(v=at)}}),Object.defineProperty(this,"path",{get:function(){return v.path},set:function(at){v.path=at}});var Lt=[];Object.defineProperty(this,"ctxStack",{get:function(){return Lt},set:function(at){Lt=at}}),Object.defineProperty(this,"fillStyle",{get:function(){return this.ctx.fillStyle},set:function(at){var O;O=_(at),this.ctx.fillStyle=O.style,this.ctx.isFillTransparent=O.a===0,this.ctx.fillOpacity=O.a,this.pdf.setFillColor(O.r,O.g,O.b,{a:O.a}),this.pdf.setTextColor(O.r,O.g,O.b,{a:O.a})}}),Object.defineProperty(this,"strokeStyle",{get:function(){return this.ctx.strokeStyle},set:function(at){var O=_(at);this.ctx.strokeStyle=O.style,this.ctx.isStrokeTransparent=O.a===0,this.ctx.strokeOpacity=O.a,O.a===0?this.pdf.setDrawColor(255,255,255):(O.a,this.pdf.setDrawColor(O.r,O.g,O.b))}}),Object.defineProperty(this,"lineCap",{get:function(){return this.ctx.lineCap},set:function(at){["butt","round","square"].indexOf(at)!==-1&&(this.ctx.lineCap=at,this.pdf.setLineCap(at))}}),Object.defineProperty(this,"lineWidth",{get:function(){return this.ctx.lineWidth},set:function(at){isNaN(at)||(this.ctx.lineWidth=at,this.pdf.setLineWidth(at))}}),Object.defineProperty(this,"lineJoin",{get:function(){return this.ctx.lineJoin},set:function(at){["bevel","round","miter"].indexOf(at)!==-1&&(this.ctx.lineJoin=at,this.pdf.setLineJoin(at))}}),Object.defineProperty(this,"miterLimit",{get:function(){return this.ctx.miterLimit},set:function(at){isNaN(at)||(this.ctx.miterLimit=at,this.pdf.setMiterLimit(at))}}),Object.defineProperty(this,"textBaseline",{get:function(){return this.ctx.textBaseline},set:function(at){this.ctx.textBaseline=at}}),Object.defineProperty(this,"textAlign",{get:function(){return this.ctx.textAlign},set:function(at){["right","end","center","left","start"].indexOf(at)!==-1&&(this.ctx.textAlign=at)}});var Ot=null;function Ct(at,O){if(Ot===null){var Kt=function(Mt){var wt=[];return Object.keys(Mt).forEach(function(xt){Mt[xt].forEach(function(kt){var Pt=null;switch(kt){case"bold":Pt={family:xt,weight:"bold"};break;case"italic":Pt={family:xt,style:"italic"};break;case"bolditalic":Pt={family:xt,weight:"bold",style:"italic"};break;case"":case"normal":Pt={family:xt}}Pt!==null&&(Pt.ref={name:xt,style:kt},wt.push(Pt))})}),wt}(at.getFontList());Ot=function(Mt){for(var wt={},xt=0;xt<Mt.length;++xt){var kt=as(Mt[xt]),Pt=kt.family,qt=kt.stretch,Gt=kt.style,Qt=kt.weight;wt[Pt]=wt[Pt]||{},wt[Pt][qt]=wt[Pt][qt]||{},wt[Pt][qt][Gt]=wt[Pt][qt][Gt]||{},wt[Pt][qt][Gt][Qt]=kt}return wt}(Kt.concat(O))}return Ot}var Wt=null;Object.defineProperty(this,"fontFaces",{get:function(){return Wt},set:function(at){Ot=null,Wt=at}}),Object.defineProperty(this,"font",{get:function(){return this.ctx.font},set:function(at){var O;if(this.ctx.font=at,(O=/^\s*(?=(?:(?:[-a-z]+\s*){0,2}(italic|oblique))?)(?=(?:(?:[-a-z]+\s*){0,2}(small-caps))?)(?=(?:(?:[-a-z]+\s*){0,2}(bold(?:er)?|lighter|[1-9]00))?)(?:(?:normal|\1|\2|\3)\s*){0,3}((?:xx?-)?(?:small|large)|medium|smaller|larger|[.\d]+(?:\%|in|[cem]m|ex|p[ctx]))(?:\s*\/\s*(normal|[.\d]+(?:\%|in|[cem]m|ex|p[ctx])))?\s*([-_,\"\'\sa-z]+?)\s*$/i.exec(at))!==null){var Kt=O[1],Mt=(O[2],O[3]),wt=O[4],xt=(O[5],O[6]),kt=/^([.\d]+)((?:%|in|[cem]m|ex|p[ctx]))$/i.exec(wt)[2];wt=Math.floor(kt==="px"?parseFloat(wt)*this.pdf.internal.scaleFactor:kt==="em"?parseFloat(wt)*this.pdf.getFontSize():parseFloat(wt)*this.pdf.internal.scaleFactor),this.pdf.setFontSize(wt);var Pt=function(Ht){var ee,It,Ge=[],oe=Ht.trim();if(oe==="")return Ko;if(oe in zs)return[zs[oe]];for(;oe!=="";){switch(It=null,ee=(oe=Hs(oe)).charAt(0)){case'"':case"'":It=lu(oe.substring(1),ee);break;default:It=fu(oe)}if(It===null||(Ge.push(It[0]),(oe=Hs(It[1]))!==""&&oe.charAt(0)!==","))return Ko;oe=oe.replace(/^,/,"")}return Ge}(xt);if(this.fontFaces){var qt=hu(Ct(this.pdf,this.fontFaces),Pt.map(function(Ht){return{family:Ht,stretch:"normal",weight:Mt,style:Kt}}));this.pdf.setFont(qt.ref.name,qt.ref.style)}else{var Gt="";(Mt==="bold"||parseInt(Mt,10)>=700||Kt==="bold")&&(Gt="bold"),Kt==="italic"&&(Gt+="italic"),Gt.length===0&&(Gt="normal");for(var Qt="",te={arial:"Helvetica",Arial:"Helvetica",verdana:"Helvetica",Verdana:"Helvetica",helvetica:"Helvetica",Helvetica:"Helvetica","sans-serif":"Helvetica",fixed:"Courier",monospace:"Courier",terminal:"Courier",cursive:"Times",fantasy:"Times",serif:"Times"},ie=0;ie<Pt.length;ie++){if(this.pdf.internal.getFont(Pt[ie],Gt,{noFallback:!0,disableWarning:!0})!==void 0){Qt=Pt[ie];break}if(Gt==="bolditalic"&&this.pdf.internal.getFont(Pt[ie],"bold",{noFallback:!0,disableWarning:!0})!==void 0)Qt=Pt[ie],Gt="bold";else if(this.pdf.internal.getFont(Pt[ie],"normal",{noFallback:!0,disableWarning:!0})!==void 0){Qt=Pt[ie],Gt="normal";break}}if(Qt===""){for(var fe=0;fe<Pt.length;fe++)if(te[Pt[fe]]){Qt=te[Pt[fe]];break}}Qt=Qt===""?"Times":Qt,this.pdf.setFont(Qt,Gt)}}}}),Object.defineProperty(this,"globalCompositeOperation",{get:function(){return this.ctx.globalCompositeOperation},set:function(at){this.ctx.globalCompositeOperation=at}}),Object.defineProperty(this,"globalAlpha",{get:function(){return this.ctx.globalAlpha},set:function(at){this.ctx.globalAlpha=at}}),Object.defineProperty(this,"lineDashOffset",{get:function(){return this.ctx.lineDashOffset},set:function(at){this.ctx.lineDashOffset=at,Et.call(this)}}),Object.defineProperty(this,"lineDash",{get:function(){return this.ctx.lineDash},set:function(at){this.ctx.lineDash=at,Et.call(this)}}),Object.defineProperty(this,"ignoreClearRect",{get:function(){return this.ctx.ignoreClearRect},set:function(at){this.ctx.ignoreClearRect=!!at}})};A.prototype.setLineDash=function(w){this.lineDash=w},A.prototype.getLineDash=function(){return this.lineDash.length%2?this.lineDash.concat(this.lineDash):this.lineDash.slice()},A.prototype.fill=function(){st.call(this,"fill",!1)},A.prototype.stroke=function(){st.call(this,"stroke",!1)},A.prototype.beginPath=function(){this.path=[{type:"begin"}]},A.prototype.moveTo=function(w,C){if(isNaN(w)||isNaN(C))throw me.error("jsPDF.context2d.moveTo: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.moveTo");var E=this.ctx.transform.applyToPoint(new h(w,C));this.path.push({type:"mt",x:E.x,y:E.y}),this.ctx.lastPoint=new h(w,C)},A.prototype.closePath=function(){var w=new h(0,0),C=0;for(C=this.path.length-1;C!==-1;C--)if(this.path[C].type==="begin"&&ve(this.path[C+1])==="object"&&typeof this.path[C+1].x=="number"){w=new h(this.path[C+1].x,this.path[C+1].y);break}this.path.push({type:"close"}),this.ctx.lastPoint=new h(w.x,w.y)},A.prototype.lineTo=function(w,C){if(isNaN(w)||isNaN(C))throw me.error("jsPDF.context2d.lineTo: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.lineTo");var E=this.ctx.transform.applyToPoint(new h(w,C));this.path.push({type:"lt",x:E.x,y:E.y}),this.ctx.lastPoint=new h(E.x,E.y)},A.prototype.clip=function(){this.ctx.clip_path=JSON.parse(JSON.stringify(this.path)),st.call(this,null,!0)},A.prototype.quadraticCurveTo=function(w,C,E,W){if(isNaN(E)||isNaN(W)||isNaN(w)||isNaN(C))throw me.error("jsPDF.context2d.quadraticCurveTo: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.quadraticCurveTo");var J=this.ctx.transform.applyToPoint(new h(E,W)),$=this.ctx.transform.applyToPoint(new h(w,C));this.path.push({type:"qct",x1:$.x,y1:$.y,x:J.x,y:J.y}),this.ctx.lastPoint=new h(J.x,J.y)},A.prototype.bezierCurveTo=function(w,C,E,W,J,$){if(isNaN(J)||isNaN($)||isNaN(w)||isNaN(C)||isNaN(E)||isNaN(W))throw me.error("jsPDF.context2d.bezierCurveTo: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.bezierCurveTo");var et=this.ctx.transform.applyToPoint(new h(J,$)),Q=this.ctx.transform.applyToPoint(new h(w,C)),At=this.ctx.transform.applyToPoint(new h(E,W));this.path.push({type:"bct",x1:Q.x,y1:Q.y,x2:At.x,y2:At.y,x:et.x,y:et.y}),this.ctx.lastPoint=new h(et.x,et.y)},A.prototype.arc=function(w,C,E,W,J,$){if(isNaN(w)||isNaN(C)||isNaN(E)||isNaN(W)||isNaN(J))throw me.error("jsPDF.context2d.arc: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.arc");if($=!!$,!this.ctx.transform.isIdentity){var et=this.ctx.transform.applyToPoint(new h(w,C));w=et.x,C=et.y;var Q=this.ctx.transform.applyToPoint(new h(0,E)),At=this.ctx.transform.applyToPoint(new h(0,0));E=Math.sqrt(Math.pow(Q.x-At.x,2)+Math.pow(Q.y-At.y,2))}Math.abs(J-W)>=2*Math.PI&&(W=0,J=2*Math.PI),this.path.push({type:"arc",x:w,y:C,radius:E,startAngle:W,endAngle:J,counterclockwise:$})},A.prototype.arcTo=function(w,C,E,W,J){throw new Error("arcTo not implemented.")},A.prototype.rect=function(w,C,E,W){if(isNaN(w)||isNaN(C)||isNaN(E)||isNaN(W))throw me.error("jsPDF.context2d.rect: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.rect");this.moveTo(w,C),this.lineTo(w+E,C),this.lineTo(w+E,C+W),this.lineTo(w,C+W),this.lineTo(w,C),this.lineTo(w+E,C),this.lineTo(w,C)},A.prototype.fillRect=function(w,C,E,W){if(isNaN(w)||isNaN(C)||isNaN(E)||isNaN(W))throw me.error("jsPDF.context2d.fillRect: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.fillRect");if(!p.call(this)){var J={};this.lineCap!=="butt"&&(J.lineCap=this.lineCap,this.lineCap="butt"),this.lineJoin!=="miter"&&(J.lineJoin=this.lineJoin,this.lineJoin="miter"),this.beginPath(),this.rect(w,C,E,W),this.fill(),J.hasOwnProperty("lineCap")&&(this.lineCap=J.lineCap),J.hasOwnProperty("lineJoin")&&(this.lineJoin=J.lineJoin)}},A.prototype.strokeRect=function(w,C,E,W){if(isNaN(w)||isNaN(C)||isNaN(E)||isNaN(W))throw me.error("jsPDF.context2d.strokeRect: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.strokeRect");B.call(this)||(this.beginPath(),this.rect(w,C,E,W),this.stroke())},A.prototype.clearRect=function(w,C,E,W){if(isNaN(w)||isNaN(C)||isNaN(E)||isNaN(W))throw me.error("jsPDF.context2d.clearRect: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.clearRect");this.ignoreClearRect||(this.fillStyle="#ffffff",this.fillRect(w,C,E,W))},A.prototype.save=function(w){w=typeof w!="boolean"||w;for(var C=this.pdf.internal.getCurrentPageInfo().pageNumber,E=0;E<this.pdf.internal.getNumberOfPages();E++)this.pdf.setPage(E+1),this.pdf.internal.out("q");if(this.pdf.setPage(C),w){this.ctx.fontSize=this.pdf.internal.getFontSize();var W=new x(this.ctx);this.ctxStack.push(this.ctx),this.ctx=W}},A.prototype.restore=function(w){w=typeof w!="boolean"||w;for(var C=this.pdf.internal.getCurrentPageInfo().pageNumber,E=0;E<this.pdf.internal.getNumberOfPages();E++)this.pdf.setPage(E+1),this.pdf.internal.out("Q");this.pdf.setPage(C),w&&this.ctxStack.length!==0&&(this.ctx=this.ctxStack.pop(),this.fillStyle=this.ctx.fillStyle,this.strokeStyle=this.ctx.strokeStyle,this.font=this.ctx.font,this.lineCap=this.ctx.lineCap,this.lineWidth=this.ctx.lineWidth,this.lineJoin=this.ctx.lineJoin,this.lineDash=this.ctx.lineDash,this.lineDashOffset=this.ctx.lineDashOffset)},A.prototype.toDataURL=function(){throw new Error("toDataUrl not implemented.")};var _=function(w){var C,E,W,J;if(w.isCanvasGradient===!0&&(w=w.getColor()),!w)return{r:0,g:0,b:0,a:0,style:w};if(/transparent|rgba\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*0+\s*\)/.test(w))C=0,E=0,W=0,J=0;else{var $=/rgb\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)/.exec(w);if($!==null)C=parseInt($[1]),E=parseInt($[2]),W=parseInt($[3]),J=1;else if(($=/rgba\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*([\d.]+)\s*\)/.exec(w))!==null)C=parseInt($[1]),E=parseInt($[2]),W=parseInt($[3]),J=parseFloat($[4]);else{if(J=1,typeof w=="string"&&w.charAt(0)!=="#"){var et=new $s(w);w=et.ok?et.toHex():"#000000"}w.length===4?(C=w.substring(1,2),C+=C,E=w.substring(2,3),E+=E,W=w.substring(3,4),W+=W):(C=w.substring(1,3),E=w.substring(3,5),W=w.substring(5,7)),C=parseInt(C,16),E=parseInt(E,16),W=parseInt(W,16)}}return{r:C,g:E,b:W,a:J,style:w}},p=function(){return this.ctx.isFillTransparent||this.globalAlpha==0},B=function(){return!!(this.ctx.isStrokeTransparent||this.globalAlpha==0)};A.prototype.fillText=function(w,C,E,W){if(isNaN(C)||isNaN(E)||typeof w!="string")throw me.error("jsPDF.context2d.fillText: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.fillText");if(W=isNaN(W)?void 0:W,!p.call(this)){var J=tt(this.ctx.transform.rotation),$=this.ctx.transform.scaleX;I.call(this,{text:w,x:C,y:E,scale:$,angle:J,align:this.textAlign,maxWidth:W})}},A.prototype.strokeText=function(w,C,E,W){if(isNaN(C)||isNaN(E)||typeof w!="string")throw me.error("jsPDF.context2d.strokeText: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.strokeText");if(!B.call(this)){W=isNaN(W)?void 0:W;var J=tt(this.ctx.transform.rotation),$=this.ctx.transform.scaleX;I.call(this,{text:w,x:C,y:E,scale:$,renderingMode:"stroke",angle:J,align:this.textAlign,maxWidth:W})}},A.prototype.measureText=function(w){if(typeof w!="string")throw me.error("jsPDF.context2d.measureText: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.measureText");var C=this.pdf,E=this.pdf.internal.scaleFactor,W=C.internal.getFontSize(),J=C.getStringUnitWidth(w)*W/C.internal.scaleFactor,$=function(et){var Q=(et=et||{}).width||0;return Object.defineProperty(this,"width",{get:function(){return Q}}),this};return new $({width:J*=Math.round(96*E/72*1e4)/1e4})},A.prototype.scale=function(w,C){if(isNaN(w)||isNaN(C))throw me.error("jsPDF.context2d.scale: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.scale");var E=new f(w,0,0,C,0,0);this.ctx.transform=this.ctx.transform.multiply(E)},A.prototype.rotate=function(w){if(isNaN(w))throw me.error("jsPDF.context2d.rotate: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.rotate");var C=new f(Math.cos(w),Math.sin(w),-Math.sin(w),Math.cos(w),0,0);this.ctx.transform=this.ctx.transform.multiply(C)},A.prototype.translate=function(w,C){if(isNaN(w)||isNaN(C))throw me.error("jsPDF.context2d.translate: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.translate");var E=new f(1,0,0,1,w,C);this.ctx.transform=this.ctx.transform.multiply(E)},A.prototype.transform=function(w,C,E,W,J,$){if(isNaN(w)||isNaN(C)||isNaN(E)||isNaN(W)||isNaN(J)||isNaN($))throw me.error("jsPDF.context2d.transform: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.transform");var et=new f(w,C,E,W,J,$);this.ctx.transform=this.ctx.transform.multiply(et)},A.prototype.setTransform=function(w,C,E,W,J,$){w=isNaN(w)?1:w,C=isNaN(C)?0:C,E=isNaN(E)?0:E,W=isNaN(W)?1:W,J=isNaN(J)?0:J,$=isNaN($)?0:$,this.ctx.transform=new f(w,C,E,W,J,$)};var F=function(){return this.margin[0]>0||this.margin[1]>0||this.margin[2]>0||this.margin[3]>0};A.prototype.drawImage=function(w,C,E,W,J,$,et,Q,At){var Lt=this.pdf.getImageProperties(w),Ot=1,Ct=1,Wt=1,at=1;W!==void 0&&Q!==void 0&&(Wt=Q/W,at=At/J,Ot=Lt.width/W*Q/W,Ct=Lt.height/J*At/J),$===void 0&&($=C,et=E,C=0,E=0),W!==void 0&&Q===void 0&&(Q=W,At=J),W===void 0&&Q===void 0&&(Q=Lt.width,At=Lt.height);for(var O,Kt=this.ctx.transform.decompose(),Mt=tt(Kt.rotate.shx),wt=new f,xt=(wt=(wt=(wt=wt.multiply(Kt.translate)).multiply(Kt.skew)).multiply(Kt.scale)).applyToRectangle(new l($-C*Wt,et-E*at,W*Ot,J*Ct)),kt=q.call(this,xt),Pt=[],qt=0;qt<kt.length;qt+=1)Pt.indexOf(kt[qt])===-1&&Pt.push(kt[qt]);if(Z(Pt),this.autoPaging)for(var Gt=Pt[0],Qt=Pt[Pt.length-1],te=Gt;te<Qt+1;te++){this.pdf.setPage(te);var ie=this.pdf.internal.pageSize.width-this.margin[3]-this.margin[1],fe=te===1?this.posY+this.margin[0]:this.margin[0],Ht=this.pdf.internal.pageSize.height-this.posY-this.margin[0]-this.margin[2],ee=this.pdf.internal.pageSize.height-this.margin[0]-this.margin[2],It=te===1?0:Ht+(te-2)*ee;if(this.ctx.clip_path.length!==0){var Ge=this.path;O=JSON.parse(JSON.stringify(this.ctx.clip_path)),this.path=M(O,this.posX+this.margin[3],-It+fe+this.ctx.prevPageLastElemOffset),dt.call(this,"fill",!0),this.path=Ge}var oe=JSON.parse(JSON.stringify(xt));oe=M([oe],this.posX+this.margin[3],-It+fe+this.ctx.prevPageLastElemOffset)[0];var An=(te>Gt||te<Qt)&&F.call(this);An&&(this.pdf.saveGraphicsState(),this.pdf.rect(this.margin[3],this.margin[0],ie,ee,null).clip().discardPath()),this.pdf.addImage(w,"JPEG",oe.x,oe.y,oe.w,oe.h,null,null,Mt),An&&this.pdf.restoreGraphicsState()}else this.pdf.addImage(w,"JPEG",xt.x,xt.y,xt.w,xt.h,null,null,Mt)};var q=function(w,C,E){var W=[];C=C||this.pdf.internal.pageSize.width,E=E||this.pdf.internal.pageSize.height-this.margin[0]-this.margin[2];var J=this.posY+this.ctx.prevPageLastElemOffset;switch(w.type){default:case"mt":case"lt":W.push(Math.floor((w.y+J)/E)+1);break;case"arc":W.push(Math.floor((w.y+J-w.radius)/E)+1),W.push(Math.floor((w.y+J+w.radius)/E)+1);break;case"qct":var $=pt(this.ctx.lastPoint.x,this.ctx.lastPoint.y,w.x1,w.y1,w.x,w.y);W.push(Math.floor(($.y+J)/E)+1),W.push(Math.floor(($.y+$.h+J)/E)+1);break;case"bct":var et=ft(this.ctx.lastPoint.x,this.ctx.lastPoint.y,w.x1,w.y1,w.x2,w.y2,w.x,w.y);W.push(Math.floor((et.y+J)/E)+1),W.push(Math.floor((et.y+et.h+J)/E)+1);break;case"rect":W.push(Math.floor((w.y+J)/E)+1),W.push(Math.floor((w.y+w.h+J)/E)+1)}for(var Q=0;Q<W.length;Q+=1)for(;this.pdf.internal.getNumberOfPages()<W[Q];)S.call(this);return W},S=function(){var w=this.fillStyle,C=this.strokeStyle,E=this.font,W=this.lineCap,J=this.lineWidth,$=this.lineJoin;this.pdf.addPage(),this.fillStyle=w,this.strokeStyle=C,this.font=E,this.lineCap=W,this.lineWidth=J,this.lineJoin=$},M=function(w,C,E){for(var W=0;W<w.length;W++)switch(w[W].type){case"bct":w[W].x2+=C,w[W].y2+=E;case"qct":w[W].x1+=C,w[W].y1+=E;case"mt":case"lt":case"arc":default:w[W].x+=C,w[W].y+=E}return w},Z=function(w){return w.sort(function(C,E){return C-E})},st=function(w,C){for(var E,W,J=this.fillStyle,$=this.strokeStyle,et=this.lineCap,Q=this.lineWidth,At=Math.abs(Q*this.ctx.transform.scaleX),Lt=this.lineJoin,Ot=JSON.parse(JSON.stringify(this.path)),Ct=JSON.parse(JSON.stringify(this.path)),Wt=[],at=0;at<Ct.length;at++)if(Ct[at].x!==void 0)for(var O=q.call(this,Ct[at]),Kt=0;Kt<O.length;Kt+=1)Wt.indexOf(O[Kt])===-1&&Wt.push(O[Kt]);for(var Mt=0;Mt<Wt.length;Mt++)for(;this.pdf.internal.getNumberOfPages()<Wt[Mt];)S.call(this);if(Z(Wt),this.autoPaging)for(var wt=Wt[0],xt=Wt[Wt.length-1],kt=wt;kt<xt+1;kt++){this.pdf.setPage(kt),this.fillStyle=J,this.strokeStyle=$,this.lineCap=et,this.lineWidth=At,this.lineJoin=Lt;var Pt=this.pdf.internal.pageSize.width-this.margin[3]-this.margin[1],qt=kt===1?this.posY+this.margin[0]:this.margin[0],Gt=this.pdf.internal.pageSize.height-this.posY-this.margin[0]-this.margin[2],Qt=this.pdf.internal.pageSize.height-this.margin[0]-this.margin[2],te=kt===1?0:Gt+(kt-2)*Qt;if(this.ctx.clip_path.length!==0){var ie=this.path;E=JSON.parse(JSON.stringify(this.ctx.clip_path)),this.path=M(E,this.posX+this.margin[3],-te+qt+this.ctx.prevPageLastElemOffset),dt.call(this,w,!0),this.path=ie}if(W=JSON.parse(JSON.stringify(Ot)),this.path=M(W,this.posX+this.margin[3],-te+qt+this.ctx.prevPageLastElemOffset),C===!1||kt===0){var fe=(kt>wt||kt<xt)&&F.call(this);fe&&(this.pdf.saveGraphicsState(),this.pdf.rect(this.margin[3],this.margin[0],Pt,Qt,null).clip().discardPath()),dt.call(this,w,C),fe&&this.pdf.restoreGraphicsState()}this.lineWidth=Q}else this.lineWidth=At,dt.call(this,w,C),this.lineWidth=Q;this.path=Ot},dt=function(w,C){if((w!=="stroke"||C||!B.call(this))&&(w==="stroke"||C||!p.call(this))){for(var E,W,J=[],$=this.path,et=0;et<$.length;et++){var Q=$[et];switch(Q.type){case"begin":J.push({begin:!0});break;case"close":J.push({close:!0});break;case"mt":J.push({start:Q,deltas:[],abs:[]});break;case"lt":var At=J.length;if($[et-1]&&!isNaN($[et-1].x)&&(E=[Q.x-$[et-1].x,Q.y-$[et-1].y],At>0)){for(;At>=0;At--)if(J[At-1].close!==!0&&J[At-1].begin!==!0){J[At-1].deltas.push(E),J[At-1].abs.push(Q);break}}break;case"bct":E=[Q.x1-$[et-1].x,Q.y1-$[et-1].y,Q.x2-$[et-1].x,Q.y2-$[et-1].y,Q.x-$[et-1].x,Q.y-$[et-1].y],J[J.length-1].deltas.push(E);break;case"qct":var Lt=$[et-1].x+2/3*(Q.x1-$[et-1].x),Ot=$[et-1].y+2/3*(Q.y1-$[et-1].y),Ct=Q.x+2/3*(Q.x1-Q.x),Wt=Q.y+2/3*(Q.y1-Q.y),at=Q.x,O=Q.y;E=[Lt-$[et-1].x,Ot-$[et-1].y,Ct-$[et-1].x,Wt-$[et-1].y,at-$[et-1].x,O-$[et-1].y],J[J.length-1].deltas.push(E);break;case"arc":J.push({deltas:[],abs:[],arc:!0}),Array.isArray(J[J.length-1].abs)&&J[J.length-1].abs.push(Q)}}W=C?null:w==="stroke"?"stroke":"fill";for(var Kt=!1,Mt=0;Mt<J.length;Mt++)if(J[Mt].arc)for(var wt=J[Mt].abs,xt=0;xt<wt.length;xt++){var kt=wt[xt];kt.type==="arc"?G.call(this,kt.x,kt.y,kt.radius,kt.startAngle,kt.endAngle,kt.counterclockwise,void 0,C,!Kt):H.call(this,kt.x,kt.y),Kt=!0}else if(J[Mt].close===!0)this.pdf.internal.out("h"),Kt=!1;else if(J[Mt].begin!==!0){var Pt=J[Mt].start.x,qt=J[Mt].start.y;R.call(this,J[Mt].deltas,Pt,qt),Kt=!0}W&&vt.call(this,W),C&&bt.call(this)}},Nt=function(w){var C=this.pdf.internal.getFontSize()/this.pdf.internal.scaleFactor,E=C*(this.pdf.internal.getLineHeightFactor()-1);switch(this.ctx.textBaseline){case"bottom":return w-E;case"top":return w+C-E;case"hanging":return w+C-2*E;case"middle":return w+C/2-E;case"ideographic":return w;case"alphabetic":default:return w}},rt=function(w){return w+this.pdf.internal.getFontSize()/this.pdf.internal.scaleFactor*(this.pdf.internal.getLineHeightFactor()-1)};A.prototype.createLinearGradient=function(){var w=function(){};return w.colorStops=[],w.addColorStop=function(C,E){this.colorStops.push([C,E])},w.getColor=function(){return this.colorStops.length===0?"#000000":this.colorStops[0][1]},w.isCanvasGradient=!0,w},A.prototype.createPattern=function(){return this.createLinearGradient()},A.prototype.createRadialGradient=function(){return this.createLinearGradient()};var G=function(w,C,E,W,J,$,et,Q,At){for(var Lt=ot.call(this,E,W,J,$),Ot=0;Ot<Lt.length;Ot++){var Ct=Lt[Ot];Ot===0&&(At?k.call(this,Ct.x1+w,Ct.y1+C):H.call(this,Ct.x1+w,Ct.y1+C)),ct.call(this,w,C,Ct.x2,Ct.y2,Ct.x3,Ct.y3,Ct.x4,Ct.y4)}Q?bt.call(this):vt.call(this,et)},vt=function(w){switch(w){case"stroke":this.pdf.internal.out("S");break;case"fill":this.pdf.internal.out("f")}},bt=function(){this.pdf.clip(),this.pdf.discardPath()},k=function(w,C){this.pdf.internal.out(r(w)+" "+a(C)+" m")},I=function(w){var C;switch(w.align){case"right":case"end":C="right";break;case"center":C="center";break;case"left":case"start":default:C="left"}var E=this.pdf.getTextDimensions(w.text),W=Nt.call(this,w.y),J=rt.call(this,W)-E.h,$=this.ctx.transform.applyToPoint(new h(w.x,W)),et=this.ctx.transform.decompose(),Q=new f;Q=(Q=(Q=Q.multiply(et.translate)).multiply(et.skew)).multiply(et.scale);for(var At,Lt,Ot,Ct=this.ctx.transform.applyToRectangle(new l(w.x,W,E.w,E.h)),Wt=Q.applyToRectangle(new l(w.x,J,E.w,E.h)),at=q.call(this,Wt),O=[],Kt=0;Kt<at.length;Kt+=1)O.indexOf(at[Kt])===-1&&O.push(at[Kt]);if(Z(O),this.autoPaging)for(var Mt=O[0],wt=O[O.length-1],xt=Mt;xt<wt+1;xt++){this.pdf.setPage(xt);var kt=xt===1?this.posY+this.margin[0]:this.margin[0],Pt=this.pdf.internal.pageSize.height-this.posY-this.margin[0]-this.margin[2],qt=this.pdf.internal.pageSize.height-this.margin[2],Gt=qt-this.margin[0],Qt=this.pdf.internal.pageSize.width-this.margin[1],te=Qt-this.margin[3],ie=xt===1?0:Pt+(xt-2)*Gt;if(this.ctx.clip_path.length!==0){var fe=this.path;At=JSON.parse(JSON.stringify(this.ctx.clip_path)),this.path=M(At,this.posX+this.margin[3],-1*ie+kt),dt.call(this,"fill",!0),this.path=fe}var Ht=M([JSON.parse(JSON.stringify(Wt))],this.posX+this.margin[3],-ie+kt+this.ctx.prevPageLastElemOffset)[0];w.scale>=.01&&(Lt=this.pdf.internal.getFontSize(),this.pdf.setFontSize(Lt*w.scale),Ot=this.lineWidth,this.lineWidth=Ot*w.scale);var ee=this.autoPaging!=="text";if(ee||Ht.y+Ht.h<=qt){if(ee||Ht.y>=kt&&Ht.x<=Qt){var It=ee?w.text:this.pdf.splitTextToSize(w.text,w.maxWidth||Qt-Ht.x)[0],Ge=M([JSON.parse(JSON.stringify(Ct))],this.posX+this.margin[3],-ie+kt+this.ctx.prevPageLastElemOffset)[0],oe=ee&&(xt>Mt||xt<wt)&&F.call(this);oe&&(this.pdf.saveGraphicsState(),this.pdf.rect(this.margin[3],this.margin[0],te,Gt,null).clip().discardPath()),this.pdf.text(It,Ge.x,Ge.y,{angle:w.angle,align:C,renderingMode:w.renderingMode}),oe&&this.pdf.restoreGraphicsState()}}else Ht.y<qt&&(this.ctx.prevPageLastElemOffset+=qt-Ht.y);w.scale>=.01&&(this.pdf.setFontSize(Lt),this.lineWidth=Ot)}else w.scale>=.01&&(Lt=this.pdf.internal.getFontSize(),this.pdf.setFontSize(Lt*w.scale),Ot=this.lineWidth,this.lineWidth=Ot*w.scale),this.pdf.text(w.text,$.x+this.posX,$.y+this.posY,{angle:w.angle,align:C,renderingMode:w.renderingMode,maxWidth:w.maxWidth}),w.scale>=.01&&(this.pdf.setFontSize(Lt),this.lineWidth=Ot)},H=function(w,C,E,W){E=E||0,W=W||0,this.pdf.internal.out(r(w+E)+" "+a(C+W)+" l")},R=function(w,C,E){return this.pdf.lines(w,C,E,null,null)},ct=function(w,C,E,W,J,$,et,Q){this.pdf.internal.out([e(u(E+w)),e(o(W+C)),e(u(J+w)),e(o($+C)),e(u(et+w)),e(o(Q+C)),"c"].join(" "))},ot=function(w,C,E,W){for(var J=2*Math.PI,$=Math.PI/2;C>E;)C-=J;var et=Math.abs(E-C);et<J&&W&&(et=J-et);for(var Q=[],At=W?-1:1,Lt=C;et>1e-5;){var Ot=Lt+At*Math.min(et,$);Q.push(mt.call(this,w,Lt,Ot)),et-=Math.abs(Ot-Lt),Lt=Ot}return Q},mt=function(w,C,E){var W=(E-C)/2,J=w*Math.cos(W),$=w*Math.sin(W),et=J,Q=-$,At=et*et+Q*Q,Lt=At+et*J+Q*$,Ot=4/3*(Math.sqrt(2*At*Lt)-Lt)/(et*$-Q*J),Ct=et-Ot*Q,Wt=Q+Ot*et,at=Ct,O=-Wt,Kt=W+C,Mt=Math.cos(Kt),wt=Math.sin(Kt);return{x1:w*Math.cos(C),y1:w*Math.sin(C),x2:Ct*Mt-Wt*wt,y2:Ct*wt+Wt*Mt,x3:at*Mt-O*wt,y3:at*wt+O*Mt,x4:w*Math.cos(E),y4:w*Math.sin(E)}},tt=function(w){return 180*w/Math.PI},pt=function(w,C,E,W,J,$){var et=w+.5*(E-w),Q=C+.5*(W-C),At=J+.5*(E-J),Lt=$+.5*(W-$),Ot=Math.min(w,J,et,At),Ct=Math.max(w,J,et,At),Wt=Math.min(C,$,Q,Lt),at=Math.max(C,$,Q,Lt);return new l(Ot,Wt,Ct-Ot,at-Wt)},ft=function(w,C,E,W,J,$,et,Q){var At,Lt,Ot,Ct,Wt,at,O,Kt,Mt,wt,xt,kt,Pt,qt,Gt=E-w,Qt=W-C,te=J-E,ie=$-W,fe=et-J,Ht=Q-$;for(Lt=0;Lt<41;Lt++)Mt=(O=(Ot=w+(At=Lt/40)*Gt)+At*((Wt=E+At*te)-Ot))+At*(Wt+At*(J+At*fe-Wt)-O),wt=(Kt=(Ct=C+At*Qt)+At*((at=W+At*ie)-Ct))+At*(at+At*($+At*Ht-at)-Kt),Lt==0?(xt=Mt,kt=wt,Pt=Mt,qt=wt):(xt=Math.min(xt,Mt),kt=Math.min(kt,wt),Pt=Math.max(Pt,Mt),qt=Math.max(qt,wt));return new l(Math.round(xt),Math.round(kt),Math.round(Pt-xt),Math.round(qt-kt))},Et=function(){if(this.prevLineDash||this.ctx.lineDash.length||this.ctx.lineDashOffset){var w,C,E=(w=this.ctx.lineDash,C=this.ctx.lineDashOffset,JSON.stringify({lineDash:w,lineDashOffset:C}));this.prevLineDash!==E&&(this.pdf.setLineDash(this.ctx.lineDash,this.ctx.lineDashOffset),this.prevLineDash=E)}}})(Ut.API),function(i){var e=function(o){var h,l,f,v,x,A,_,p,B,F;for(l=[],f=0,v=(o+=h="\0\0\0\0".slice(o.length%4||4)).length;v>f;f+=4)(x=(o.charCodeAt(f)<<24)+(o.charCodeAt(f+1)<<16)+(o.charCodeAt(f+2)<<8)+o.charCodeAt(f+3))!==0?(A=(x=((x=((x=((x=(x-(F=x%85))/85)-(B=x%85))/85)-(p=x%85))/85)-(_=x%85))/85)%85,l.push(A+33,_+33,p+33,B+33,F+33)):l.push(122);return function(q,S){for(var M=S;M>0;M--)q.pop()}(l,h.length),String.fromCharCode.apply(String,l)+"~>"},r=function(o){var h,l,f,v,x,A=String,_="length",p=255,B="charCodeAt",F="slice",q="replace";for(o[F](-2),o=o[F](0,-2)[q](/\s/g,"")[q]("z","!!!!!"),f=[],v=0,x=(o+=h="uuuuu"[F](o[_]%5||5))[_];x>v;v+=5)l=52200625*(o[B](v)-33)+614125*(o[B](v+1)-33)+7225*(o[B](v+2)-33)+85*(o[B](v+3)-33)+(o[B](v+4)-33),f.push(p&l>>24,p&l>>16,p&l>>8,p&l);return function(S,M){for(var Z=M;Z>0;Z--)S.pop()}(f,h[_]),A.fromCharCode.apply(A,f)},a=function(o){var h=new RegExp(/^([0-9A-Fa-f]{2})+$/);if((o=o.replace(/\s/g,"")).indexOf(">")!==-1&&(o=o.substr(0,o.indexOf(">"))),o.length%2&&(o+="0"),h.test(o)===!1)return"";for(var l="",f=0;f<o.length;f+=2)l+=String.fromCharCode("0x"+(o[f]+o[f+1]));return l},u=function(o){for(var h=new Uint8Array(o.length),l=o.length;l--;)h[l]=o.charCodeAt(l);return o=(h=$o(h)).reduce(function(f,v){return f+String.fromCharCode(v)},"")};i.processDataByFilters=function(o,h){var l=0,f=o||"",v=[];for(typeof(h=h||[])=="string"&&(h=[h]),l=0;l<h.length;l+=1)switch(h[l]){case"ASCII85Decode":case"/ASCII85Decode":f=r(f),v.push("/ASCII85Encode");break;case"ASCII85Encode":case"/ASCII85Encode":f=e(f),v.push("/ASCII85Decode");break;case"ASCIIHexDecode":case"/ASCIIHexDecode":f=a(f),v.push("/ASCIIHexEncode");break;case"ASCIIHexEncode":case"/ASCIIHexEncode":f=f.split("").map(function(x){return("0"+x.charCodeAt().toString(16)).slice(-2)}).join("")+">",v.push("/ASCIIHexDecode");break;case"FlateEncode":case"/FlateEncode":f=u(f),v.push("/FlateDecode");break;default:throw new Error('The filter: "'+h[l]+'" is not implemented')}return{data:f,reverseChain:v.reverse().join(" ")}}}(Ut.API),function(i){i.loadFile=function(e,r,a){return function(u,o,h){o=o!==!1,h=typeof h=="function"?h:function(){};var l=void 0;try{l=function(f,v,x){var A=new XMLHttpRequest,_=0,p=function(B){var F=B.length,q=[],S=String.fromCharCode;for(_=0;_<F;_+=1)q.push(S(255&B.charCodeAt(_)));return q.join("")};if(A.open("GET",f,!v),A.overrideMimeType("text/plain; charset=x-user-defined"),v===!1&&(A.onload=function(){A.status===200?x(p(this.responseText)):x(void 0)}),A.send(null),v&&A.status===200)return p(A.responseText)}(u,o,h)}catch{}return l}(e,r,a)},i.loadImageFile=i.loadFile}(Ut.API),function(i){function e(){return(zt.html2canvas?Promise.resolve(zt.html2canvas):Qo(()=>import("./html2pdf.js-19c9759c.js").then(h=>h.h),[])).catch(function(h){return Promise.reject(new Error("Could not load html2canvas: "+h))}).then(function(h){return h.default?h.default:h})}function r(){return(zt.DOMPurify?Promise.resolve(zt.DOMPurify):Qo(()=>import("./purify.es-3fb4e735.js"),[])).catch(function(h){return Promise.reject(new Error("Could not load dompurify: "+h))}).then(function(h){return h.default?h.default:h})}var a=function(h){var l=ve(h);return l==="undefined"?"undefined":l==="string"||h instanceof String?"string":l==="number"||h instanceof Number?"number":l==="function"||h instanceof Function?"function":h&&h.constructor===Array?"array":h&&h.nodeType===1?"element":l==="object"?"object":"unknown"},u=function(h,l){var f=document.createElement(h);for(var v in l.className&&(f.className=l.className),l.innerHTML&&l.dompurify&&(f.innerHTML=l.dompurify.sanitize(l.innerHTML)),l.style)f.style[v]=l.style[v];return f},o=function h(l){var f=Object.assign(h.convert(Promise.resolve()),JSON.parse(JSON.stringify(h.template))),v=h.convert(Promise.resolve(),f);return v=(v=v.setProgress(1,h,1,[h])).set(l)};(o.prototype=Object.create(Promise.prototype)).constructor=o,o.convert=function(h,l){return h.__proto__=l||o.prototype,h},o.template={prop:{src:null,container:null,overlay:null,canvas:null,img:null,pdf:null,pageSize:null,callback:function(){}},progress:{val:0,state:null,n:0,stack:[]},opt:{filename:"file.pdf",margin:[0,0,0,0],enableLinks:!0,x:0,y:0,html2canvas:{},jsPDF:{},backgroundColor:"transparent"}},o.prototype.from=function(h,l){return this.then(function(){switch(l=l||function(f){switch(a(f)){case"string":return"string";case"element":return f.nodeName.toLowerCase()==="canvas"?"canvas":"element";default:return"unknown"}}(h)){case"string":return this.then(r).then(function(f){return this.set({src:u("div",{innerHTML:h,dompurify:f})})});case"element":return this.set({src:h});case"canvas":return this.set({canvas:h});case"img":return this.set({img:h});default:return this.error("Unknown source type.")}})},o.prototype.to=function(h){switch(h){case"container":return this.toContainer();case"canvas":return this.toCanvas();case"img":return this.toImg();case"pdf":return this.toPdf();default:return this.error("Invalid target.")}},o.prototype.toContainer=function(){return this.thenList([function(){return this.prop.src||this.error("Cannot duplicate - no source HTML.")},function(){return this.prop.pageSize||this.setPageSize()}]).then(function(){var h={position:"relative",display:"inline-block",width:(typeof this.opt.width!="number"||isNaN(this.opt.width)||typeof this.opt.windowWidth!="number"||isNaN(this.opt.windowWidth)?Math.max(this.prop.src.clientWidth,this.prop.src.scrollWidth,this.prop.src.offsetWidth):this.opt.windowWidth)+"px",left:0,right:0,top:0,margin:"auto",backgroundColor:this.opt.backgroundColor},l=function f(v,x){for(var A=v.nodeType===3?document.createTextNode(v.nodeValue):v.cloneNode(!1),_=v.firstChild;_;_=_.nextSibling)x!==!0&&_.nodeType===1&&_.nodeName==="SCRIPT"||A.appendChild(f(_,x));return v.nodeType===1&&(v.nodeName==="CANVAS"?(A.width=v.width,A.height=v.height,A.getContext("2d").drawImage(v,0,0)):v.nodeName!=="TEXTAREA"&&v.nodeName!=="SELECT"||(A.value=v.value),A.addEventListener("load",function(){A.scrollTop=v.scrollTop,A.scrollLeft=v.scrollLeft},!0)),A}(this.prop.src,this.opt.html2canvas.javascriptEnabled);l.tagName==="BODY"&&(h.height=Math.max(document.body.scrollHeight,document.body.offsetHeight,document.documentElement.clientHeight,document.documentElement.scrollHeight,document.documentElement.offsetHeight)+"px"),this.prop.overlay=u("div",{className:"html2pdf__overlay",style:{position:"fixed",overflow:"hidden",zIndex:1e3,left:"-100000px",right:0,bottom:0,top:0}}),this.prop.container=u("div",{className:"html2pdf__container",style:h}),this.prop.container.appendChild(l),this.prop.container.firstChild.appendChild(u("div",{style:{clear:"both",border:"0 none transparent",margin:0,padding:0,height:0}})),this.prop.container.style.float="none",this.prop.overlay.appendChild(this.prop.container),document.body.appendChild(this.prop.overlay),this.prop.container.firstChild.style.position="relative",this.prop.container.height=Math.max(this.prop.container.firstChild.clientHeight,this.prop.container.firstChild.scrollHeight,this.prop.container.firstChild.offsetHeight)+"px"})},o.prototype.toCanvas=function(){var h=[function(){return document.body.contains(this.prop.container)||this.toContainer()}];return this.thenList(h).then(e).then(function(l){var f=Object.assign({},this.opt.html2canvas);return delete f.onrendered,l(this.prop.container,f)}).then(function(l){(this.opt.html2canvas.onrendered||function(){})(l),this.prop.canvas=l,document.body.removeChild(this.prop.overlay)})},o.prototype.toContext2d=function(){var h=[function(){return document.body.contains(this.prop.container)||this.toContainer()}];return this.thenList(h).then(e).then(function(l){var f=this.opt.jsPDF,v=this.opt.fontFaces,x=typeof this.opt.width!="number"||isNaN(this.opt.width)||typeof this.opt.windowWidth!="number"||isNaN(this.opt.windowWidth)?1:this.opt.width/this.opt.windowWidth,A=Object.assign({async:!0,allowTaint:!0,scale:x,scrollX:this.opt.scrollX||0,scrollY:this.opt.scrollY||0,backgroundColor:"#ffffff",imageTimeout:15e3,logging:!0,proxy:null,removeContainer:!0,foreignObjectRendering:!1,useCORS:!1},this.opt.html2canvas);if(delete A.onrendered,f.context2d.autoPaging=this.opt.autoPaging===void 0||this.opt.autoPaging,f.context2d.posX=this.opt.x,f.context2d.posY=this.opt.y,f.context2d.margin=this.opt.margin,f.context2d.fontFaces=v,v)for(var _=0;_<v.length;++_){var p=v[_],B=p.src.find(function(F){return F.format==="truetype"});B&&f.addFont(B.url,p.ref.name,p.ref.style)}return A.windowHeight=A.windowHeight||0,A.windowHeight=A.windowHeight==0?Math.max(this.prop.container.clientHeight,this.prop.container.scrollHeight,this.prop.container.offsetHeight):A.windowHeight,f.context2d.save(!0),l(this.prop.container,A)}).then(function(l){this.opt.jsPDF.context2d.restore(!0),(this.opt.html2canvas.onrendered||function(){})(l),this.prop.canvas=l,document.body.removeChild(this.prop.overlay)})},o.prototype.toImg=function(){return this.thenList([function(){return this.prop.canvas||this.toCanvas()}]).then(function(){var h=this.prop.canvas.toDataURL("image/"+this.opt.image.type,this.opt.image.quality);this.prop.img=document.createElement("img"),this.prop.img.src=h})},o.prototype.toPdf=function(){return this.thenList([function(){return this.toContext2d()}]).then(function(){this.prop.pdf=this.prop.pdf||this.opt.jsPDF})},o.prototype.output=function(h,l,f){return(f=f||"pdf").toLowerCase()==="img"||f.toLowerCase()==="image"?this.outputImg(h,l):this.outputPdf(h,l)},o.prototype.outputPdf=function(h,l){return this.thenList([function(){return this.prop.pdf||this.toPdf()}]).then(function(){return this.prop.pdf.output(h,l)})},o.prototype.outputImg=function(h){return this.thenList([function(){return this.prop.img||this.toImg()}]).then(function(){switch(h){case void 0:case"img":return this.prop.img;case"datauristring":case"dataurlstring":return this.prop.img.src;case"datauri":case"dataurl":return document.location.href=this.prop.img.src;default:throw'Image output type "'+h+'" is not supported.'}})},o.prototype.save=function(h){return this.thenList([function(){return this.prop.pdf||this.toPdf()}]).set(h?{filename:h}:null).then(function(){this.prop.pdf.save(this.opt.filename)})},o.prototype.doCallback=function(){return this.thenList([function(){return this.prop.pdf||this.toPdf()}]).then(function(){this.prop.callback(this.prop.pdf)})},o.prototype.set=function(h){if(a(h)!=="object")return this;var l=Object.keys(h||{}).map(function(f){if(f in o.template.prop)return function(){this.prop[f]=h[f]};switch(f){case"margin":return this.setMargin.bind(this,h.margin);case"jsPDF":return function(){return this.opt.jsPDF=h.jsPDF,this.setPageSize()};case"pageSize":return this.setPageSize.bind(this,h.pageSize);default:return function(){this.opt[f]=h[f]}}},this);return this.then(function(){return this.thenList(l)})},o.prototype.get=function(h,l){return this.then(function(){var f=h in o.template.prop?this.prop[h]:this.opt[h];return l?l(f):f})},o.prototype.setMargin=function(h){return this.then(function(){switch(a(h)){case"number":h=[h,h,h,h];case"array":if(h.length===2&&(h=[h[0],h[1],h[0],h[1]]),h.length===4)break;default:return this.error("Invalid margin array.")}this.opt.margin=h}).then(this.setPageSize)},o.prototype.setPageSize=function(h){function l(f,v){return Math.floor(f*v/72*96)}return this.then(function(){(h=h||Ut.getPageSize(this.opt.jsPDF)).hasOwnProperty("inner")||(h.inner={width:h.width-this.opt.margin[1]-this.opt.margin[3],height:h.height-this.opt.margin[0]-this.opt.margin[2]},h.inner.px={width:l(h.inner.width,h.k),height:l(h.inner.height,h.k)},h.inner.ratio=h.inner.height/h.inner.width),this.prop.pageSize=h})},o.prototype.setProgress=function(h,l,f,v){return h!=null&&(this.progress.val=h),l!=null&&(this.progress.state=l),f!=null&&(this.progress.n=f),v!=null&&(this.progress.stack=v),this.progress.ratio=this.progress.val/this.progress.state,this},o.prototype.updateProgress=function(h,l,f,v){return this.setProgress(h?this.progress.val+h:null,l||null,f?this.progress.n+f:null,v?this.progress.stack.concat(v):null)},o.prototype.then=function(h,l){var f=this;return this.thenCore(h,l,function(v,x){return f.updateProgress(null,null,1,[v]),Promise.prototype.then.call(this,function(A){return f.updateProgress(null,v),A}).then(v,x).then(function(A){return f.updateProgress(1),A})})},o.prototype.thenCore=function(h,l,f){f=f||Promise.prototype.then,h&&(h=h.bind(this)),l&&(l=l.bind(this));var v=Promise.toString().indexOf("[native code]")!==-1&&Promise.name==="Promise"?this:o.convert(Object.assign({},this),Promise.prototype),x=f.call(v,h,l);return o.convert(x,this.__proto__)},o.prototype.thenExternal=function(h,l){return Promise.prototype.then.call(this,h,l)},o.prototype.thenList=function(h){var l=this;return h.forEach(function(f){l=l.thenCore(f)}),l},o.prototype.catch=function(h){h&&(h=h.bind(this));var l=Promise.prototype.catch.call(this,h);return o.convert(l,this)},o.prototype.catchExternal=function(h){return Promise.prototype.catch.call(this,h)},o.prototype.error=function(h){return this.then(function(){throw new Error(h)})},o.prototype.using=o.prototype.set,o.prototype.saveAs=o.prototype.save,o.prototype.export=o.prototype.output,o.prototype.run=o.prototype.then,Ut.getPageSize=function(h,l,f){if(ve(h)==="object"){var v=h;h=v.orientation,l=v.unit||l,f=v.format||f}l=l||"mm",f=f||"a4",h=(""+(h||"P")).toLowerCase();var x,A=(""+f).toLowerCase(),_={a0:[2383.94,3370.39],a1:[1683.78,2383.94],a2:[1190.55,1683.78],a3:[841.89,1190.55],a4:[595.28,841.89],a5:[419.53,595.28],a6:[297.64,419.53],a7:[209.76,297.64],a8:[147.4,209.76],a9:[104.88,147.4],a10:[73.7,104.88],b0:[2834.65,4008.19],b1:[2004.09,2834.65],b2:[1417.32,2004.09],b3:[1000.63,1417.32],b4:[708.66,1000.63],b5:[498.9,708.66],b6:[354.33,498.9],b7:[249.45,354.33],b8:[175.75,249.45],b9:[124.72,175.75],b10:[87.87,124.72],c0:[2599.37,3676.54],c1:[1836.85,2599.37],c2:[1298.27,1836.85],c3:[918.43,1298.27],c4:[649.13,918.43],c5:[459.21,649.13],c6:[323.15,459.21],c7:[229.61,323.15],c8:[161.57,229.61],c9:[113.39,161.57],c10:[79.37,113.39],dl:[311.81,623.62],letter:[612,792],"government-letter":[576,756],legal:[612,1008],"junior-legal":[576,360],ledger:[1224,792],tabloid:[792,1224],"credit-card":[153,243]};switch(l){case"pt":x=1;break;case"mm":x=72/25.4;break;case"cm":x=72/2.54;break;case"in":x=72;break;case"px":x=.75;break;case"pc":case"em":x=12;break;case"ex":x=6;break;default:throw"Invalid unit: "+l}var p,B=0,F=0;if(_.hasOwnProperty(A))B=_[A][1]/x,F=_[A][0]/x;else try{B=f[1],F=f[0]}catch{throw new Error("Invalid format: "+f)}if(h==="p"||h==="portrait")h="p",F>B&&(p=F,F=B,B=p);else{if(h!=="l"&&h!=="landscape")throw"Invalid orientation: "+h;h="l",B>F&&(p=F,F=B,B=p)}return{width:F,height:B,unit:l,k:x,orientation:h}},i.html=function(h,l){(l=l||{}).callback=l.callback||function(){},l.html2canvas=l.html2canvas||{},l.html2canvas.canvas=l.html2canvas.canvas||this.canvas,l.jsPDF=l.jsPDF||this,l.fontFaces=l.fontFaces?l.fontFaces.map(as):null;var f=new o(l);return l.worker?f:f.from(h).doCallback()}}(Ut.API),Ut.API.addJS=function(i){return Vs=i,this.internal.events.subscribe("postPutResources",function(){Ya=this.internal.newObject(),this.internal.out("<<"),this.internal.out("/Names [(EmbeddedJS) "+(Ya+1)+" 0 R]"),this.internal.out(">>"),this.internal.out("endobj"),Ws=this.internal.newObject(),this.internal.out("<<"),this.internal.out("/S /JavaScript"),this.internal.out("/JS ("+Vs+")"),this.internal.out(">>"),this.internal.out("endobj")}),this.internal.events.subscribe("putCatalog",function(){Ya!==void 0&&Ws!==void 0&&this.internal.out("/Names <</JavaScript "+Ya+" 0 R>>")}),this},function(i){var e;i.events.push(["postPutResources",function(){var r=this,a=/^(\d+) 0 obj$/;if(this.outline.root.children.length>0)for(var u=r.outline.render().split(/\r\n/),o=0;o<u.length;o++){var h=u[o],l=a.exec(h);if(l!=null){var f=l[1];r.internal.newObjectDeferredBegin(f,!1)}r.internal.write(h)}if(this.outline.createNamedDestinations){var v=this.internal.pages.length,x=[];for(o=0;o<v;o++){var A=r.internal.newObject();x.push(A);var _=r.internal.getPageInfo(o+1);r.internal.write("<< /D["+_.objId+" 0 R /XYZ null null null]>> endobj")}var p=r.internal.newObject();for(r.internal.write("<< /Names [ "),o=0;o<x.length;o++)r.internal.write("(page_"+(o+1)+")"+x[o]+" 0 R");r.internal.write(" ] >>","endobj"),e=r.internal.newObject(),r.internal.write("<< /Dests "+p+" 0 R"),r.internal.write(">>","endobj")}}]),i.events.push(["putCatalog",function(){this.outline.root.children.length>0&&(this.internal.write("/Outlines",this.outline.makeRef(this.outline.root)),this.outline.createNamedDestinations&&this.internal.write("/Names "+e+" 0 R"))}]),i.events.push(["initialized",function(){var r=this;r.outline={createNamedDestinations:!1,root:{children:[]}},r.outline.add=function(a,u,o){var h={title:u,options:o,children:[]};return a==null&&(a=this.root),a.children.push(h),h},r.outline.render=function(){return this.ctx={},this.ctx.val="",this.ctx.pdf=r,this.genIds_r(this.root),this.renderRoot(this.root),this.renderItems(this.root),this.ctx.val},r.outline.genIds_r=function(a){a.id=r.internal.newObjectDeferred();for(var u=0;u<a.children.length;u++)this.genIds_r(a.children[u])},r.outline.renderRoot=function(a){this.objStart(a),this.line("/Type /Outlines"),a.children.length>0&&(this.line("/First "+this.makeRef(a.children[0])),this.line("/Last "+this.makeRef(a.children[a.children.length-1]))),this.line("/Count "+this.count_r({count:0},a)),this.objEnd()},r.outline.renderItems=function(a){for(var u=this.ctx.pdf.internal.getVerticalCoordinateString,o=0;o<a.children.length;o++){var h=a.children[o];this.objStart(h),this.line("/Title "+this.makeString(h.title)),this.line("/Parent "+this.makeRef(a)),o>0&&this.line("/Prev "+this.makeRef(a.children[o-1])),o<a.children.length-1&&this.line("/Next "+this.makeRef(a.children[o+1])),h.children.length>0&&(this.line("/First "+this.makeRef(h.children[0])),this.line("/Last "+this.makeRef(h.children[h.children.length-1])));var l=this.count=this.count_r({count:0},h);if(l>0&&this.line("/Count "+l),h.options&&h.options.pageNumber){var f=r.internal.getPageInfo(h.options.pageNumber);this.line("/Dest ["+f.objId+" 0 R /XYZ 0 "+u(0)+" 0]")}this.objEnd()}for(var v=0;v<a.children.length;v++)this.renderItems(a.children[v])},r.outline.line=function(a){this.ctx.val+=a+`\r
`},r.outline.makeRef=function(a){return a.id+" 0 R"},r.outline.makeString=function(a){return"("+r.internal.pdfEscape(a)+")"},r.outline.objStart=function(a){this.ctx.val+=`\r
`+a.id+` 0 obj\r
<<\r
`},r.outline.objEnd=function(){this.ctx.val+=`>> \r
endobj\r
`},r.outline.count_r=function(a,u){for(var o=0;o<u.children.length;o++)a.count++,this.count_r(a,u.children[o]);return a.count}}])}(Ut.API),function(i){var e=[192,193,194,195,196,197,198,199];i.processJPEG=function(r,a,u,o,h,l){var f,v=this.decode.DCT_DECODE,x=null;if(typeof r=="string"||this.__addimage__.isArrayBuffer(r)||this.__addimage__.isArrayBufferView(r)){switch(r=h||r,r=this.__addimage__.isArrayBuffer(r)?new Uint8Array(r):r,(f=function(A){for(var _,p=256*A.charCodeAt(4)+A.charCodeAt(5),B=A.length,F={width:0,height:0,numcomponents:1},q=4;q<B;q+=2){if(q+=p,e.indexOf(A.charCodeAt(q+1))!==-1){_=256*A.charCodeAt(q+5)+A.charCodeAt(q+6),F={width:256*A.charCodeAt(q+7)+A.charCodeAt(q+8),height:_,numcomponents:A.charCodeAt(q+9)};break}p=256*A.charCodeAt(q+2)+A.charCodeAt(q+3)}return F}(r=this.__addimage__.isArrayBufferView(r)?this.__addimage__.arrayBufferToBinaryString(r):r)).numcomponents){case 1:l=this.color_spaces.DEVICE_GRAY;break;case 4:l=this.color_spaces.DEVICE_CMYK;break;case 3:l=this.color_spaces.DEVICE_RGB}x={data:r,width:f.width,height:f.height,colorSpace:l,bitsPerComponent:8,filter:v,index:a,alias:u}}return x}}(Ut.API);var vi,Xa,Gs,Js,Ys,du=function(){var i,e,r;function a(o){var h,l,f,v,x,A,_,p,B,F,q,S,M,Z;for(this.data=o,this.pos=8,this.palette=[],this.imgData=[],this.transparency={},this.animation=null,this.text={},A=null;;){switch(h=this.readUInt32(),B=(function(){var st,dt;for(dt=[],st=0;st<4;++st)dt.push(String.fromCharCode(this.data[this.pos++]));return dt}).call(this).join("")){case"IHDR":this.width=this.readUInt32(),this.height=this.readUInt32(),this.bits=this.data[this.pos++],this.colorType=this.data[this.pos++],this.compressionMethod=this.data[this.pos++],this.filterMethod=this.data[this.pos++],this.interlaceMethod=this.data[this.pos++];break;case"acTL":this.animation={numFrames:this.readUInt32(),numPlays:this.readUInt32()||1/0,frames:[]};break;case"PLTE":this.palette=this.read(h);break;case"fcTL":A&&this.animation.frames.push(A),this.pos+=4,A={width:this.readUInt32(),height:this.readUInt32(),xOffset:this.readUInt32(),yOffset:this.readUInt32()},x=this.readUInt16(),v=this.readUInt16()||100,A.delay=1e3*x/v,A.disposeOp=this.data[this.pos++],A.blendOp=this.data[this.pos++],A.data=[];break;case"IDAT":case"fdAT":for(B==="fdAT"&&(this.pos+=4,h-=4),o=(A!=null?A.data:void 0)||this.imgData,S=0;0<=h?S<h:S>h;0<=h?++S:--S)o.push(this.data[this.pos++]);break;case"tRNS":switch(this.transparency={},this.colorType){case 3:if(f=this.palette.length/3,this.transparency.indexed=this.read(h),this.transparency.indexed.length>f)throw new Error("More transparent colors than palette size");if((F=f-this.transparency.indexed.length)>0)for(M=0;0<=F?M<F:M>F;0<=F?++M:--M)this.transparency.indexed.push(255);break;case 0:this.transparency.grayscale=this.read(h)[0];break;case 2:this.transparency.rgb=this.read(h)}break;case"tEXt":_=(q=this.read(h)).indexOf(0),p=String.fromCharCode.apply(String,q.slice(0,_)),this.text[p]=String.fromCharCode.apply(String,q.slice(_+1));break;case"IEND":return A&&this.animation.frames.push(A),this.colors=(function(){switch(this.colorType){case 0:case 3:case 4:return 1;case 2:case 6:return 3}}).call(this),this.hasAlphaChannel=(Z=this.colorType)===4||Z===6,l=this.colors+(this.hasAlphaChannel?1:0),this.pixelBitlength=this.bits*l,this.colorSpace=(function(){switch(this.colors){case 1:return"DeviceGray";case 3:return"DeviceRGB"}}).call(this),void(this.imgData=new Uint8Array(this.imgData));default:this.pos+=h}if(this.pos+=4,this.pos>this.data.length)throw new Error("Incomplete or corrupt PNG file")}}a.prototype.read=function(o){var h,l;for(l=[],h=0;0<=o?h<o:h>o;0<=o?++h:--h)l.push(this.data[this.pos++]);return l},a.prototype.readUInt32=function(){return this.data[this.pos++]<<24|this.data[this.pos++]<<16|this.data[this.pos++]<<8|this.data[this.pos++]},a.prototype.readUInt16=function(){return this.data[this.pos++]<<8|this.data[this.pos++]},a.prototype.decodePixels=function(o){var h=this.pixelBitlength/8,l=new Uint8Array(this.width*this.height*h),f=0,v=this;if(o==null&&(o=this.imgData),o.length===0)return new Uint8Array(0);function x(A,_,p,B){var F,q,S,M,Z,st,dt,Nt,rt,G,vt,bt,k,I,H,R,ct,ot,mt,tt,pt,ft=Math.ceil((v.width-A)/p),Et=Math.ceil((v.height-_)/B),w=v.width==ft&&v.height==Et;for(I=h*ft,bt=w?l:new Uint8Array(I*Et),st=o.length,k=0,q=0;k<Et&&f<st;){switch(o[f++]){case 0:for(M=ct=0;ct<I;M=ct+=1)bt[q++]=o[f++];break;case 1:for(M=ot=0;ot<I;M=ot+=1)F=o[f++],Z=M<h?0:bt[q-h],bt[q++]=(F+Z)%256;break;case 2:for(M=mt=0;mt<I;M=mt+=1)F=o[f++],S=(M-M%h)/h,H=k&&bt[(k-1)*I+S*h+M%h],bt[q++]=(H+F)%256;break;case 3:for(M=tt=0;tt<I;M=tt+=1)F=o[f++],S=(M-M%h)/h,Z=M<h?0:bt[q-h],H=k&&bt[(k-1)*I+S*h+M%h],bt[q++]=(F+Math.floor((Z+H)/2))%256;break;case 4:for(M=pt=0;pt<I;M=pt+=1)F=o[f++],S=(M-M%h)/h,Z=M<h?0:bt[q-h],k===0?H=R=0:(H=bt[(k-1)*I+S*h+M%h],R=S&&bt[(k-1)*I+(S-1)*h+M%h]),dt=Z+H-R,Nt=Math.abs(dt-Z),G=Math.abs(dt-H),vt=Math.abs(dt-R),rt=Nt<=G&&Nt<=vt?Z:G<=vt?H:R,bt[q++]=(F+rt)%256;break;default:throw new Error("Invalid filter algorithm: "+o[f-1])}if(!w){var C=((_+k*B)*v.width+A)*h,E=k*I;for(M=0;M<ft;M+=1){for(var W=0;W<h;W+=1)l[C++]=bt[E++];C+=(p-1)*h}}k++}}return o=Jc(o),v.interlaceMethod==1?(x(0,0,8,8),x(4,0,8,8),x(0,4,4,8),x(2,0,4,4),x(0,2,2,4),x(1,0,2,2),x(0,1,1,2)):x(0,0,1,1),l},a.prototype.decodePalette=function(){var o,h,l,f,v,x,A,_,p;for(l=this.palette,x=this.transparency.indexed||[],v=new Uint8Array((x.length||0)+l.length),f=0,o=0,h=A=0,_=l.length;A<_;h=A+=3)v[f++]=l[h],v[f++]=l[h+1],v[f++]=l[h+2],v[f++]=(p=x[o++])!=null?p:255;return v},a.prototype.copyToImageData=function(o,h){var l,f,v,x,A,_,p,B,F,q,S;if(f=this.colors,F=null,l=this.hasAlphaChannel,this.palette.length&&(F=(S=this._decodedPalette)!=null?S:this._decodedPalette=this.decodePalette(),f=4,l=!0),B=(v=o.data||o).length,A=F||h,x=_=0,f===1)for(;x<B;)p=F?4*h[x/4]:_,q=A[p++],v[x++]=q,v[x++]=q,v[x++]=q,v[x++]=l?A[p++]:255,_=p;else for(;x<B;)p=F?4*h[x/4]:_,v[x++]=A[p++],v[x++]=A[p++],v[x++]=A[p++],v[x++]=l?A[p++]:255,_=p},a.prototype.decode=function(){var o;return o=new Uint8Array(this.width*this.height*4),this.copyToImageData(o,this.decodePixels()),o};var u=function(){if(Object.prototype.toString.call(zt)==="[object Window]"){try{e=zt.document.createElement("canvas"),r=e.getContext("2d")}catch{return!1}return!0}return!1};return u(),i=function(o){var h;if(u()===!0)return r.width=o.width,r.height=o.height,r.clearRect(0,0,o.width,o.height),r.putImageData(o,0,0),(h=new Image).src=e.toDataURL(),h;throw new Error("This method requires a Browser with Canvas-capability.")},a.prototype.decodeFrames=function(o){var h,l,f,v,x,A,_,p;if(this.animation){for(p=[],l=x=0,A=(_=this.animation.frames).length;x<A;l=++x)h=_[l],f=o.createImageData(h.width,h.height),v=this.decodePixels(new Uint8Array(h.data)),this.copyToImageData(f,v),h.imageData=f,p.push(h.image=i(f));return p}},a.prototype.renderFrame=function(o,h){var l,f,v;return l=(f=this.animation.frames)[h],v=f[h-1],h===0&&o.clearRect(0,0,this.width,this.height),(v!=null?v.disposeOp:void 0)===1?o.clearRect(v.xOffset,v.yOffset,v.width,v.height):(v!=null?v.disposeOp:void 0)===2&&o.putImageData(v.imageData,v.xOffset,v.yOffset),l.blendOp===0&&o.clearRect(l.xOffset,l.yOffset,l.width,l.height),o.drawImage(l.image,l.xOffset,l.yOffset)},a.prototype.animate=function(o){var h,l,f,v,x,A,_=this;return l=0,A=this.animation,v=A.numFrames,f=A.frames,x=A.numPlays,(h=function(){var p,B;if(p=l++%v,B=f[p],_.renderFrame(o,p),v>1&&l/v<x)return _.animation._timeout=setTimeout(h,B.delay)})()},a.prototype.stopAnimation=function(){var o;return clearTimeout((o=this.animation)!=null?o._timeout:void 0)},a.prototype.render=function(o){var h,l;return o._png&&o._png.stopAnimation(),o._png=this,o.width=this.width,o.height=this.height,h=o.getContext("2d"),this.animation?(this.decodeFrames(h),this.animate(h)):(l=h.createImageData(this.width,this.height),this.copyToImageData(l,this.decodePixels()),h.putImageData(l,0,0))},a}();/**
 * @license
 *
 * Copyright (c) 2014 James Robb, https://github.com/jamesbrobb
 *
 * Permission is hereby granted, free of charge, to any person obtaining
 * a copy of this software and associated documentation files (the
 * "Software"), to deal in the Software without restriction, including
 * without limitation the rights to use, copy, modify, merge, publish,
 * distribute, sublicense, and/or sell copies of the Software, and to
 * permit persons to whom the Software is furnished to do so, subject to
 * the following conditions:
 *
 * The above copyright notice and this permission notice shall be
 * included in all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 * EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 * NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE
 * LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION
 * OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION
 * WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 * ====================================================================
 *//**
 * @license
 * (c) Dean McNamee <<EMAIL>>, 2013.
 *
 * https://github.com/deanm/omggif
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to
 * deal in the Software without restriction, including without limitation the
 * rights to use, copy, modify, merge, publish, distribute, sublicense, and/or
 * sell copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 * FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS
 * IN THE SOFTWARE.
 *
 * omggif is a JavaScript implementation of a GIF 89a encoder and decoder,
 * including animation and compression.  It does not rely on any specific
 * underlying system, so should run in the browser, Node, or Plask.
 */function pu(i){var e=0;if(i[e++]!==71||i[e++]!==73||i[e++]!==70||i[e++]!==56||(i[e++]+1&253)!=56||i[e++]!==97)throw new Error("Invalid GIF 87a/89a header.");var r=i[e++]|i[e++]<<8,a=i[e++]|i[e++]<<8,u=i[e++],o=u>>7,h=1<<(7&u)+1;i[e++],i[e++];var l=null,f=null;o&&(l=e,f=h,e+=3*h);var v=!0,x=[],A=0,_=null,p=0,B=null;for(this.width=r,this.height=a;v&&e<i.length;)switch(i[e++]){case 33:switch(i[e++]){case 255:if(i[e]!==11||i[e+1]==78&&i[e+2]==69&&i[e+3]==84&&i[e+4]==83&&i[e+5]==67&&i[e+6]==65&&i[e+7]==80&&i[e+8]==69&&i[e+9]==50&&i[e+10]==46&&i[e+11]==48&&i[e+12]==3&&i[e+13]==1&&i[e+16]==0)e+=14,B=i[e++]|i[e++]<<8,e++;else for(e+=12;;){if(!((k=i[e++])>=0))throw Error("Invalid block size");if(k===0)break;e+=k}break;case 249:if(i[e++]!==4||i[e+4]!==0)throw new Error("Invalid graphics extension block.");var F=i[e++];A=i[e++]|i[e++]<<8,_=i[e++],!(1&F)&&(_=null),p=F>>2&7,e++;break;case 254:for(;;){if(!((k=i[e++])>=0))throw Error("Invalid block size");if(k===0)break;e+=k}break;default:throw new Error("Unknown graphic control label: 0x"+i[e-1].toString(16))}break;case 44:var q=i[e++]|i[e++]<<8,S=i[e++]|i[e++]<<8,M=i[e++]|i[e++]<<8,Z=i[e++]|i[e++]<<8,st=i[e++],dt=st>>6&1,Nt=1<<(7&st)+1,rt=l,G=f,vt=!1;st>>7&&(vt=!0,rt=e,G=Nt,e+=3*Nt);var bt=e;for(e++;;){var k;if(!((k=i[e++])>=0))throw Error("Invalid block size");if(k===0)break;e+=k}x.push({x:q,y:S,width:M,height:Z,has_local_palette:vt,palette_offset:rt,palette_size:G,data_offset:bt,data_length:e-bt,transparent_index:_,interlaced:!!dt,delay:A,disposal:p});break;case 59:v=!1;break;default:throw new Error("Unknown gif block: 0x"+i[e-1].toString(16))}this.numFrames=function(){return x.length},this.loopCount=function(){return B},this.frameInfo=function(I){if(I<0||I>=x.length)throw new Error("Frame index out of range.");return x[I]},this.decodeAndBlitFrameBGRA=function(I,H){var R=this.frameInfo(I),ct=R.width*R.height,ot=new Uint8Array(ct);Xs(i,R.data_offset,ot,ct);var mt=R.palette_offset,tt=R.transparent_index;tt===null&&(tt=256);var pt=R.width,ft=r-pt,Et=pt,w=4*(R.y*r+R.x),C=4*((R.y+R.height)*r+R.x),E=w,W=4*ft;R.interlaced===!0&&(W+=4*r*7);for(var J=8,$=0,et=ot.length;$<et;++$){var Q=ot[$];if(Et===0&&(Et=pt,(E+=W)>=C&&(W=4*ft+4*r*(J-1),E=w+(pt+ft)*(J<<1),J>>=1)),Q===tt)E+=4;else{var At=i[mt+3*Q],Lt=i[mt+3*Q+1],Ot=i[mt+3*Q+2];H[E++]=Ot,H[E++]=Lt,H[E++]=At,H[E++]=255}--Et}},this.decodeAndBlitFrameRGBA=function(I,H){var R=this.frameInfo(I),ct=R.width*R.height,ot=new Uint8Array(ct);Xs(i,R.data_offset,ot,ct);var mt=R.palette_offset,tt=R.transparent_index;tt===null&&(tt=256);var pt=R.width,ft=r-pt,Et=pt,w=4*(R.y*r+R.x),C=4*((R.y+R.height)*r+R.x),E=w,W=4*ft;R.interlaced===!0&&(W+=4*r*7);for(var J=8,$=0,et=ot.length;$<et;++$){var Q=ot[$];if(Et===0&&(Et=pt,(E+=W)>=C&&(W=4*ft+4*r*(J-1),E=w+(pt+ft)*(J<<1),J>>=1)),Q===tt)E+=4;else{var At=i[mt+3*Q],Lt=i[mt+3*Q+1],Ot=i[mt+3*Q+2];H[E++]=At,H[E++]=Lt,H[E++]=Ot,H[E++]=255}--Et}}}function Xs(i,e,r,a){for(var u=i[e++],o=1<<u,h=o+1,l=h+1,f=u+1,v=(1<<f)-1,x=0,A=0,_=0,p=i[e++],B=new Int32Array(4096),F=null;;){for(;x<16&&p!==0;)A|=i[e++]<<x,x+=8,p===1?p=i[e++]:--p;if(x<f)break;var q=A&v;if(A>>=f,x-=f,q!==o){if(q===h)break;for(var S=q<l?q:F,M=0,Z=S;Z>o;)Z=B[Z]>>8,++M;var st=Z;if(_+M+(S!==q?1:0)>a)return void me.log("Warning, gif stream longer than expected.");r[_++]=st;var dt=_+=M;for(S!==q&&(r[_++]=st),Z=S;M--;)Z=B[Z],r[--dt]=255&Z,Z>>=8;F!==null&&l<4096&&(B[l++]=F<<8|st,l>=v+1&&f<12&&(++f,v=v<<1|1)),F=q}else l=h+1,v=(1<<(f=u+1))-1,F=null}return _!==a&&me.log("Warning, gif stream shorter than expected."),r}/**
 * @license
  Copyright (c) 2008, Adobe Systems Incorporated
  All rights reserved.

  Redistribution and use in source and binary forms, with or without 
  modification, are permitted provided that the following conditions are
  met:

  * Redistributions of source code must retain the above copyright notice, 
    this list of conditions and the following disclaimer.
  
  * Redistributions in binary form must reproduce the above copyright
    notice, this list of conditions and the following disclaimer in the 
    documentation and/or other materials provided with the distribution.
  
  * Neither the name of Adobe Systems Incorporated nor the names of its 
    contributors may be used to endorse or promote products derived from 
    this software without specific prior written permission.

  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS
  IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
  THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
  PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR 
  CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
  EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
  PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
  LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
  NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
  SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
*/function Zo(i){var e,r,a,u,o,h=Math.floor,l=new Array(64),f=new Array(64),v=new Array(64),x=new Array(64),A=new Array(65535),_=new Array(65535),p=new Array(64),B=new Array(64),F=[],q=0,S=7,M=new Array(64),Z=new Array(64),st=new Array(64),dt=new Array(256),Nt=new Array(2048),rt=[0,1,5,6,14,15,27,28,2,4,7,13,16,26,29,42,3,8,12,17,25,30,41,43,9,11,18,24,31,40,44,53,10,19,23,32,39,45,52,54,20,22,33,38,46,51,55,60,21,34,37,47,50,56,59,61,35,36,48,49,57,58,62,63],G=[0,0,1,5,1,1,1,1,1,1,0,0,0,0,0,0,0],vt=[0,1,2,3,4,5,6,7,8,9,10,11],bt=[0,0,2,1,3,3,2,4,3,5,5,4,4,0,0,1,125],k=[1,2,3,0,4,17,5,18,33,49,65,6,19,81,97,7,34,113,20,50,129,145,161,8,35,66,177,193,21,82,209,240,36,51,98,114,130,9,10,22,23,24,25,26,37,38,39,40,41,42,52,53,54,55,56,57,58,67,68,69,70,71,72,73,74,83,84,85,86,87,88,89,90,99,100,101,102,103,104,105,106,115,116,117,118,119,120,121,122,131,132,133,134,135,136,137,138,146,147,148,149,150,151,152,153,154,162,163,164,165,166,167,168,169,170,178,179,180,181,182,183,184,185,186,194,195,196,197,198,199,200,201,202,210,211,212,213,214,215,216,217,218,225,226,227,228,229,230,231,232,233,234,241,242,243,244,245,246,247,248,249,250],I=[0,0,3,1,1,1,1,1,1,1,1,1,0,0,0,0,0],H=[0,1,2,3,4,5,6,7,8,9,10,11],R=[0,0,2,1,2,4,4,3,4,7,5,4,4,0,1,2,119],ct=[0,1,2,3,17,4,5,33,49,6,18,65,81,7,97,113,19,34,50,129,8,20,66,145,161,177,193,9,35,51,82,240,21,98,114,209,10,22,36,52,225,37,241,23,24,25,26,38,39,40,41,42,53,54,55,56,57,58,67,68,69,70,71,72,73,74,83,84,85,86,87,88,89,90,99,100,101,102,103,104,105,106,115,116,117,118,119,120,121,122,130,131,132,133,134,135,136,137,138,146,147,148,149,150,151,152,153,154,162,163,164,165,166,167,168,169,170,178,179,180,181,182,183,184,185,186,194,195,196,197,198,199,200,201,202,210,211,212,213,214,215,216,217,218,226,227,228,229,230,231,232,233,234,242,243,244,245,246,247,248,249,250];function ot(w,C){for(var E=0,W=0,J=new Array,$=1;$<=16;$++){for(var et=1;et<=w[$];et++)J[C[W]]=[],J[C[W]][0]=E,J[C[W]][1]=$,W++,E++;E*=2}return J}function mt(w){for(var C=w[0],E=w[1]-1;E>=0;)C&1<<E&&(q|=1<<S),E--,--S<0&&(q==255?(tt(255),tt(0)):tt(q),S=7,q=0)}function tt(w){F.push(w)}function pt(w){tt(w>>8&255),tt(255&w)}function ft(w,C,E,W,J){for(var $,et=J[0],Q=J[240],At=function(wt,xt){var kt,Pt,qt,Gt,Qt,te,ie,fe,Ht,ee,It=0;for(Ht=0;Ht<8;++Ht){kt=wt[It],Pt=wt[It+1],qt=wt[It+2],Gt=wt[It+3],Qt=wt[It+4],te=wt[It+5],ie=wt[It+6];var Ge=kt+(fe=wt[It+7]),oe=kt-fe,An=Pt+ie,pe=Pt-ie,we=qt+te,qn=qt-te,ce=Gt+Qt,xr=Gt-Qt,Ne=Ge+ce,xn=Ge-ce,Kn=An+we,Ae=An-we;wt[It]=Ne+Kn,wt[It+4]=Ne-Kn;var Jt=.707106781*(Ae+xn);wt[It+2]=xn+Jt,wt[It+6]=xn-Jt;var ue=.382683433*((Ne=xr+qn)-(Ae=pe+oe)),Sr=.5411961*Ne+ue,He=1.306562965*Ae+ue,Dn=.707106781*(Kn=qn+pe),Rn=oe+Dn,Tt=oe-Dn;wt[It+5]=Tt+Sr,wt[It+3]=Tt-Sr,wt[It+1]=Rn+He,wt[It+7]=Rn-He,It+=8}for(It=0,Ht=0;Ht<8;++Ht){kt=wt[It],Pt=wt[It+8],qt=wt[It+16],Gt=wt[It+24],Qt=wt[It+32],te=wt[It+40],ie=wt[It+48];var Sn=kt+(fe=wt[It+56]),Tn=kt-fe,nn=Pt+ie,qe=Pt-ie,Oe=qt+te,hn=qt-te,zr=Gt+Qt,Zn=Gt-Qt,_n=Sn+zr,Pn=Sn-zr,kn=nn+Oe,zn=nn-Oe;wt[It]=_n+kn,wt[It+32]=_n-kn;var gn=.707106781*(zn+Pn);wt[It+16]=Pn+gn,wt[It+48]=Pn-gn;var Un=.382683433*((_n=Zn+hn)-(zn=qe+Tn)),_r=.5411961*_n+Un,Ur=1.306562965*zn+Un,Hr=.707106781*(kn=hn+qe),Wr=Tn+Hr,Vr=Tn-Hr;wt[It+40]=Vr+_r,wt[It+24]=Vr-_r,wt[It+8]=Wr+Ur,wt[It+56]=Wr-Ur,It++}for(Ht=0;Ht<64;++Ht)ee=wt[Ht]*xt[Ht],p[Ht]=ee>0?ee+.5|0:ee-.5|0;return p}(w,C),Lt=0;Lt<64;++Lt)B[rt[Lt]]=At[Lt];var Ot=B[0]-E;E=B[0],Ot==0?mt(W[0]):(mt(W[_[$=32767+Ot]]),mt(A[$]));for(var Ct=63;Ct>0&&B[Ct]==0;)Ct--;if(Ct==0)return mt(et),E;for(var Wt,at=1;at<=Ct;){for(var O=at;B[at]==0&&at<=Ct;)++at;var Kt=at-O;if(Kt>=16){Wt=Kt>>4;for(var Mt=1;Mt<=Wt;++Mt)mt(Q);Kt&=15}$=32767+B[at],mt(J[(Kt<<4)+_[$]]),mt(A[$]),at++}return Ct!=63&&mt(et),E}function Et(w){w=Math.min(Math.max(w,1),100),o!=w&&(function(C){for(var E=[16,11,10,16,24,40,51,61,12,12,14,19,26,58,60,55,14,13,16,24,40,57,69,56,14,17,22,29,51,87,80,62,18,22,37,56,68,109,103,77,24,35,55,64,81,104,113,92,49,64,78,87,103,121,120,101,72,92,95,98,112,100,103,99],W=0;W<64;W++){var J=h((E[W]*C+50)/100);J=Math.min(Math.max(J,1),255),l[rt[W]]=J}for(var $=[17,18,24,47,99,99,99,99,18,21,26,66,99,99,99,99,24,26,56,99,99,99,99,99,47,66,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99],et=0;et<64;et++){var Q=h(($[et]*C+50)/100);Q=Math.min(Math.max(Q,1),255),f[rt[et]]=Q}for(var At=[1,1.387039845,1.306562965,1.175875602,1,.785694958,.5411961,.275899379],Lt=0,Ot=0;Ot<8;Ot++)for(var Ct=0;Ct<8;Ct++)v[Lt]=1/(l[rt[Lt]]*At[Ot]*At[Ct]*8),x[Lt]=1/(f[rt[Lt]]*At[Ot]*At[Ct]*8),Lt++}(w<50?Math.floor(5e3/w):Math.floor(200-2*w)),o=w)}this.encode=function(w,C){C&&Et(C),F=new Array,q=0,S=7,pt(65496),pt(65504),pt(16),tt(74),tt(70),tt(73),tt(70),tt(0),tt(1),tt(1),tt(0),pt(1),pt(1),tt(0),tt(0),function(){pt(65499),pt(132),tt(0);for(var Pt=0;Pt<64;Pt++)tt(l[Pt]);tt(1);for(var qt=0;qt<64;qt++)tt(f[qt])}(),function(Pt,qt){pt(65472),pt(17),tt(8),pt(qt),pt(Pt),tt(3),tt(1),tt(17),tt(0),tt(2),tt(17),tt(1),tt(3),tt(17),tt(1)}(w.width,w.height),function(){pt(65476),pt(418),tt(0);for(var Pt=0;Pt<16;Pt++)tt(G[Pt+1]);for(var qt=0;qt<=11;qt++)tt(vt[qt]);tt(16);for(var Gt=0;Gt<16;Gt++)tt(bt[Gt+1]);for(var Qt=0;Qt<=161;Qt++)tt(k[Qt]);tt(1);for(var te=0;te<16;te++)tt(I[te+1]);for(var ie=0;ie<=11;ie++)tt(H[ie]);tt(17);for(var fe=0;fe<16;fe++)tt(R[fe+1]);for(var Ht=0;Ht<=161;Ht++)tt(ct[Ht])}(),pt(65498),pt(12),tt(3),tt(1),tt(0),tt(2),tt(17),tt(3),tt(17),tt(0),tt(63),tt(0);var E=0,W=0,J=0;q=0,S=7,this.encode.displayName="_encode_";for(var $,et,Q,At,Lt,Ot,Ct,Wt,at,O=w.data,Kt=w.width,Mt=w.height,wt=4*Kt,xt=0;xt<Mt;){for($=0;$<wt;){for(Lt=wt*xt+$,Ct=-1,Wt=0,at=0;at<64;at++)Ot=Lt+(Wt=at>>3)*wt+(Ct=4*(7&at)),xt+Wt>=Mt&&(Ot-=wt*(xt+1+Wt-Mt)),$+Ct>=wt&&(Ot-=$+Ct-wt+4),et=O[Ot++],Q=O[Ot++],At=O[Ot++],M[at]=(Nt[et]+Nt[Q+256>>0]+Nt[At+512>>0]>>16)-128,Z[at]=(Nt[et+768>>0]+Nt[Q+1024>>0]+Nt[At+1280>>0]>>16)-128,st[at]=(Nt[et+1280>>0]+Nt[Q+1536>>0]+Nt[At+1792>>0]>>16)-128;E=ft(M,v,E,e,a),W=ft(Z,x,W,r,u),J=ft(st,x,J,r,u),$+=32}xt+=8}if(S>=0){var kt=[];kt[1]=S+1,kt[0]=(1<<S+1)-1,mt(kt)}return pt(65497),new Uint8Array(F)},i=i||50,function(){for(var w=String.fromCharCode,C=0;C<256;C++)dt[C]=w(C)}(),e=ot(G,vt),r=ot(I,H),a=ot(bt,k),u=ot(R,ct),function(){for(var w=1,C=2,E=1;E<=15;E++){for(var W=w;W<C;W++)_[32767+W]=E,A[32767+W]=[],A[32767+W][1]=E,A[32767+W][0]=W;for(var J=-(C-1);J<=-w;J++)_[32767+J]=E,A[32767+J]=[],A[32767+J][1]=E,A[32767+J][0]=C-1+J;w<<=1,C<<=1}}(),function(){for(var w=0;w<256;w++)Nt[w]=19595*w,Nt[w+256>>0]=38470*w,Nt[w+512>>0]=7471*w+32768,Nt[w+768>>0]=-11059*w,Nt[w+1024>>0]=-21709*w,Nt[w+1280>>0]=32768*w+8421375,Nt[w+1536>>0]=-27439*w,Nt[w+1792>>0]=-5329*w}(),Et(i)}/**
 * @license
 * Copyright (c) 2017 Aras Abbasi
 *
 * Licensed under the MIT License.
 * http://opensource.org/licenses/mit-license
 */function Bn(i,e){if(this.pos=0,this.buffer=i,this.datav=new DataView(i.buffer),this.is_with_alpha=!!e,this.bottom_up=!0,this.flag=String.fromCharCode(this.buffer[0])+String.fromCharCode(this.buffer[1]),this.pos+=2,["BM","BA","CI","CP","IC","PT"].indexOf(this.flag)===-1)throw new Error("Invalid BMP File");this.parseHeader(),this.parseBGR()}function Ks(i){function e(G){if(!G)throw Error("assert :P")}function r(G,vt,bt){for(var k=0;4>k;k++)if(G[vt+k]!=bt.charCodeAt(k))return!0;return!1}function a(G,vt,bt,k,I){for(var H=0;H<I;H++)G[vt+H]=bt[k+H]}function u(G,vt,bt,k){for(var I=0;I<k;I++)G[vt+I]=bt}function o(G){return new Int32Array(G)}function h(G,vt){for(var bt=[],k=0;k<G;k++)bt.push(new vt);return bt}function l(G,vt){var bt=[];return function k(I,H,R){for(var ct=R[H],ot=0;ot<ct&&(I.push(R.length>H+1?[]:new vt),!(R.length<H+1));ot++)k(I[ot],H+1,R)}(bt,0,G),bt}var f=function(){var G=this;function vt(t,n){for(var c=1<<n-1>>>0;t&c;)c>>>=1;return c?(t&c-1)+c:t}function bt(t,n,c,d,g){e(!(d%c));do t[n+(d-=c)]=g;while(0<d)}function k(t,n,c,d,g){if(e(2328>=g),512>=g)var b=o(512);else if((b=o(g))==null)return 0;return function(y,L,N,P,T,X){var K,V,lt=L,nt=1<<N,z=o(16),U=o(16);for(e(T!=0),e(P!=null),e(y!=null),e(0<N),V=0;V<T;++V){if(15<P[V])return 0;++z[P[V]]}if(z[0]==T)return 0;for(U[1]=0,K=1;15>K;++K){if(z[K]>1<<K)return 0;U[K+1]=U[K]+z[K]}for(V=0;V<T;++V)K=P[V],0<P[V]&&(X[U[K]++]=V);if(U[15]==1)return(P=new I).g=0,P.value=X[0],bt(y,lt,1,nt,P),nt;var ut,gt=-1,ht=nt-1,jt=0,St=1,Rt=1,_t=1<<N;for(V=0,K=1,T=2;K<=N;++K,T<<=1){if(St+=Rt<<=1,0>(Rt-=z[K]))return 0;for(;0<z[K];--z[K])(P=new I).g=K,P.value=X[V++],bt(y,lt+jt,T,_t,P),jt=vt(jt,K)}for(K=N+1,T=2;15>=K;++K,T<<=1){if(St+=Rt<<=1,0>(Rt-=z[K]))return 0;for(;0<z[K];--z[K]){if(P=new I,(jt&ht)!=gt){for(lt+=_t,ut=1<<(gt=K)-N;15>gt&&!(0>=(ut-=z[gt]));)++gt,ut<<=1;nt+=_t=1<<(ut=gt-N),y[L+(gt=jt&ht)].g=ut+N,y[L+gt].value=lt-L-gt}P.g=K-N,P.value=X[V++],bt(y,lt+(jt>>N),T,_t,P),jt=vt(jt,K)}}return St!=2*U[15]-1?0:nt}(t,n,c,d,g,b)}function I(){this.value=this.g=0}function H(){this.value=this.g=0}function R(){this.G=h(5,I),this.H=o(5),this.jc=this.Qb=this.qb=this.nd=0,this.pd=h(We,H)}function ct(t,n,c,d){e(t!=null),e(n!=null),e(2147483648>d),t.Ca=254,t.I=0,t.b=-8,t.Ka=0,t.oa=n,t.pa=c,t.Jd=n,t.Yc=c+d,t.Zc=4<=d?c+d-4+1:c,$(t)}function ot(t,n){for(var c=0;0<n--;)c|=Q(t,128)<<n;return c}function mt(t,n){var c=ot(t,n);return et(t)?-c:c}function tt(t,n,c,d){var g,b=0;for(e(t!=null),e(n!=null),e(4294967288>d),t.Sb=d,t.Ra=0,t.u=0,t.h=0,4<d&&(d=4),g=0;g<d;++g)b+=n[c+g]<<8*g;t.Ra=b,t.bb=d,t.oa=n,t.pa=c}function pt(t){for(;8<=t.u&&t.bb<t.Sb;)t.Ra>>>=8,t.Ra+=t.oa[t.pa+t.bb]<<oi-8>>>0,++t.bb,t.u-=8;E(t)&&(t.h=1,t.u=0)}function ft(t,n){if(e(0<=n),!t.h&&n<=ai){var c=C(t)&ii[n];return t.u+=n,pt(t),c}return t.h=1,t.u=0}function Et(){this.b=this.Ca=this.I=0,this.oa=[],this.pa=0,this.Jd=[],this.Yc=0,this.Zc=[],this.Ka=0}function w(){this.Ra=0,this.oa=[],this.h=this.u=this.bb=this.Sb=this.pa=0}function C(t){return t.Ra>>>(t.u&oi-1)>>>0}function E(t){return e(t.bb<=t.Sb),t.h||t.bb==t.Sb&&t.u>oi}function W(t,n){t.u=n,t.h=E(t)}function J(t){t.u>=Hi&&(e(t.u>=Hi),pt(t))}function $(t){e(t!=null&&t.oa!=null),t.pa<t.Zc?(t.I=(t.oa[t.pa++]|t.I<<8)>>>0,t.b+=8):(e(t!=null&&t.oa!=null),t.pa<t.Yc?(t.b+=8,t.I=t.oa[t.pa++]|t.I<<8):t.Ka?t.b=0:(t.I<<=8,t.b+=8,t.Ka=1))}function et(t){return ot(t,1)}function Q(t,n){var c=t.Ca;0>t.b&&$(t);var d=t.b,g=c*n>>>8,b=(t.I>>>d>g)+0;for(b?(c-=g,t.I-=g+1<<d>>>0):c=g+1,d=c,g=0;256<=d;)g+=8,d>>=8;return d=7^g+an[d],t.b-=d,t.Ca=(c<<d)-1,b}function At(t,n,c){t[n+0]=c>>24&255,t[n+1]=c>>16&255,t[n+2]=c>>8&255,t[n+3]=c>>0&255}function Lt(t,n){return t[n+0]<<0|t[n+1]<<8}function Ot(t,n){return Lt(t,n)|t[n+2]<<16}function Ct(t,n){return Lt(t,n)|Lt(t,n+2)<<16}function Wt(t,n){var c=1<<n;return e(t!=null),e(0<n),t.X=o(c),t.X==null?0:(t.Mb=32-n,t.Xa=n,1)}function at(t,n){e(t!=null),e(n!=null),e(t.Xa==n.Xa),a(n.X,0,t.X,0,1<<n.Xa)}function O(){this.X=[],this.Xa=this.Mb=0}function Kt(t,n,c,d){e(c!=null),e(d!=null);var g=c[0],b=d[0];return g==0&&(g=(t*b+n/2)/n),b==0&&(b=(n*g+t/2)/t),0>=g||0>=b?0:(c[0]=g,d[0]=b,1)}function Mt(t,n){return t+(1<<n)-1>>>n}function wt(t,n){return((4278255360&t)+(4278255360&n)>>>0&4278255360)+((16711935&t)+(16711935&n)>>>0&16711935)>>>0}function xt(t,n){G[n]=function(c,d,g,b,y,L,N){var P;for(P=0;P<y;++P){var T=G[t](L[N+P-1],g,b+P);L[N+P]=wt(c[d+P],T)}}}function kt(){this.ud=this.hd=this.jd=0}function Pt(t,n){return((4278124286&(t^n))>>>1)+(t&n)>>>0}function qt(t){return 0<=t&&256>t?t:0>t?0:255<t?255:void 0}function Gt(t,n){return qt(t+(t-n+.5>>1))}function Qt(t,n,c){return Math.abs(n-c)-Math.abs(t-c)}function te(t,n,c,d,g,b,y){for(d=b[y-1],c=0;c<g;++c)b[y+c]=d=wt(t[n+c],d)}function ie(t,n,c,d,g){var b;for(b=0;b<c;++b){var y=t[n+b],L=y>>8&255,N=16711935&(N=(N=16711935&y)+((L<<16)+L));d[g+b]=(4278255360&y)+N>>>0}}function fe(t,n){n.jd=t>>0&255,n.hd=t>>8&255,n.ud=t>>16&255}function Ht(t,n,c,d,g,b){var y;for(y=0;y<d;++y){var L=n[c+y],N=L>>>8,P=L,T=255&(T=(T=L>>>16)+((t.jd<<24>>24)*(N<<24>>24)>>>5));P=255&(P=(P=P+((t.hd<<24>>24)*(N<<24>>24)>>>5))+((t.ud<<24>>24)*(T<<24>>24)>>>5)),g[b+y]=(4278255360&L)+(T<<16)+P}}function ee(t,n,c,d,g){G[n]=function(b,y,L,N,P,T,X,K,V){for(N=X;N<K;++N)for(X=0;X<V;++X)P[T++]=g(L[d(b[y++])])},G[t]=function(b,y,L,N,P,T,X){var K=8>>b.b,V=b.Ea,lt=b.K[0],nt=b.w;if(8>K)for(b=(1<<b.b)-1,nt=(1<<K)-1;y<L;++y){var z,U=0;for(z=0;z<V;++z)z&b||(U=d(N[P++])),T[X++]=g(lt[U&nt]),U>>=K}else G["VP8LMapColor"+c](N,P,lt,nt,T,X,y,L,V)}}function It(t,n,c,d,g){for(c=n+c;n<c;){var b=t[n++];d[g++]=b>>16&255,d[g++]=b>>8&255,d[g++]=b>>0&255}}function Ge(t,n,c,d,g){for(c=n+c;n<c;){var b=t[n++];d[g++]=b>>16&255,d[g++]=b>>8&255,d[g++]=b>>0&255,d[g++]=b>>24&255}}function oe(t,n,c,d,g){for(c=n+c;n<c;){var b=(y=t[n++])>>16&240|y>>12&15,y=y>>0&240|y>>28&15;d[g++]=b,d[g++]=y}}function An(t,n,c,d,g){for(c=n+c;n<c;){var b=(y=t[n++])>>16&248|y>>13&7,y=y>>5&224|y>>3&31;d[g++]=b,d[g++]=y}}function pe(t,n,c,d,g){for(c=n+c;n<c;){var b=t[n++];d[g++]=b>>0&255,d[g++]=b>>8&255,d[g++]=b>>16&255}}function we(t,n,c,d,g,b){if(b==0)for(c=n+c;n<c;)At(d,((b=t[n++])[0]>>24|b[1]>>8&65280|b[2]<<8&16711680|b[3]<<24)>>>0),g+=32;else a(d,g,t,n,c)}function qn(t,n){G[n][0]=G[t+"0"],G[n][1]=G[t+"1"],G[n][2]=G[t+"2"],G[n][3]=G[t+"3"],G[n][4]=G[t+"4"],G[n][5]=G[t+"5"],G[n][6]=G[t+"6"],G[n][7]=G[t+"7"],G[n][8]=G[t+"8"],G[n][9]=G[t+"9"],G[n][10]=G[t+"10"],G[n][11]=G[t+"11"],G[n][12]=G[t+"12"],G[n][13]=G[t+"13"],G[n][14]=G[t+"0"],G[n][15]=G[t+"0"]}function ce(t){return t==jo||t==Co||t==Ca||t==Oo}function xr(){this.eb=[],this.size=this.A=this.fb=0}function Ne(){this.y=[],this.f=[],this.ea=[],this.F=[],this.Tc=this.Ed=this.Cd=this.Fd=this.lb=this.Db=this.Ab=this.fa=this.J=this.W=this.N=this.O=0}function xn(){this.Rd=this.height=this.width=this.S=0,this.f={},this.f.RGBA=new xr,this.f.kb=new Ne,this.sd=null}function Kn(){this.width=[0],this.height=[0],this.Pd=[0],this.Qd=[0],this.format=[0]}function Ae(){this.Id=this.fd=this.Md=this.hb=this.ib=this.da=this.bd=this.cd=this.j=this.v=this.Da=this.Sd=this.ob=0}function Jt(t){return alert("todo:WebPSamplerProcessPlane"),t.T}function ue(t,n){var c=t.T,d=n.ba.f.RGBA,g=d.eb,b=d.fb+t.ka*d.A,y=Ln[n.ba.S],L=t.y,N=t.O,P=t.f,T=t.N,X=t.ea,K=t.W,V=n.cc,lt=n.dc,nt=n.Mc,z=n.Nc,U=t.ka,ut=t.ka+t.T,gt=t.U,ht=gt+1>>1;for(U==0?y(L,N,null,null,P,T,X,K,P,T,X,K,g,b,null,null,gt):(y(n.ec,n.fc,L,N,V,lt,nt,z,P,T,X,K,g,b-d.A,g,b,gt),++c);U+2<ut;U+=2)V=P,lt=T,nt=X,z=K,T+=t.Rc,K+=t.Rc,b+=2*d.A,y(L,(N+=2*t.fa)-t.fa,L,N,V,lt,nt,z,P,T,X,K,g,b-d.A,g,b,gt);return N+=t.fa,t.j+ut<t.o?(a(n.ec,n.fc,L,N,gt),a(n.cc,n.dc,P,T,ht),a(n.Mc,n.Nc,X,K,ht),c--):1&ut||y(L,N,null,null,P,T,X,K,P,T,X,K,g,b+d.A,null,null,gt),c}function Sr(t,n,c){var d=t.F,g=[t.J];if(d!=null){var b=t.U,y=n.ba.S,L=y==ja||y==Ca;n=n.ba.f.RGBA;var N=[0],P=t.ka;N[0]=t.T,t.Kb&&(P==0?--N[0]:(--P,g[0]-=t.width),t.j+t.ka+t.T==t.o&&(N[0]=t.o-t.j-P));var T=n.eb;P=n.fb+P*n.A,t=ye(d,g[0],t.width,b,N,T,P+(L?0:3),n.A),e(c==N),t&&ce(y)&&yn(T,P,L,b,N,n.A)}return 0}function He(t){var n=t.ma,c=n.ba.S,d=11>c,g=c==Ia||c==Fa||c==ja||c==Fo||c==12||ce(c);if(n.memory=null,n.Ib=null,n.Jb=null,n.Nd=null,!zi(n.Oa,t,g?11:12))return 0;if(g&&ce(c)&&yt(),t.da)alert("todo:use_scaling");else{if(d){if(n.Ib=Jt,t.Kb){if(c=t.U+1>>1,n.memory=o(t.U+2*c),n.memory==null)return 0;n.ec=n.memory,n.fc=0,n.cc=n.ec,n.dc=n.fc+t.U,n.Mc=n.cc,n.Nc=n.dc+c,n.Ib=ue,yt()}}else alert("todo:EmitYUV");g&&(n.Jb=Sr,d&&Y())}if(d&&!vs){for(t=0;256>t;++t)kc[t]=89858*(t-128)+Ba>>Oa,jc[t]=-22014*(t-128)+Ba,Fc[t]=-45773*(t-128),Ic[t]=113618*(t-128)+Ba>>Oa;for(t=Ki;t<Eo;++t)n=76283*(t-16)+Ba>>Oa,Cc[t-Ki]=ln(n,255),Oc[t-Ki]=ln(n+8>>4,15);vs=1}return 1}function Dn(t){var n=t.ma,c=t.U,d=t.T;return e(!(1&t.ka)),0>=c||0>=d?0:(c=n.Ib(t,n),n.Jb!=null&&n.Jb(t,n,c),n.Dc+=c,1)}function Rn(t){t.ma.memory=null}function Tt(t,n,c,d){return ft(t,8)!=47?0:(n[0]=ft(t,14)+1,c[0]=ft(t,14)+1,d[0]=ft(t,1),ft(t,3)!=0?0:!t.h)}function Sn(t,n){if(4>t)return t+1;var c=t-2>>1;return(2+(1&t)<<c)+ft(n,c)+1}function Tn(t,n){return 120<n?n-120:1<=(c=((c=pc[n-1])>>4)*t+(8-(15&c)))?c:1;var c}function nn(t,n,c){var d=C(c),g=t[n+=255&d].g-8;return 0<g&&(W(c,c.u+8),d=C(c),n+=t[n].value,n+=d&(1<<g)-1),W(c,c.u+t[n].g),t[n].value}function qe(t,n,c){return c.g+=t.g,c.value+=t.value<<n>>>0,e(8>=c.g),t.g}function Oe(t,n,c){var d=t.xc;return e((n=d==0?0:t.vc[t.md*(c>>d)+(n>>d)])<t.Wb),t.Ya[n]}function hn(t,n,c,d){var g=t.ab,b=t.c*n,y=t.C;n=y+n;var L=c,N=d;for(d=t.Ta,c=t.Ua;0<g--;){var P=t.gc[g],T=y,X=n,K=L,V=N,lt=(N=d,L=c,P.Ea);switch(e(T<X),e(X<=P.nc),P.hc){case 2:Na(K,V,(X-T)*lt,N,L);break;case 0:var nt=T,z=X,U=N,ut=L,gt=(_t=P).Ea;nt==0&&(ko(K,V,null,null,1,U,ut),te(K,V+1,0,0,gt-1,U,ut+1),V+=gt,ut+=gt,++nt);for(var ht=1<<_t.b,jt=ht-1,St=Mt(gt,_t.b),Rt=_t.K,_t=_t.w+(nt>>_t.b)*St;nt<z;){var se=Rt,he=_t,ae=1;for(Wi(K,V,U,ut-gt,1,U,ut);ae<gt;){var ne=(ae&~jt)+ht;ne>gt&&(ne=gt),(0,hr[se[he++]>>8&15])(K,V+ +ae,U,ut+ae-gt,ne-ae,U,ut+ae),ae=ne}V+=gt,ut+=gt,++nt&jt||(_t+=St)}X!=P.nc&&a(N,L-lt,N,L+(X-T-1)*lt,lt);break;case 1:for(lt=K,z=V,gt=(K=P.Ea)-(ut=K&~(U=(V=1<<P.b)-1)),nt=Mt(K,P.b),ht=P.K,P=P.w+(T>>P.b)*nt;T<X;){for(jt=ht,St=P,Rt=new kt,_t=z+ut,se=z+K;z<_t;)fe(jt[St++],Rt),jr(Rt,lt,z,V,N,L),z+=V,L+=V;z<se&&(fe(jt[St++],Rt),jr(Rt,lt,z,gt,N,L),z+=gt,L+=gt),++T&U||(P+=nt)}break;case 3:if(K==N&&V==L&&0<P.b){for(z=N,K=lt=L+(X-T)*lt-(ut=(X-T)*Mt(P.Ea,P.b)),V=N,U=L,nt=[],ut=(gt=ut)-1;0<=ut;--ut)nt[ut]=V[U+ut];for(ut=gt-1;0<=ut;--ut)z[K+ut]=nt[ut];mn(P,T,X,N,lt,N,L)}else mn(P,T,X,K,V,N,L)}L=d,N=c}N!=c&&a(d,c,L,N,b)}function zr(t,n){var c=t.V,d=t.Ba+t.c*t.C,g=n-t.C;if(e(n<=t.l.o),e(16>=g),0<g){var b=t.l,y=t.Ta,L=t.Ua,N=b.width;if(hn(t,g,c,d),g=L=[L],e((c=t.C)<(d=n)),e(b.v<b.va),d>b.o&&(d=b.o),c<b.j){var P=b.j-c;c=b.j,g[0]+=P*N}if(c>=d?c=0:(g[0]+=4*b.v,b.ka=c-b.j,b.U=b.va-b.v,b.T=d-c,c=1),c){if(L=L[0],11>(c=t.ca).S){var T=c.f.RGBA,X=(d=c.S,g=b.U,b=b.T,P=T.eb,T.A),K=b;for(T=T.fb+t.Ma*T.A;0<K--;){var V=y,lt=L,nt=g,z=P,U=T;switch(d){case ka:on(V,lt,nt,z,U);break;case Ia:Qe(V,lt,nt,z,U);break;case jo:Qe(V,lt,nt,z,U),yn(z,U,0,nt,1,0);break;case ss:er(V,lt,nt,z,U);break;case Fa:we(V,lt,nt,z,U,1);break;case Co:we(V,lt,nt,z,U,1),yn(z,U,0,nt,1,0);break;case ja:we(V,lt,nt,z,U,0);break;case Ca:we(V,lt,nt,z,U,0),yn(z,U,1,nt,1,0);break;case Fo:lr(V,lt,nt,z,U);break;case Oo:lr(V,lt,nt,z,U),be(z,U,nt,1,0);break;case cs:tr(V,lt,nt,z,U);break;default:e(0)}L+=N,T+=X}t.Ma+=b}else alert("todo:EmitRescaledRowsYUVA");e(t.Ma<=c.height)}}t.C=n,e(t.C<=t.i)}function Zn(t){var n;if(0<t.ua)return 0;for(n=0;n<t.Wb;++n){var c=t.Ya[n].G,d=t.Ya[n].H;if(0<c[1][d[1]+0].g||0<c[2][d[2]+0].g||0<c[3][d[3]+0].g)return 0}return 1}function _n(t,n,c,d,g,b){if(t.Z!=0){var y=t.qd,L=t.rd;for(e(gr[t.Z]!=null);n<c;++n)gr[t.Z](y,L,d,g,d,g,b),y=d,L=g,g+=b;t.qd=y,t.rd=L}}function Pn(t,n){var c=t.l.ma,d=c.Z==0||c.Z==1?t.l.j:t.C;if(d=t.C<d?d:t.C,e(n<=t.l.o),n>d){var g=t.l.width,b=c.ca,y=c.tb+g*d,L=t.V,N=t.Ba+t.c*d,P=t.gc;e(t.ab==1),e(P[0].hc==3),Aa(P[0],d,n,L,N,b,y),_n(c,d,n,b,y,g)}t.C=t.Ma=n}function kn(t,n,c,d,g,b,y){var L=t.$/d,N=t.$%d,P=t.m,T=t.s,X=c+t.$,K=X;g=c+d*g;var V=c+d*b,lt=280+T.ua,nt=t.Pb?L:16777216,z=0<T.ua?T.Wa:null,U=T.wc,ut=X<V?Oe(T,N,L):null;e(t.C<b),e(V<=g);var gt=!1;t:for(;;){for(;gt||X<V;){var ht=0;if(L>=nt){var jt=X-c;e((nt=t).Pb),nt.wd=nt.m,nt.xd=jt,0<nt.s.ua&&at(nt.s.Wa,nt.s.vb),nt=L+mc}if(N&U||(ut=Oe(T,N,L)),e(ut!=null),ut.Qb&&(n[X]=ut.qb,gt=!0),!gt)if(J(P),ut.jc){ht=P,jt=n;var St=X,Rt=ut.pd[C(ht)&We-1];e(ut.jc),256>Rt.g?(W(ht,ht.u+Rt.g),jt[St]=Rt.value,ht=0):(W(ht,ht.u+Rt.g-256),e(256<=Rt.value),ht=Rt.value),ht==0&&(gt=!0)}else ht=nn(ut.G[0],ut.H[0],P);if(P.h)break;if(gt||256>ht){if(!gt)if(ut.nd)n[X]=(ut.qb|ht<<8)>>>0;else{if(J(P),gt=nn(ut.G[1],ut.H[1],P),J(P),jt=nn(ut.G[2],ut.H[2],P),St=nn(ut.G[3],ut.H[3],P),P.h)break;n[X]=(St<<24|gt<<16|ht<<8|jt)>>>0}if(gt=!1,++X,++N>=d&&(N=0,++L,y!=null&&L<=b&&!(L%16)&&y(t,L),z!=null))for(;K<X;)ht=n[K++],z.X[(506832829*ht&**********)>>>z.Mb]=ht}else if(280>ht){if(ht=Sn(ht-256,P),jt=nn(ut.G[4],ut.H[4],P),J(P),jt=Tn(d,jt=Sn(jt,P)),P.h)break;if(X-c<jt||g-X<ht)break t;for(St=0;St<ht;++St)n[X+St]=n[X+St-jt];for(X+=ht,N+=ht;N>=d;)N-=d,++L,y!=null&&L<=b&&!(L%16)&&y(t,L);if(e(X<=g),N&U&&(ut=Oe(T,N,L)),z!=null)for(;K<X;)ht=n[K++],z.X[(506832829*ht&**********)>>>z.Mb]=ht}else{if(!(ht<lt))break t;for(gt=ht-280,e(z!=null);K<X;)ht=n[K++],z.X[(506832829*ht&**********)>>>z.Mb]=ht;ht=X,e(!(gt>>>(jt=z).Xa)),n[ht]=jt.X[gt],gt=!0}gt||e(P.h==E(P))}if(t.Pb&&P.h&&X<g)e(t.m.h),t.a=5,t.m=t.wd,t.$=t.xd,0<t.s.ua&&at(t.s.vb,t.s.Wa);else{if(P.h)break t;y!=null&&y(t,L>b?b:L),t.a=0,t.$=X-c}return 1}return t.a=3,0}function zn(t){e(t!=null),t.vc=null,t.yc=null,t.Ya=null;var n=t.Wa;n!=null&&(n.X=null),t.vb=null,e(t!=null)}function gn(){var t=new Po;return t==null?null:(t.a=0,t.xb=ls,qn("Predictor","VP8LPredictors"),qn("Predictor","VP8LPredictors_C"),qn("PredictorAdd","VP8LPredictorsAdd"),qn("PredictorAdd","VP8LPredictorsAdd_C"),Na=ie,jr=Ht,on=It,Qe=Ge,lr=oe,tr=An,er=pe,G.VP8LMapColor32b=si,G.VP8LMapColor8b=xa,t)}function Un(t,n,c,d,g){var b=1,y=[t],L=[n],N=d.m,P=d.s,T=null,X=0;t:for(;;){if(c)for(;b&&ft(N,1);){var K=y,V=L,lt=d,nt=1,z=lt.m,U=lt.gc[lt.ab],ut=ft(z,2);if(lt.Oc&1<<ut)b=0;else{switch(lt.Oc|=1<<ut,U.hc=ut,U.Ea=K[0],U.nc=V[0],U.K=[null],++lt.ab,e(4>=lt.ab),ut){case 0:case 1:U.b=ft(z,3)+2,nt=Un(Mt(U.Ea,U.b),Mt(U.nc,U.b),0,lt,U.K),U.K=U.K[0];break;case 3:var gt,ht=ft(z,8)+1,jt=16<ht?0:4<ht?1:2<ht?2:3;if(K[0]=Mt(U.Ea,jt),U.b=jt,gt=nt=Un(ht,1,0,lt,U.K)){var St,Rt=ht,_t=U,se=1<<(8>>_t.b),he=o(se);if(he==null)gt=0;else{var ae=_t.K[0],ne=_t.w;for(he[0]=_t.K[0][0],St=1;St<1*Rt;++St)he[St]=wt(ae[ne+St],he[St-1]);for(;St<4*se;++St)he[St]=0;_t.K[0]=null,_t.K[0]=he,gt=1}}nt=gt;break;case 2:break;default:e(0)}b=nt}}if(y=y[0],L=L[0],b&&ft(N,1)&&!(b=1<=(X=ft(N,4))&&11>=X)){d.a=3;break t}var ge;if(ge=b)e:{var de,$t,Me,sn=d,Ee=y,cn=L,le=X,dn=c,pn=sn.m,Te=sn.s,Ve=[null],en=1,Nn=0,Gn=gc[le];n:for(;;){if(dn&&ft(pn,1)){var ze=ft(pn,3)+2,ar=Mt(Ee,ze),Er=Mt(cn,ze),fi=ar*Er;if(!Un(ar,Er,0,sn,Ve))break n;for(Ve=Ve[0],Te.xc=ze,de=0;de<fi;++de){var mr=Ve[de]>>8&65535;Ve[de]=mr,mr>=en&&(en=mr+1)}}if(pn.h)break n;for($t=0;5>$t;++$t){var Se=us[$t];!$t&&0<le&&(Se+=1<<le),Nn<Se&&(Nn=Se)}var qo=h(en*Gn,I),ws=en,Ls=h(ws,R);if(Ls==null)var Ea=null;else e(65536>=ws),Ea=Ls;var Zi=o(Nn);if(Ea==null||Zi==null||qo==null){sn.a=1;break n}var qa=qo;for(de=Me=0;de<en;++de){var On=Ea[de],di=On.G,pi=On.H,Ns=0,Da=1,As=0;for($t=0;5>$t;++$t){Se=us[$t],di[$t]=qa,pi[$t]=Me,!$t&&0<le&&(Se+=1<<le);i:{var Ra,Do=Se,Ta=sn,$i=Zi,Ec=qa,qc=Me,Ro=0,vr=Ta.m,Dc=ft(vr,1);if(u($i,0,0,Do),Dc){var Rc=ft(vr,1)+1,Tc=ft(vr,1),xs=ft(vr,Tc==0?1:8);$i[xs]=1,Rc==2&&($i[xs=ft(vr,8)]=1);var za=1}else{var Ss=o(19),_s=ft(vr,4)+4;if(19<_s){Ta.a=3;var Ua=0;break i}for(Ra=0;Ra<_s;++Ra)Ss[dc[Ra]]=ft(vr,3);var To=void 0,Qi=void 0,Ps=Ta,zc=Ss,Ha=Do,ks=$i,zo=0,br=Ps.m,Is=8,Fs=h(128,I);r:for(;k(Fs,0,7,zc,19);){if(ft(br,1)){var Uc=2+2*ft(br,3);if((To=2+ft(br,Uc))>Ha)break r}else To=Ha;for(Qi=0;Qi<Ha&&To--;){J(br);var js=Fs[0+(127&C(br))];W(br,br.u+js.g);var gi=js.value;if(16>gi)ks[Qi++]=gi,gi!=0&&(Is=gi);else{var Hc=gi==16,Cs=gi-16,Wc=lc[Cs],Os=ft(br,hc[Cs])+Wc;if(Qi+Os>Ha)break r;for(var Vc=Hc?Is:0;0<Os--;)ks[Qi++]=Vc}}zo=1;break r}zo||(Ps.a=3),za=zo}(za=za&&!vr.h)&&(Ro=k(Ec,qc,8,$i,Do)),za&&Ro!=0?Ua=Ro:(Ta.a=3,Ua=0)}if(Ua==0)break n;if(Da&&fc[$t]==1&&(Da=qa[Me].g==0),Ns+=qa[Me].g,Me+=Ua,3>=$t){var ta,Uo=Zi[0];for(ta=1;ta<Se;++ta)Zi[ta]>Uo&&(Uo=Zi[ta]);As+=Uo}}if(On.nd=Da,On.Qb=0,Da&&(On.qb=(di[3][pi[3]+0].value<<24|di[1][pi[1]+0].value<<16|di[2][pi[2]+0].value)>>>0,Ns==0&&256>di[0][pi[0]+0].value&&(On.Qb=1,On.qb+=di[0][pi[0]+0].value<<8)),On.jc=!On.Qb&&6>As,On.jc){var Wa,or=On;for(Wa=0;Wa<We;++Wa){var yr=Wa,wr=or.pd[yr],Va=or.G[0][or.H[0]+yr];256<=Va.value?(wr.g=Va.g+256,wr.value=Va.value):(wr.g=0,wr.value=0,yr>>=qe(Va,8,wr),yr>>=qe(or.G[1][or.H[1]+yr],16,wr),yr>>=qe(or.G[2][or.H[2]+yr],0,wr),qe(or.G[3][or.H[3]+yr],24,wr))}}}Te.vc=Ve,Te.Wb=en,Te.Ya=Ea,Te.yc=qo,ge=1;break e}ge=0}if(!(b=ge)){d.a=3;break t}if(0<X){if(P.ua=1<<X,!Wt(P.Wa,X)){d.a=1,b=0;break t}}else P.ua=0;var Ho=d,Bs=y,Gc=L,Wo=Ho.s,Vo=Wo.xc;if(Ho.c=Bs,Ho.i=Gc,Wo.md=Mt(Bs,Vo),Wo.wc=Vo==0?-1:(1<<Vo)-1,c){d.xb=Ac;break t}if((T=o(y*L))==null){d.a=1,b=0;break t}b=(b=kn(d,T,0,y,L,L,null))&&!N.h;break t}return b?(g!=null?g[0]=T:(e(T==null),e(c)),d.$=0,c||zn(P)):zn(P),b}function _r(t,n){var c=t.c*t.i,d=c+n+16*n;return e(t.c<=n),t.V=o(d),t.V==null?(t.Ta=null,t.Ua=0,t.a=1,0):(t.Ta=t.V,t.Ua=t.Ba+c+n,1)}function Ur(t,n){var c=t.C,d=n-c,g=t.V,b=t.Ba+t.c*c;for(e(n<=t.l.o);0<d;){var y=16<d?16:d,L=t.l.ma,N=t.l.width,P=N*y,T=L.ca,X=L.tb+N*c,K=t.Ta,V=t.Ua;hn(t,y,g,b),Ie(K,V,T,X,P),_n(L,c,c+y,T,X,N),d-=y,g+=y*t.c,c+=y}e(c==n),t.C=t.Ma=n}function Hr(){this.ub=this.yd=this.td=this.Rb=0}function Wr(){this.Kd=this.Ld=this.Ud=this.Td=this.i=this.c=0}function Vr(){this.Fb=this.Bb=this.Cb=0,this.Zb=o(4),this.Lb=o(4)}function na(){this.Yb=function(){var t=[];return function n(c,d,g){for(var b=g[d],y=0;y<b&&(c.push(g.length>d+1?[]:0),!(g.length<d+1));y++)n(c[y],d+1,g)}(t,0,[3,11]),t}()}function ro(){this.jb=o(3),this.Wc=l([4,8],na),this.Xc=l([4,17],na)}function io(){this.Pc=this.wb=this.Tb=this.zd=0,this.vd=new o(4),this.od=new o(4)}function Gr(){this.ld=this.La=this.dd=this.tc=0}function ra(){this.Na=this.la=0}function ao(){this.Sc=[0,0],this.Eb=[0,0],this.Qc=[0,0],this.ia=this.lc=0}function Si(){this.ad=o(384),this.Za=0,this.Ob=o(16),this.$b=this.Ad=this.ia=this.Gc=this.Hc=this.Dd=0}function oo(){this.uc=this.M=this.Nb=0,this.wa=Array(new Gr),this.Y=0,this.ya=Array(new Si),this.aa=0,this.l=new Jr}function ia(){this.y=o(16),this.f=o(8),this.ea=o(8)}function so(){this.cb=this.a=0,this.sc="",this.m=new Et,this.Od=new Hr,this.Kc=new Wr,this.ed=new io,this.Qa=new Vr,this.Ic=this.$c=this.Aa=0,this.D=new oo,this.Xb=this.Va=this.Hb=this.zb=this.yb=this.Ub=this.za=0,this.Jc=h(8,Et),this.ia=0,this.pb=h(4,ao),this.Pa=new ro,this.Bd=this.kc=0,this.Ac=[],this.Bc=0,this.zc=[0,0,0,0],this.Gd=Array(new ia),this.Hd=0,this.rb=Array(new ra),this.sb=0,this.wa=Array(new Gr),this.Y=0,this.oc=[],this.pc=0,this.sa=[],this.ta=0,this.qa=[],this.ra=0,this.Ha=[],this.B=this.R=this.Ia=0,this.Ec=[],this.M=this.ja=this.Vb=this.Fc=0,this.ya=Array(new Si),this.L=this.aa=0,this.gd=l([4,2],Gr),this.ga=null,this.Fa=[],this.Cc=this.qc=this.P=0,this.Gb=[],this.Uc=0,this.mb=[],this.nb=0,this.rc=[],this.Ga=this.Vc=0}function Jr(){this.T=this.U=this.ka=this.height=this.width=0,this.y=[],this.f=[],this.ea=[],this.Rc=this.fa=this.W=this.N=this.O=0,this.ma="void",this.put="VP8IoPutHook",this.ac="VP8IoSetupHook",this.bc="VP8IoTeardownHook",this.ha=this.Kb=0,this.data=[],this.hb=this.ib=this.da=this.o=this.j=this.va=this.v=this.Da=this.ob=this.w=0,this.F=[],this.J=0}function co(){var t=new so;return t!=null&&(t.a=0,t.sc="OK",t.cb=0,t.Xb=0,Xi||(Xi=sa)),t}function Pe(t,n,c){return t.a==0&&(t.a=n,t.sc=c,t.cb=0),0}function aa(t,n,c){return 3<=c&&t[n+0]==157&&t[n+1]==1&&t[n+2]==42}function oa(t,n){if(t==null)return 0;if(t.a=0,t.sc="OK",n==null)return Pe(t,2,"null VP8Io passed to VP8GetHeaders()");var c=n.data,d=n.w,g=n.ha;if(4>g)return Pe(t,7,"Truncated header.");var b=c[d+0]|c[d+1]<<8|c[d+2]<<16,y=t.Od;if(y.Rb=!(1&b),y.td=b>>1&7,y.yd=b>>4&1,y.ub=b>>5,3<y.td)return Pe(t,3,"Incorrect keyframe parameters.");if(!y.yd)return Pe(t,4,"Frame not displayable.");d+=3,g-=3;var L=t.Kc;if(y.Rb){if(7>g)return Pe(t,7,"cannot parse picture header");if(!aa(c,d,g))return Pe(t,3,"Bad code word");L.c=16383&(c[d+4]<<8|c[d+3]),L.Td=c[d+4]>>6,L.i=16383&(c[d+6]<<8|c[d+5]),L.Ud=c[d+6]>>6,d+=7,g-=7,t.za=L.c+15>>4,t.Ub=L.i+15>>4,n.width=L.c,n.height=L.i,n.Da=0,n.j=0,n.v=0,n.va=n.width,n.o=n.height,n.da=0,n.ib=n.width,n.hb=n.height,n.U=n.width,n.T=n.height,u((b=t.Pa).jb,0,255,b.jb.length),e((b=t.Qa)!=null),b.Cb=0,b.Bb=0,b.Fb=1,u(b.Zb,0,0,b.Zb.length),u(b.Lb,0,0,b.Lb)}if(y.ub>g)return Pe(t,7,"bad partition length");ct(b=t.m,c,d,y.ub),d+=y.ub,g-=y.ub,y.Rb&&(L.Ld=et(b),L.Kd=et(b)),L=t.Qa;var N,P=t.Pa;if(e(b!=null),e(L!=null),L.Cb=et(b),L.Cb){if(L.Bb=et(b),et(b)){for(L.Fb=et(b),N=0;4>N;++N)L.Zb[N]=et(b)?mt(b,7):0;for(N=0;4>N;++N)L.Lb[N]=et(b)?mt(b,6):0}if(L.Bb)for(N=0;3>N;++N)P.jb[N]=et(b)?ot(b,8):255}else L.Bb=0;if(b.Ka)return Pe(t,3,"cannot parse segment header");if((L=t.ed).zd=et(b),L.Tb=ot(b,6),L.wb=ot(b,3),L.Pc=et(b),L.Pc&&et(b)){for(P=0;4>P;++P)et(b)&&(L.vd[P]=mt(b,6));for(P=0;4>P;++P)et(b)&&(L.od[P]=mt(b,6))}if(t.L=L.Tb==0?0:L.zd?1:2,b.Ka)return Pe(t,3,"cannot parse filter header");var T=g;if(g=N=d,d=N+T,L=T,t.Xb=(1<<ot(t.m,2))-1,T<3*(P=t.Xb))c=7;else{for(N+=3*P,L-=3*P,T=0;T<P;++T){var X=c[g+0]|c[g+1]<<8|c[g+2]<<16;X>L&&(X=L),ct(t.Jc[+T],c,N,X),N+=X,L-=X,g+=3}ct(t.Jc[+P],c,N,L),c=N<d?0:5}if(c!=0)return Pe(t,c,"cannot parse partitions");for(c=ot(N=t.m,7),g=et(N)?mt(N,4):0,d=et(N)?mt(N,4):0,L=et(N)?mt(N,4):0,P=et(N)?mt(N,4):0,N=et(N)?mt(N,4):0,T=t.Qa,X=0;4>X;++X){if(T.Cb){var K=T.Zb[X];T.Fb||(K+=c)}else{if(0<X){t.pb[X]=t.pb[0];continue}K=c}var V=t.pb[X];V.Sc[0]=Bo[ln(K+g,127)],V.Sc[1]=Mo[ln(K+0,127)],V.Eb[0]=2*Bo[ln(K+d,127)],V.Eb[1]=101581*Mo[ln(K+L,127)]>>16,8>V.Eb[1]&&(V.Eb[1]=8),V.Qc[0]=Bo[ln(K+P,117)],V.Qc[1]=Mo[ln(K+N,127)],V.lc=K+N}if(!y.Rb)return Pe(t,4,"Not a key frame.");for(et(b),y=t.Pa,c=0;4>c;++c){for(g=0;8>g;++g)for(d=0;3>d;++d)for(L=0;11>L;++L)P=Q(b,Lc[c][g][d][L])?ot(b,8):yc[c][g][d][L],y.Wc[c][g].Yb[d][L]=P;for(g=0;17>g;++g)y.Xc[c][g]=y.Wc[c][Nc[g]]}return t.kc=et(b),t.kc&&(t.Bd=ot(b,8)),t.cb=1}function sa(t,n,c,d,g,b,y){var L=n[g].Yb[c];for(c=0;16>g;++g){if(!Q(t,L[c+0]))return g;for(;!Q(t,L[c+1]);)if(L=n[++g].Yb[0],c=0,g==16)return 16;var N=n[g+1].Yb;if(Q(t,L[c+2])){var P=t,T=0;if(Q(P,(K=L)[(X=c)+3]))if(Q(P,K[X+6])){for(L=0,X=2*(T=Q(P,K[X+8]))+(K=Q(P,K[X+9+T])),T=0,K=vc[X];K[L];++L)T+=T+Q(P,K[L]);T+=3+(8<<X)}else Q(P,K[X+7])?(T=7+2*Q(P,165),T+=Q(P,145)):T=5+Q(P,159);else T=Q(P,K[X+4])?3+Q(P,K[X+5]):2;L=N[2]}else T=1,L=N[1];N=y+bc[g],0>(P=t).b&&$(P);var X,K=P.b,V=(X=P.Ca>>1)-(P.I>>K)>>31;--P.b,P.Ca+=V,P.Ca|=1,P.I-=(X+1&V)<<K,b[N]=((T^V)-V)*d[(0<g)+0]}return 16}function _i(t){var n=t.rb[t.sb-1];n.la=0,n.Na=0,u(t.zc,0,0,t.zc.length),t.ja=0}function uo(t,n){if(t==null)return 0;if(n==null)return Pe(t,2,"NULL VP8Io parameter in VP8Decode().");if(!t.cb&&!oa(t,n))return 0;if(e(t.cb),n.ac==null||n.ac(n)){n.ob&&(t.L=0);var c=Ma[t.L];if(t.L==2?(t.yb=0,t.zb=0):(t.yb=n.v-c>>4,t.zb=n.j-c>>4,0>t.yb&&(t.yb=0),0>t.zb&&(t.zb=0)),t.Va=n.o+15+c>>4,t.Hb=n.va+15+c>>4,t.Hb>t.za&&(t.Hb=t.za),t.Va>t.Ub&&(t.Va=t.Ub),0<t.L){var d=t.ed;for(c=0;4>c;++c){var g;if(t.Qa.Cb){var b=t.Qa.Lb[c];t.Qa.Fb||(b+=d.Tb)}else b=d.Tb;for(g=0;1>=g;++g){var y=t.gd[c][g],L=b;if(d.Pc&&(L+=d.vd[0],g&&(L+=d.od[0])),0<(L=0>L?0:63<L?63:L)){var N=L;0<d.wb&&(N=4<d.wb?N>>2:N>>1)>9-d.wb&&(N=9-d.wb),1>N&&(N=1),y.dd=N,y.tc=2*L+N,y.ld=40<=L?2:15<=L?1:0}else y.tc=0;y.La=g}}}c=0}else Pe(t,6,"Frame setup failed"),c=t.a;if(c=c==0){if(c){t.$c=0,0<t.Aa||(t.Ic=Mc);t:{c=t.Ic,d=4*(N=t.za);var P=32*N,T=N+1,X=0<t.L?N*(0<t.Aa?2:1):0,K=(t.Aa==2?2:1)*N;if((y=d+832+(g=3*(16*c+Ma[t.L])/2*P)+(b=t.Fa!=null&&0<t.Fa.length?t.Kc.c*t.Kc.i:0))!=y)c=0;else{if(y>t.Vb){if(t.Vb=0,t.Ec=o(y),t.Fc=0,t.Ec==null){c=Pe(t,1,"no memory during frame initialization.");break t}t.Vb=y}y=t.Ec,L=t.Fc,t.Ac=y,t.Bc=L,L+=d,t.Gd=h(P,ia),t.Hd=0,t.rb=h(T+1,ra),t.sb=1,t.wa=X?h(X,Gr):null,t.Y=0,t.D.Nb=0,t.D.wa=t.wa,t.D.Y=t.Y,0<t.Aa&&(t.D.Y+=N),e(!0),t.oc=y,t.pc=L,L+=832,t.ya=h(K,Si),t.aa=0,t.D.ya=t.ya,t.D.aa=t.aa,t.Aa==2&&(t.D.aa+=N),t.R=16*N,t.B=8*N,N=(P=Ma[t.L])*t.R,P=P/2*t.B,t.sa=y,t.ta=L+N,t.qa=t.sa,t.ra=t.ta+16*c*t.R+P,t.Ha=t.qa,t.Ia=t.ra+8*c*t.B+P,t.$c=0,L+=g,t.mb=b?y:null,t.nb=b?L:null,e(L+b<=t.Fc+t.Vb),_i(t),u(t.Ac,t.Bc,0,d),c=1}}if(c){if(n.ka=0,n.y=t.sa,n.O=t.ta,n.f=t.qa,n.N=t.ra,n.ea=t.Ha,n.Vd=t.Ia,n.fa=t.R,n.Rc=t.B,n.F=null,n.J=0,!_a){for(c=-255;255>=c;++c)De[255+c]=0>c?-c:c;for(c=-1020;1020>=c;++c)rr[1020+c]=-128>c?-128:127<c?127:c;for(c=-112;112>=c;++c)Yi[112+c]=-16>c?-16:15<c?15:c;for(c=-255;510>=c;++c)li[255+c]=0>c?0:255<c?255:c;_a=1}ci=fo,nr=ho,Vi=ua,tn=lo,vn=ha,ke=ca,ui=Ci,Sa=Ir,Gi=_o,Cr=Oi,Or=So,fr=Qr,Br=Bi,hi=ya,Mr=ba,dr=Wn,Ji=Qn,bn=xo,Cn[0]=Hn,Cn[1]=po,Cn[2]=bo,Cn[3]=yo,Cn[4]=da,Cn[5]=Zr,Cn[6]=pa,Cn[7]=Ii,Cn[8]=Lo,Cn[9]=wo,pr[0]=la,pr[1]=mo,pr[2]=$n,pr[3]=Xr,pr[4]=Je,pr[5]=vo,pr[6]=fa,ir[0]=sr,ir[1]=go,ir[2]=No,ir[3]=Fi,ir[4]=kr,ir[5]=Ao,ir[6]=ji,c=1}else c=0}c&&(c=function(V,lt){for(V.M=0;V.M<V.Va;++V.M){var nt,z=V.Jc[V.M&V.Xb],U=V.m,ut=V;for(nt=0;nt<ut.za;++nt){var gt=U,ht=ut,jt=ht.Ac,St=ht.Bc+4*nt,Rt=ht.zc,_t=ht.ya[ht.aa+nt];if(ht.Qa.Bb?_t.$b=Q(gt,ht.Pa.jb[0])?2+Q(gt,ht.Pa.jb[2]):Q(gt,ht.Pa.jb[1]):_t.$b=0,ht.kc&&(_t.Ad=Q(gt,ht.Bd)),_t.Za=!Q(gt,145)+0,_t.Za){var se=_t.Ob,he=0;for(ht=0;4>ht;++ht){var ae,ne=Rt[0+ht];for(ae=0;4>ae;++ae){ne=wc[jt[St+ae]][ne];for(var ge=hs[Q(gt,ne[0])];0<ge;)ge=hs[2*ge+Q(gt,ne[ge])];ne=-ge,jt[St+ae]=ne}a(se,he,jt,St,4),he+=4,Rt[0+ht]=ne}}else ne=Q(gt,156)?Q(gt,128)?1:3:Q(gt,163)?2:0,_t.Ob[0]=ne,u(jt,St,ne,4),u(Rt,0,ne,4);_t.Dd=Q(gt,142)?Q(gt,114)?Q(gt,183)?1:3:2:0}if(ut.m.Ka)return Pe(V,7,"Premature end-of-partition0 encountered.");for(;V.ja<V.za;++V.ja){if(ut=z,gt=(U=V).rb[U.sb-1],jt=U.rb[U.sb+U.ja],nt=U.ya[U.aa+U.ja],St=U.kc?nt.Ad:0)gt.la=jt.la=0,nt.Za||(gt.Na=jt.Na=0),nt.Hc=0,nt.Gc=0,nt.ia=0;else{var de,$t;if(gt=jt,jt=ut,St=U.Pa.Xc,Rt=U.ya[U.aa+U.ja],_t=U.pb[Rt.$b],ht=Rt.ad,se=0,he=U.rb[U.sb-1],ne=ae=0,u(ht,se,0,384),Rt.Za)var Me=0,sn=St[3];else{ge=o(16);var Ee=gt.Na+he.Na;if(Ee=Xi(jt,St[1],Ee,_t.Eb,0,ge,0),gt.Na=he.Na=(0<Ee)+0,1<Ee)ci(ge,0,ht,se);else{var cn=ge[0]+3>>3;for(ge=0;256>ge;ge+=16)ht[se+ge]=cn}Me=1,sn=St[0]}var le=15&gt.la,dn=15&he.la;for(ge=0;4>ge;++ge){var pn=1&dn;for(cn=$t=0;4>cn;++cn)le=le>>1|(pn=(Ee=Xi(jt,sn,Ee=pn+(1&le),_t.Sc,Me,ht,se))>Me)<<7,$t=$t<<2|(3<Ee?3:1<Ee?2:ht[se+0]!=0),se+=16;le>>=4,dn=dn>>1|pn<<7,ae=(ae<<8|$t)>>>0}for(sn=le,Me=dn>>4,de=0;4>de;de+=2){for($t=0,le=gt.la>>4+de,dn=he.la>>4+de,ge=0;2>ge;++ge){for(pn=1&dn,cn=0;2>cn;++cn)Ee=pn+(1&le),le=le>>1|(pn=0<(Ee=Xi(jt,St[2],Ee,_t.Qc,0,ht,se)))<<3,$t=$t<<2|(3<Ee?3:1<Ee?2:ht[se+0]!=0),se+=16;le>>=2,dn=dn>>1|pn<<5}ne|=$t<<4*de,sn|=le<<4<<de,Me|=(240&dn)<<de}gt.la=sn,he.la=Me,Rt.Hc=ae,Rt.Gc=ne,Rt.ia=43690&ne?0:_t.ia,St=!(ae|ne)}if(0<U.L&&(U.wa[U.Y+U.ja]=U.gd[nt.$b][nt.Za],U.wa[U.Y+U.ja].La|=!St),ut.Ka)return Pe(V,7,"Premature end-of-file encountered.")}if(_i(V),U=lt,ut=1,nt=(z=V).D,gt=0<z.L&&z.M>=z.zb&&z.M<=z.Va,z.Aa==0)t:{if(nt.M=z.M,nt.uc=gt,Ti(z,nt),ut=1,nt=($t=z.D).Nb,gt=(ne=Ma[z.L])*z.R,jt=ne/2*z.B,ge=16*nt*z.R,cn=8*nt*z.B,St=z.sa,Rt=z.ta-gt+ge,_t=z.qa,ht=z.ra-jt+cn,se=z.Ha,he=z.Ia-jt+cn,dn=(le=$t.M)==0,ae=le>=z.Va-1,z.Aa==2&&Ti(z,$t),$t.uc)for(pn=(Ee=z).D.M,e(Ee.D.uc),$t=Ee.yb;$t<Ee.Hb;++$t){Me=$t,sn=pn;var Te=(Ve=(Se=Ee).D).Nb;de=Se.R;var Ve=Ve.wa[Ve.Y+Me],en=Se.sa,Nn=Se.ta+16*Te*de+16*Me,Gn=Ve.dd,ze=Ve.tc;if(ze!=0)if(e(3<=ze),Se.L==1)0<Me&&dr(en,Nn,de,ze+4),Ve.La&&bn(en,Nn,de,ze),0<sn&&Mr(en,Nn,de,ze+4),Ve.La&&Ji(en,Nn,de,ze);else{var ar=Se.B,Er=Se.qa,fi=Se.ra+8*Te*ar+8*Me,mr=Se.Ha,Se=Se.Ia+8*Te*ar+8*Me;Te=Ve.ld,0<Me&&(Sa(en,Nn,de,ze+4,Gn,Te),Cr(Er,fi,mr,Se,ar,ze+4,Gn,Te)),Ve.La&&(fr(en,Nn,de,ze,Gn,Te),hi(Er,fi,mr,Se,ar,ze,Gn,Te)),0<sn&&(ui(en,Nn,de,ze+4,Gn,Te),Gi(Er,fi,mr,Se,ar,ze+4,Gn,Te)),Ve.La&&(Or(en,Nn,de,ze,Gn,Te),Br(Er,fi,mr,Se,ar,ze,Gn,Te))}}if(z.ia&&alert("todo:DitherRow"),U.put!=null){if($t=16*le,le=16*(le+1),dn?(U.y=z.sa,U.O=z.ta+ge,U.f=z.qa,U.N=z.ra+cn,U.ea=z.Ha,U.W=z.Ia+cn):($t-=ne,U.y=St,U.O=Rt,U.f=_t,U.N=ht,U.ea=se,U.W=he),ae||(le-=ne),le>U.o&&(le=U.o),U.F=null,U.J=null,z.Fa!=null&&0<z.Fa.length&&$t<le&&(U.J=Di(z,U,$t,le-$t),U.F=z.mb,U.F==null&&U.F.length==0)){ut=Pe(z,3,"Could not decode alpha data.");break t}$t<U.j&&(ne=U.j-$t,$t=U.j,e(!(1&ne)),U.O+=z.R*ne,U.N+=z.B*(ne>>1),U.W+=z.B*(ne>>1),U.F!=null&&(U.J+=U.width*ne)),$t<le&&(U.O+=U.v,U.N+=U.v>>1,U.W+=U.v>>1,U.F!=null&&(U.J+=U.v),U.ka=$t-U.j,U.U=U.va-U.v,U.T=le-$t,ut=U.put(U))}nt+1!=z.Ic||ae||(a(z.sa,z.ta-gt,St,Rt+16*z.R,gt),a(z.qa,z.ra-jt,_t,ht+8*z.B,jt),a(z.Ha,z.Ia-jt,se,he+8*z.B,jt))}if(!ut)return Pe(V,6,"Output aborted.")}return 1}(t,n)),n.bc!=null&&n.bc(n),c&=1}return c?(t.cb=0,c):0}function In(t,n,c,d,g){g=t[n+c+32*d]+(g>>3),t[n+c+32*d]=-256&g?0>g?0:255:g}function Yr(t,n,c,d,g,b){In(t,n,0,c,d+g),In(t,n,1,c,d+b),In(t,n,2,c,d-b),In(t,n,3,c,d-g)}function rn(t){return(20091*t>>16)+t}function Pi(t,n,c,d){var g,b=0,y=o(16);for(g=0;4>g;++g){var L=t[n+0]+t[n+8],N=t[n+0]-t[n+8],P=(35468*t[n+4]>>16)-rn(t[n+12]),T=rn(t[n+4])+(35468*t[n+12]>>16);y[b+0]=L+T,y[b+1]=N+P,y[b+2]=N-P,y[b+3]=L-T,b+=4,n++}for(g=b=0;4>g;++g)L=(t=y[b+0]+4)+y[b+8],N=t-y[b+8],P=(35468*y[b+4]>>16)-rn(y[b+12]),In(c,d,0,0,L+(T=rn(y[b+4])+(35468*y[b+12]>>16))),In(c,d,1,0,N+P),In(c,d,2,0,N-P),In(c,d,3,0,L-T),b++,d+=32}function ca(t,n,c,d){var g=t[n+0]+4,b=35468*t[n+4]>>16,y=rn(t[n+4]),L=35468*t[n+1]>>16;Yr(c,d,0,g+y,t=rn(t[n+1]),L),Yr(c,d,1,g+b,t,L),Yr(c,d,2,g-b,t,L),Yr(c,d,3,g-y,t,L)}function ho(t,n,c,d,g){Pi(t,n,c,d),g&&Pi(t,n+16,c,d+4)}function ua(t,n,c,d){nr(t,n+0,c,d,1),nr(t,n+32,c,d+128,1)}function lo(t,n,c,d){var g;for(t=t[n+0]+4,g=0;4>g;++g)for(n=0;4>n;++n)In(c,d,n,g,t)}function ha(t,n,c,d){t[n+0]&&tn(t,n+0,c,d),t[n+16]&&tn(t,n+16,c,d+4),t[n+32]&&tn(t,n+32,c,d+128),t[n+48]&&tn(t,n+48,c,d+128+4)}function fo(t,n,c,d){var g,b=o(16);for(g=0;4>g;++g){var y=t[n+0+g]+t[n+12+g],L=t[n+4+g]+t[n+8+g],N=t[n+4+g]-t[n+8+g],P=t[n+0+g]-t[n+12+g];b[0+g]=y+L,b[8+g]=y-L,b[4+g]=P+N,b[12+g]=P-N}for(g=0;4>g;++g)y=(t=b[0+4*g]+3)+b[3+4*g],L=b[1+4*g]+b[2+4*g],N=b[1+4*g]-b[2+4*g],P=t-b[3+4*g],c[d+0]=y+L>>3,c[d+16]=P+N>>3,c[d+32]=y-L>>3,c[d+48]=P-N>>3,d+=64}function ki(t,n,c){var d,g=n-32,b=fn,y=255-t[g-1];for(d=0;d<c;++d){var L,N=b,P=y+t[n-1];for(L=0;L<c;++L)t[n+L]=N[P+t[g+L]];n+=32}}function po(t,n){ki(t,n,4)}function go(t,n){ki(t,n,8)}function mo(t,n){ki(t,n,16)}function $n(t,n){var c;for(c=0;16>c;++c)a(t,n+32*c,t,n-32,16)}function Xr(t,n){var c;for(c=16;0<c;--c)u(t,n,t[n-1],16),n+=32}function Kr(t,n,c){var d;for(d=0;16>d;++d)u(n,c+32*d,t,16)}function la(t,n){var c,d=16;for(c=0;16>c;++c)d+=t[n-1+32*c]+t[n+c-32];Kr(d>>5,t,n)}function Je(t,n){var c,d=8;for(c=0;16>c;++c)d+=t[n-1+32*c];Kr(d>>4,t,n)}function vo(t,n){var c,d=8;for(c=0;16>c;++c)d+=t[n+c-32];Kr(d>>4,t,n)}function fa(t,n){Kr(128,t,n)}function Vt(t,n,c){return t+2*n+c+2>>2}function bo(t,n){var c,d=n-32;for(d=new Uint8Array([Vt(t[d-1],t[d+0],t[d+1]),Vt(t[d+0],t[d+1],t[d+2]),Vt(t[d+1],t[d+2],t[d+3]),Vt(t[d+2],t[d+3],t[d+4])]),c=0;4>c;++c)a(t,n+32*c,d,0,d.length)}function yo(t,n){var c=t[n-1],d=t[n-1+32],g=t[n-1+64],b=t[n-1+96];At(t,n+0,16843009*Vt(t[n-1-32],c,d)),At(t,n+32,16843009*Vt(c,d,g)),At(t,n+64,16843009*Vt(d,g,b)),At(t,n+96,16843009*Vt(g,b,b))}function Hn(t,n){var c,d=4;for(c=0;4>c;++c)d+=t[n+c-32]+t[n-1+32*c];for(d>>=3,c=0;4>c;++c)u(t,n+32*c,d,4)}function da(t,n){var c=t[n-1+0],d=t[n-1+32],g=t[n-1+64],b=t[n-1-32],y=t[n+0-32],L=t[n+1-32],N=t[n+2-32],P=t[n+3-32];t[n+0+96]=Vt(d,g,t[n-1+96]),t[n+1+96]=t[n+0+64]=Vt(c,d,g),t[n+2+96]=t[n+1+64]=t[n+0+32]=Vt(b,c,d),t[n+3+96]=t[n+2+64]=t[n+1+32]=t[n+0+0]=Vt(y,b,c),t[n+3+64]=t[n+2+32]=t[n+1+0]=Vt(L,y,b),t[n+3+32]=t[n+2+0]=Vt(N,L,y),t[n+3+0]=Vt(P,N,L)}function pa(t,n){var c=t[n+1-32],d=t[n+2-32],g=t[n+3-32],b=t[n+4-32],y=t[n+5-32],L=t[n+6-32],N=t[n+7-32];t[n+0+0]=Vt(t[n+0-32],c,d),t[n+1+0]=t[n+0+32]=Vt(c,d,g),t[n+2+0]=t[n+1+32]=t[n+0+64]=Vt(d,g,b),t[n+3+0]=t[n+2+32]=t[n+1+64]=t[n+0+96]=Vt(g,b,y),t[n+3+32]=t[n+2+64]=t[n+1+96]=Vt(b,y,L),t[n+3+64]=t[n+2+96]=Vt(y,L,N),t[n+3+96]=Vt(L,N,N)}function Zr(t,n){var c=t[n-1+0],d=t[n-1+32],g=t[n-1+64],b=t[n-1-32],y=t[n+0-32],L=t[n+1-32],N=t[n+2-32],P=t[n+3-32];t[n+0+0]=t[n+1+64]=b+y+1>>1,t[n+1+0]=t[n+2+64]=y+L+1>>1,t[n+2+0]=t[n+3+64]=L+N+1>>1,t[n+3+0]=N+P+1>>1,t[n+0+96]=Vt(g,d,c),t[n+0+64]=Vt(d,c,b),t[n+0+32]=t[n+1+96]=Vt(c,b,y),t[n+1+32]=t[n+2+96]=Vt(b,y,L),t[n+2+32]=t[n+3+96]=Vt(y,L,N),t[n+3+32]=Vt(L,N,P)}function Ii(t,n){var c=t[n+0-32],d=t[n+1-32],g=t[n+2-32],b=t[n+3-32],y=t[n+4-32],L=t[n+5-32],N=t[n+6-32],P=t[n+7-32];t[n+0+0]=c+d+1>>1,t[n+1+0]=t[n+0+64]=d+g+1>>1,t[n+2+0]=t[n+1+64]=g+b+1>>1,t[n+3+0]=t[n+2+64]=b+y+1>>1,t[n+0+32]=Vt(c,d,g),t[n+1+32]=t[n+0+96]=Vt(d,g,b),t[n+2+32]=t[n+1+96]=Vt(g,b,y),t[n+3+32]=t[n+2+96]=Vt(b,y,L),t[n+3+64]=Vt(y,L,N),t[n+3+96]=Vt(L,N,P)}function wo(t,n){var c=t[n-1+0],d=t[n-1+32],g=t[n-1+64],b=t[n-1+96];t[n+0+0]=c+d+1>>1,t[n+2+0]=t[n+0+32]=d+g+1>>1,t[n+2+32]=t[n+0+64]=g+b+1>>1,t[n+1+0]=Vt(c,d,g),t[n+3+0]=t[n+1+32]=Vt(d,g,b),t[n+3+32]=t[n+1+64]=Vt(g,b,b),t[n+3+64]=t[n+2+64]=t[n+0+96]=t[n+1+96]=t[n+2+96]=t[n+3+96]=b}function Lo(t,n){var c=t[n-1+0],d=t[n-1+32],g=t[n-1+64],b=t[n-1+96],y=t[n-1-32],L=t[n+0-32],N=t[n+1-32],P=t[n+2-32];t[n+0+0]=t[n+2+32]=c+y+1>>1,t[n+0+32]=t[n+2+64]=d+c+1>>1,t[n+0+64]=t[n+2+96]=g+d+1>>1,t[n+0+96]=b+g+1>>1,t[n+3+0]=Vt(L,N,P),t[n+2+0]=Vt(y,L,N),t[n+1+0]=t[n+3+32]=Vt(c,y,L),t[n+1+32]=t[n+3+64]=Vt(d,c,y),t[n+1+64]=t[n+3+96]=Vt(g,d,c),t[n+1+96]=Vt(b,g,d)}function No(t,n){var c;for(c=0;8>c;++c)a(t,n+32*c,t,n-32,8)}function Fi(t,n){var c;for(c=0;8>c;++c)u(t,n,t[n-1],8),n+=32}function Pr(t,n,c){var d;for(d=0;8>d;++d)u(n,c+32*d,t,8)}function sr(t,n){var c,d=8;for(c=0;8>c;++c)d+=t[n+c-32]+t[n-1+32*c];Pr(d>>4,t,n)}function Ao(t,n){var c,d=4;for(c=0;8>c;++c)d+=t[n+c-32];Pr(d>>3,t,n)}function kr(t,n){var c,d=4;for(c=0;8>c;++c)d+=t[n-1+32*c];Pr(d>>3,t,n)}function ji(t,n){Pr(128,t,n)}function $r(t,n,c){var d=t[n-c],g=t[n+0],b=3*(g-d)+Io[1020+t[n-2*c]-t[n+c]],y=Pa[112+(b+4>>3)];t[n-c]=fn[255+d+Pa[112+(b+3>>3)]],t[n+0]=fn[255+g-y]}function ga(t,n,c,d){var g=t[n+0],b=t[n+c];return wn[255+t[n-2*c]-t[n-c]]>d||wn[255+b-g]>d}function ma(t,n,c,d){return 4*wn[255+t[n-c]-t[n+0]]+wn[255+t[n-2*c]-t[n+c]]<=d}function va(t,n,c,d,g){var b=t[n-3*c],y=t[n-2*c],L=t[n-c],N=t[n+0],P=t[n+c],T=t[n+2*c],X=t[n+3*c];return 4*wn[255+L-N]+wn[255+y-P]>d?0:wn[255+t[n-4*c]-b]<=g&&wn[255+b-y]<=g&&wn[255+y-L]<=g&&wn[255+X-T]<=g&&wn[255+T-P]<=g&&wn[255+P-N]<=g}function ba(t,n,c,d){var g=2*d+1;for(d=0;16>d;++d)ma(t,n+d,c,g)&&$r(t,n+d,c)}function Wn(t,n,c,d){var g=2*d+1;for(d=0;16>d;++d)ma(t,n+d*c,1,g)&&$r(t,n+d*c,1)}function Qn(t,n,c,d){var g;for(g=3;0<g;--g)ba(t,n+=4*c,c,d)}function xo(t,n,c,d){var g;for(g=3;0<g;--g)Wn(t,n+=4,c,d)}function cr(t,n,c,d,g,b,y,L){for(b=2*b+1;0<g--;){if(va(t,n,c,b,y))if(ga(t,n,c,L))$r(t,n,c);else{var N=t,P=n,T=c,X=N[P-2*T],K=N[P-T],V=N[P+0],lt=N[P+T],nt=N[P+2*T],z=27*(ut=Io[1020+3*(V-K)+Io[1020+X-lt]])+63>>7,U=18*ut+63>>7,ut=9*ut+63>>7;N[P-3*T]=fn[255+N[P-3*T]+ut],N[P-2*T]=fn[255+X+U],N[P-T]=fn[255+K+z],N[P+0]=fn[255+V-z],N[P+T]=fn[255+lt-U],N[P+2*T]=fn[255+nt-ut]}n+=d}}function Fn(t,n,c,d,g,b,y,L){for(b=2*b+1;0<g--;){if(va(t,n,c,b,y))if(ga(t,n,c,L))$r(t,n,c);else{var N=t,P=n,T=c,X=N[P-T],K=N[P+0],V=N[P+T],lt=Pa[112+((nt=3*(K-X))+4>>3)],nt=Pa[112+(nt+3>>3)],z=lt+1>>1;N[P-2*T]=fn[255+N[P-2*T]+z],N[P-T]=fn[255+X+nt],N[P+0]=fn[255+K-lt],N[P+T]=fn[255+V-z]}n+=d}}function Ci(t,n,c,d,g,b){cr(t,n,c,1,16,d,g,b)}function Ir(t,n,c,d,g,b){cr(t,n,1,c,16,d,g,b)}function So(t,n,c,d,g,b){var y;for(y=3;0<y;--y)Fn(t,n+=4*c,c,1,16,d,g,b)}function Qr(t,n,c,d,g,b){var y;for(y=3;0<y;--y)Fn(t,n+=4,1,c,16,d,g,b)}function _o(t,n,c,d,g,b,y,L){cr(t,n,g,1,8,b,y,L),cr(c,d,g,1,8,b,y,L)}function Oi(t,n,c,d,g,b,y,L){cr(t,n,1,g,8,b,y,L),cr(c,d,1,g,8,b,y,L)}function Bi(t,n,c,d,g,b,y,L){Fn(t,n+4*g,g,1,8,b,y,L),Fn(c,d+4*g,g,1,8,b,y,L)}function ya(t,n,c,d,g,b,y,L){Fn(t,n+4,1,g,8,b,y,L),Fn(c,d+4,1,g,8,b,y,L)}function ti(){this.ba=new xn,this.ec=[],this.cc=[],this.Mc=[],this.Dc=this.Nc=this.dc=this.fc=0,this.Oa=new Ae,this.memory=0,this.Ib="OutputFunc",this.Jb="OutputAlphaFunc",this.Nd="OutputRowFunc"}function Mi(){this.data=[],this.offset=this.kd=this.ha=this.w=0,this.na=[],this.xa=this.gb=this.Ja=this.Sa=this.P=0}function Ei(){this.nc=this.Ea=this.b=this.hc=0,this.K=[],this.w=0}function wa(){this.ua=0,this.Wa=new O,this.vb=new O,this.md=this.xc=this.wc=0,this.vc=[],this.Wb=0,this.Ya=new R,this.yc=new I}function Po(){this.xb=this.a=0,this.l=new Jr,this.ca=new xn,this.V=[],this.Ba=0,this.Ta=[],this.Ua=0,this.m=new w,this.Pb=0,this.wd=new w,this.Ma=this.$=this.C=this.i=this.c=this.xd=0,this.s=new wa,this.ab=0,this.gc=h(4,Ei),this.Oc=0}function ei(){this.Lc=this.Z=this.$a=this.i=this.c=0,this.l=new Jr,this.ic=0,this.ca=[],this.tb=0,this.qd=null,this.rd=0}function Fr(t,n,c,d,g,b,y){for(t=t==null?0:t[n+0],n=0;n<y;++n)g[b+n]=t+c[d+n]&255,t=g[b+n]}function qi(t,n,c,d,g,b,y){var L;if(t==null)Fr(null,null,c,d,g,b,y);else for(L=0;L<y;++L)g[b+L]=t[n+L]+c[d+L]&255}function ur(t,n,c,d,g,b,y){if(t==null)Fr(null,null,c,d,g,b,y);else{var L,N=t[n+0],P=N,T=N;for(L=0;L<y;++L)P=T+(N=t[n+L])-P,T=c[d+L]+(-256&P?0>P?0:255:P)&255,P=N,g[b+L]=T}}function Di(t,n,c,d){var g=n.width,b=n.o;if(e(t!=null&&n!=null),0>c||0>=d||c+d>b)return null;if(!t.Cc){if(t.ga==null){var y;if(t.ga=new ei,(y=t.ga==null)||(y=n.width*n.o,e(t.Gb.length==0),t.Gb=o(y),t.Uc=0,t.Gb==null?y=0:(t.mb=t.Gb,t.nb=t.Uc,t.rc=null,y=1),y=!y),!y){y=t.ga;var L=t.Fa,N=t.P,P=t.qc,T=t.mb,X=t.nb,K=N+1,V=P-1,lt=y.l;if(e(L!=null&&T!=null&&n!=null),gr[0]=null,gr[1]=Fr,gr[2]=qi,gr[3]=ur,y.ca=T,y.tb=X,y.c=n.width,y.i=n.height,e(0<y.c&&0<y.i),1>=P)n=0;else if(y.$a=L[N+0]>>0&3,y.Z=L[N+0]>>2&3,y.Lc=L[N+0]>>4&3,N=L[N+0]>>6&3,0>y.$a||1<y.$a||4<=y.Z||1<y.Lc||N)n=0;else if(lt.put=Dn,lt.ac=He,lt.bc=Rn,lt.ma=y,lt.width=n.width,lt.height=n.height,lt.Da=n.Da,lt.v=n.v,lt.va=n.va,lt.j=n.j,lt.o=n.o,y.$a)t:{e(y.$a==1),n=gn();e:for(;;){if(n==null){n=0;break t}if(e(y!=null),y.mc=n,n.c=y.c,n.i=y.i,n.l=y.l,n.l.ma=y,n.l.width=y.c,n.l.height=y.i,n.a=0,tt(n.m,L,K,V),!Un(y.c,y.i,1,n,null)||(n.ab==1&&n.gc[0].hc==3&&Zn(n.s)?(y.ic=1,L=n.c*n.i,n.Ta=null,n.Ua=0,n.V=o(L),n.Ba=0,n.V==null?(n.a=1,n=0):n=1):(y.ic=0,n=_r(n,y.c)),!n))break e;n=1;break t}y.mc=null,n=0}else n=V>=y.c*y.i;y=!n}if(y)return null;t.ga.Lc!=1?t.Ga=0:d=b-c}e(t.ga!=null),e(c+d<=b);t:{if(n=(L=t.ga).c,b=L.l.o,L.$a==0){if(K=t.rc,V=t.Vc,lt=t.Fa,N=t.P+1+c*n,P=t.mb,T=t.nb+c*n,e(N<=t.P+t.qc),L.Z!=0)for(e(gr[L.Z]!=null),y=0;y<d;++y)gr[L.Z](K,V,lt,N,P,T,n),K=P,V=T,T+=n,N+=n;else for(y=0;y<d;++y)a(P,T,lt,N,n),K=P,V=T,T+=n,N+=n;t.rc=K,t.Vc=V}else{if(e(L.mc!=null),n=c+d,e((y=L.mc)!=null),e(n<=y.i),y.C>=n)n=1;else if(L.ic||Y(),L.ic){L=y.V,K=y.Ba,V=y.c;var nt=y.i,z=(lt=1,N=y.$/V,P=y.$%V,T=y.m,X=y.s,y.$),U=V*nt,ut=V*n,gt=X.wc,ht=z<ut?Oe(X,P,N):null;e(z<=U),e(n<=nt),e(Zn(X));e:for(;;){for(;!T.h&&z<ut;){if(P&gt||(ht=Oe(X,P,N)),e(ht!=null),J(T),256>(nt=nn(ht.G[0],ht.H[0],T)))L[K+z]=nt,++z,++P>=V&&(P=0,++N<=n&&!(N%16)&&Pn(y,N));else{if(!(280>nt)){lt=0;break e}nt=Sn(nt-256,T);var jt,St=nn(ht.G[4],ht.H[4],T);if(J(T),!(z>=(St=Tn(V,St=Sn(St,T)))&&U-z>=nt)){lt=0;break e}for(jt=0;jt<nt;++jt)L[K+z+jt]=L[K+z+jt-St];for(z+=nt,P+=nt;P>=V;)P-=V,++N<=n&&!(N%16)&&Pn(y,N);z<ut&&P&gt&&(ht=Oe(X,P,N))}e(T.h==E(T))}Pn(y,N>n?n:N);break e}!lt||T.h&&z<U?(lt=0,y.a=T.h?5:3):y.$=z,n=lt}else n=kn(y,y.V,y.Ba,y.c,y.i,n,Ur);if(!n){d=0;break t}}c+d>=b&&(t.Cc=1),d=1}if(!d)return null;if(t.Cc&&((d=t.ga)!=null&&(d.mc=null),t.ga=null,0<t.Ga))return alert("todo:WebPDequantizeLevels"),null}return t.nb+c*g}function s(t,n,c,d,g,b){for(;0<g--;){var y,L=t,N=n+(c?1:0),P=t,T=n+(c?0:3);for(y=0;y<d;++y){var X=P[T+4*y];X!=255&&(X*=32897,L[N+4*y+0]=L[N+4*y+0]*X>>23,L[N+4*y+1]=L[N+4*y+1]*X>>23,L[N+4*y+2]=L[N+4*y+2]*X>>23)}n+=b}}function m(t,n,c,d,g){for(;0<d--;){var b;for(b=0;b<c;++b){var y=t[n+2*b+0],L=15&(P=t[n+2*b+1]),N=4369*L,P=(240&P|P>>4)*N>>16;t[n+2*b+0]=(240&y|y>>4)*N>>16&240|(15&y|y<<4)*N>>16>>4&15,t[n+2*b+1]=240&P|L}n+=g}}function j(t,n,c,d,g,b,y,L){var N,P,T=255;for(P=0;P<g;++P){for(N=0;N<d;++N){var X=t[n+N];b[y+4*N]=X,T&=X}n+=c,y+=L}return T!=255}function D(t,n,c,d,g){var b;for(b=0;b<g;++b)c[d+b]=t[n+b]>>8}function Y(){yn=s,be=m,ye=j,Ie=D}function it(t,n,c){G[t]=function(d,g,b,y,L,N,P,T,X,K,V,lt,nt,z,U,ut,gt){var ht,jt=gt-1>>1,St=L[N+0]|P[T+0]<<16,Rt=X[K+0]|V[lt+0]<<16;e(d!=null);var _t=3*St+Rt+131074>>2;for(n(d[g+0],255&_t,_t>>16,nt,z),b!=null&&(_t=3*Rt+St+131074>>2,n(b[y+0],255&_t,_t>>16,U,ut)),ht=1;ht<=jt;++ht){var se=L[N+ht]|P[T+ht]<<16,he=X[K+ht]|V[lt+ht]<<16,ae=St+se+Rt+he+524296,ne=ae+2*(se+Rt)>>3;_t=ne+St>>1,St=(ae=ae+2*(St+he)>>3)+se>>1,n(d[g+2*ht-1],255&_t,_t>>16,nt,z+(2*ht-1)*c),n(d[g+2*ht-0],255&St,St>>16,nt,z+(2*ht-0)*c),b!=null&&(_t=ae+Rt>>1,St=ne+he>>1,n(b[y+2*ht-1],255&_t,_t>>16,U,ut+(2*ht-1)*c),n(b[y+2*ht+0],255&St,St>>16,U,ut+(2*ht+0)*c)),St=se,Rt=he}1&gt||(_t=3*St+Rt+131074>>2,n(d[g+gt-1],255&_t,_t>>16,nt,z+(gt-1)*c),b!=null&&(_t=3*Rt+St+131074>>2,n(b[y+gt-1],255&_t,_t>>16,U,ut+(gt-1)*c)))}}function yt(){Ln[ka]=xc,Ln[Ia]=fs,Ln[ss]=Sc,Ln[Fa]=ds,Ln[ja]=ps,Ln[Fo]=gs,Ln[cs]=_c,Ln[jo]=fs,Ln[Co]=ds,Ln[Ca]=ps,Ln[Oo]=gs}function Ft(t){return t&~Pc?0>t?0:255:t>>ms}function Dt(t,n){return Ft((19077*t>>8)+(26149*n>>8)-14234)}function Zt(t,n,c){return Ft((19077*t>>8)-(6419*n>>8)-(13320*c>>8)+8708)}function Yt(t,n){return Ft((19077*t>>8)+(33050*n>>8)-17685)}function re(t,n,c,d,g){d[g+0]=Dt(t,c),d[g+1]=Zt(t,n,c),d[g+2]=Yt(t,n)}function Le(t,n,c,d,g){d[g+0]=Yt(t,n),d[g+1]=Zt(t,n,c),d[g+2]=Dt(t,c)}function xe(t,n,c,d,g){var b=Zt(t,n,c);n=b<<3&224|Yt(t,n)>>3,d[g+0]=248&Dt(t,c)|b>>5,d[g+1]=n}function Be(t,n,c,d,g){var b=240&Yt(t,n)|15;d[g+0]=240&Dt(t,c)|Zt(t,n,c)>>4,d[g+1]=b}function Ye(t,n,c,d,g){d[g+0]=255,re(t,n,c,d,g+1)}function Re(t,n,c,d,g){Le(t,n,c,d,g),d[g+3]=255}function jn(t,n,c,d,g){re(t,n,c,d,g),d[g+3]=255}function ln(t,n){return 0>t?0:t>n?n:t}function Vn(t,n,c){G[t]=function(d,g,b,y,L,N,P,T,X){for(var K=T+(-2&X)*c;T!=K;)n(d[g+0],b[y+0],L[N+0],P,T),n(d[g+1],b[y+0],L[N+0],P,T+c),g+=2,++y,++N,T+=2*c;1&X&&n(d[g+0],b[y+0],L[N+0],P,T)}}function La(t,n,c){return c==0?t==0?n==0?6:5:n==0?4:0:c}function Ri(t,n,c,d,g){switch(t>>>30){case 3:nr(n,c,d,g,0);break;case 2:ke(n,c,d,g);break;case 1:tn(n,c,d,g)}}function Ti(t,n){var c,d,g=n.M,b=n.Nb,y=t.oc,L=t.pc+40,N=t.oc,P=t.pc+584,T=t.oc,X=t.pc+600;for(c=0;16>c;++c)y[L+32*c-1]=129;for(c=0;8>c;++c)N[P+32*c-1]=129,T[X+32*c-1]=129;for(0<g?y[L-1-32]=N[P-1-32]=T[X-1-32]=129:(u(y,L-32-1,127,21),u(N,P-32-1,127,9),u(T,X-32-1,127,9)),d=0;d<t.za;++d){var K=n.ya[n.aa+d];if(0<d){for(c=-1;16>c;++c)a(y,L+32*c-4,y,L+32*c+12,4);for(c=-1;8>c;++c)a(N,P+32*c-4,N,P+32*c+4,4),a(T,X+32*c-4,T,X+32*c+4,4)}var V=t.Gd,lt=t.Hd+d,nt=K.ad,z=K.Hc;if(0<g&&(a(y,L-32,V[lt].y,0,16),a(N,P-32,V[lt].f,0,8),a(T,X-32,V[lt].ea,0,8)),K.Za){var U=y,ut=L-32+16;for(0<g&&(d>=t.za-1?u(U,ut,V[lt].y[15],4):a(U,ut,V[lt+1].y,0,4)),c=0;4>c;c++)U[ut+128+c]=U[ut+256+c]=U[ut+384+c]=U[ut+0+c];for(c=0;16>c;++c,z<<=2)U=y,ut=L+bs[c],Cn[K.Ob[c]](U,ut),Ri(z,nt,16*+c,U,ut)}else if(U=La(d,g,K.Ob[0]),pr[U](y,L),z!=0)for(c=0;16>c;++c,z<<=2)Ri(z,nt,16*+c,y,L+bs[c]);for(c=K.Gc,U=La(d,g,K.Dd),ir[U](N,P),ir[U](T,X),z=nt,U=N,ut=P,255&(K=c>>0)&&(170&K?Vi(z,256,U,ut):vn(z,256,U,ut)),K=T,z=X,255&(c>>=8)&&(170&c?Vi(nt,320,K,z):vn(nt,320,K,z)),g<t.Ub-1&&(a(V[lt].y,0,y,L+480,16),a(V[lt].f,0,N,P+224,8),a(V[lt].ea,0,T,X+224,8)),c=8*b*t.B,V=t.sa,lt=t.ta+16*d+16*b*t.R,nt=t.qa,K=t.ra+8*d+c,z=t.Ha,U=t.Ia+8*d+c,c=0;16>c;++c)a(V,lt+c*t.R,y,L+32*c,16);for(c=0;8>c;++c)a(nt,K+c*t.B,N,P+32*c,8),a(z,U+c*t.B,T,X+32*c,8)}}function ni(t,n,c,d,g,b,y,L,N){var P=[0],T=[0],X=0,K=N!=null?N.kd:0,V=N??new Mi;if(t==null||12>c)return 7;V.data=t,V.w=n,V.ha=c,n=[n],c=[c],V.gb=[V.gb];t:{var lt=n,nt=c,z=V.gb;if(e(t!=null),e(nt!=null),e(z!=null),z[0]=0,12<=nt[0]&&!r(t,lt[0],"RIFF")){if(r(t,lt[0]+8,"WEBP")){z=3;break t}var U=Ct(t,lt[0]+4);if(12>U||4294967286<U){z=3;break t}if(K&&U>nt[0]-8){z=7;break t}z[0]=U,lt[0]+=12,nt[0]-=12}z=0}if(z!=0)return z;for(U=0<V.gb[0],c=c[0];;){t:{var ut=t;nt=n,z=c;var gt=P,ht=T,jt=lt=[0];if((_t=X=[X])[0]=0,8>z[0])z=7;else{if(!r(ut,nt[0],"VP8X")){if(Ct(ut,nt[0]+4)!=10){z=3;break t}if(18>z[0]){z=7;break t}var St=Ct(ut,nt[0]+8),Rt=1+Ot(ut,nt[0]+12);if(2147483648<=Rt*(ut=1+Ot(ut,nt[0]+15))){z=3;break t}jt!=null&&(jt[0]=St),gt!=null&&(gt[0]=Rt),ht!=null&&(ht[0]=ut),nt[0]+=18,z[0]-=18,_t[0]=1}z=0}}if(X=X[0],lt=lt[0],z!=0)return z;if(nt=!!(2&lt),!U&&X)return 3;if(b!=null&&(b[0]=!!(16&lt)),y!=null&&(y[0]=nt),L!=null&&(L[0]=0),y=P[0],lt=T[0],X&&nt&&N==null){z=0;break}if(4>c){z=7;break}if(U&&X||!U&&!X&&!r(t,n[0],"ALPH")){c=[c],V.na=[V.na],V.P=[V.P],V.Sa=[V.Sa];t:{St=t,z=n,U=c;var _t=V.gb;gt=V.na,ht=V.P,jt=V.Sa,Rt=22,e(St!=null),e(U!=null),ut=z[0];var se=U[0];for(e(gt!=null),e(jt!=null),gt[0]=null,ht[0]=null,jt[0]=0;;){if(z[0]=ut,U[0]=se,8>se){z=7;break t}var he=Ct(St,ut+4);if(4294967286<he){z=3;break t}var ae=8+he+1&-2;if(Rt+=ae,0<_t&&Rt>_t){z=3;break t}if(!r(St,ut,"VP8 ")||!r(St,ut,"VP8L")){z=0;break t}if(se[0]<ae){z=7;break t}r(St,ut,"ALPH")||(gt[0]=St,ht[0]=ut+8,jt[0]=he),ut+=ae,se-=ae}}if(c=c[0],V.na=V.na[0],V.P=V.P[0],V.Sa=V.Sa[0],z!=0)break}c=[c],V.Ja=[V.Ja],V.xa=[V.xa];t:if(_t=t,z=n,U=c,gt=V.gb[0],ht=V.Ja,jt=V.xa,St=z[0],ut=!r(_t,St,"VP8 "),Rt=!r(_t,St,"VP8L"),e(_t!=null),e(U!=null),e(ht!=null),e(jt!=null),8>U[0])z=7;else{if(ut||Rt){if(_t=Ct(_t,St+4),12<=gt&&_t>gt-12){z=3;break t}if(K&&_t>U[0]-8){z=7;break t}ht[0]=_t,z[0]+=8,U[0]-=8,jt[0]=Rt}else jt[0]=5<=U[0]&&_t[St+0]==47&&!(_t[St+4]>>5),ht[0]=U[0];z=0}if(c=c[0],V.Ja=V.Ja[0],V.xa=V.xa[0],n=n[0],z!=0)break;if(4294967286<V.Ja)return 3;if(L==null||nt||(L[0]=V.xa?2:1),y=[y],lt=[lt],V.xa){if(5>c){z=7;break}L=y,K=lt,nt=b,t==null||5>c?t=0:5<=c&&t[n+0]==47&&!(t[n+4]>>5)?(U=[0],_t=[0],gt=[0],tt(ht=new w,t,n,c),Tt(ht,U,_t,gt)?(L!=null&&(L[0]=U[0]),K!=null&&(K[0]=_t[0]),nt!=null&&(nt[0]=gt[0]),t=1):t=0):t=0}else{if(10>c){z=7;break}L=lt,t==null||10>c||!aa(t,n+3,c-3)?t=0:(K=t[n+0]|t[n+1]<<8|t[n+2]<<16,nt=16383&(t[n+7]<<8|t[n+6]),t=16383&(t[n+9]<<8|t[n+8]),1&K||3<(K>>1&7)||!(K>>4&1)||K>>5>=V.Ja||!nt||!t?t=0:(y&&(y[0]=nt),L&&(L[0]=t),t=1))}if(!t||(y=y[0],lt=lt[0],X&&(P[0]!=y||T[0]!=lt)))return 3;N!=null&&(N[0]=V,N.offset=n-N.w,e(4294967286>n-N.w),e(N.offset==N.ha-c));break}return z==0||z==7&&X&&N==null?(b!=null&&(b[0]|=V.na!=null&&0<V.na.length),d!=null&&(d[0]=y),g!=null&&(g[0]=lt),0):z}function zi(t,n,c){var d=n.width,g=n.height,b=0,y=0,L=d,N=g;if(n.Da=t!=null&&0<t.Da,n.Da&&(L=t.cd,N=t.bd,b=t.v,y=t.j,11>c||(b&=-2,y&=-2),0>b||0>y||0>=L||0>=N||b+L>d||y+N>g))return 0;if(n.v=b,n.j=y,n.va=b+L,n.o=y+N,n.U=L,n.T=N,n.da=t!=null&&0<t.da,n.da){if(!Kt(L,N,c=[t.ib],b=[t.hb]))return 0;n.ib=c[0],n.hb=b[0]}return n.ob=t!=null&&t.ob,n.Kb=t==null||!t.Sd,n.da&&(n.ob=n.ib<3*d/4&&n.hb<3*g/4,n.Kb=0),1}function Ui(t){if(t==null)return 2;if(11>t.S){var n=t.f.RGBA;n.fb+=(t.height-1)*n.A,n.A=-n.A}else n=t.f.kb,t=t.height,n.O+=(t-1)*n.fa,n.fa=-n.fa,n.N+=(t-1>>1)*n.Ab,n.Ab=-n.Ab,n.W+=(t-1>>1)*n.Db,n.Db=-n.Db,n.F!=null&&(n.J+=(t-1)*n.lb,n.lb=-n.lb);return 0}function ri(t,n,c,d){if(d==null||0>=t||0>=n)return 2;if(c!=null){if(c.Da){var g=c.cd,b=c.bd,y=-2&c.v,L=-2&c.j;if(0>y||0>L||0>=g||0>=b||y+g>t||L+b>n)return 2;t=g,n=b}if(c.da){if(!Kt(t,n,g=[c.ib],b=[c.hb]))return 2;t=g[0],n=b[0]}}d.width=t,d.height=n;t:{var N=d.width,P=d.height;if(t=d.S,0>=N||0>=P||!(t>=ka&&13>t))t=2;else{if(0>=d.Rd&&d.sd==null){y=b=g=n=0;var T=(L=N*ys[t])*P;if(11>t||(b=(P+1)/2*(n=(N+1)/2),t==12&&(y=(g=N)*P)),(P=o(T+2*b+y))==null){t=1;break t}d.sd=P,11>t?((N=d.f.RGBA).eb=P,N.fb=0,N.A=L,N.size=T):((N=d.f.kb).y=P,N.O=0,N.fa=L,N.Fd=T,N.f=P,N.N=0+T,N.Ab=n,N.Cd=b,N.ea=P,N.W=0+T+b,N.Db=n,N.Ed=b,t==12&&(N.F=P,N.J=0+T+2*b),N.Tc=y,N.lb=g)}if(n=1,g=d.S,b=d.width,y=d.height,g>=ka&&13>g)if(11>g)t=d.f.RGBA,n&=(L=Math.abs(t.A))*(y-1)+b<=t.size,n&=L>=b*ys[g],n&=t.eb!=null;else{t=d.f.kb,L=(b+1)/2,T=(y+1)/2,N=Math.abs(t.fa),P=Math.abs(t.Ab);var X=Math.abs(t.Db),K=Math.abs(t.lb),V=K*(y-1)+b;n&=N*(y-1)+b<=t.Fd,n&=P*(T-1)+L<=t.Cd,n=(n&=X*(T-1)+L<=t.Ed)&N>=b&P>=L&X>=L,n&=t.y!=null,n&=t.f!=null,n&=t.ea!=null,g==12&&(n&=K>=b,n&=V<=t.Tc,n&=t.F!=null)}else n=0;t=n?0:2}}return t!=0||c!=null&&c.fd&&(t=Ui(d)),t}var We=64,ii=[0,1,3,7,15,31,63,127,255,511,1023,2047,4095,8191,16383,32767,65535,131071,262143,524287,1048575,2097151,4194303,8388607,16777215],ai=24,oi=32,Hi=8,an=[0,0,1,1,2,2,2,2,3,3,3,3,3,3,3,3,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7];xt("Predictor0","PredictorAdd0"),G.Predictor0=function(){return **********},G.Predictor1=function(t){return t},G.Predictor2=function(t,n,c){return n[c+0]},G.Predictor3=function(t,n,c){return n[c+1]},G.Predictor4=function(t,n,c){return n[c-1]},G.Predictor5=function(t,n,c){return Pt(Pt(t,n[c+1]),n[c+0])},G.Predictor6=function(t,n,c){return Pt(t,n[c-1])},G.Predictor7=function(t,n,c){return Pt(t,n[c+0])},G.Predictor8=function(t,n,c){return Pt(n[c-1],n[c+0])},G.Predictor9=function(t,n,c){return Pt(n[c+0],n[c+1])},G.Predictor10=function(t,n,c){return Pt(Pt(t,n[c-1]),Pt(n[c+0],n[c+1]))},G.Predictor11=function(t,n,c){var d=n[c+0];return 0>=Qt(d>>24&255,t>>24&255,(n=n[c-1])>>24&255)+Qt(d>>16&255,t>>16&255,n>>16&255)+Qt(d>>8&255,t>>8&255,n>>8&255)+Qt(255&d,255&t,255&n)?d:t},G.Predictor12=function(t,n,c){var d=n[c+0];return(qt((t>>24&255)+(d>>24&255)-((n=n[c-1])>>24&255))<<24|qt((t>>16&255)+(d>>16&255)-(n>>16&255))<<16|qt((t>>8&255)+(d>>8&255)-(n>>8&255))<<8|qt((255&t)+(255&d)-(255&n)))>>>0},G.Predictor13=function(t,n,c){var d=n[c-1];return(Gt((t=Pt(t,n[c+0]))>>24&255,d>>24&255)<<24|Gt(t>>16&255,d>>16&255)<<16|Gt(t>>8&255,d>>8&255)<<8|Gt(t>>0&255,d>>0&255))>>>0};var ko=G.PredictorAdd0;G.PredictorAdd1=te,xt("Predictor2","PredictorAdd2"),xt("Predictor3","PredictorAdd3"),xt("Predictor4","PredictorAdd4"),xt("Predictor5","PredictorAdd5"),xt("Predictor6","PredictorAdd6"),xt("Predictor7","PredictorAdd7"),xt("Predictor8","PredictorAdd8"),xt("Predictor9","PredictorAdd9"),xt("Predictor10","PredictorAdd10"),xt("Predictor11","PredictorAdd11"),xt("Predictor12","PredictorAdd12"),xt("Predictor13","PredictorAdd13");var Wi=G.PredictorAdd2;ee("ColorIndexInverseTransform","MapARGB","32b",function(t){return t>>8&255},function(t){return t}),ee("VP8LColorIndexInverseTransformAlpha","MapAlpha","8b",function(t){return t},function(t){return t>>8&255});var Na,mn=G.ColorIndexInverseTransform,si=G.MapARGB,Aa=G.VP8LColorIndexInverseTransformAlpha,xa=G.MapAlpha,hr=G.VP8LPredictorsAdd=[];hr.length=16,(G.VP8LPredictors=[]).length=16,(G.VP8LPredictorsAdd_C=[]).length=16,(G.VP8LPredictors_C=[]).length=16;var jr,on,Qe,lr,tr,er,ci,nr,ke,Vi,tn,vn,ui,Sa,Gi,Cr,Or,fr,Br,hi,Mr,dr,Ji,bn,yn,be,ye,Ie,De=o(511),rr=o(2041),Yi=o(225),li=o(767),_a=0,Io=rr,Pa=Yi,fn=li,wn=De,ka=0,Ia=1,ss=2,Fa=3,ja=4,Fo=5,cs=6,jo=7,Co=8,Ca=9,Oo=10,hc=[2,3,7],lc=[3,3,11],us=[280,256,256,256,40],fc=[0,1,1,1,0],dc=[17,18,0,1,2,3,4,5,16,6,7,8,9,10,11,12,13,14,15],pc=[24,7,23,25,40,6,39,41,22,26,38,42,56,5,55,57,21,27,54,58,37,43,72,4,71,73,20,28,53,59,70,74,36,44,88,69,75,52,60,3,87,89,19,29,86,90,35,45,68,76,85,91,51,61,104,2,103,105,18,30,102,106,34,46,84,92,67,77,101,107,50,62,120,1,119,121,83,93,17,31,100,108,66,78,118,122,33,47,117,123,49,63,99,109,82,94,0,116,124,65,79,16,32,98,110,48,115,125,81,95,64,114,126,97,111,80,113,127,96,112],gc=[2954,2956,2958,2962,2970,2986,3018,3082,3212,3468,3980,5004],mc=8,Bo=[4,5,6,7,8,9,10,10,11,12,13,14,15,16,17,17,18,19,20,20,21,21,22,22,23,23,24,25,25,26,27,28,29,30,31,32,33,34,35,36,37,37,38,39,40,41,42,43,44,45,46,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,76,77,78,79,80,81,82,83,84,85,86,87,88,89,91,93,95,96,98,100,101,102,104,106,108,110,112,114,116,118,122,124,126,128,130,132,134,136,138,140,143,145,148,151,154,157],Mo=[4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,60,62,64,66,68,70,72,74,76,78,80,82,84,86,88,90,92,94,96,98,100,102,104,106,108,110,112,114,116,119,122,125,128,131,134,137,140,143,146,149,152,155,158,161,164,167,170,173,177,181,185,189,193,197,201,205,209,213,217,221,225,229,234,239,245,249,254,259,264,269,274,279,284],Xi=null,vc=[[173,148,140,0],[176,155,140,135,0],[180,157,141,134,130,0],[254,254,243,230,196,177,153,140,133,130,129,0]],bc=[0,1,4,8,5,2,3,6,9,12,13,10,7,11,14,15],hs=[-0,1,-1,2,-2,3,4,6,-3,5,-4,-5,-6,7,-7,8,-8,-9],yc=[[[[128,128,128,128,128,128,128,128,128,128,128],[128,128,128,128,128,128,128,128,128,128,128],[128,128,128,128,128,128,128,128,128,128,128]],[[253,136,254,255,228,219,128,128,128,128,128],[189,129,242,255,227,213,255,219,128,128,128],[106,126,227,252,214,209,255,255,128,128,128]],[[1,98,248,255,236,226,255,255,128,128,128],[181,133,238,254,221,234,255,154,128,128,128],[78,134,202,247,198,180,255,219,128,128,128]],[[1,185,249,255,243,255,128,128,128,128,128],[184,150,247,255,236,224,128,128,128,128,128],[77,110,216,255,236,230,128,128,128,128,128]],[[1,101,251,255,241,255,128,128,128,128,128],[170,139,241,252,236,209,255,255,128,128,128],[37,116,196,243,228,255,255,255,128,128,128]],[[1,204,254,255,245,255,128,128,128,128,128],[207,160,250,255,238,128,128,128,128,128,128],[102,103,231,255,211,171,128,128,128,128,128]],[[1,152,252,255,240,255,128,128,128,128,128],[177,135,243,255,234,225,128,128,128,128,128],[80,129,211,255,194,224,128,128,128,128,128]],[[1,1,255,128,128,128,128,128,128,128,128],[246,1,255,128,128,128,128,128,128,128,128],[255,128,128,128,128,128,128,128,128,128,128]]],[[[198,35,237,223,193,187,162,160,145,155,62],[131,45,198,221,172,176,220,157,252,221,1],[68,47,146,208,149,167,221,162,255,223,128]],[[1,149,241,255,221,224,255,255,128,128,128],[184,141,234,253,222,220,255,199,128,128,128],[81,99,181,242,176,190,249,202,255,255,128]],[[1,129,232,253,214,197,242,196,255,255,128],[99,121,210,250,201,198,255,202,128,128,128],[23,91,163,242,170,187,247,210,255,255,128]],[[1,200,246,255,234,255,128,128,128,128,128],[109,178,241,255,231,245,255,255,128,128,128],[44,130,201,253,205,192,255,255,128,128,128]],[[1,132,239,251,219,209,255,165,128,128,128],[94,136,225,251,218,190,255,255,128,128,128],[22,100,174,245,186,161,255,199,128,128,128]],[[1,182,249,255,232,235,128,128,128,128,128],[124,143,241,255,227,234,128,128,128,128,128],[35,77,181,251,193,211,255,205,128,128,128]],[[1,157,247,255,236,231,255,255,128,128,128],[121,141,235,255,225,227,255,255,128,128,128],[45,99,188,251,195,217,255,224,128,128,128]],[[1,1,251,255,213,255,128,128,128,128,128],[203,1,248,255,255,128,128,128,128,128,128],[137,1,177,255,224,255,128,128,128,128,128]]],[[[253,9,248,251,207,208,255,192,128,128,128],[175,13,224,243,193,185,249,198,255,255,128],[73,17,171,221,161,179,236,167,255,234,128]],[[1,95,247,253,212,183,255,255,128,128,128],[239,90,244,250,211,209,255,255,128,128,128],[155,77,195,248,188,195,255,255,128,128,128]],[[1,24,239,251,218,219,255,205,128,128,128],[201,51,219,255,196,186,128,128,128,128,128],[69,46,190,239,201,218,255,228,128,128,128]],[[1,191,251,255,255,128,128,128,128,128,128],[223,165,249,255,213,255,128,128,128,128,128],[141,124,248,255,255,128,128,128,128,128,128]],[[1,16,248,255,255,128,128,128,128,128,128],[190,36,230,255,236,255,128,128,128,128,128],[149,1,255,128,128,128,128,128,128,128,128]],[[1,226,255,128,128,128,128,128,128,128,128],[247,192,255,128,128,128,128,128,128,128,128],[240,128,255,128,128,128,128,128,128,128,128]],[[1,134,252,255,255,128,128,128,128,128,128],[213,62,250,255,255,128,128,128,128,128,128],[55,93,255,128,128,128,128,128,128,128,128]],[[128,128,128,128,128,128,128,128,128,128,128],[128,128,128,128,128,128,128,128,128,128,128],[128,128,128,128,128,128,128,128,128,128,128]]],[[[202,24,213,235,186,191,220,160,240,175,255],[126,38,182,232,169,184,228,174,255,187,128],[61,46,138,219,151,178,240,170,255,216,128]],[[1,112,230,250,199,191,247,159,255,255,128],[166,109,228,252,211,215,255,174,128,128,128],[39,77,162,232,172,180,245,178,255,255,128]],[[1,52,220,246,198,199,249,220,255,255,128],[124,74,191,243,183,193,250,221,255,255,128],[24,71,130,219,154,170,243,182,255,255,128]],[[1,182,225,249,219,240,255,224,128,128,128],[149,150,226,252,216,205,255,171,128,128,128],[28,108,170,242,183,194,254,223,255,255,128]],[[1,81,230,252,204,203,255,192,128,128,128],[123,102,209,247,188,196,255,233,128,128,128],[20,95,153,243,164,173,255,203,128,128,128]],[[1,222,248,255,216,213,128,128,128,128,128],[168,175,246,252,235,205,255,255,128,128,128],[47,116,215,255,211,212,255,255,128,128,128]],[[1,121,236,253,212,214,255,255,128,128,128],[141,84,213,252,201,202,255,219,128,128,128],[42,80,160,240,162,185,255,205,128,128,128]],[[1,1,255,128,128,128,128,128,128,128,128],[244,1,255,128,128,128,128,128,128,128,128],[238,1,255,128,128,128,128,128,128,128,128]]]],wc=[[[231,120,48,89,115,113,120,152,112],[152,179,64,126,170,118,46,70,95],[175,69,143,80,85,82,72,155,103],[56,58,10,171,218,189,17,13,152],[114,26,17,163,44,195,21,10,173],[121,24,80,195,26,62,44,64,85],[144,71,10,38,171,213,144,34,26],[170,46,55,19,136,160,33,206,71],[63,20,8,114,114,208,12,9,226],[81,40,11,96,182,84,29,16,36]],[[134,183,89,137,98,101,106,165,148],[72,187,100,130,157,111,32,75,80],[66,102,167,99,74,62,40,234,128],[41,53,9,178,241,141,26,8,107],[74,43,26,146,73,166,49,23,157],[65,38,105,160,51,52,31,115,128],[104,79,12,27,217,255,87,17,7],[87,68,71,44,114,51,15,186,23],[47,41,14,110,182,183,21,17,194],[66,45,25,102,197,189,23,18,22]],[[88,88,147,150,42,46,45,196,205],[43,97,183,117,85,38,35,179,61],[39,53,200,87,26,21,43,232,171],[56,34,51,104,114,102,29,93,77],[39,28,85,171,58,165,90,98,64],[34,22,116,206,23,34,43,166,73],[107,54,32,26,51,1,81,43,31],[68,25,106,22,64,171,36,225,114],[34,19,21,102,132,188,16,76,124],[62,18,78,95,85,57,50,48,51]],[[193,101,35,159,215,111,89,46,111],[60,148,31,172,219,228,21,18,111],[112,113,77,85,179,255,38,120,114],[40,42,1,196,245,209,10,25,109],[88,43,29,140,166,213,37,43,154],[61,63,30,155,67,45,68,1,209],[100,80,8,43,154,1,51,26,71],[142,78,78,16,255,128,34,197,171],[41,40,5,102,211,183,4,1,221],[51,50,17,168,209,192,23,25,82]],[[138,31,36,171,27,166,38,44,229],[67,87,58,169,82,115,26,59,179],[63,59,90,180,59,166,93,73,154],[40,40,21,116,143,209,34,39,175],[47,15,16,183,34,223,49,45,183],[46,17,33,183,6,98,15,32,183],[57,46,22,24,128,1,54,17,37],[65,32,73,115,28,128,23,128,205],[40,3,9,115,51,192,18,6,223],[87,37,9,115,59,77,64,21,47]],[[104,55,44,218,9,54,53,130,226],[64,90,70,205,40,41,23,26,57],[54,57,112,184,5,41,38,166,213],[30,34,26,133,152,116,10,32,134],[39,19,53,221,26,114,32,73,255],[31,9,65,234,2,15,1,118,73],[75,32,12,51,192,255,160,43,51],[88,31,35,67,102,85,55,186,85],[56,21,23,111,59,205,45,37,192],[55,38,70,124,73,102,1,34,98]],[[125,98,42,88,104,85,117,175,82],[95,84,53,89,128,100,113,101,45],[75,79,123,47,51,128,81,171,1],[57,17,5,71,102,57,53,41,49],[38,33,13,121,57,73,26,1,85],[41,10,67,138,77,110,90,47,114],[115,21,2,10,102,255,166,23,6],[101,29,16,10,85,128,101,196,26],[57,18,10,102,102,213,34,20,43],[117,20,15,36,163,128,68,1,26]],[[102,61,71,37,34,53,31,243,192],[69,60,71,38,73,119,28,222,37],[68,45,128,34,1,47,11,245,171],[62,17,19,70,146,85,55,62,70],[37,43,37,154,100,163,85,160,1],[63,9,92,136,28,64,32,201,85],[75,15,9,9,64,255,184,119,16],[86,6,28,5,64,255,25,248,1],[56,8,17,132,137,255,55,116,128],[58,15,20,82,135,57,26,121,40]],[[164,50,31,137,154,133,25,35,218],[51,103,44,131,131,123,31,6,158],[86,40,64,135,148,224,45,183,128],[22,26,17,131,240,154,14,1,209],[45,16,21,91,64,222,7,1,197],[56,21,39,155,60,138,23,102,213],[83,12,13,54,192,255,68,47,28],[85,26,85,85,128,128,32,146,171],[18,11,7,63,144,171,4,4,246],[35,27,10,146,174,171,12,26,128]],[[190,80,35,99,180,80,126,54,45],[85,126,47,87,176,51,41,20,32],[101,75,128,139,118,146,116,128,85],[56,41,15,176,236,85,37,9,62],[71,30,17,119,118,255,17,18,138],[101,38,60,138,55,70,43,26,142],[146,36,19,30,171,255,97,27,20],[138,45,61,62,219,1,81,188,64],[32,41,20,117,151,142,20,21,163],[112,19,12,61,195,128,48,4,24]]],Lc=[[[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[176,246,255,255,255,255,255,255,255,255,255],[223,241,252,255,255,255,255,255,255,255,255],[249,253,253,255,255,255,255,255,255,255,255]],[[255,244,252,255,255,255,255,255,255,255,255],[234,254,254,255,255,255,255,255,255,255,255],[253,255,255,255,255,255,255,255,255,255,255]],[[255,246,254,255,255,255,255,255,255,255,255],[239,253,254,255,255,255,255,255,255,255,255],[254,255,254,255,255,255,255,255,255,255,255]],[[255,248,254,255,255,255,255,255,255,255,255],[251,255,254,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,253,254,255,255,255,255,255,255,255,255],[251,254,254,255,255,255,255,255,255,255,255],[254,255,254,255,255,255,255,255,255,255,255]],[[255,254,253,255,254,255,255,255,255,255,255],[250,255,254,255,254,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]]],[[[217,255,255,255,255,255,255,255,255,255,255],[225,252,241,253,255,255,254,255,255,255,255],[234,250,241,250,253,255,253,254,255,255,255]],[[255,254,255,255,255,255,255,255,255,255,255],[223,254,254,255,255,255,255,255,255,255,255],[238,253,254,254,255,255,255,255,255,255,255]],[[255,248,254,255,255,255,255,255,255,255,255],[249,254,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,253,255,255,255,255,255,255,255,255,255],[247,254,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,253,254,255,255,255,255,255,255,255,255],[252,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,254,254,255,255,255,255,255,255,255,255],[253,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,254,253,255,255,255,255,255,255,255,255],[250,255,255,255,255,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]]],[[[186,251,250,255,255,255,255,255,255,255,255],[234,251,244,254,255,255,255,255,255,255,255],[251,251,243,253,254,255,254,255,255,255,255]],[[255,253,254,255,255,255,255,255,255,255,255],[236,253,254,255,255,255,255,255,255,255,255],[251,253,253,254,254,255,255,255,255,255,255]],[[255,254,254,255,255,255,255,255,255,255,255],[254,254,254,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,254,255,255,255,255,255,255,255,255,255],[254,254,255,255,255,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]]],[[[248,255,255,255,255,255,255,255,255,255,255],[250,254,252,254,255,255,255,255,255,255,255],[248,254,249,253,255,255,255,255,255,255,255]],[[255,253,253,255,255,255,255,255,255,255,255],[246,253,253,255,255,255,255,255,255,255,255],[252,254,251,254,254,255,255,255,255,255,255]],[[255,254,252,255,255,255,255,255,255,255,255],[248,254,253,255,255,255,255,255,255,255,255],[253,255,254,254,255,255,255,255,255,255,255]],[[255,251,254,255,255,255,255,255,255,255,255],[245,251,254,255,255,255,255,255,255,255,255],[253,253,254,255,255,255,255,255,255,255,255]],[[255,251,253,255,255,255,255,255,255,255,255],[252,253,254,255,255,255,255,255,255,255,255],[255,254,255,255,255,255,255,255,255,255,255]],[[255,252,255,255,255,255,255,255,255,255,255],[249,255,254,255,255,255,255,255,255,255,255],[255,255,254,255,255,255,255,255,255,255,255]],[[255,255,253,255,255,255,255,255,255,255,255],[250,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]]]],Nc=[0,1,2,3,6,4,5,6,6,6,6,6,6,6,6,7,0],pr=[],Cn=[],ir=[],Ac=1,ls=2,gr=[],Ln=[];it("UpsampleRgbLinePair",re,3),it("UpsampleBgrLinePair",Le,3),it("UpsampleRgbaLinePair",jn,4),it("UpsampleBgraLinePair",Re,4),it("UpsampleArgbLinePair",Ye,4),it("UpsampleRgba4444LinePair",Be,2),it("UpsampleRgb565LinePair",xe,2);var xc=G.UpsampleRgbLinePair,Sc=G.UpsampleBgrLinePair,fs=G.UpsampleRgbaLinePair,ds=G.UpsampleBgraLinePair,ps=G.UpsampleArgbLinePair,gs=G.UpsampleRgba4444LinePair,_c=G.UpsampleRgb565LinePair,Oa=16,Ba=1<<Oa-1,Ki=-227,Eo=482,ms=6,Pc=(256<<ms)-1,vs=0,kc=o(256),Ic=o(256),Fc=o(256),jc=o(256),Cc=o(Eo-Ki),Oc=o(Eo-Ki);Vn("YuvToRgbRow",re,3),Vn("YuvToBgrRow",Le,3),Vn("YuvToRgbaRow",jn,4),Vn("YuvToBgraRow",Re,4),Vn("YuvToArgbRow",Ye,4),Vn("YuvToRgba4444Row",Be,2),Vn("YuvToRgb565Row",xe,2);var bs=[0,4,8,12,128,132,136,140,256,260,264,268,384,388,392,396],Ma=[0,2,8],Bc=[8,7,6,4,4,2,2,2,1,1,1,1],Mc=1;this.WebPDecodeRGBA=function(t,n,c,d,g){var b=Ia,y=new ti,L=new xn;y.ba=L,L.S=b,L.width=[L.width],L.height=[L.height];var N=L.width,P=L.height,T=new Kn;if(T==null||t==null)var X=2;else e(T!=null),X=ni(t,n,c,T.width,T.height,T.Pd,T.Qd,T.format,null);if(X!=0?N=0:(N!=null&&(N[0]=T.width[0]),P!=null&&(P[0]=T.height[0]),N=1),N){L.width=L.width[0],L.height=L.height[0],d!=null&&(d[0]=L.width),g!=null&&(g[0]=L.height);t:{if(d=new Jr,(g=new Mi).data=t,g.w=n,g.ha=c,g.kd=1,n=[0],e(g!=null),((t=ni(g.data,g.w,g.ha,null,null,null,n,null,g))==0||t==7)&&n[0]&&(t=4),(n=t)==0){if(e(y!=null),d.data=g.data,d.w=g.w+g.offset,d.ha=g.ha-g.offset,d.put=Dn,d.ac=He,d.bc=Rn,d.ma=y,g.xa){if((t=gn())==null){y=1;break t}if(function(K,V){var lt=[0],nt=[0],z=[0];e:for(;;){if(K==null)return 0;if(V==null)return K.a=2,0;if(K.l=V,K.a=0,tt(K.m,V.data,V.w,V.ha),!Tt(K.m,lt,nt,z)){K.a=3;break e}if(K.xb=ls,V.width=lt[0],V.height=nt[0],!Un(lt[0],nt[0],1,K,null))break e;return 1}return e(K.a!=0),0}(t,d)){if(d=(n=ri(d.width,d.height,y.Oa,y.ba))==0){e:{d=t;n:for(;;){if(d==null){d=0;break e}if(e(d.s.yc!=null),e(d.s.Ya!=null),e(0<d.s.Wb),e((c=d.l)!=null),e((g=c.ma)!=null),d.xb!=0){if(d.ca=g.ba,d.tb=g.tb,e(d.ca!=null),!zi(g.Oa,c,Fa)){d.a=2;break n}if(!_r(d,c.width)||c.da)break n;if((c.da||ce(d.ca.S))&&Y(),11>d.ca.S||(alert("todo:WebPInitConvertARGBToYUV"),d.ca.f.kb.F!=null&&Y()),d.Pb&&0<d.s.ua&&d.s.vb.X==null&&!Wt(d.s.vb,d.s.Wa.Xa)){d.a=1;break n}d.xb=0}if(!kn(d,d.V,d.Ba,d.c,d.i,c.o,zr))break n;g.Dc=d.Ma,d=1;break e}e(d.a!=0),d=0}d=!d}d&&(n=t.a)}else n=t.a}else{if((t=new co)==null){y=1;break t}if(t.Fa=g.na,t.P=g.P,t.qc=g.Sa,oa(t,d)){if((n=ri(d.width,d.height,y.Oa,y.ba))==0){if(t.Aa=0,c=y.Oa,e((g=t)!=null),c!=null){if(0<(N=0>(N=c.Md)?0:100<N?255:255*N/100)){for(P=T=0;4>P;++P)12>(X=g.pb[P]).lc&&(X.ia=N*Bc[0>X.lc?0:X.lc]>>3),T|=X.ia;T&&(alert("todo:VP8InitRandom"),g.ia=1)}g.Ga=c.Id,100<g.Ga?g.Ga=100:0>g.Ga&&(g.Ga=0)}uo(t,d)||(n=t.a)}}else n=t.a}n==0&&y.Oa!=null&&y.Oa.fd&&(n=Ui(y.ba))}y=n}b=y!=0?null:11>b?L.f.RGBA.eb:L.f.kb.y}else b=null;return b};var ys=[3,4,3,4,4,2,2,4,4,4,2,1,1]};function v(G,vt){for(var bt="",k=0;k<4;k++)bt+=String.fromCharCode(G[vt++]);return bt}function x(G,vt){return(G[vt+0]<<0|G[vt+1]<<8|G[vt+2]<<16)>>>0}function A(G,vt){return(G[vt+0]<<0|G[vt+1]<<8|G[vt+2]<<16|G[vt+3]<<24)>>>0}new f;var _=[0],p=[0],B=[],F=new f,q=i,S=function(G,vt){var bt={},k=0,I=!1,H=0,R=0;if(bt.frames=[],!function(C,E,W,J){for(var $=0;$<J;$++)if(C[E+$]!=W.charCodeAt($))return!0;return!1}(G,vt,"RIFF",4)){var ct,ot;for(A(G,vt+=4),vt+=8;vt<G.length;){var mt=v(G,vt),tt=A(G,vt+=4);vt+=4;var pt=tt+(1&tt);switch(mt){case"VP8 ":case"VP8L":bt.frames[k]===void 0&&(bt.frames[k]={}),(w=bt.frames[k]).src_off=I?R:vt-8,w.src_size=H+tt+8,k++,I&&(I=!1,H=0,R=0);break;case"VP8X":(w=bt.header={}).feature_flags=G[vt];var ft=vt+4;w.canvas_width=1+x(G,ft),ft+=3,w.canvas_height=1+x(G,ft),ft+=3;break;case"ALPH":I=!0,H=pt+8,R=vt-8;break;case"ANIM":(w=bt.header).bgcolor=A(G,vt),ft=vt+4,w.loop_count=(ct=G)[(ot=ft)+0]<<0|ct[ot+1]<<8,ft+=2;break;case"ANMF":var Et,w;(w=bt.frames[k]={}).offset_x=2*x(G,vt),vt+=3,w.offset_y=2*x(G,vt),vt+=3,w.width=1+x(G,vt),vt+=3,w.height=1+x(G,vt),vt+=3,w.duration=x(G,vt),vt+=3,Et=G[vt++],w.dispose=1&Et,w.blend=Et>>1&1}mt!="ANMF"&&(vt+=pt)}return bt}}(q,0);S.response=q,S.rgbaoutput=!0,S.dataurl=!1;var M=S.header?S.header:null,Z=S.frames?S.frames:null;if(M){M.loop_counter=M.loop_count,_=[M.canvas_height],p=[M.canvas_width];for(var st=0;st<Z.length&&Z[st].blend!=0;st++);}var dt=Z[0],Nt=F.WebPDecodeRGBA(q,dt.src_off,dt.src_size,p,_);dt.rgba=Nt,dt.imgwidth=p[0],dt.imgheight=_[0];for(var rt=0;rt<p[0]*_[0]*4;rt++)B[rt]=Nt[rt];return this.width=p,this.height=_,this.data=B,this}(function(i){var e=function(){return typeof $o=="function"},r=function(_,p,B,F){var q=4,S=h;switch(F){case i.image_compression.FAST:q=1,S=o;break;case i.image_compression.MEDIUM:q=6,S=l;break;case i.image_compression.SLOW:q=9,S=f}_=a(_,p,B,S);var M=$o(_,{level:q});return i.__addimage__.arrayBufferToBinaryString(M)},a=function(_,p,B,F){for(var q,S,M,Z=_.length/p,st=new Uint8Array(_.length+Z),dt=x(),Nt=0;Nt<Z;Nt+=1){if(M=Nt*p,q=_.subarray(M,M+p),F)st.set(F(q,B,S),M+Nt);else{for(var rt,G=dt.length,vt=[];rt<G;rt+=1)vt[rt]=dt[rt](q,B,S);var bt=A(vt.concat());st.set(vt[bt],M+Nt)}S=q}return st},u=function(_){var p=Array.apply([],_);return p.unshift(0),p},o=function(_,p){var B,F=[],q=_.length;F[0]=1;for(var S=0;S<q;S+=1)B=_[S-p]||0,F[S+1]=_[S]-B+256&255;return F},h=function(_,p,B){var F,q=[],S=_.length;q[0]=2;for(var M=0;M<S;M+=1)F=B&&B[M]||0,q[M+1]=_[M]-F+256&255;return q},l=function(_,p,B){var F,q,S=[],M=_.length;S[0]=3;for(var Z=0;Z<M;Z+=1)F=_[Z-p]||0,q=B&&B[Z]||0,S[Z+1]=_[Z]+256-(F+q>>>1)&255;return S},f=function(_,p,B){var F,q,S,M,Z=[],st=_.length;Z[0]=4;for(var dt=0;dt<st;dt+=1)F=_[dt-p]||0,q=B&&B[dt]||0,S=B&&B[dt-p]||0,M=v(F,q,S),Z[dt+1]=_[dt]-M+256&255;return Z},v=function(_,p,B){if(_===p&&p===B)return _;var F=Math.abs(p-B),q=Math.abs(_-B),S=Math.abs(_+p-B-B);return F<=q&&F<=S?_:q<=S?p:B},x=function(){return[u,o,h,l,f]},A=function(_){var p=_.map(function(B){return B.reduce(function(F,q){return F+Math.abs(q)},0)});return p.indexOf(Math.min.apply(null,p))};i.processPNG=function(_,p,B,F){var q,S,M,Z,st,dt,Nt,rt,G,vt,bt,k,I,H,R,ct=this.decode.FLATE_DECODE,ot="";if(this.__addimage__.isArrayBuffer(_)&&(_=new Uint8Array(_)),this.__addimage__.isArrayBufferView(_)){if(_=(M=new du(_)).imgData,S=M.bits,q=M.colorSpace,st=M.colors,[4,6].indexOf(M.colorType)!==-1){if(M.bits===8){G=(rt=M.pixelBitlength==32?new Uint32Array(M.decodePixels().buffer):M.pixelBitlength==16?new Uint16Array(M.decodePixels().buffer):new Uint8Array(M.decodePixels().buffer)).length,bt=new Uint8Array(G*M.colors),vt=new Uint8Array(G);var mt,tt=M.pixelBitlength-M.bits;for(H=0,R=0;H<G;H++){for(I=rt[H],mt=0;mt<tt;)bt[R++]=I>>>mt&255,mt+=M.bits;vt[H]=I>>>mt&255}}if(M.bits===16){G=(rt=new Uint32Array(M.decodePixels().buffer)).length,bt=new Uint8Array(G*(32/M.pixelBitlength)*M.colors),vt=new Uint8Array(G*(32/M.pixelBitlength)),k=M.colors>1,H=0,R=0;for(var pt=0;H<G;)I=rt[H++],bt[R++]=I>>>0&255,k&&(bt[R++]=I>>>16&255,I=rt[H++],bt[R++]=I>>>0&255),vt[pt++]=I>>>16&255;S=8}F!==i.image_compression.NONE&&e()?(_=r(bt,M.width*M.colors,M.colors,F),Nt=r(vt,M.width,1,F)):(_=bt,Nt=vt,ct=void 0)}if(M.colorType===3&&(q=this.color_spaces.INDEXED,dt=M.palette,M.transparency.indexed)){var ft=M.transparency.indexed,Et=0;for(H=0,G=ft.length;H<G;++H)Et+=ft[H];if((Et/=255)===G-1&&ft.indexOf(0)!==-1)Z=[ft.indexOf(0)];else if(Et!==G){for(rt=M.decodePixels(),vt=new Uint8Array(rt.length),H=0,G=rt.length;H<G;H++)vt[H]=ft[rt[H]];Nt=r(vt,M.width,1)}}var w=function(C){var E;switch(C){case i.image_compression.FAST:E=11;break;case i.image_compression.MEDIUM:E=13;break;case i.image_compression.SLOW:E=14;break;default:E=12}return E}(F);return ct===this.decode.FLATE_DECODE&&(ot="/Predictor "+w+" "),ot+="/Colors "+st+" /BitsPerComponent "+S+" /Columns "+M.width,(this.__addimage__.isArrayBuffer(_)||this.__addimage__.isArrayBufferView(_))&&(_=this.__addimage__.arrayBufferToBinaryString(_)),(Nt&&this.__addimage__.isArrayBuffer(Nt)||this.__addimage__.isArrayBufferView(Nt))&&(Nt=this.__addimage__.arrayBufferToBinaryString(Nt)),{alias:B,data:_,index:p,filter:ct,decodeParameters:ot,transparency:Z,palette:dt,sMask:Nt,predictor:w,width:M.width,height:M.height,bitsPerComponent:S,colorSpace:q}}}})(Ut.API),function(i){i.processGIF89A=function(e,r,a,u){var o=new pu(e),h=o.width,l=o.height,f=[];o.decodeAndBlitFrameRGBA(0,f);var v={data:f,width:h,height:l},x=new Zo(100).encode(v,100);return i.processJPEG.call(this,x,r,a,u)},i.processGIF87A=i.processGIF89A}(Ut.API),Bn.prototype.parseHeader=function(){if(this.fileSize=this.datav.getUint32(this.pos,!0),this.pos+=4,this.reserved=this.datav.getUint32(this.pos,!0),this.pos+=4,this.offset=this.datav.getUint32(this.pos,!0),this.pos+=4,this.headerSize=this.datav.getUint32(this.pos,!0),this.pos+=4,this.width=this.datav.getUint32(this.pos,!0),this.pos+=4,this.height=this.datav.getInt32(this.pos,!0),this.pos+=4,this.planes=this.datav.getUint16(this.pos,!0),this.pos+=2,this.bitPP=this.datav.getUint16(this.pos,!0),this.pos+=2,this.compress=this.datav.getUint32(this.pos,!0),this.pos+=4,this.rawSize=this.datav.getUint32(this.pos,!0),this.pos+=4,this.hr=this.datav.getUint32(this.pos,!0),this.pos+=4,this.vr=this.datav.getUint32(this.pos,!0),this.pos+=4,this.colors=this.datav.getUint32(this.pos,!0),this.pos+=4,this.importantColors=this.datav.getUint32(this.pos,!0),this.pos+=4,this.bitPP===16&&this.is_with_alpha&&(this.bitPP=15),this.bitPP<15){var i=this.colors===0?1<<this.bitPP:this.colors;this.palette=new Array(i);for(var e=0;e<i;e++){var r=this.datav.getUint8(this.pos++,!0),a=this.datav.getUint8(this.pos++,!0),u=this.datav.getUint8(this.pos++,!0),o=this.datav.getUint8(this.pos++,!0);this.palette[e]={red:u,green:a,blue:r,quad:o}}}this.height<0&&(this.height*=-1,this.bottom_up=!1)},Bn.prototype.parseBGR=function(){this.pos=this.offset;try{var i="bit"+this.bitPP,e=this.width*this.height*4;this.data=new Uint8Array(e),this[i]()}catch(r){me.log("bit decode error:"+r)}},Bn.prototype.bit1=function(){var i,e=Math.ceil(this.width/8),r=e%4;for(i=this.height-1;i>=0;i--){for(var a=this.bottom_up?i:this.height-1-i,u=0;u<e;u++)for(var o=this.datav.getUint8(this.pos++,!0),h=a*this.width*4+8*u*4,l=0;l<8&&8*u+l<this.width;l++){var f=this.palette[o>>7-l&1];this.data[h+4*l]=f.blue,this.data[h+4*l+1]=f.green,this.data[h+4*l+2]=f.red,this.data[h+4*l+3]=255}r!==0&&(this.pos+=4-r)}},Bn.prototype.bit4=function(){for(var i=Math.ceil(this.width/2),e=i%4,r=this.height-1;r>=0;r--){for(var a=this.bottom_up?r:this.height-1-r,u=0;u<i;u++){var o=this.datav.getUint8(this.pos++,!0),h=a*this.width*4+2*u*4,l=o>>4,f=15&o,v=this.palette[l];if(this.data[h]=v.blue,this.data[h+1]=v.green,this.data[h+2]=v.red,this.data[h+3]=255,2*u+1>=this.width)break;v=this.palette[f],this.data[h+4]=v.blue,this.data[h+4+1]=v.green,this.data[h+4+2]=v.red,this.data[h+4+3]=255}e!==0&&(this.pos+=4-e)}},Bn.prototype.bit8=function(){for(var i=this.width%4,e=this.height-1;e>=0;e--){for(var r=this.bottom_up?e:this.height-1-e,a=0;a<this.width;a++){var u=this.datav.getUint8(this.pos++,!0),o=r*this.width*4+4*a;if(u<this.palette.length){var h=this.palette[u];this.data[o]=h.red,this.data[o+1]=h.green,this.data[o+2]=h.blue,this.data[o+3]=255}else this.data[o]=255,this.data[o+1]=255,this.data[o+2]=255,this.data[o+3]=255}i!==0&&(this.pos+=4-i)}},Bn.prototype.bit15=function(){for(var i=this.width%3,e=parseInt("11111",2),r=this.height-1;r>=0;r--){for(var a=this.bottom_up?r:this.height-1-r,u=0;u<this.width;u++){var o=this.datav.getUint16(this.pos,!0);this.pos+=2;var h=(o&e)/e*255|0,l=(o>>5&e)/e*255|0,f=(o>>10&e)/e*255|0,v=o>>15?255:0,x=a*this.width*4+4*u;this.data[x]=f,this.data[x+1]=l,this.data[x+2]=h,this.data[x+3]=v}this.pos+=i}},Bn.prototype.bit16=function(){for(var i=this.width%3,e=parseInt("11111",2),r=parseInt("111111",2),a=this.height-1;a>=0;a--){for(var u=this.bottom_up?a:this.height-1-a,o=0;o<this.width;o++){var h=this.datav.getUint16(this.pos,!0);this.pos+=2;var l=(h&e)/e*255|0,f=(h>>5&r)/r*255|0,v=(h>>11)/e*255|0,x=u*this.width*4+4*o;this.data[x]=v,this.data[x+1]=f,this.data[x+2]=l,this.data[x+3]=255}this.pos+=i}},Bn.prototype.bit24=function(){for(var i=this.height-1;i>=0;i--){for(var e=this.bottom_up?i:this.height-1-i,r=0;r<this.width;r++){var a=this.datav.getUint8(this.pos++,!0),u=this.datav.getUint8(this.pos++,!0),o=this.datav.getUint8(this.pos++,!0),h=e*this.width*4+4*r;this.data[h]=o,this.data[h+1]=u,this.data[h+2]=a,this.data[h+3]=255}this.pos+=this.width%4}},Bn.prototype.bit32=function(){for(var i=this.height-1;i>=0;i--)for(var e=this.bottom_up?i:this.height-1-i,r=0;r<this.width;r++){var a=this.datav.getUint8(this.pos++,!0),u=this.datav.getUint8(this.pos++,!0),o=this.datav.getUint8(this.pos++,!0),h=this.datav.getUint8(this.pos++,!0),l=e*this.width*4+4*r;this.data[l]=o,this.data[l+1]=u,this.data[l+2]=a,this.data[l+3]=h}},Bn.prototype.getData=function(){return this.data},function(i){i.processBMP=function(e,r,a,u){var o=new Bn(e,!1),h=o.width,l=o.height,f={data:o.getData(),width:h,height:l},v=new Zo(100).encode(f,100);return i.processJPEG.call(this,v,r,a,u)}}(Ut.API),Ks.prototype.getData=function(){return this.data},function(i){i.processWEBP=function(e,r,a,u){var o=new Ks(e),h=o.width,l=o.height,f={data:o.getData(),width:h,height:l},v=new Zo(100).encode(f,100);return i.processJPEG.call(this,v,r,a,u)}}(Ut.API),Ut.API.processRGBA=function(i,e,r){for(var a=i.data,u=a.length,o=new Uint8Array(u/4*3),h=new Uint8Array(u/4),l=0,f=0,v=0;v<u;v+=4){var x=a[v],A=a[v+1],_=a[v+2],p=a[v+3];o[l++]=x,o[l++]=A,o[l++]=_,h[f++]=p}var B=this.__addimage__.arrayBufferToBinaryString(o);return{alpha:this.__addimage__.arrayBufferToBinaryString(h),data:B,index:e,alias:r,colorSpace:"DeviceRGB",bitsPerComponent:8,width:i.width,height:i.height}},Ut.API.setLanguage=function(i){return this.internal.languageSettings===void 0&&(this.internal.languageSettings={},this.internal.languageSettings.isSubscribed=!1),{af:"Afrikaans",sq:"Albanian",ar:"Arabic (Standard)","ar-DZ":"Arabic (Algeria)","ar-BH":"Arabic (Bahrain)","ar-EG":"Arabic (Egypt)","ar-IQ":"Arabic (Iraq)","ar-JO":"Arabic (Jordan)","ar-KW":"Arabic (Kuwait)","ar-LB":"Arabic (Lebanon)","ar-LY":"Arabic (Libya)","ar-MA":"Arabic (Morocco)","ar-OM":"Arabic (Oman)","ar-QA":"Arabic (Qatar)","ar-SA":"Arabic (Saudi Arabia)","ar-SY":"Arabic (Syria)","ar-TN":"Arabic (Tunisia)","ar-AE":"Arabic (U.A.E.)","ar-YE":"Arabic (Yemen)",an:"Aragonese",hy:"Armenian",as:"Assamese",ast:"Asturian",az:"Azerbaijani",eu:"Basque",be:"Belarusian",bn:"Bengali",bs:"Bosnian",br:"Breton",bg:"Bulgarian",my:"Burmese",ca:"Catalan",ch:"Chamorro",ce:"Chechen",zh:"Chinese","zh-HK":"Chinese (Hong Kong)","zh-CN":"Chinese (PRC)","zh-SG":"Chinese (Singapore)","zh-TW":"Chinese (Taiwan)",cv:"Chuvash",co:"Corsican",cr:"Cree",hr:"Croatian",cs:"Czech",da:"Danish",nl:"Dutch (Standard)","nl-BE":"Dutch (Belgian)",en:"English","en-AU":"English (Australia)","en-BZ":"English (Belize)","en-CA":"English (Canada)","en-IE":"English (Ireland)","en-JM":"English (Jamaica)","en-NZ":"English (New Zealand)","en-PH":"English (Philippines)","en-ZA":"English (South Africa)","en-TT":"English (Trinidad & Tobago)","en-GB":"English (United Kingdom)","en-US":"English (United States)","en-ZW":"English (Zimbabwe)",eo:"Esperanto",et:"Estonian",fo:"Faeroese",fj:"Fijian",fi:"Finnish",fr:"French (Standard)","fr-BE":"French (Belgium)","fr-CA":"French (Canada)","fr-FR":"French (France)","fr-LU":"French (Luxembourg)","fr-MC":"French (Monaco)","fr-CH":"French (Switzerland)",fy:"Frisian",fur:"Friulian",gd:"Gaelic (Scots)","gd-IE":"Gaelic (Irish)",gl:"Galacian",ka:"Georgian",de:"German (Standard)","de-AT":"German (Austria)","de-DE":"German (Germany)","de-LI":"German (Liechtenstein)","de-LU":"German (Luxembourg)","de-CH":"German (Switzerland)",el:"Greek",gu:"Gujurati",ht:"Haitian",he:"Hebrew",hi:"Hindi",hu:"Hungarian",is:"Icelandic",id:"Indonesian",iu:"Inuktitut",ga:"Irish",it:"Italian (Standard)","it-CH":"Italian (Switzerland)",ja:"Japanese",kn:"Kannada",ks:"Kashmiri",kk:"Kazakh",km:"Khmer",ky:"Kirghiz",tlh:"Klingon",ko:"Korean","ko-KP":"Korean (North Korea)","ko-KR":"Korean (South Korea)",la:"Latin",lv:"Latvian",lt:"Lithuanian",lb:"Luxembourgish",mk:"North Macedonia",ms:"Malay",ml:"Malayalam",mt:"Maltese",mi:"Maori",mr:"Marathi",mo:"Moldavian",nv:"Navajo",ng:"Ndonga",ne:"Nepali",no:"Norwegian",nb:"Norwegian (Bokmal)",nn:"Norwegian (Nynorsk)",oc:"Occitan",or:"Oriya",om:"Oromo",fa:"Persian","fa-IR":"Persian/Iran",pl:"Polish",pt:"Portuguese","pt-BR":"Portuguese (Brazil)",pa:"Punjabi","pa-IN":"Punjabi (India)","pa-PK":"Punjabi (Pakistan)",qu:"Quechua",rm:"Rhaeto-Romanic",ro:"Romanian","ro-MO":"Romanian (Moldavia)",ru:"Russian","ru-MO":"Russian (Moldavia)",sz:"Sami (Lappish)",sg:"Sango",sa:"Sanskrit",sc:"Sardinian",sd:"Sindhi",si:"Singhalese",sr:"Serbian",sk:"Slovak",sl:"Slovenian",so:"Somani",sb:"Sorbian",es:"Spanish","es-AR":"Spanish (Argentina)","es-BO":"Spanish (Bolivia)","es-CL":"Spanish (Chile)","es-CO":"Spanish (Colombia)","es-CR":"Spanish (Costa Rica)","es-DO":"Spanish (Dominican Republic)","es-EC":"Spanish (Ecuador)","es-SV":"Spanish (El Salvador)","es-GT":"Spanish (Guatemala)","es-HN":"Spanish (Honduras)","es-MX":"Spanish (Mexico)","es-NI":"Spanish (Nicaragua)","es-PA":"Spanish (Panama)","es-PY":"Spanish (Paraguay)","es-PE":"Spanish (Peru)","es-PR":"Spanish (Puerto Rico)","es-ES":"Spanish (Spain)","es-UY":"Spanish (Uruguay)","es-VE":"Spanish (Venezuela)",sx:"Sutu",sw:"Swahili",sv:"Swedish","sv-FI":"Swedish (Finland)","sv-SV":"Swedish (Sweden)",ta:"Tamil",tt:"Tatar",te:"Teluga",th:"Thai",tig:"Tigre",ts:"Tsonga",tn:"Tswana",tr:"Turkish",tk:"Turkmen",uk:"Ukrainian",hsb:"Upper Sorbian",ur:"Urdu",ve:"Venda",vi:"Vietnamese",vo:"Volapuk",wa:"Walloon",cy:"Welsh",xh:"Xhosa",ji:"Yiddish",zu:"Zulu"}[i]!==void 0&&(this.internal.languageSettings.languageCode=i,this.internal.languageSettings.isSubscribed===!1&&(this.internal.events.subscribe("putCatalog",function(){this.internal.write("/Lang ("+this.internal.languageSettings.languageCode+")")}),this.internal.languageSettings.isSubscribed=!0)),this},vi=Ut.API,Xa=vi.getCharWidthsArray=function(i,e){var r,a,u=(e=e||{}).font||this.internal.getFont(),o=e.fontSize||this.internal.getFontSize(),h=e.charSpace||this.internal.getCharSpace(),l=e.widths?e.widths:u.metadata.Unicode.widths,f=l.fof?l.fof:1,v=e.kerning?e.kerning:u.metadata.Unicode.kerning,x=v.fof?v.fof:1,A=e.doKerning!==!1,_=0,p=i.length,B=0,F=l[0]||f,q=[];for(r=0;r<p;r++)a=i.charCodeAt(r),typeof u.metadata.widthOfString=="function"?q.push((u.metadata.widthOfGlyph(u.metadata.characterToGlyph(a))+h*(1e3/o)||0)/1e3):(_=A&&ve(v[a])==="object"&&!isNaN(parseInt(v[a][B],10))?v[a][B]/x:0,q.push((l[a]||F)/f+_)),B=a;return q},Gs=vi.getStringUnitWidth=function(i,e){var r=(e=e||{}).fontSize||this.internal.getFontSize(),a=e.font||this.internal.getFont(),u=e.charSpace||this.internal.getCharSpace();return vi.processArabic&&(i=vi.processArabic(i)),typeof a.metadata.widthOfString=="function"?a.metadata.widthOfString(i,r,u)/r:Xa.apply(this,arguments).reduce(function(o,h){return o+h},0)},Js=function(i,e,r,a){for(var u=[],o=0,h=i.length,l=0;o!==h&&l+e[o]<r;)l+=e[o],o++;u.push(i.slice(0,o));var f=o;for(l=0;o!==h;)l+e[o]>a&&(u.push(i.slice(f,o)),l=0,f=o),l+=e[o],o++;return f!==o&&u.push(i.slice(f,o)),u},Ys=function(i,e,r){r||(r={});var a,u,o,h,l,f,v,x=[],A=[x],_=r.textIndent||0,p=0,B=0,F=i.split(" "),q=Xa.apply(this,[" ",r])[0];if(f=r.lineIndent===-1?F[0].length+2:r.lineIndent||0){var S=Array(f).join(" "),M=[];F.map(function(st){(st=st.split(/\s*\n/)).length>1?M=M.concat(st.map(function(dt,Nt){return(Nt&&dt.length?`
`:"")+dt})):M.push(st[0])}),F=M,f=Gs.apply(this,[S,r])}for(o=0,h=F.length;o<h;o++){var Z=0;if(a=F[o],f&&a[0]==`
`&&(a=a.substr(1),Z=1),_+p+(B=(u=Xa.apply(this,[a,r])).reduce(function(st,dt){return st+dt},0))>e||Z){if(B>e){for(l=Js.apply(this,[a,u,e-(_+p),e]),x.push(l.shift()),x=[l.pop()];l.length;)A.push([l.shift()]);B=u.slice(a.length-(x[0]?x[0].length:0)).reduce(function(st,dt){return st+dt},0)}else x=[a];A.push(x),_=B+f,p=q}else x.push(a),_+=p+B,p=q}return v=f?function(st,dt){return(dt?S:"")+st.join(" ")}:function(st){return st.join(" ")},A.map(v)},vi.splitTextToSize=function(i,e,r){var a,u=(r=r||{}).fontSize||this.internal.getFontSize(),o=(function(x){if(x.widths&&x.kerning)return{widths:x.widths,kerning:x.kerning};var A=this.internal.getFont(x.fontName,x.fontStyle);return A.metadata.Unicode?{widths:A.metadata.Unicode.widths||{0:1},kerning:A.metadata.Unicode.kerning||{}}:{font:A.metadata,fontSize:this.internal.getFontSize(),charSpace:this.internal.getCharSpace()}}).call(this,r);a=Array.isArray(i)?i:String(i).split(/\r?\n/);var h=1*this.internal.scaleFactor*e/u;o.textIndent=r.textIndent?1*r.textIndent*this.internal.scaleFactor/u:0,o.lineIndent=r.lineIndent;var l,f,v=[];for(l=0,f=a.length;l<f;l++)v=v.concat(Ys.apply(this,[a[l],h,o]));return v},function(i){i.__fontmetrics__=i.__fontmetrics__||{};for(var e="klmnopqrstuvwxyz",r={},a={},u=0;u<e.length;u++)r[e[u]]="0123456789abcdef"[u],a["0123456789abcdef"[u]]=e[u];var o=function(A){return"0x"+parseInt(A,10).toString(16)},h=i.__fontmetrics__.compress=function(A){var _,p,B,F,q=["{"];for(var S in A){if(_=A[S],isNaN(parseInt(S,10))?p="'"+S+"'":(S=parseInt(S,10),p=(p=o(S).slice(2)).slice(0,-1)+a[p.slice(-1)]),typeof _=="number")_<0?(B=o(_).slice(3),F="-"):(B=o(_).slice(2),F=""),B=F+B.slice(0,-1)+a[B.slice(-1)];else{if(ve(_)!=="object")throw new Error("Don't know what to do with value type "+ve(_)+".");B=h(_)}q.push(p+B)}return q.push("}"),q.join("")},l=i.__fontmetrics__.uncompress=function(A){if(typeof A!="string")throw new Error("Invalid argument passed to uncompress.");for(var _,p,B,F,q={},S=1,M=q,Z=[],st="",dt="",Nt=A.length-1,rt=1;rt<Nt;rt+=1)(F=A[rt])=="'"?_?(B=_.join(""),_=void 0):_=[]:_?_.push(F):F=="{"?(Z.push([M,B]),M={},B=void 0):F=="}"?((p=Z.pop())[0][p[1]]=M,B=void 0,M=p[0]):F=="-"?S=-1:B===void 0?r.hasOwnProperty(F)?(st+=r[F],B=parseInt(st,16)*S,S=1,st=""):st+=F:r.hasOwnProperty(F)?(dt+=r[F],M[B]=parseInt(dt,16)*S,S=1,B=void 0,dt=""):dt+=F;return q},f={codePages:["WinAnsiEncoding"],WinAnsiEncoding:l("{19m8n201n9q201o9r201s9l201t9m201u8m201w9n201x9o201y8o202k8q202l8r202m9p202q8p20aw8k203k8t203t8v203u9v2cq8s212m9t15m8w15n9w2dw9s16k8u16l9u17s9z17x8y17y9y}")},v={Unicode:{Courier:f,"Courier-Bold":f,"Courier-BoldOblique":f,"Courier-Oblique":f,Helvetica:f,"Helvetica-Bold":f,"Helvetica-BoldOblique":f,"Helvetica-Oblique":f,"Times-Roman":f,"Times-Bold":f,"Times-BoldItalic":f,"Times-Italic":f}},x={Unicode:{"Courier-Oblique":l("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Times-BoldItalic":l("{'widths'{k3o2q4ycx2r201n3m201o6o201s2l201t2l201u2l201w3m201x3m201y3m2k1t2l2r202m2n2n3m2o3m2p5n202q6o2r1w2s2l2t2l2u3m2v3t2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v2l3w3t3x3t3y3t3z3m4k5n4l4m4m4m4n4m4o4s4p4m4q4m4r4s4s4y4t2r4u3m4v4m4w3x4x5t4y4s4z4s5k3x5l4s5m4m5n3r5o3x5p4s5q4m5r5t5s4m5t3x5u3x5v2l5w1w5x2l5y3t5z3m6k2l6l3m6m3m6n2w6o3m6p2w6q2l6r3m6s3r6t1w6u1w6v3m6w1w6x4y6y3r6z3m7k3m7l3m7m2r7n2r7o1w7p3r7q2w7r4m7s3m7t2w7u2r7v2n7w1q7x2n7y3t202l3mcl4mal2ram3man3mao3map3mar3mas2lat4uau1uav3maw3way4uaz2lbk2sbl3t'fof'6obo2lbp3tbq3mbr1tbs2lbu1ybv3mbz3mck4m202k3mcm4mcn4mco4mcp4mcq5ycr4mcs4mct4mcu4mcv4mcw2r2m3rcy2rcz2rdl4sdm4sdn4sdo4sdp4sdq4sds4sdt4sdu4sdv4sdw4sdz3mek3mel3mem3men3meo3mep3meq4ser2wes2wet2weu2wev2wew1wex1wey1wez1wfl3rfm3mfn3mfo3mfp3mfq3mfr3tfs3mft3rfu3rfv3rfw3rfz2w203k6o212m6o2dw2l2cq2l3t3m3u2l17s3x19m3m}'kerning'{cl{4qu5kt5qt5rs17ss5ts}201s{201ss}201t{cks4lscmscnscoscpscls2wu2yu201ts}201x{2wu2yu}2k{201ts}2w{4qx5kx5ou5qx5rs17su5tu}2x{17su5tu5ou}2y{4qx5kx5ou5qx5rs17ss5ts}'fof'-6ofn{17sw5tw5ou5qw5rs}7t{cksclscmscnscoscps4ls}3u{17su5tu5os5qs}3v{17su5tu5os5qs}7p{17su5tu}ck{4qu5kt5qt5rs17ss5ts}4l{4qu5kt5qt5rs17ss5ts}cm{4qu5kt5qt5rs17ss5ts}cn{4qu5kt5qt5rs17ss5ts}co{4qu5kt5qt5rs17ss5ts}cp{4qu5kt5qt5rs17ss5ts}6l{4qu5ou5qw5rt17su5tu}5q{ckuclucmucnucoucpu4lu}5r{ckuclucmucnucoucpu4lu}7q{cksclscmscnscoscps4ls}6p{4qu5ou5qw5rt17sw5tw}ek{4qu5ou5qw5rt17su5tu}el{4qu5ou5qw5rt17su5tu}em{4qu5ou5qw5rt17su5tu}en{4qu5ou5qw5rt17su5tu}eo{4qu5ou5qw5rt17su5tu}ep{4qu5ou5qw5rt17su5tu}es{17ss5ts5qs4qu}et{4qu5ou5qw5rt17sw5tw}eu{4qu5ou5qw5rt17ss5ts}ev{17ss5ts5qs4qu}6z{17sw5tw5ou5qw5rs}fm{17sw5tw5ou5qw5rs}7n{201ts}fo{17sw5tw5ou5qw5rs}fp{17sw5tw5ou5qw5rs}fq{17sw5tw5ou5qw5rs}7r{cksclscmscnscoscps4ls}fs{17sw5tw5ou5qw5rs}ft{17su5tu}fu{17su5tu}fv{17su5tu}fw{17su5tu}fz{cksclscmscnscoscps4ls}}}"),"Helvetica-Bold":l("{'widths'{k3s2q4scx1w201n3r201o6o201s1w201t1w201u1w201w3m201x3m201y3m2k1w2l2l202m2n2n3r2o3r2p5t202q6o2r1s2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v2l3w3u3x3u3y3u3z3x4k6l4l4s4m4s4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3r4v4s4w3x4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v2l5w1w5x2l5y3u5z3r6k2l6l3r6m3x6n3r6o3x6p3r6q2l6r3x6s3x6t1w6u1w6v3r6w1w6x5t6y3x6z3x7k3x7l3x7m2r7n3r7o2l7p3x7q3r7r4y7s3r7t3r7u3m7v2r7w1w7x2r7y3u202l3rcl4sal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3xbq3rbr1wbs2lbu2obv3rbz3xck4s202k3rcm4scn4sco4scp4scq6ocr4scs4mct4mcu4mcv4mcw1w2m2zcy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3res3ret3reu3rev3rew1wex1wey1wez1wfl3xfm3xfn3xfo3xfp3xfq3xfr3ufs3xft3xfu3xfv3xfw3xfz3r203k6o212m6o2dw2l2cq2l3t3r3u2l17s4m19m3r}'kerning'{cl{4qs5ku5ot5qs17sv5tv}201t{2ww4wy2yw}201w{2ks}201x{2ww4wy2yw}2k{201ts201xs}2w{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}2x{5ow5qs}2y{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}'fof'-6o7p{17su5tu5ot}ck{4qs5ku5ot5qs17sv5tv}4l{4qs5ku5ot5qs17sv5tv}cm{4qs5ku5ot5qs17sv5tv}cn{4qs5ku5ot5qs17sv5tv}co{4qs5ku5ot5qs17sv5tv}cp{4qs5ku5ot5qs17sv5tv}6l{17st5tt5os}17s{2kwclvcmvcnvcovcpv4lv4wwckv}5o{2kucltcmtcntcotcpt4lt4wtckt}5q{2ksclscmscnscoscps4ls4wvcks}5r{2ks4ws}5t{2kwclvcmvcnvcovcpv4lv4wwckv}eo{17st5tt5os}fu{17su5tu5ot}6p{17ss5ts}ek{17st5tt5os}el{17st5tt5os}em{17st5tt5os}en{17st5tt5os}6o{201ts}ep{17st5tt5os}es{17ss5ts}et{17ss5ts}eu{17ss5ts}ev{17ss5ts}6z{17su5tu5os5qt}fm{17su5tu5os5qt}fn{17su5tu5os5qt}fo{17su5tu5os5qt}fp{17su5tu5os5qt}fq{17su5tu5os5qt}fs{17su5tu5os5qt}ft{17su5tu5ot}7m{5os}fv{17su5tu5ot}fw{17su5tu5ot}}}"),Courier:l("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Courier-BoldOblique":l("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Times-Bold":l("{'widths'{k3q2q5ncx2r201n3m201o6o201s2l201t2l201u2l201w3m201x3m201y3m2k1t2l2l202m2n2n3m2o3m2p6o202q6o2r1w2s2l2t2l2u3m2v3t2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v2l3w3t3x3t3y3t3z3m4k5x4l4s4m4m4n4s4o4s4p4m4q3x4r4y4s4y4t2r4u3m4v4y4w4m4x5y4y4s4z4y5k3x5l4y5m4s5n3r5o4m5p4s5q4s5r6o5s4s5t4s5u4m5v2l5w1w5x2l5y3u5z3m6k2l6l3m6m3r6n2w6o3r6p2w6q2l6r3m6s3r6t1w6u2l6v3r6w1w6x5n6y3r6z3m7k3r7l3r7m2w7n2r7o2l7p3r7q3m7r4s7s3m7t3m7u2w7v2r7w1q7x2r7y3o202l3mcl4sal2lam3man3mao3map3mar3mas2lat4uau1yav3maw3tay4uaz2lbk2sbl3t'fof'6obo2lbp3rbr1tbs2lbu2lbv3mbz3mck4s202k3mcm4scn4sco4scp4scq6ocr4scs4mct4mcu4mcv4mcw2r2m3rcy2rcz2rdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3rek3mel3mem3men3meo3mep3meq4ser2wes2wet2weu2wev2wew1wex1wey1wez1wfl3rfm3mfn3mfo3mfp3mfq3mfr3tfs3mft3rfu3rfv3rfw3rfz3m203k6o212m6o2dw2l2cq2l3t3m3u2l17s4s19m3m}'kerning'{cl{4qt5ks5ot5qy5rw17sv5tv}201t{cks4lscmscnscoscpscls4wv}2k{201ts}2w{4qu5ku7mu5os5qx5ru17su5tu}2x{17su5tu5ou5qs}2y{4qv5kv7mu5ot5qz5ru17su5tu}'fof'-6o7t{cksclscmscnscoscps4ls}3u{17su5tu5os5qu}3v{17su5tu5os5qu}fu{17su5tu5ou5qu}7p{17su5tu5ou5qu}ck{4qt5ks5ot5qy5rw17sv5tv}4l{4qt5ks5ot5qy5rw17sv5tv}cm{4qt5ks5ot5qy5rw17sv5tv}cn{4qt5ks5ot5qy5rw17sv5tv}co{4qt5ks5ot5qy5rw17sv5tv}cp{4qt5ks5ot5qy5rw17sv5tv}6l{17st5tt5ou5qu}17s{ckuclucmucnucoucpu4lu4wu}5o{ckuclucmucnucoucpu4lu4wu}5q{ckzclzcmzcnzcozcpz4lz4wu}5r{ckxclxcmxcnxcoxcpx4lx4wu}5t{ckuclucmucnucoucpu4lu4wu}7q{ckuclucmucnucoucpu4lu}6p{17sw5tw5ou5qu}ek{17st5tt5qu}el{17st5tt5ou5qu}em{17st5tt5qu}en{17st5tt5qu}eo{17st5tt5qu}ep{17st5tt5ou5qu}es{17ss5ts5qu}et{17sw5tw5ou5qu}eu{17sw5tw5ou5qu}ev{17ss5ts5qu}6z{17sw5tw5ou5qu5rs}fm{17sw5tw5ou5qu5rs}fn{17sw5tw5ou5qu5rs}fo{17sw5tw5ou5qu5rs}fp{17sw5tw5ou5qu5rs}fq{17sw5tw5ou5qu5rs}7r{cktcltcmtcntcotcpt4lt5os}fs{17sw5tw5ou5qu5rs}ft{17su5tu5ou5qu}7m{5os}fv{17su5tu5ou5qu}fw{17su5tu5ou5qu}fz{cksclscmscnscoscps4ls}}}"),Symbol:l("{'widths'{k3uaw4r19m3m2k1t2l2l202m2y2n3m2p5n202q6o3k3m2s2l2t2l2v3r2w1t3m3m2y1t2z1wbk2sbl3r'fof'6o3n3m3o3m3p3m3q3m3r3m3s3m3t3m3u1w3v1w3w3r3x3r3y3r3z2wbp3t3l3m5v2l5x2l5z3m2q4yfr3r7v3k7w1o7x3k}'kerning'{'fof'-6o}}"),Helvetica:l("{'widths'{k3p2q4mcx1w201n3r201o6o201s1q201t1q201u1q201w2l201x2l201y2l2k1w2l1w202m2n2n3r2o3r2p5t202q6o2r1n2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v1w3w3u3x3u3y3u3z3r4k6p4l4m4m4m4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3m4v4m4w3r4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v1w5w1w5x1w5y2z5z3r6k2l6l3r6m3r6n3m6o3r6p3r6q1w6r3r6s3r6t1q6u1q6v3m6w1q6x5n6y3r6z3r7k3r7l3r7m2l7n3m7o1w7p3r7q3m7r4s7s3m7t3m7u3m7v2l7w1u7x2l7y3u202l3rcl4mal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3rbr1wbs2lbu2obv3rbz3xck4m202k3rcm4mcn4mco4mcp4mcq6ocr4scs4mct4mcu4mcv4mcw1w2m2ncy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3mes3ret3reu3rev3rew1wex1wey1wez1wfl3rfm3rfn3rfo3rfp3rfq3rfr3ufs3xft3rfu3rfv3rfw3rfz3m203k6o212m6o2dw2l2cq2l3t3r3u1w17s4m19m3r}'kerning'{5q{4wv}cl{4qs5kw5ow5qs17sv5tv}201t{2wu4w1k2yu}201x{2wu4wy2yu}17s{2ktclucmucnu4otcpu4lu4wycoucku}2w{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}2x{17sy5ty5oy5qs}2y{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}'fof'-6o7p{17sv5tv5ow}ck{4qs5kw5ow5qs17sv5tv}4l{4qs5kw5ow5qs17sv5tv}cm{4qs5kw5ow5qs17sv5tv}cn{4qs5kw5ow5qs17sv5tv}co{4qs5kw5ow5qs17sv5tv}cp{4qs5kw5ow5qs17sv5tv}6l{17sy5ty5ow}do{17st5tt}4z{17st5tt}7s{fst}dm{17st5tt}dn{17st5tt}5o{ckwclwcmwcnwcowcpw4lw4wv}dp{17st5tt}dq{17st5tt}7t{5ow}ds{17st5tt}5t{2ktclucmucnu4otcpu4lu4wycoucku}fu{17sv5tv5ow}6p{17sy5ty5ow5qs}ek{17sy5ty5ow}el{17sy5ty5ow}em{17sy5ty5ow}en{5ty}eo{17sy5ty5ow}ep{17sy5ty5ow}es{17sy5ty5qs}et{17sy5ty5ow5qs}eu{17sy5ty5ow5qs}ev{17sy5ty5ow5qs}6z{17sy5ty5ow5qs}fm{17sy5ty5ow5qs}fn{17sy5ty5ow5qs}fo{17sy5ty5ow5qs}fp{17sy5ty5qs}fq{17sy5ty5ow5qs}7r{5ow}fs{17sy5ty5ow5qs}ft{17sv5tv5ow}7m{5ow}fv{17sv5tv5ow}fw{17sv5tv5ow}}}"),"Helvetica-BoldOblique":l("{'widths'{k3s2q4scx1w201n3r201o6o201s1w201t1w201u1w201w3m201x3m201y3m2k1w2l2l202m2n2n3r2o3r2p5t202q6o2r1s2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v2l3w3u3x3u3y3u3z3x4k6l4l4s4m4s4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3r4v4s4w3x4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v2l5w1w5x2l5y3u5z3r6k2l6l3r6m3x6n3r6o3x6p3r6q2l6r3x6s3x6t1w6u1w6v3r6w1w6x5t6y3x6z3x7k3x7l3x7m2r7n3r7o2l7p3x7q3r7r4y7s3r7t3r7u3m7v2r7w1w7x2r7y3u202l3rcl4sal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3xbq3rbr1wbs2lbu2obv3rbz3xck4s202k3rcm4scn4sco4scp4scq6ocr4scs4mct4mcu4mcv4mcw1w2m2zcy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3res3ret3reu3rev3rew1wex1wey1wez1wfl3xfm3xfn3xfo3xfp3xfq3xfr3ufs3xft3xfu3xfv3xfw3xfz3r203k6o212m6o2dw2l2cq2l3t3r3u2l17s4m19m3r}'kerning'{cl{4qs5ku5ot5qs17sv5tv}201t{2ww4wy2yw}201w{2ks}201x{2ww4wy2yw}2k{201ts201xs}2w{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}2x{5ow5qs}2y{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}'fof'-6o7p{17su5tu5ot}ck{4qs5ku5ot5qs17sv5tv}4l{4qs5ku5ot5qs17sv5tv}cm{4qs5ku5ot5qs17sv5tv}cn{4qs5ku5ot5qs17sv5tv}co{4qs5ku5ot5qs17sv5tv}cp{4qs5ku5ot5qs17sv5tv}6l{17st5tt5os}17s{2kwclvcmvcnvcovcpv4lv4wwckv}5o{2kucltcmtcntcotcpt4lt4wtckt}5q{2ksclscmscnscoscps4ls4wvcks}5r{2ks4ws}5t{2kwclvcmvcnvcovcpv4lv4wwckv}eo{17st5tt5os}fu{17su5tu5ot}6p{17ss5ts}ek{17st5tt5os}el{17st5tt5os}em{17st5tt5os}en{17st5tt5os}6o{201ts}ep{17st5tt5os}es{17ss5ts}et{17ss5ts}eu{17ss5ts}ev{17ss5ts}6z{17su5tu5os5qt}fm{17su5tu5os5qt}fn{17su5tu5os5qt}fo{17su5tu5os5qt}fp{17su5tu5os5qt}fq{17su5tu5os5qt}fs{17su5tu5os5qt}ft{17su5tu5ot}7m{5os}fv{17su5tu5ot}fw{17su5tu5ot}}}"),ZapfDingbats:l("{'widths'{k4u2k1w'fof'6o}'kerning'{'fof'-6o}}"),"Courier-Bold":l("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Times-Italic":l("{'widths'{k3n2q4ycx2l201n3m201o5t201s2l201t2l201u2l201w3r201x3r201y3r2k1t2l2l202m2n2n3m2o3m2p5n202q5t2r1p2s2l2t2l2u3m2v4n2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v2l3w4n3x4n3y4n3z3m4k5w4l3x4m3x4n4m4o4s4p3x4q3x4r4s4s4s4t2l4u2w4v4m4w3r4x5n4y4m4z4s5k3x5l4s5m3x5n3m5o3r5p4s5q3x5r5n5s3x5t3r5u3r5v2r5w1w5x2r5y2u5z3m6k2l6l3m6m3m6n2w6o3m6p2w6q1w6r3m6s3m6t1w6u1w6v2w6w1w6x4s6y3m6z3m7k3m7l3m7m2r7n2r7o1w7p3m7q2w7r4m7s2w7t2w7u2r7v2s7w1v7x2s7y3q202l3mcl3xal2ram3man3mao3map3mar3mas2lat4wau1vav3maw4nay4waz2lbk2sbl4n'fof'6obo2lbp3mbq3obr1tbs2lbu1zbv3mbz3mck3x202k3mcm3xcn3xco3xcp3xcq5tcr4mcs3xct3xcu3xcv3xcw2l2m2ucy2lcz2ldl4mdm4sdn4sdo4sdp4sdq4sds4sdt4sdu4sdv4sdw4sdz3mek3mel3mem3men3meo3mep3meq4mer2wes2wet2weu2wev2wew1wex1wey1wez1wfl3mfm3mfn3mfo3mfp3mfq3mfr4nfs3mft3mfu3mfv3mfw3mfz2w203k6o212m6m2dw2l2cq2l3t3m3u2l17s3r19m3m}'kerning'{cl{5kt4qw}201s{201sw}201t{201tw2wy2yy6q-t}201x{2wy2yy}2k{201tw}2w{7qs4qy7rs5ky7mw5os5qx5ru17su5tu}2x{17ss5ts5os}2y{7qs4qy7rs5ky7mw5os5qx5ru17su5tu}'fof'-6o6t{17ss5ts5qs}7t{5os}3v{5qs}7p{17su5tu5qs}ck{5kt4qw}4l{5kt4qw}cm{5kt4qw}cn{5kt4qw}co{5kt4qw}cp{5kt4qw}6l{4qs5ks5ou5qw5ru17su5tu}17s{2ks}5q{ckvclvcmvcnvcovcpv4lv}5r{ckuclucmucnucoucpu4lu}5t{2ks}6p{4qs5ks5ou5qw5ru17su5tu}ek{4qs5ks5ou5qw5ru17su5tu}el{4qs5ks5ou5qw5ru17su5tu}em{4qs5ks5ou5qw5ru17su5tu}en{4qs5ks5ou5qw5ru17su5tu}eo{4qs5ks5ou5qw5ru17su5tu}ep{4qs5ks5ou5qw5ru17su5tu}es{5ks5qs4qs}et{4qs5ks5ou5qw5ru17su5tu}eu{4qs5ks5qw5ru17su5tu}ev{5ks5qs4qs}ex{17ss5ts5qs}6z{4qv5ks5ou5qw5ru17su5tu}fm{4qv5ks5ou5qw5ru17su5tu}fn{4qv5ks5ou5qw5ru17su5tu}fo{4qv5ks5ou5qw5ru17su5tu}fp{4qv5ks5ou5qw5ru17su5tu}fq{4qv5ks5ou5qw5ru17su5tu}7r{5os}fs{4qv5ks5ou5qw5ru17su5tu}ft{17su5tu5qs}fu{17su5tu5qs}fv{17su5tu5qs}fw{17su5tu5qs}}}"),"Times-Roman":l("{'widths'{k3n2q4ycx2l201n3m201o6o201s2l201t2l201u2l201w2w201x2w201y2w2k1t2l2l202m2n2n3m2o3m2p5n202q6o2r1m2s2l2t2l2u3m2v3s2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v1w3w3s3x3s3y3s3z2w4k5w4l4s4m4m4n4m4o4s4p3x4q3r4r4s4s4s4t2l4u2r4v4s4w3x4x5t4y4s4z4s5k3r5l4s5m4m5n3r5o3x5p4s5q4s5r5y5s4s5t4s5u3x5v2l5w1w5x2l5y2z5z3m6k2l6l2w6m3m6n2w6o3m6p2w6q2l6r3m6s3m6t1w6u1w6v3m6w1w6x4y6y3m6z3m7k3m7l3m7m2l7n2r7o1w7p3m7q3m7r4s7s3m7t3m7u2w7v3k7w1o7x3k7y3q202l3mcl4sal2lam3man3mao3map3mar3mas2lat4wau1vav3maw3say4waz2lbk2sbl3s'fof'6obo2lbp3mbq2xbr1tbs2lbu1zbv3mbz2wck4s202k3mcm4scn4sco4scp4scq5tcr4mcs3xct3xcu3xcv3xcw2l2m2tcy2lcz2ldl4sdm4sdn4sdo4sdp4sdq4sds4sdt4sdu4sdv4sdw4sdz3mek2wel2wem2wen2weo2wep2weq4mer2wes2wet2weu2wev2wew1wex1wey1wez1wfl3mfm3mfn3mfo3mfp3mfq3mfr3sfs3mft3mfu3mfv3mfw3mfz3m203k6o212m6m2dw2l2cq2l3t3m3u1w17s4s19m3m}'kerning'{cl{4qs5ku17sw5ou5qy5rw201ss5tw201ws}201s{201ss}201t{ckw4lwcmwcnwcowcpwclw4wu201ts}2k{201ts}2w{4qs5kw5os5qx5ru17sx5tx}2x{17sw5tw5ou5qu}2y{4qs5kw5os5qx5ru17sx5tx}'fof'-6o7t{ckuclucmucnucoucpu4lu5os5rs}3u{17su5tu5qs}3v{17su5tu5qs}7p{17sw5tw5qs}ck{4qs5ku17sw5ou5qy5rw201ss5tw201ws}4l{4qs5ku17sw5ou5qy5rw201ss5tw201ws}cm{4qs5ku17sw5ou5qy5rw201ss5tw201ws}cn{4qs5ku17sw5ou5qy5rw201ss5tw201ws}co{4qs5ku17sw5ou5qy5rw201ss5tw201ws}cp{4qs5ku17sw5ou5qy5rw201ss5tw201ws}6l{17su5tu5os5qw5rs}17s{2ktclvcmvcnvcovcpv4lv4wuckv}5o{ckwclwcmwcnwcowcpw4lw4wu}5q{ckyclycmycnycoycpy4ly4wu5ms}5r{cktcltcmtcntcotcpt4lt4ws}5t{2ktclvcmvcnvcovcpv4lv4wuckv}7q{cksclscmscnscoscps4ls}6p{17su5tu5qw5rs}ek{5qs5rs}el{17su5tu5os5qw5rs}em{17su5tu5os5qs5rs}en{17su5qs5rs}eo{5qs5rs}ep{17su5tu5os5qw5rs}es{5qs}et{17su5tu5qw5rs}eu{17su5tu5qs5rs}ev{5qs}6z{17sv5tv5os5qx5rs}fm{5os5qt5rs}fn{17sv5tv5os5qx5rs}fo{17sv5tv5os5qx5rs}fp{5os5qt5rs}fq{5os5qt5rs}7r{ckuclucmucnucoucpu4lu5os}fs{17sv5tv5os5qx5rs}ft{17ss5ts5qs}fu{17sw5tw5qs}fv{17sw5tw5qs}fw{17ss5ts5qs}fz{ckuclucmucnucoucpu4lu5os5rs}}}"),"Helvetica-Oblique":l("{'widths'{k3p2q4mcx1w201n3r201o6o201s1q201t1q201u1q201w2l201x2l201y2l2k1w2l1w202m2n2n3r2o3r2p5t202q6o2r1n2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v1w3w3u3x3u3y3u3z3r4k6p4l4m4m4m4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3m4v4m4w3r4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v1w5w1w5x1w5y2z5z3r6k2l6l3r6m3r6n3m6o3r6p3r6q1w6r3r6s3r6t1q6u1q6v3m6w1q6x5n6y3r6z3r7k3r7l3r7m2l7n3m7o1w7p3r7q3m7r4s7s3m7t3m7u3m7v2l7w1u7x2l7y3u202l3rcl4mal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3rbr1wbs2lbu2obv3rbz3xck4m202k3rcm4mcn4mco4mcp4mcq6ocr4scs4mct4mcu4mcv4mcw1w2m2ncy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3mes3ret3reu3rev3rew1wex1wey1wez1wfl3rfm3rfn3rfo3rfp3rfq3rfr3ufs3xft3rfu3rfv3rfw3rfz3m203k6o212m6o2dw2l2cq2l3t3r3u1w17s4m19m3r}'kerning'{5q{4wv}cl{4qs5kw5ow5qs17sv5tv}201t{2wu4w1k2yu}201x{2wu4wy2yu}17s{2ktclucmucnu4otcpu4lu4wycoucku}2w{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}2x{17sy5ty5oy5qs}2y{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}'fof'-6o7p{17sv5tv5ow}ck{4qs5kw5ow5qs17sv5tv}4l{4qs5kw5ow5qs17sv5tv}cm{4qs5kw5ow5qs17sv5tv}cn{4qs5kw5ow5qs17sv5tv}co{4qs5kw5ow5qs17sv5tv}cp{4qs5kw5ow5qs17sv5tv}6l{17sy5ty5ow}do{17st5tt}4z{17st5tt}7s{fst}dm{17st5tt}dn{17st5tt}5o{ckwclwcmwcnwcowcpw4lw4wv}dp{17st5tt}dq{17st5tt}7t{5ow}ds{17st5tt}5t{2ktclucmucnu4otcpu4lu4wycoucku}fu{17sv5tv5ow}6p{17sy5ty5ow5qs}ek{17sy5ty5ow}el{17sy5ty5ow}em{17sy5ty5ow}en{5ty}eo{17sy5ty5ow}ep{17sy5ty5ow}es{17sy5ty5qs}et{17sy5ty5ow5qs}eu{17sy5ty5ow5qs}ev{17sy5ty5ow5qs}6z{17sy5ty5ow5qs}fm{17sy5ty5ow5qs}fn{17sy5ty5ow5qs}fo{17sy5ty5ow5qs}fp{17sy5ty5qs}fq{17sy5ty5ow5qs}7r{5ow}fs{17sy5ty5ow5qs}ft{17sv5tv5ow}7m{5ow}fv{17sv5tv5ow}fw{17sv5tv5ow}}}")}};i.events.push(["addFont",function(A){var _=A.font,p=x.Unicode[_.postScriptName];p&&(_.metadata.Unicode={},_.metadata.Unicode.widths=p.widths,_.metadata.Unicode.kerning=p.kerning);var B=v.Unicode[_.postScriptName];B&&(_.metadata.Unicode.encoding=B,_.encoding=B.codePages[0])}])}(Ut.API),function(i){var e=function(r){for(var a=r.length,u=new Uint8Array(a),o=0;o<a;o++)u[o]=r.charCodeAt(o);return u};i.API.events.push(["addFont",function(r){var a=void 0,u=r.font,o=r.instance;if(!u.isStandardFont){if(o===void 0)throw new Error("Font does not exist in vFS, import fonts or remove declaration doc.addFont('"+u.postScriptName+"').");if(typeof(a=o.existsFileInVFS(u.postScriptName)===!1?o.loadFile(u.postScriptName):o.getFileFromVFS(u.postScriptName))!="string")throw new Error("Font is not stored as string-data in vFS, import fonts or remove declaration doc.addFont('"+u.postScriptName+"').");(function(h,l){l=/^\x00\x01\x00\x00/.test(l)?e(l):e(ea(l)),h.metadata=i.API.TTFFont.open(l),h.metadata.Unicode=h.metadata.Unicode||{encoding:{},kerning:{},widths:[]},h.metadata.glyIdsUsed=[0]})(u,a)}}])}(Ut),function(i){function e(){return(zt.canvg?Promise.resolve(zt.canvg):Qo(()=>import("./index.es-bbb57ed8.js"),["assets/index.es-bbb57ed8.js","assets/vendor-dd4ba10b.js","assets/react-select-fbb128b2.js","assets/html2pdf.js-19c9759c.js"])).catch(function(r){return Promise.reject(new Error("Could not load canvg: "+r))}).then(function(r){return r.default?r.default:r})}Ut.API.addSvgAsImage=function(r,a,u,o,h,l,f,v){if(isNaN(a)||isNaN(u))throw me.error("jsPDF.addSvgAsImage: Invalid coordinates",arguments),new Error("Invalid coordinates passed to jsPDF.addSvgAsImage");if(isNaN(o)||isNaN(h))throw me.error("jsPDF.addSvgAsImage: Invalid measurements",arguments),new Error("Invalid measurements (width and/or height) passed to jsPDF.addSvgAsImage");var x=document.createElement("canvas");x.width=o,x.height=h;var A=x.getContext("2d");A.fillStyle="#fff",A.fillRect(0,0,x.width,x.height);var _={ignoreMouse:!0,ignoreAnimation:!0,ignoreDimensions:!0},p=this;return e().then(function(B){return B.fromString(A,r,_)},function(){return Promise.reject(new Error("Could not load canvg."))}).then(function(B){return B.render(_)}).then(function(){p.addImage(x.toDataURL("image/jpeg",1),a,u,o,h,f,v)})}}(),Ut.API.putTotalPages=function(i){var e,r=0;parseInt(this.internal.getFont().id.substr(1),10)<15?(e=new RegExp(i,"g"),r=this.internal.getNumberOfPages()):(e=new RegExp(this.pdfEscape16(i,this.internal.getFont()),"g"),r=this.pdfEscape16(this.internal.getNumberOfPages()+"",this.internal.getFont()));for(var a=1;a<=this.internal.getNumberOfPages();a++)for(var u=0;u<this.internal.pages[a].length;u++)this.internal.pages[a][u]=this.internal.pages[a][u].replace(e,r);return this},Ut.API.viewerPreferences=function(i,e){var r;i=i||{},e=e||!1;var a,u,o,h={HideToolbar:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},HideMenubar:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},HideWindowUI:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},FitWindow:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},CenterWindow:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},DisplayDocTitle:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.4},NonFullScreenPageMode:{defaultValue:"UseNone",value:"UseNone",type:"name",explicitSet:!1,valueSet:["UseNone","UseOutlines","UseThumbs","UseOC"],pdfVersion:1.3},Direction:{defaultValue:"L2R",value:"L2R",type:"name",explicitSet:!1,valueSet:["L2R","R2L"],pdfVersion:1.3},ViewArea:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},ViewClip:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},PrintArea:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},PrintClip:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},PrintScaling:{defaultValue:"AppDefault",value:"AppDefault",type:"name",explicitSet:!1,valueSet:["AppDefault","None"],pdfVersion:1.6},Duplex:{defaultValue:"",value:"none",type:"name",explicitSet:!1,valueSet:["Simplex","DuplexFlipShortEdge","DuplexFlipLongEdge","none"],pdfVersion:1.7},PickTrayByPDFSize:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.7},PrintPageRange:{defaultValue:"",value:"",type:"array",explicitSet:!1,valueSet:null,pdfVersion:1.7},NumCopies:{defaultValue:1,value:1,type:"integer",explicitSet:!1,valueSet:null,pdfVersion:1.7}},l=Object.keys(h),f=[],v=0,x=0,A=0;function _(B,F){var q,S=!1;for(q=0;q<B.length;q+=1)B[q]===F&&(S=!0);return S}if(this.internal.viewerpreferences===void 0&&(this.internal.viewerpreferences={},this.internal.viewerpreferences.configuration=JSON.parse(JSON.stringify(h)),this.internal.viewerpreferences.isSubscribed=!1),r=this.internal.viewerpreferences.configuration,i==="reset"||e===!0){var p=l.length;for(A=0;A<p;A+=1)r[l[A]].value=r[l[A]].defaultValue,r[l[A]].explicitSet=!1}if(ve(i)==="object"){for(u in i)if(o=i[u],_(l,u)&&o!==void 0){if(r[u].type==="boolean"&&typeof o=="boolean")r[u].value=o;else if(r[u].type==="name"&&_(r[u].valueSet,o))r[u].value=o;else if(r[u].type==="integer"&&Number.isInteger(o))r[u].value=o;else if(r[u].type==="array"){for(v=0;v<o.length;v+=1)if(a=!0,o[v].length===1&&typeof o[v][0]=="number")f.push(String(o[v]-1));else if(o[v].length>1){for(x=0;x<o[v].length;x+=1)typeof o[v][x]!="number"&&(a=!1);a===!0&&f.push([o[v][0]-1,o[v][1]-1].join(" "))}r[u].value="["+f.join(" ")+"]"}else r[u].value=r[u].defaultValue;r[u].explicitSet=!0}}return this.internal.viewerpreferences.isSubscribed===!1&&(this.internal.events.subscribe("putCatalog",function(){var B,F=[];for(B in r)r[B].explicitSet===!0&&(r[B].type==="name"?F.push("/"+B+" /"+r[B].value):F.push("/"+B+" "+r[B].value));F.length!==0&&this.internal.write(`/ViewerPreferences
<<
`+F.join(`
`)+`
>>`)}),this.internal.viewerpreferences.isSubscribed=!0),this.internal.viewerpreferences.configuration=r,this},function(i){var e=function(){var a='<rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"><rdf:Description rdf:about="" xmlns:jspdf="'+this.internal.__metadata__.namespaceuri+'"><jspdf:metadata>',u=unescape(encodeURIComponent('<x:xmpmeta xmlns:x="adobe:ns:meta/">')),o=unescape(encodeURIComponent(a)),h=unescape(encodeURIComponent(this.internal.__metadata__.metadata)),l=unescape(encodeURIComponent("</jspdf:metadata></rdf:Description></rdf:RDF>")),f=unescape(encodeURIComponent("</x:xmpmeta>")),v=o.length+h.length+l.length+u.length+f.length;this.internal.__metadata__.metadata_object_number=this.internal.newObject(),this.internal.write("<< /Type /Metadata /Subtype /XML /Length "+v+" >>"),this.internal.write("stream"),this.internal.write(u+o+h+l+f),this.internal.write("endstream"),this.internal.write("endobj")},r=function(){this.internal.__metadata__.metadata_object_number&&this.internal.write("/Metadata "+this.internal.__metadata__.metadata_object_number+" 0 R")};i.addMetadata=function(a,u){return this.internal.__metadata__===void 0&&(this.internal.__metadata__={metadata:a,namespaceuri:u||"http://jspdf.default.namespaceuri/"},this.internal.events.subscribe("putCatalog",r),this.internal.events.subscribe("postPutResources",e)),this}}(Ut.API),function(i){var e=i.API,r=e.pdfEscape16=function(o,h){for(var l,f=h.metadata.Unicode.widths,v=["","0","00","000","0000"],x=[""],A=0,_=o.length;A<_;++A){if(l=h.metadata.characterToGlyph(o.charCodeAt(A)),h.metadata.glyIdsUsed.push(l),h.metadata.toUnicode[l]=o.charCodeAt(A),f.indexOf(l)==-1&&(f.push(l),f.push([parseInt(h.metadata.widthOfGlyph(l),10)])),l=="0")return x.join("");l=l.toString(16),x.push(v[4-l.length],l)}return x.join("")},a=function(o){var h,l,f,v,x,A,_;for(x=`/CIDInit /ProcSet findresource begin
12 dict begin
begincmap
/CIDSystemInfo <<
  /Registry (Adobe)
  /Ordering (UCS)
  /Supplement 0
>> def
/CMapName /Adobe-Identity-UCS def
/CMapType 2 def
1 begincodespacerange
<0000><ffff>
endcodespacerange`,f=[],A=0,_=(l=Object.keys(o).sort(function(p,B){return p-B})).length;A<_;A++)h=l[A],f.length>=100&&(x+=`
`+f.length+` beginbfchar
`+f.join(`
`)+`
endbfchar`,f=[]),o[h]!==void 0&&o[h]!==null&&typeof o[h].toString=="function"&&(v=("0000"+o[h].toString(16)).slice(-4),h=("0000"+(+h).toString(16)).slice(-4),f.push("<"+h+"><"+v+">"));return f.length&&(x+=`
`+f.length+` beginbfchar
`+f.join(`
`)+`
endbfchar
`),x+=`endcmap
CMapName currentdict /CMap defineresource pop
end
end`};e.events.push(["putFont",function(o){(function(h){var l=h.font,f=h.out,v=h.newObject,x=h.putStream;if(l.metadata instanceof i.API.TTFFont&&l.encoding==="Identity-H"){for(var A=l.metadata.Unicode.widths,_=l.metadata.subset.encode(l.metadata.glyIdsUsed,1),p="",B=0;B<_.length;B++)p+=String.fromCharCode(_[B]);var F=v();x({data:p,addLength1:!0,objectId:F}),f("endobj");var q=v();x({data:a(l.metadata.toUnicode),addLength1:!0,objectId:q}),f("endobj");var S=v();f("<<"),f("/Type /FontDescriptor"),f("/FontName /"+yi(l.fontName)),f("/FontFile2 "+F+" 0 R"),f("/FontBBox "+i.API.PDFObject.convert(l.metadata.bbox)),f("/Flags "+l.metadata.flags),f("/StemV "+l.metadata.stemV),f("/ItalicAngle "+l.metadata.italicAngle),f("/Ascent "+l.metadata.ascender),f("/Descent "+l.metadata.decender),f("/CapHeight "+l.metadata.capHeight),f(">>"),f("endobj");var M=v();f("<<"),f("/Type /Font"),f("/BaseFont /"+yi(l.fontName)),f("/FontDescriptor "+S+" 0 R"),f("/W "+i.API.PDFObject.convert(A)),f("/CIDToGIDMap /Identity"),f("/DW 1000"),f("/Subtype /CIDFontType2"),f("/CIDSystemInfo"),f("<<"),f("/Supplement 0"),f("/Registry (Adobe)"),f("/Ordering ("+l.encoding+")"),f(">>"),f(">>"),f("endobj"),l.objectNumber=v(),f("<<"),f("/Type /Font"),f("/Subtype /Type0"),f("/ToUnicode "+q+" 0 R"),f("/BaseFont /"+yi(l.fontName)),f("/Encoding /"+l.encoding),f("/DescendantFonts ["+M+" 0 R]"),f(">>"),f("endobj"),l.isAlreadyPutted=!0}})(o)}]),e.events.push(["putFont",function(o){(function(h){var l=h.font,f=h.out,v=h.newObject,x=h.putStream;if(l.metadata instanceof i.API.TTFFont&&l.encoding==="WinAnsiEncoding"){for(var A=l.metadata.rawData,_="",p=0;p<A.length;p++)_+=String.fromCharCode(A[p]);var B=v();x({data:_,addLength1:!0,objectId:B}),f("endobj");var F=v();x({data:a(l.metadata.toUnicode),addLength1:!0,objectId:F}),f("endobj");var q=v();f("<<"),f("/Descent "+l.metadata.decender),f("/CapHeight "+l.metadata.capHeight),f("/StemV "+l.metadata.stemV),f("/Type /FontDescriptor"),f("/FontFile2 "+B+" 0 R"),f("/Flags 96"),f("/FontBBox "+i.API.PDFObject.convert(l.metadata.bbox)),f("/FontName /"+yi(l.fontName)),f("/ItalicAngle "+l.metadata.italicAngle),f("/Ascent "+l.metadata.ascender),f(">>"),f("endobj"),l.objectNumber=v();for(var S=0;S<l.metadata.hmtx.widths.length;S++)l.metadata.hmtx.widths[S]=parseInt(l.metadata.hmtx.widths[S]*(1e3/l.metadata.head.unitsPerEm));f("<</Subtype/TrueType/Type/Font/ToUnicode "+F+" 0 R/BaseFont/"+yi(l.fontName)+"/FontDescriptor "+q+" 0 R/Encoding/"+l.encoding+" /FirstChar 29 /LastChar 255 /Widths "+i.API.PDFObject.convert(l.metadata.hmtx.widths)+">>"),f("endobj"),l.isAlreadyPutted=!0}})(o)}]);var u=function(o){var h,l=o.text||"",f=o.x,v=o.y,x=o.options||{},A=o.mutex||{},_=A.pdfEscape,p=A.activeFontKey,B=A.fonts,F=p,q="",S=0,M="",Z=B[F].encoding;if(B[F].encoding!=="Identity-H")return{text:l,x:f,y:v,options:x,mutex:A};for(M=l,F=p,Array.isArray(l)&&(M=l[0]),S=0;S<M.length;S+=1)B[F].metadata.hasOwnProperty("cmap")&&(h=B[F].metadata.cmap.unicode.codeMap[M[S].charCodeAt(0)]),h||M[S].charCodeAt(0)<256&&B[F].metadata.hasOwnProperty("Unicode")?q+=M[S]:q+="";var st="";return parseInt(F.slice(1))<14||Z==="WinAnsiEncoding"?st=_(q,F).split("").map(function(dt){return dt.charCodeAt(0).toString(16)}).join(""):Z==="Identity-H"&&(st=r(q,B[F])),A.isHex=!0,{text:st,x:f,y:v,options:x,mutex:A}};e.events.push(["postProcessText",function(o){var h=o.text||"",l=[],f={text:h,x:o.x,y:o.y,options:o.options,mutex:o.mutex};if(Array.isArray(h)){var v=0;for(v=0;v<h.length;v+=1)Array.isArray(h[v])&&h[v].length===3?l.push([u(Object.assign({},f,{text:h[v][0]})).text,h[v][1],h[v][2]]):l.push(u(Object.assign({},f,{text:h[v]})).text);o.text=l}else o.text=u(Object.assign({},f,{text:h})).text}])}(Ut),function(i){var e=function(){return this.internal.vFS===void 0&&(this.internal.vFS={}),!0};i.existsFileInVFS=function(r){return e.call(this),this.internal.vFS[r]!==void 0},i.addFileToVFS=function(r,a){return e.call(this),this.internal.vFS[r]=a,this},i.getFileFromVFS=function(r){return e.call(this),this.internal.vFS[r]!==void 0?this.internal.vFS[r]:null}}(Ut.API),function(i){i.__bidiEngine__=i.prototype.__bidiEngine__=function(a){var u,o,h,l,f,v,x,A=e,_=[[0,3,0,1,0,0,0],[0,3,0,1,2,2,0],[0,3,0,17,2,0,1],[0,3,5,5,4,1,0],[0,3,21,21,4,0,1],[0,3,5,5,4,2,0]],p=[[2,0,1,1,0,1,0],[2,0,1,1,0,2,0],[2,0,2,1,3,2,0],[2,0,2,33,3,1,1]],B={L:0,R:1,EN:2,AN:3,N:4,B:5,S:6},F={0:0,5:1,6:2,7:3,32:4,251:5,254:6,255:7},q=["(",")","(","<",">","<","[","]","[","{","}","{","«","»","«","‹","›","‹","⁅","⁆","⁅","⁽","⁾","⁽","₍","₎","₍","≤","≥","≤","〈","〉","〈","﹙","﹚","﹙","﹛","﹜","﹛","﹝","﹞","﹝","﹤","﹥","﹤"],S=new RegExp(/^([1-4|9]|1[0-9]|2[0-9]|3[0168]|4[04589]|5[012]|7[78]|159|16[0-9]|17[0-2]|21[569]|22[03489]|250)$/),M=!1,Z=0;this.__bidiEngine__={};var st=function(k){var I=k.charCodeAt(),H=I>>8,R=F[H];return R!==void 0?A[256*R+(255&I)]:H===252||H===253?"AL":S.test(H)?"L":H===8?"R":"N"},dt=function(k){for(var I,H=0;H<k.length;H++){if((I=st(k.charAt(H)))==="L")return!1;if(I==="R")return!0}return!1},Nt=function(k,I,H,R){var ct,ot,mt,tt,pt=I[R];switch(pt){case"L":case"R":M=!1;break;case"N":case"AN":break;case"EN":M&&(pt="AN");break;case"AL":M=!0,pt="R";break;case"WS":pt="N";break;case"CS":R<1||R+1>=I.length||(ct=H[R-1])!=="EN"&&ct!=="AN"||(ot=I[R+1])!=="EN"&&ot!=="AN"?pt="N":M&&(ot="AN"),pt=ot===ct?ot:"N";break;case"ES":pt=(ct=R>0?H[R-1]:"B")==="EN"&&R+1<I.length&&I[R+1]==="EN"?"EN":"N";break;case"ET":if(R>0&&H[R-1]==="EN"){pt="EN";break}if(M){pt="N";break}for(mt=R+1,tt=I.length;mt<tt&&I[mt]==="ET";)mt++;pt=mt<tt&&I[mt]==="EN"?"EN":"N";break;case"NSM":if(h&&!l){for(tt=I.length,mt=R+1;mt<tt&&I[mt]==="NSM";)mt++;if(mt<tt){var ft=k[R],Et=ft>=1425&&ft<=2303||ft===64286;if(ct=I[mt],Et&&(ct==="R"||ct==="AL")){pt="R";break}}}pt=R<1||(ct=I[R-1])==="B"?"N":H[R-1];break;case"B":M=!1,u=!0,pt=Z;break;case"S":o=!0,pt="N";break;case"LRE":case"RLE":case"LRO":case"RLO":case"PDF":M=!1;break;case"BN":pt="N"}return pt},rt=function(k,I,H){var R=k.split("");return H&&G(R,H,{hiLevel:Z}),R.reverse(),I&&I.reverse(),R.join("")},G=function(k,I,H){var R,ct,ot,mt,tt,pt=-1,ft=k.length,Et=0,w=[],C=Z?p:_,E=[];for(M=!1,u=!1,o=!1,ct=0;ct<ft;ct++)E[ct]=st(k[ct]);for(ot=0;ot<ft;ot++){if(tt=Et,w[ot]=Nt(k,E,w,ot),R=240&(Et=C[tt][B[w[ot]]]),Et&=15,I[ot]=mt=C[Et][5],R>0)if(R===16){for(ct=pt;ct<ot;ct++)I[ct]=1;pt=-1}else pt=-1;if(C[Et][6])pt===-1&&(pt=ot);else if(pt>-1){for(ct=pt;ct<ot;ct++)I[ct]=mt;pt=-1}E[ot]==="B"&&(I[ot]=0),H.hiLevel|=mt}o&&function(W,J,$){for(var et=0;et<$;et++)if(W[et]==="S"){J[et]=Z;for(var Q=et-1;Q>=0&&W[Q]==="WS";Q--)J[Q]=Z}}(E,I,ft)},vt=function(k,I,H,R,ct){if(!(ct.hiLevel<k)){if(k===1&&Z===1&&!u)return I.reverse(),void(H&&H.reverse());for(var ot,mt,tt,pt,ft=I.length,Et=0;Et<ft;){if(R[Et]>=k){for(tt=Et+1;tt<ft&&R[tt]>=k;)tt++;for(pt=Et,mt=tt-1;pt<mt;pt++,mt--)ot=I[pt],I[pt]=I[mt],I[mt]=ot,H&&(ot=H[pt],H[pt]=H[mt],H[mt]=ot);Et=tt}Et++}}},bt=function(k,I,H){var R=k.split(""),ct={hiLevel:Z};return H||(H=[]),G(R,H,ct),function(ot,mt,tt){if(tt.hiLevel!==0&&x)for(var pt,ft=0;ft<ot.length;ft++)mt[ft]===1&&(pt=q.indexOf(ot[ft]))>=0&&(ot[ft]=q[pt+1])}(R,H,ct),vt(2,R,I,H,ct),vt(1,R,I,H,ct),R.join("")};return this.__bidiEngine__.doBidiReorder=function(k,I,H){if(function(ct,ot){if(ot)for(var mt=0;mt<ct.length;mt++)ot[mt]=mt;l===void 0&&(l=dt(ct)),v===void 0&&(v=dt(ct))}(k,I),h||!f||v)if(h&&f&&l^v)Z=l?1:0,k=rt(k,I,H);else if(!h&&f&&v)Z=l?1:0,k=bt(k,I,H),k=rt(k,I);else if(!h||l||f||v){if(h&&!f&&l^v)k=rt(k,I),l?(Z=0,k=bt(k,I,H)):(Z=1,k=bt(k,I,H),k=rt(k,I));else if(h&&l&&!f&&v)Z=1,k=bt(k,I,H),k=rt(k,I);else if(!h&&!f&&l^v){var R=x;l?(Z=1,k=bt(k,I,H),Z=0,x=!1,k=bt(k,I,H),x=R):(Z=0,k=bt(k,I,H),k=rt(k,I),Z=1,x=!1,k=bt(k,I,H),x=R,k=rt(k,I))}}else Z=0,k=bt(k,I,H);else Z=l?1:0,k=bt(k,I,H);return k},this.__bidiEngine__.setOptions=function(k){k&&(h=k.isInputVisual,f=k.isOutputVisual,l=k.isInputRtl,v=k.isOutputRtl,x=k.isSymmetricSwapping)},this.__bidiEngine__.setOptions(a),this.__bidiEngine__};var e=["BN","BN","BN","BN","BN","BN","BN","BN","BN","S","B","S","WS","B","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","B","B","B","S","WS","N","N","ET","ET","ET","N","N","N","N","N","ES","CS","ES","CS","CS","EN","EN","EN","EN","EN","EN","EN","EN","EN","EN","CS","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","N","BN","BN","BN","BN","BN","BN","B","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","CS","N","ET","ET","ET","ET","N","N","N","N","L","N","N","BN","N","N","ET","ET","EN","EN","N","L","N","N","N","EN","L","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","L","L","L","L","L","L","L","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","L","N","N","N","N","N","ET","N","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","R","NSM","R","NSM","NSM","R","NSM","NSM","R","NSM","N","N","N","N","N","N","N","N","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","N","N","N","N","N","R","R","R","R","R","N","N","N","N","N","N","N","N","N","N","N","AN","AN","AN","AN","AN","AN","N","N","AL","ET","ET","AL","CS","AL","N","N","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","AL","AL","N","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","AN","AN","AN","AN","AN","AN","AN","AN","AN","AN","ET","AN","AN","AL","AL","AL","NSM","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","AN","N","NSM","NSM","NSM","NSM","NSM","NSM","AL","AL","NSM","NSM","N","NSM","NSM","NSM","NSM","AL","AL","EN","EN","EN","EN","EN","EN","EN","EN","EN","EN","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","N","AL","AL","NSM","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","N","N","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","AL","N","N","N","N","N","N","N","N","N","N","N","N","N","N","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","R","R","N","N","N","N","R","N","N","N","N","N","WS","WS","WS","WS","WS","WS","WS","WS","WS","WS","WS","BN","BN","BN","L","R","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","WS","B","LRE","RLE","PDF","LRO","RLO","CS","ET","ET","ET","ET","ET","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","CS","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","WS","BN","BN","BN","BN","BN","N","LRI","RLI","FSI","PDI","BN","BN","BN","BN","BN","BN","EN","L","N","N","EN","EN","EN","EN","EN","EN","ES","ES","N","N","N","L","EN","EN","EN","EN","EN","EN","EN","EN","EN","EN","ES","ES","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","L","L","L","L","L","L","L","N","N","N","N","N","N","N","N","N","N","N","N","L","L","L","L","L","N","N","N","N","N","R","NSM","R","R","R","R","R","R","R","R","R","R","ES","R","R","R","R","R","R","R","R","R","R","R","R","R","N","R","R","R","R","R","N","R","N","R","R","N","R","R","N","R","R","R","R","R","R","R","R","R","R","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","CS","N","CS","N","N","CS","N","N","N","N","N","N","N","N","N","ET","N","N","ES","ES","N","N","N","N","N","ET","ET","N","N","N","N","N","AL","AL","AL","AL","AL","N","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","N","N","BN","N","N","N","ET","ET","ET","N","N","N","N","N","ES","CS","ES","CS","CS","EN","EN","EN","EN","EN","EN","EN","EN","EN","EN","CS","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","N","N","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","L","L","L","L","L","L","N","N","L","L","L","L","L","L","N","N","L","L","L","L","L","L","N","N","L","L","L","N","N","N","ET","ET","N","N","N","ET","ET","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N"],r=new i.__bidiEngine__({isInputVisual:!0});i.API.events.push(["postProcessText",function(a){var u=a.text,o=(a.x,a.y,a.options||{}),h=(a.mutex,o.lang,[]);if(o.isInputVisual=typeof o.isInputVisual!="boolean"||o.isInputVisual,r.setOptions(o),Object.prototype.toString.call(u)==="[object Array]"){var l=0;for(h=[],l=0;l<u.length;l+=1)Object.prototype.toString.call(u[l])==="[object Array]"?h.push([r.doBidiReorder(u[l][0]),u[l][1],u[l][2]]):h.push([r.doBidiReorder(u[l])]);a.text=h}else a.text=r.doBidiReorder(u);r.setOptions({isInputVisual:!0})}])}(Ut),Ut.API.TTFFont=function(){function i(e){var r;if(this.rawData=e,r=this.contents=new Ar(e),this.contents.pos=4,r.readString(4)==="ttcf")throw new Error("TTCF not supported.");r.pos=0,this.parse(),this.subset=new Iu(this),this.registerTTF()}return i.open=function(e){return new i(e)},i.prototype.parse=function(){return this.directory=new gu(this.contents),this.head=new vu(this),this.name=new Nu(this),this.cmap=new cc(this),this.toUnicode={},this.hhea=new bu(this),this.maxp=new Au(this),this.hmtx=new xu(this),this.post=new wu(this),this.os2=new yu(this),this.loca=new ku(this),this.glyf=new Su(this),this.ascender=this.os2.exists&&this.os2.ascender||this.hhea.ascender,this.decender=this.os2.exists&&this.os2.decender||this.hhea.decender,this.lineGap=this.os2.exists&&this.os2.lineGap||this.hhea.lineGap,this.bbox=[this.head.xMin,this.head.yMin,this.head.xMax,this.head.yMax]},i.prototype.registerTTF=function(){var e,r,a,u,o;if(this.scaleFactor=1e3/this.head.unitsPerEm,this.bbox=(function(){var h,l,f,v;for(v=[],h=0,l=(f=this.bbox).length;h<l;h++)e=f[h],v.push(Math.round(e*this.scaleFactor));return v}).call(this),this.stemV=0,this.post.exists?(a=255&(u=this.post.italic_angle),32768&(r=u>>16)&&(r=-(1+(65535^r))),this.italicAngle=+(r+"."+a)):this.italicAngle=0,this.ascender=Math.round(this.ascender*this.scaleFactor),this.decender=Math.round(this.decender*this.scaleFactor),this.lineGap=Math.round(this.lineGap*this.scaleFactor),this.capHeight=this.os2.exists&&this.os2.capHeight||this.ascender,this.xHeight=this.os2.exists&&this.os2.xHeight||0,this.familyClass=(this.os2.exists&&this.os2.familyClass||0)>>8,this.isSerif=(o=this.familyClass)===1||o===2||o===3||o===4||o===5||o===7,this.isScript=this.familyClass===10,this.flags=0,this.post.isFixedPitch&&(this.flags|=1),this.isSerif&&(this.flags|=2),this.isScript&&(this.flags|=8),this.italicAngle!==0&&(this.flags|=64),this.flags|=32,!this.cmap.unicode)throw new Error("No unicode cmap for font")},i.prototype.characterToGlyph=function(e){var r;return((r=this.cmap.unicode)!=null?r.codeMap[e]:void 0)||0},i.prototype.widthOfGlyph=function(e){var r;return r=1e3/this.head.unitsPerEm,this.hmtx.forGlyph(e).advance*r},i.prototype.widthOfString=function(e,r,a){var u,o,h,l;for(h=0,o=0,l=(e=""+e).length;0<=l?o<l:o>l;o=0<=l?++o:--o)u=e.charCodeAt(o),h+=this.widthOfGlyph(this.characterToGlyph(u))+a*(1e3/r)||0;return h*(r/1e3)},i.prototype.lineHeight=function(e,r){var a;return r==null&&(r=!1),a=r?this.lineGap:0,(this.ascender+a-this.decender)/1e3*e},i}();var En,Ar=function(){function i(e){this.data=e??[],this.pos=0,this.length=this.data.length}return i.prototype.readByte=function(){return this.data[this.pos++]},i.prototype.writeByte=function(e){return this.data[this.pos++]=e},i.prototype.readUInt32=function(){return 16777216*this.readByte()+(this.readByte()<<16)+(this.readByte()<<8)+this.readByte()},i.prototype.writeUInt32=function(e){return this.writeByte(e>>>24&255),this.writeByte(e>>16&255),this.writeByte(e>>8&255),this.writeByte(255&e)},i.prototype.readInt32=function(){var e;return(e=this.readUInt32())>=2147483648?e-4294967296:e},i.prototype.writeInt32=function(e){return e<0&&(e+=4294967296),this.writeUInt32(e)},i.prototype.readUInt16=function(){return this.readByte()<<8|this.readByte()},i.prototype.writeUInt16=function(e){return this.writeByte(e>>8&255),this.writeByte(255&e)},i.prototype.readInt16=function(){var e;return(e=this.readUInt16())>=32768?e-65536:e},i.prototype.writeInt16=function(e){return e<0&&(e+=65536),this.writeUInt16(e)},i.prototype.readString=function(e){var r,a;for(a=[],r=0;0<=e?r<e:r>e;r=0<=e?++r:--r)a[r]=String.fromCharCode(this.readByte());return a.join("")},i.prototype.writeString=function(e){var r,a,u;for(u=[],r=0,a=e.length;0<=a?r<a:r>a;r=0<=a?++r:--r)u.push(this.writeByte(e.charCodeAt(r)));return u},i.prototype.readShort=function(){return this.readInt16()},i.prototype.writeShort=function(e){return this.writeInt16(e)},i.prototype.readLongLong=function(){var e,r,a,u,o,h,l,f;return e=this.readByte(),r=this.readByte(),a=this.readByte(),u=this.readByte(),o=this.readByte(),h=this.readByte(),l=this.readByte(),f=this.readByte(),128&e?-1*(72057594037927940*(255^e)+281474976710656*(255^r)+1099511627776*(255^a)+4294967296*(255^u)+16777216*(255^o)+65536*(255^h)+256*(255^l)+(255^f)+1):72057594037927940*e+281474976710656*r+1099511627776*a+4294967296*u+16777216*o+65536*h+256*l+f},i.prototype.writeLongLong=function(e){var r,a;return r=Math.floor(e/4294967296),a=**********&e,this.writeByte(r>>24&255),this.writeByte(r>>16&255),this.writeByte(r>>8&255),this.writeByte(255&r),this.writeByte(a>>24&255),this.writeByte(a>>16&255),this.writeByte(a>>8&255),this.writeByte(255&a)},i.prototype.readInt=function(){return this.readInt32()},i.prototype.writeInt=function(e){return this.writeInt32(e)},i.prototype.read=function(e){var r,a;for(r=[],a=0;0<=e?a<e:a>e;a=0<=e?++a:--a)r.push(this.readByte());return r},i.prototype.write=function(e){var r,a,u,o;for(o=[],a=0,u=e.length;a<u;a++)r=e[a],o.push(this.writeByte(r));return o},i}(),gu=function(){var i;function e(r){var a,u,o;for(this.scalarType=r.readInt(),this.tableCount=r.readShort(),this.searchRange=r.readShort(),this.entrySelector=r.readShort(),this.rangeShift=r.readShort(),this.tables={},u=0,o=this.tableCount;0<=o?u<o:u>o;u=0<=o?++u:--u)a={tag:r.readString(4),checksum:r.readInt(),offset:r.readInt(),length:r.readInt()},this.tables[a.tag]=a}return e.prototype.encode=function(r){var a,u,o,h,l,f,v,x,A,_,p,B,F;for(F in p=Object.keys(r).length,f=Math.log(2),A=16*Math.floor(Math.log(p)/f),h=Math.floor(A/f),x=16*p-A,(u=new Ar).writeInt(this.scalarType),u.writeShort(p),u.writeShort(A),u.writeShort(h),u.writeShort(x),o=16*p,v=u.pos+o,l=null,B=[],r)for(_=r[F],u.writeString(F),u.writeInt(i(_)),u.writeInt(v),u.writeInt(_.length),B=B.concat(_),F==="head"&&(l=v),v+=_.length;v%4;)B.push(0),v++;return u.write(B),a=2981146554-i(u.data),u.pos=l+8,u.writeUInt32(a),u.data},i=function(r){var a,u,o,h;for(r=uc.call(r);r.length%4;)r.push(0);for(o=new Ar(r),u=0,a=0,h=r.length;a<h;a=a+=4)u+=o.readUInt32();return **********&u},e}(),mu={}.hasOwnProperty,Xn=function(i,e){for(var r in e)mu.call(e,r)&&(i[r]=e[r]);function a(){this.constructor=i}return a.prototype=e.prototype,i.prototype=new a,i.__super__=e.prototype,i};En=function(){function i(e){var r;this.file=e,r=this.file.directory.tables[this.tag],this.exists=!!r,r&&(this.offset=r.offset,this.length=r.length,this.parse(this.file.contents))}return i.prototype.parse=function(){},i.prototype.encode=function(){},i.prototype.raw=function(){return this.exists?(this.file.contents.pos=this.offset,this.file.contents.read(this.length)):null},i}();var vu=function(i){function e(){return e.__super__.constructor.apply(this,arguments)}return Xn(e,En),e.prototype.tag="head",e.prototype.parse=function(r){return r.pos=this.offset,this.version=r.readInt(),this.revision=r.readInt(),this.checkSumAdjustment=r.readInt(),this.magicNumber=r.readInt(),this.flags=r.readShort(),this.unitsPerEm=r.readShort(),this.created=r.readLongLong(),this.modified=r.readLongLong(),this.xMin=r.readShort(),this.yMin=r.readShort(),this.xMax=r.readShort(),this.yMax=r.readShort(),this.macStyle=r.readShort(),this.lowestRecPPEM=r.readShort(),this.fontDirectionHint=r.readShort(),this.indexToLocFormat=r.readShort(),this.glyphDataFormat=r.readShort()},e.prototype.encode=function(r){var a;return(a=new Ar).writeInt(this.version),a.writeInt(this.revision),a.writeInt(this.checkSumAdjustment),a.writeInt(this.magicNumber),a.writeShort(this.flags),a.writeShort(this.unitsPerEm),a.writeLongLong(this.created),a.writeLongLong(this.modified),a.writeShort(this.xMin),a.writeShort(this.yMin),a.writeShort(this.xMax),a.writeShort(this.yMax),a.writeShort(this.macStyle),a.writeShort(this.lowestRecPPEM),a.writeShort(this.fontDirectionHint),a.writeShort(r),a.writeShort(this.glyphDataFormat),a.data},e}(),Zs=function(){function i(e,r){var a,u,o,h,l,f,v,x,A,_,p,B,F,q,S,M,Z;switch(this.platformID=e.readUInt16(),this.encodingID=e.readShort(),this.offset=r+e.readInt(),A=e.pos,e.pos=this.offset,this.format=e.readUInt16(),this.length=e.readUInt16(),this.language=e.readUInt16(),this.isUnicode=this.platformID===3&&this.encodingID===1&&this.format===4||this.platformID===0&&this.format===4,this.codeMap={},this.format){case 0:for(f=0;f<256;++f)this.codeMap[f]=e.readByte();break;case 4:for(p=e.readUInt16(),_=p/2,e.pos+=6,o=function(){var st,dt;for(dt=[],f=st=0;0<=_?st<_:st>_;f=0<=_?++st:--st)dt.push(e.readUInt16());return dt}(),e.pos+=2,F=function(){var st,dt;for(dt=[],f=st=0;0<=_?st<_:st>_;f=0<=_?++st:--st)dt.push(e.readUInt16());return dt}(),v=function(){var st,dt;for(dt=[],f=st=0;0<=_?st<_:st>_;f=0<=_?++st:--st)dt.push(e.readUInt16());return dt}(),x=function(){var st,dt;for(dt=[],f=st=0;0<=_?st<_:st>_;f=0<=_?++st:--st)dt.push(e.readUInt16());return dt}(),u=(this.length-e.pos+this.offset)/2,l=function(){var st,dt;for(dt=[],f=st=0;0<=u?st<u:st>u;f=0<=u?++st:--st)dt.push(e.readUInt16());return dt}(),f=S=0,Z=o.length;S<Z;f=++S)for(q=o[f],a=M=B=F[f];B<=q?M<=q:M>=q;a=B<=q?++M:--M)x[f]===0?h=a+v[f]:(h=l[x[f]/2+(a-B)-(_-f)]||0)!==0&&(h+=v[f]),this.codeMap[a]=65535&h}e.pos=A}return i.encode=function(e,r){var a,u,o,h,l,f,v,x,A,_,p,B,F,q,S,M,Z,st,dt,Nt,rt,G,vt,bt,k,I,H,R,ct,ot,mt,tt,pt,ft,Et,w,C,E,W,J,$,et,Q,At,Lt,Ot;switch(R=new Ar,h=Object.keys(e).sort(function(Ct,Wt){return Ct-Wt}),r){case"macroman":for(F=0,q=function(){var Ct=[];for(B=0;B<256;++B)Ct.push(0);return Ct}(),M={0:0},o={},ct=0,pt=h.length;ct<pt;ct++)M[Q=e[u=h[ct]]]==null&&(M[Q]=++F),o[u]={old:e[u],new:M[e[u]]},q[u]=M[e[u]];return R.writeUInt16(1),R.writeUInt16(0),R.writeUInt32(12),R.writeUInt16(0),R.writeUInt16(262),R.writeUInt16(0),R.write(q),{charMap:o,subtable:R.data,maxGlyphID:F+1};case"unicode":for(I=[],A=[],Z=0,M={},a={},S=v=null,ot=0,ft=h.length;ot<ft;ot++)M[dt=e[u=h[ot]]]==null&&(M[dt]=++Z),a[u]={old:dt,new:M[dt]},l=M[dt]-u,S!=null&&l===v||(S&&A.push(S),I.push(u),v=l),S=u;for(S&&A.push(S),A.push(65535),I.push(65535),bt=2*(vt=I.length),G=2*Math.pow(Math.log(vt)/Math.LN2,2),_=Math.log(G/2)/Math.LN2,rt=2*vt-G,f=[],Nt=[],p=[],B=mt=0,Et=I.length;mt<Et;B=++mt){if(k=I[B],x=A[B],k===65535){f.push(0),Nt.push(0);break}if(k-(H=a[k].new)>=32768)for(f.push(0),Nt.push(2*(p.length+vt-B)),u=tt=k;k<=x?tt<=x:tt>=x;u=k<=x?++tt:--tt)p.push(a[u].new);else f.push(H-k),Nt.push(0)}for(R.writeUInt16(3),R.writeUInt16(1),R.writeUInt32(12),R.writeUInt16(4),R.writeUInt16(16+8*vt+2*p.length),R.writeUInt16(0),R.writeUInt16(bt),R.writeUInt16(G),R.writeUInt16(_),R.writeUInt16(rt),$=0,w=A.length;$<w;$++)u=A[$],R.writeUInt16(u);for(R.writeUInt16(0),et=0,C=I.length;et<C;et++)u=I[et],R.writeUInt16(u);for(At=0,E=f.length;At<E;At++)l=f[At],R.writeUInt16(l);for(Lt=0,W=Nt.length;Lt<W;Lt++)st=Nt[Lt],R.writeUInt16(st);for(Ot=0,J=p.length;Ot<J;Ot++)F=p[Ot],R.writeUInt16(F);return{charMap:a,subtable:R.data,maxGlyphID:Z+1}}},i}(),cc=function(i){function e(){return e.__super__.constructor.apply(this,arguments)}return Xn(e,En),e.prototype.tag="cmap",e.prototype.parse=function(r){var a,u,o;for(r.pos=this.offset,this.version=r.readUInt16(),o=r.readUInt16(),this.tables=[],this.unicode=null,u=0;0<=o?u<o:u>o;u=0<=o?++u:--u)a=new Zs(r,this.offset),this.tables.push(a),a.isUnicode&&this.unicode==null&&(this.unicode=a);return!0},e.encode=function(r,a){var u,o;return a==null&&(a="macroman"),u=Zs.encode(r,a),(o=new Ar).writeUInt16(0),o.writeUInt16(1),u.table=o.data.concat(u.subtable),u},e}(),bu=function(i){function e(){return e.__super__.constructor.apply(this,arguments)}return Xn(e,En),e.prototype.tag="hhea",e.prototype.parse=function(r){return r.pos=this.offset,this.version=r.readInt(),this.ascender=r.readShort(),this.decender=r.readShort(),this.lineGap=r.readShort(),this.advanceWidthMax=r.readShort(),this.minLeftSideBearing=r.readShort(),this.minRightSideBearing=r.readShort(),this.xMaxExtent=r.readShort(),this.caretSlopeRise=r.readShort(),this.caretSlopeRun=r.readShort(),this.caretOffset=r.readShort(),r.pos+=8,this.metricDataFormat=r.readShort(),this.numberOfMetrics=r.readUInt16()},e}(),yu=function(i){function e(){return e.__super__.constructor.apply(this,arguments)}return Xn(e,En),e.prototype.tag="OS/2",e.prototype.parse=function(r){if(r.pos=this.offset,this.version=r.readUInt16(),this.averageCharWidth=r.readShort(),this.weightClass=r.readUInt16(),this.widthClass=r.readUInt16(),this.type=r.readShort(),this.ySubscriptXSize=r.readShort(),this.ySubscriptYSize=r.readShort(),this.ySubscriptXOffset=r.readShort(),this.ySubscriptYOffset=r.readShort(),this.ySuperscriptXSize=r.readShort(),this.ySuperscriptYSize=r.readShort(),this.ySuperscriptXOffset=r.readShort(),this.ySuperscriptYOffset=r.readShort(),this.yStrikeoutSize=r.readShort(),this.yStrikeoutPosition=r.readShort(),this.familyClass=r.readShort(),this.panose=function(){var a,u;for(u=[],a=0;a<10;++a)u.push(r.readByte());return u}(),this.charRange=function(){var a,u;for(u=[],a=0;a<4;++a)u.push(r.readInt());return u}(),this.vendorID=r.readString(4),this.selection=r.readShort(),this.firstCharIndex=r.readShort(),this.lastCharIndex=r.readShort(),this.version>0&&(this.ascent=r.readShort(),this.descent=r.readShort(),this.lineGap=r.readShort(),this.winAscent=r.readShort(),this.winDescent=r.readShort(),this.codePageRange=function(){var a,u;for(u=[],a=0;a<2;a=++a)u.push(r.readInt());return u}(),this.version>1))return this.xHeight=r.readShort(),this.capHeight=r.readShort(),this.defaultChar=r.readShort(),this.breakChar=r.readShort(),this.maxContext=r.readShort()},e}(),wu=function(i){function e(){return e.__super__.constructor.apply(this,arguments)}return Xn(e,En),e.prototype.tag="post",e.prototype.parse=function(r){var a,u,o;switch(r.pos=this.offset,this.format=r.readInt(),this.italicAngle=r.readInt(),this.underlinePosition=r.readShort(),this.underlineThickness=r.readShort(),this.isFixedPitch=r.readInt(),this.minMemType42=r.readInt(),this.maxMemType42=r.readInt(),this.minMemType1=r.readInt(),this.maxMemType1=r.readInt(),this.format){case 65536:break;case 131072:var h;for(u=r.readUInt16(),this.glyphNameIndex=[],h=0;0<=u?h<u:h>u;h=0<=u?++h:--h)this.glyphNameIndex.push(r.readUInt16());for(this.names=[],o=[];r.pos<this.offset+this.length;)a=r.readByte(),o.push(this.names.push(r.readString(a)));return o;case 151552:return u=r.readUInt16(),this.offsets=r.read(u);case 196608:break;case 262144:return this.map=(function(){var l,f,v;for(v=[],h=l=0,f=this.file.maxp.numGlyphs;0<=f?l<f:l>f;h=0<=f?++l:--l)v.push(r.readUInt32());return v}).call(this)}},e}(),Lu=function(i,e){this.raw=i,this.length=i.length,this.platformID=e.platformID,this.encodingID=e.encodingID,this.languageID=e.languageID},Nu=function(i){function e(){return e.__super__.constructor.apply(this,arguments)}return Xn(e,En),e.prototype.tag="name",e.prototype.parse=function(r){var a,u,o,h,l,f,v,x,A,_,p;for(r.pos=this.offset,r.readShort(),a=r.readShort(),f=r.readShort(),u=[],h=0;0<=a?h<a:h>a;h=0<=a?++h:--h)u.push({platformID:r.readShort(),encodingID:r.readShort(),languageID:r.readShort(),nameID:r.readShort(),length:r.readShort(),offset:this.offset+f+r.readShort()});for(v={},h=A=0,_=u.length;A<_;h=++A)o=u[h],r.pos=o.offset,x=r.readString(o.length),l=new Lu(x,o),v[p=o.nameID]==null&&(v[p]=[]),v[o.nameID].push(l);this.strings=v,this.copyright=v[0],this.fontFamily=v[1],this.fontSubfamily=v[2],this.uniqueSubfamily=v[3],this.fontName=v[4],this.version=v[5];try{this.postscriptName=v[6][0].raw.replace(/[\x00-\x19\x80-\xff]/g,"")}catch{this.postscriptName=v[4][0].raw.replace(/[\x00-\x19\x80-\xff]/g,"")}return this.trademark=v[7],this.manufacturer=v[8],this.designer=v[9],this.description=v[10],this.vendorUrl=v[11],this.designerUrl=v[12],this.license=v[13],this.licenseUrl=v[14],this.preferredFamily=v[15],this.preferredSubfamily=v[17],this.compatibleFull=v[18],this.sampleText=v[19]},e}(),Au=function(i){function e(){return e.__super__.constructor.apply(this,arguments)}return Xn(e,En),e.prototype.tag="maxp",e.prototype.parse=function(r){return r.pos=this.offset,this.version=r.readInt(),this.numGlyphs=r.readUInt16(),this.maxPoints=r.readUInt16(),this.maxContours=r.readUInt16(),this.maxCompositePoints=r.readUInt16(),this.maxComponentContours=r.readUInt16(),this.maxZones=r.readUInt16(),this.maxTwilightPoints=r.readUInt16(),this.maxStorage=r.readUInt16(),this.maxFunctionDefs=r.readUInt16(),this.maxInstructionDefs=r.readUInt16(),this.maxStackElements=r.readUInt16(),this.maxSizeOfInstructions=r.readUInt16(),this.maxComponentElements=r.readUInt16(),this.maxComponentDepth=r.readUInt16()},e}(),xu=function(i){function e(){return e.__super__.constructor.apply(this,arguments)}return Xn(e,En),e.prototype.tag="hmtx",e.prototype.parse=function(r){var a,u,o,h,l,f,v;for(r.pos=this.offset,this.metrics=[],a=0,f=this.file.hhea.numberOfMetrics;0<=f?a<f:a>f;a=0<=f?++a:--a)this.metrics.push({advance:r.readUInt16(),lsb:r.readInt16()});for(o=this.file.maxp.numGlyphs-this.file.hhea.numberOfMetrics,this.leftSideBearings=function(){var x,A;for(A=[],a=x=0;0<=o?x<o:x>o;a=0<=o?++x:--x)A.push(r.readInt16());return A}(),this.widths=(function(){var x,A,_,p;for(p=[],x=0,A=(_=this.metrics).length;x<A;x++)h=_[x],p.push(h.advance);return p}).call(this),u=this.widths[this.widths.length-1],v=[],a=l=0;0<=o?l<o:l>o;a=0<=o?++l:--l)v.push(this.widths.push(u));return v},e.prototype.forGlyph=function(r){return r in this.metrics?this.metrics[r]:{advance:this.metrics[this.metrics.length-1].advance,lsb:this.leftSideBearings[r-this.metrics.length]}},e}(),uc=[].slice,Su=function(i){function e(){return e.__super__.constructor.apply(this,arguments)}return Xn(e,En),e.prototype.tag="glyf",e.prototype.parse=function(){return this.cache={}},e.prototype.glyphFor=function(r){var a,u,o,h,l,f,v,x,A,_;return r in this.cache?this.cache[r]:(h=this.file.loca,a=this.file.contents,u=h.indexOf(r),(o=h.lengthOf(r))===0?this.cache[r]=null:(a.pos=this.offset+u,l=(f=new Ar(a.read(o))).readShort(),x=f.readShort(),_=f.readShort(),v=f.readShort(),A=f.readShort(),this.cache[r]=l===-1?new Pu(f,x,_,v,A):new _u(f,l,x,_,v,A),this.cache[r]))},e.prototype.encode=function(r,a,u){var o,h,l,f,v;for(l=[],h=[],f=0,v=a.length;f<v;f++)o=r[a[f]],h.push(l.length),o&&(l=l.concat(o.encode(u)));return h.push(l.length),{table:l,offsets:h}},e}(),_u=function(){function i(e,r,a,u,o,h){this.raw=e,this.numberOfContours=r,this.xMin=a,this.yMin=u,this.xMax=o,this.yMax=h,this.compound=!1}return i.prototype.encode=function(){return this.raw.data},i}(),Pu=function(){function i(e,r,a,u,o){var h,l;for(this.raw=e,this.xMin=r,this.yMin=a,this.xMax=u,this.yMax=o,this.compound=!0,this.glyphIDs=[],this.glyphOffsets=[],h=this.raw;l=h.readShort(),this.glyphOffsets.push(h.pos),this.glyphIDs.push(h.readUInt16()),32&l;)h.pos+=1&l?4:2,128&l?h.pos+=8:64&l?h.pos+=4:8&l&&(h.pos+=2)}return i.prototype.encode=function(){var e,r,a;for(r=new Ar(uc.call(this.raw.data)),e=0,a=this.glyphIDs.length;e<a;++e)r.pos=this.glyphOffsets[e];return r.data},i}(),ku=function(i){function e(){return e.__super__.constructor.apply(this,arguments)}return Xn(e,En),e.prototype.tag="loca",e.prototype.parse=function(r){var a,u;return r.pos=this.offset,a=this.file.head.indexToLocFormat,this.offsets=a===0?(function(){var o,h;for(h=[],u=0,o=this.length;u<o;u+=2)h.push(2*r.readUInt16());return h}).call(this):(function(){var o,h;for(h=[],u=0,o=this.length;u<o;u+=4)h.push(r.readUInt32());return h}).call(this)},e.prototype.indexOf=function(r){return this.offsets[r]},e.prototype.lengthOf=function(r){return this.offsets[r+1]-this.offsets[r]},e.prototype.encode=function(r,a){for(var u=new Uint32Array(this.offsets.length),o=0,h=0,l=0;l<u.length;++l)if(u[l]=o,h<a.length&&a[h]==l){++h,u[l]=o;var f=this.offsets[l],v=this.offsets[l+1]-f;v>0&&(o+=v)}for(var x=new Array(4*u.length),A=0;A<u.length;++A)x[4*A+3]=255&u[A],x[4*A+2]=(65280&u[A])>>8,x[4*A+1]=(16711680&u[A])>>16,x[4*A]=(**********&u[A])>>24;return x},e}(),Iu=function(){function i(e){this.font=e,this.subset={},this.unicodes={},this.next=33}return i.prototype.generateCmap=function(){var e,r,a,u,o;for(r in u=this.font.cmap.tables[0].codeMap,e={},o=this.subset)a=o[r],e[r]=u[a];return e},i.prototype.glyphsFor=function(e){var r,a,u,o,h,l,f;for(u={},h=0,l=e.length;h<l;h++)u[o=e[h]]=this.font.glyf.glyphFor(o);for(o in r=[],u)(a=u[o])!=null&&a.compound&&r.push.apply(r,a.glyphIDs);if(r.length>0)for(o in f=this.glyphsFor(r))a=f[o],u[o]=a;return u},i.prototype.encode=function(e,r){var a,u,o,h,l,f,v,x,A,_,p,B,F,q,S;for(u in a=cc.encode(this.generateCmap(),"unicode"),h=this.glyphsFor(e),p={0:0},S=a.charMap)p[(f=S[u]).old]=f.new;for(B in _=a.maxGlyphID,h)B in p||(p[B]=_++);return x=function(M){var Z,st;for(Z in st={},M)st[M[Z]]=Z;return st}(p),A=Object.keys(x).sort(function(M,Z){return M-Z}),F=function(){var M,Z,st;for(st=[],M=0,Z=A.length;M<Z;M++)l=A[M],st.push(x[l]);return st}(),o=this.font.glyf.encode(h,F,p),v=this.font.loca.encode(o.offsets,F),q={cmap:this.font.cmap.raw(),glyf:o.table,loca:v,hmtx:this.font.hmtx.raw(),hhea:this.font.hhea.raw(),maxp:this.font.maxp.raw(),post:this.font.post.raw(),name:this.font.name.raw(),head:this.font.head.encode(r)},this.font.os2.exists&&(q["OS/2"]=this.font.os2.raw()),this.font.directory.encode(q)},i}();Ut.API.PDFObject=function(){var i;function e(){}return i=function(r,a){return(Array(a+1).join("0")+r).slice(-a)},e.convert=function(r){var a,u,o,h;if(Array.isArray(r))return"["+function(){var l,f,v;for(v=[],l=0,f=r.length;l<f;l++)a=r[l],v.push(e.convert(a));return v}().join(" ")+"]";if(typeof r=="string")return"/"+r;if(r!=null&&r.isString)return"("+r+")";if(r instanceof Date)return"(D:"+i(r.getUTCFullYear(),4)+i(r.getUTCMonth(),2)+i(r.getUTCDate(),2)+i(r.getUTCHours(),2)+i(r.getUTCMinutes(),2)+i(r.getUTCSeconds(),2)+"Z)";if({}.toString.call(r)==="[object Object]"){for(u in o=["<<"],r)h=r[u],o.push("/"+u+" "+e.convert(h));return o.push(">>"),o.join(`
`)}return""+r},e}();

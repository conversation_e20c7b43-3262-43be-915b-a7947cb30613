import{j as e}from"./@react-google-maps/api-4794cf1a.js";import{R as g,b as ce,u as me,r as i}from"./vendor-dd4ba10b.js";import{P as G}from"./index.esm-c8f76a7c.js";import{M as u,G as X,A as Z,f as xe,e as ue,h as pe,s as c,t as P}from"./index-3efdd896.js";import{E as K}from"./@stripe/react-stripe-js-1762f471.js";import{l as he}from"./@stripe/stripe-js-6b714a86.js";import fe from"./CardDetails-d4a40607.js";import"./react-icons-0b96c072.js";import"./react-confirm-alert-5d5c0db6.js";import"./html2pdf.js-19c9759c.js";import"./@headlessui/react-7b0d4887.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";import"./@fortawesome/react-fontawesome-0b111e8e.js";import"./logo-a4db6296.js";let M=new u;const be=({onClose:p,planId:d,price:C,trial:b,onSuccess:E})=>{const{dispatch:A}=g.useContext(X),{dispatch:k}=g.useContext(Z),[B,w]=g.useState(!1),[T,h]=g.useState({number:"",name:"",expiry:"",cvc:"",issuer:"",focused:"",formData:null});g.useRef(null);const F=ce(),j=({target:s})=>{h({focused:s.name})},f=({target:s})=>{s.name==="number"?s.value=xe(s.value):s.name==="expiry"?s.value=ue(s.value):s.name==="cvc"&&(s.value=pe(s.value)),h({[s.name]:s.value})},D=async s=>{s.preventDefault();const y=[...s.target.elements].filter(l=>l.name).reduce((l,n)=>(l[n.name]=n.value,l),{});h({formData:y});const L=b?`After trial, you will be billed $${C} monthly.`:`Your $${C} payment was successful.`;w(!0);try{const l=await M.addStripeCard(y);if(l.error||!l.id)throw new Error(l.error.code||"Error adding stripe card");const n=await M.callRawAPI("/v2/api/lambda/stripe/customer/card",{sourceToken:l==null?void 0:l.id},"POST");if(n.error)throw new Error(n.message||"Error adding stripe card (lambda)");const I=await M.callRawAPI("/v3/api/custom/jordan/user/subscription",{planId:d},"POST");if(I.error)throw new Error(I.message||"Error occurred while paying");c(A,L,1e4,"success"),w(!1),E&&E(),p(),F("/user/profile")}catch(l){c(A,l.message,4e3,"error"),w(!1),P(k,l.message)}};return e.jsx("div",{className:"fixed inset-0 z-50 bg-black bg-opacity-50 flex items-center justify-center",children:e.jsxs("div",{className:"bg-white h-[calc(100vh-7rem)] md:h-auto md:rounded-lg p-8 w-screen md:w-[480px] relative",children:[e.jsx("button",{onClick:p,className:"absolute right-6 top-6 text-gray-500 hover:text-gray-700",children:e.jsxs("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M18 6L6 18",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M6 6L18 18",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})]})}),e.jsx("h2",{className:"text-xl font-semibold mb-6",children:"Payment Details"}),e.jsxs("form",{ref:s=>s,onSubmit:D,className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-[16px] font-medium text-[#111928] mb-2",children:"Card Number"}),e.jsx("input",{type:"tel",name:"number",className:"w-full p-3 border border-gray-300 rounded-md",placeholder:"Card Number",pattern:"[\\d| ]{16,22}",required:!0,onChange:f,onFocus:j})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-[16px] font-medium text-[#111928] mb-2",children:"Expiry date"}),e.jsx("input",{type:"tel",name:"expiry",className:"w-full p-3 border border-gray-300 rounded-md",placeholder:"MM/YY",pattern:"\\d\\d/\\d\\d",required:!0,onChange:f,onFocus:j})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-[16px] font-medium text-[#111928] mb-2",children:"CVV"}),e.jsx("input",{type:"tel",name:"cvc",className:"w-full p-3 border border-gray-300 rounded-md",placeholder:"CVC",pattern:"\\d{3,4}",required:!0,onChange:f,onFocus:j})]})]}),e.jsx("button",{type:"submit",disabled:B,className:"w-full bg-[#054FB1] text-white py-3 rounded-md hover:bg-blue-700 transition-colors text-[16px] font-medium h-[50px] disabled:opacity-50",children:B?"Processing...":"PAY NOW"})]})]})})};new u;const V=he("pk_test_51R3UZOBazfqAX4xwvEnCZFjXvlOqYkch3qznif64teEvrxi2IzKXj2XKc5LTHhunoQiUE266ZCokcSNHAvswJOt900QLGddBPx");new u;const W=()=>e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[e.jsx("path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"}),e.jsx("circle",{cx:"12",cy:"12",r:"3"})]}),Y=()=>e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[e.jsx("path",{d:"M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-10-7-10-7a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 10 7 10 7a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"}),e.jsx("line",{x1:"1",y1:"1",x2:"23",y2:"23"})]}),Te=()=>{var z,q;const{dispatch:p}=g.useContext(Z),{dispatch:d}=g.useContext(X),C=me(),[b,E]=i.useState(((z=C.state)==null?void 0:z.activeTab)||"Details"),[A,k]=i.useState(((q=C.state)==null?void 0:q.showUpgradeOptions)||!1),[B,w]=i.useState(!1),[T,h]=i.useState(!1),[F,j]=i.useState(!0),[f,D]=i.useState({current:!1,new:!1,confirm:!1}),[s,y]=i.useState({email:"",firstName:"",lastName:"",currentPassword:"",newPassword:"",confirmPassword:"",userId:null}),[L,l]=i.useState([]),[n,I]=i.useState(null),[x,we]=i.useState(null),[J,ge]=i.useState(!1),[o,H]=i.useState(null),[Q,U]=i.useState(!1),_=async()=>{try{j(!0);const r=await new u().getProfile();r.error||(y({...s,email:r.email||"",firstName:r.first_name||"",lastName:r.last_name||"",userId:r.id}),p({type:"UPDATE_PROFILE",payload:r}))}catch(t){console.error("Error fetching user data:",t),c(d,"Failed to fetch user data",4e3,"error"),P(p,t.message)}finally{j(!1)}},ee=async()=>{try{const r=await new u().callRawAPI("/v4/api/records/stripe_price",{order:"id,desc",page:"1,10"},"GET");if(!r.error&&r.list){const m=r.list.map(a=>{var S;const v=JSON.parse(a.object);return{...a,price_id:a.id,displayAmount:`$${a.amount}`,interval:v.recurring?"Monthly":"One-time",features:[a.type==="recurring"?"Monthly subscription":"One-time payment",a.is_usage_metered?"Usage-based billing":"Fixed price",((S=v.recurring)==null?void 0:S.interval)==="month"?"Billed monthly":null].filter(Boolean)}});m.sort((a,v)=>a.amount-v.amount),l(m)}}catch(t){console.error("Error fetching plans:",t),c(d,"Failed to fetch plans",4e3,"error")}},O=async()=>{try{const r=await new u().getCustomerStripeSubscription();console.log("Subscription response:",r),H(r)}catch(t){console.error("Subscription check error:",t),c(d,"Failed to fetch subscription status",4e3,"error")}};i.useEffect(()=>{_(),ee(),O()},[]);const N=t=>{const{name:r,value:m}=t.target;y(a=>({...a,[r]:m}))},se=async t=>{t.preventDefault();try{(await new u().updateProfile({first_name:s.firstName,last_name:s.lastName})).error||(c(d,"Profile Updated Successfully",4e3,"success"),_())}catch(r){console.error("Error updating profile:",r),c(d,"Failed to update profile",4e3,"error"),P(p,r.message)}},te=async t=>{if(t.preventDefault(),s.newPassword!==s.confirmPassword){c(d,"Passwords do not match",4e3,"error");return}try{(await new u().updatePassword(s.newPassword)).error||(c(d,"Password Updated Successfully",4e3,"success"),y({...s,currentPassword:"",newPassword:"",confirmPassword:""}))}catch(r){console.error("Error updating password:",r),c(d,"Failed to update password",4e3,"error"),P(p,r.message)}},R=t=>{D(r=>({...r,[t]:!r[t]}))},re=t=>{I(t),t.type==="one_time"?U(!0):w(!0)},ae=t=>{switch(t){case 5:return"Basic Plan";case 6:return"Starter Plan";case 7:return"Pro Plan";default:return"No Plan"}},$=()=>{O(),w(!1),k(!1)},ne=async()=>{var t;try{if(!((t=o==null?void 0:o.customer)!=null&&t.subId)){c(d,"No active subscription found",4e3,"error");return}if(!(await new u().callRawAPI(`/v3/api/custom/jordan/user/cancel-subscription/${o.customer.subId}`,{},"POST")).error)c(d,"Subscription cancelled successfully",4e3,"success"),O(),h(!1);else throw new Error("Failed to cancel subscription")}catch(r){console.error("Error cancelling subscription:",r),c(d,"Something went wrong",4e3,"error"),P(p,r.message),h(!1)}},le=()=>e.jsx("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:e.jsxs("div",{className:"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0",children:[e.jsx("div",{className:"fixed inset-0 transition-opacity","aria-hidden":"true",children:e.jsx("div",{className:"absolute inset-0 bg-gray-500 opacity-75"})}),e.jsx("span",{className:"hidden sm:inline-block sm:align-middle sm:h-screen","aria-hidden":"true",children:"​"}),e.jsxs("div",{className:"inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6",children:[e.jsx("div",{children:e.jsxs("div",{className:"mt-3 text-center sm:mt-5",children:[e.jsx("h3",{className:"text-lg leading-6 font-medium text-gray-900 mb-4",children:"Cancel Subscription"}),e.jsx("div",{className:"mt-2",children:e.jsx("p",{className:"text-sm text-gray-500",children:"Are you sure you want to cancel your subscription? This action cannot be undone."})})]})}),e.jsxs("div",{className:"mt-5 sm:mt-6 sm:grid sm:grid-cols-2 sm:gap-3 sm:grid-flow-row-dense",children:[e.jsx("button",{type:"button",onClick:ne,className:"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none sm:col-start-2 sm:text-sm",children:"Cancel Subscription"}),e.jsx("button",{type:"button",onClick:()=>h(!1),className:"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none sm:mt-0 sm:col-start-1 sm:text-sm",children:"Keep Subscription"})]})]})]})}),oe=()=>e.jsxs("div",{className:"md:p-6 pt-[1rem] md:pt-0",children:[F?e.jsx("div",{className:"flex justify-center items-center py-8",children:e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"})}):e.jsxs("div",{className:"mb-6 flex items-center gap-2 mt-[1rem]",children:[e.jsx(G,{className:"text-[20px]"}),e.jsx("span",{className:"text-[16px] text-[#373A4B]",children:s.email})]}),e.jsx("form",{onSubmit:se,children:e.jsxs("div",{className:"space-y-4 mt-[2rem]",children:[e.jsxs("div",{className:"flex flex-col gap-2",children:[e.jsx("label",{className:"mb-2 block text-sm font-medium text-[16px] text-[#111928]",children:"First Name"}),e.jsx("input",{type:"text",name:"firstName",className:" w-full md:w-[433px] h-[46px] rounded-md border border-gray-300 px-3 py-2",placeholder:"Enter first name",value:s.firstName,onChange:N})]}),e.jsxs("div",{className:"flex flex-col gap-2",children:[e.jsx("label",{className:"mb-2 block text-sm font-medium text-[16px] text-[#111928]",children:"Last Name"}),e.jsx("input",{type:"text",name:"lastName",className:" w-full md:w-[433px] h-[46px] rounded-md border border-gray-300 px-3 py-2",placeholder:"Enter last name",value:s.lastName,onChange:N})]}),e.jsxs("div",{className:"flex flex-col gap-2",children:[e.jsx("label",{className:"mb-2 block text-sm font-medium text-[16px] text-[#111928]",children:"Email"}),e.jsx("input",{type:"email",name:"email",className:"rounded-md border border-gray-300 px-3 py-2  w-full md:w-[433px] h-[46px]",placeholder:"Enter email",value:s.email,onChange:N})]}),e.jsx("div",{children:e.jsx("button",{type:"submit",className:"rounded-md bg-[#054FB1] px-4  w-full md:w-[433px] h-[50px] text-medium text-[16px] py-2 text-white hover:bg-blue-700",children:"Save Changes"})})]})})]}),ie=()=>e.jsxs("div",{className:"md:p-6 pt-[1rem] md:pt-0",children:[e.jsxs("div",{className:"mb-6 flex items-center gap-2 mt-[1rem]",children:[e.jsx(G,{className:"text-[20px]"}),e.jsx("span",{className:"text-[16px] text-[#373A4B]",children:s.email})]}),e.jsx("form",{onSubmit:te,children:e.jsxs("div",{className:"space-y-4 mt-[2rem]",children:[e.jsxs("div",{className:"flex flex-col",children:[e.jsx("label",{className:"mb-2 block text-sm font-medium text-[16px] text-[#111928]",children:"Current Password"}),e.jsx("div",{className:"relative w-full md:w-[433px] mt-[-1.5rem]",children:e.jsx("input",{type:"text",name:"currentPassword",className:"w-full h-[46px] rounded-md border border-gray-300 px-3 py-2",placeholder:"*****",value:s.currentPassword,onChange:N,disabled:!0})})]}),e.jsxs("div",{className:"flex flex-col",children:[e.jsx("label",{className:"mb-2 block text-sm font-medium text-[16px] text-[#111928]",children:"New Password"}),e.jsxs("div",{className:"relative  w-full md:w-[433px] mt-[-1.5rem]",children:[e.jsx("input",{type:f.new?"text":"password",name:"newPassword",className:"w-full h-[46px] rounded-md border border-gray-300 px-3 py-2 pr-10",placeholder:"Enter new password",value:s.newPassword,onChange:N}),e.jsx("button",{type:"button",onClick:()=>R("new"),className:"absolute right-3 top-1/2 transform text-gray-500 bg-white",children:f.new?e.jsx(Y,{}):e.jsx(W,{})})]})]}),e.jsxs("div",{className:"flex flex-col",children:[e.jsx("label",{className:"mb-2 block text-sm font-medium text-[16px] text-[#111928]",children:"Confirm New Password"}),e.jsxs("div",{className:"relative  w-full md:w-[433px] mt-[-1.5rem] ",children:[e.jsx("input",{type:f.confirm?"text":"password",name:"confirmPassword",className:"w-full h-[46px] rounded-md border border-gray-300 px-3 py-2 pr-10",placeholder:"Confirm new password",value:s.confirmPassword,onChange:N}),e.jsx("button",{type:"button",onClick:()=>R("confirm"),className:"absolute right-3 top-1/2 transform text-gray-500 bg-white",children:f.confirm?e.jsx(Y,{}):e.jsx(W,{})})]})]}),e.jsx("div",{children:e.jsx("button",{type:"submit",className:"rounded-md bg-[#054FB1] px-4  w-full md:w-[433px] h-[50px] text-medium text-[16px] py-2 text-white hover:bg-blue-700",children:"Update Password"})})]})})]}),de=()=>{var t,r,m;return e.jsxs("div",{className:"",children:[A?e.jsxs("div",{children:[e.jsx("h3",{className:"text-xl font-semibold mb-6 mt-4",children:"Available Plans"}),e.jsx("div",{className:"flex flex-col md:flex-row gap-6",children:L.filter(a=>a.type==="recurring").map(a=>{var S;const v=JSON.parse(a.object);return["Monthly subscription",a.is_usage_metered?"Usage-based billing":"Fixed price",((S=v.recurring)==null?void 0:S.interval)==="month"?"Billed monthly":null].filter(Boolean),e.jsxs("div",{className:"rounded-lg border border-gray-200 p-6 bg-white",children:[e.jsx("h3",{className:"mb-2 text-lg font-semibold",children:a.name}),e.jsxs("p",{className:"mb-4 text-2xl font-bold",children:["$",a.amount,"/mo"]}),e.jsxs("div",{className:"mb-4 space-y-2",children:[e.jsxs("p",{className:"text-[#373A4B] text-[16px] leading-[24px] flex items-center",children:[e.jsx("span",{className:"mr-2",children:"•"}),"Monthly Subscription"]}),e.jsxs("p",{className:"text-[#373A4B] text-[16px] leading-[24px] flex items-center",children:[e.jsx("span",{className:"mr-2",children:"•"}),"Recurring Billing"]}),e.jsxs("p",{className:"text-[#373A4B] text-[16px] leading-[24px] flex items-center",children:[e.jsx("span",{className:"mr-2",children:"•"}),a.usage_limit," quizzes genneration"]})]}),e.jsx("button",{onClick:()=>re(a),disabled:(x==null?void 0:x.id)===a.id,className:`rounded-md px-4 py-2 text-white text-[16px] font-medium h-[50px] w-[213px] ${(x==null?void 0:x.id)===a.id?"bg-gray-400 cursor-not-allowed":"bg-blue-600 hover:bg-blue-700"}`,children:(x==null?void 0:x.id)===a.id?"CURRENT PLAN":"SUBSCRIBE"})]},a.id)})}),e.jsx("div",{className:"mt-6",children:e.jsx("button",{onClick:()=>k(!1),className:"rounded-md border border-blue-600 px-4 py-2 h-[50px] w-[85px] mt-[3rem] hover:bg-blue-50 font-medium text-[16px] text-[#054FB1]",children:"BACK"})})]}):e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center justify-between bg-white p-6",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600",children:"Current Plan"}),e.jsx("p",{className:"text-lg font-semibold",children:(t=o==null?void 0:o.customer)!=null&&t.planId?ae(o.customer.planId):"No Active Plan"}),((r=o==null?void 0:o.customer)==null?void 0:r.subId)&&e.jsxs("p",{className:"text-sm text-gray-500 mt-1",children:["Subscription ID: ",o.customer.subId]})]}),e.jsx("button",{onClick:()=>k(!0),className:"rounded-md bg-blue-600 px-4 py-2 text-white hover:bg-blue-700 text-[14px] md:text-[16px] font-medium h-[50px] w-[100px] md:w-[194px]",children:"UPGRADE"})]}),((m=o==null?void 0:o.customer)==null?void 0:m.planId)&&e.jsx("div",{className:"mt-4 flex justify-end",children:e.jsx("button",{onClick:()=>h(!0),className:"text-[#054FB1] text-[16px] font-semibold hover:text-blue-700",children:"Cancel subscription"})})]}),B&&e.jsx(K,{stripe:V,children:e.jsx(be,{onClose:()=>w(!1),planId:n==null?void 0:n.id,price:n==null?void 0:n.amount,trial:n==null?void 0:n.trial_days,onSuccess:$})}),T&&e.jsx(le,{}),Q&&e.jsx(K,{stripe:V,children:e.jsx("div",{className:"fixed inset-0 z-50 bg-black bg-opacity-50 flex items-center justify-center",children:e.jsx(fe,{onClose:()=>U(!1),onSubmit:handlePaymentSubmit,price:n==null?void 0:n.amount,loading:J,userId:s.email,priceId:n==null?void 0:n.id,onSuccess:$})})})]})};return e.jsx("div",{className:"w-full text-7xl min-h-screen h-full text-gray-700 bg-[#E2E6EB] md:p-[1rem]",children:e.jsx("div",{className:"w-[98%] md:w-[90%] mx-auto md:mx-0",children:e.jsx("div",{className:"mx-auto max-w-5xl p-6",children:e.jsxs("div",{className:"rounded-lg border border-gray-200 ",children:[e.jsx("div",{className:"border-b border-gray-200",children:e.jsx("div",{className:"flex justify-between md:justify-evenly md:space-x-[6rem] md:px-6 bg-white h-[54px]",children:["Details","Security","Current plan"].map(t=>e.jsxs("button",{onClick:()=>E(t),className:`relative px-3 py-2 text-[16px] font-medium ${b===t?"text-blue-600":"text-gray-500 hover:text-[#637381]"}`,children:[t,b===t&&e.jsx("div",{className:"absolute bottom-0 left-0 h-0.5 w-full bg-blue-600"})]},t))})}),b==="Details"&&oe(),b==="Security"&&ie(),b==="Current plan"&&de()]})})})})};export{Te as default};

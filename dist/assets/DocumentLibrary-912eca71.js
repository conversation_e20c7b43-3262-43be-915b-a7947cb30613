import{j as e}from"./@react-google-maps/api-4794cf1a.js";import{R as h,r}from"./vendor-dd4ba10b.js";import{A as w,G as D,M as N,s as E}from"./index-3efdd896.js";import{D as v}from"./DeleteModal-a879d8f5.js";import"./react-confirm-alert-5d5c0db6.js";import"./html2pdf.js-19c9759c.js";import"./@headlessui/react-7b0d4887.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-1762f471.js";import"./@fortawesome/react-fontawesome-0b111e8e.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";const P=()=>{h.useContext(w);const{dispatch:u}=h.useContext(D),[n,d]=r.useState([]),[C,p]=r.useState(!0),[o,a]=r.useState(null),[m,c]=r.useState(!1),i=new N,f=localStorage.getItem("user");console.log("userid",f),r.useEffect(()=>{(async()=>{try{const s=await i.callRawAPI("/v3/api/custom/jordan/user/document-library",{page:1,limit:100},"GET");console.log("Documents Response:",s),s&&s.list&&d(s.list)}catch(s){console.error("Error fetching documents:",s)}finally{p(!1)}})()},[]);const b=t=>t?t.length<=50?t:t.substring(0,47)+"...":"",g=async t=>{try{const j=await(await fetch(t)).blob(),x=window.URL.createObjectURL(j),l=document.createElement("a");l.href=x,l.download="document.pdf",document.body.appendChild(l),l.click(),document.body.removeChild(l),window.URL.revokeObjectURL(x)}catch(s){console.error("Error downloading file:",s)}},y=async()=>{if(console.log("deleteid",o),o!=null)try{i.setTable("document_library"),await i.callRestAPI({id:o},"DELETE"),d(n.filter(t=>t.id!==o)),E(u,"Document Deleted"),c(!1),a(null)}catch(t){console.error(t)}};return console.log("documents",n),e.jsxs(e.Fragment,{children:[m&&e.jsx(v,{isOpen:m,onClose:()=>{c(!1),a(null)},onConfirm:y,itemName:"document"}),e.jsx("div",{className:"md:pl-[3rem] w-full text-7xl min-h-screen h-full text-gray-700 bg-[#E2E6EB] p-[1rem]",children:e.jsxs("div",{className:"w-[95%] md:w-[90%] mx-auto md:mx-0",children:[e.jsx("h1",{className:"text-[20px] text-[#111928] font-semibold mb-6",children:"Documents Library"}),e.jsx("div",{className:"bg-white border border-gray-200 rounded-lg overflow-hidden mt-[1rem] p-[2rem] pb-[4rem]",children:e.jsx("div",{className:"overflow-x-auto",style:{msOverflowStyle:"none",scrollbarWidth:"none",WebkitOverflowScrolling:"touch"},children:e.jsx("div",{style:{width:"100%",minWidth:"600px"},children:e.jsxs("table",{className:"w-full",children:[e.jsx("thead",{children:e.jsxs("tr",{className:"bg-[#E4E4E4]",children:[e.jsx("th",{className:"text-left py-3 px-4 text-sm font-semibold text-gray-700",children:"Document name"}),e.jsx("th",{className:"text-left py-3 px-4 text-sm font-semibold text-gray-700",children:"Document pdf"}),e.jsx("th",{className:"text-left py-3 px-4 text-sm font-semibold text-gray-700",children:"Date generated"}),e.jsx("th",{className:"text-left py-3 px-4 text-sm font-semibold text-gray-700",children:"Actions"})]})}),e.jsx("tbody",{children:n.map((t,s)=>e.jsxs("tr",{className:`${s%2===0?"bg-white hover:bg-[#efefefb8]":"bg-[#EFEFEF]"}`,children:[e.jsx("td",{className:"py-3 px-4 text-sm text-gray-900",children:t.title||"Untitled"}),e.jsx("td",{className:"py-3 px-4 text-sm text-blue-600",children:e.jsx("a",{href:t.merged_theme,target:"_blank",rel:"noopener noreferrer",className:"hover:text-blue-800 underline",children:b(t.merged_theme)})}),e.jsx("td",{className:"py-3 px-4 text-sm text-gray-900",children:new Date(t.create_at).toLocaleDateString()}),e.jsx("td",{className:"py-3 px-4 text-sm",children:e.jsxs("div",{className:"flex gap-[3rem]",children:[e.jsx("button",{onClick:()=>g(t.merged_theme),className:"text-blue-600 hover:text-blue-800 underline",children:"Download"}),e.jsx("button",{onClick:()=>{a(t.id),c(!0)},className:"text-blue-600 hover:text-blue-800 underline",children:"Delete"})]})})]},t.id))})]})})})})]})})]})};export{P as default};

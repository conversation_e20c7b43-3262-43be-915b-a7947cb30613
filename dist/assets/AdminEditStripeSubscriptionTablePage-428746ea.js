import{j as t}from"./@react-google-maps/api-4794cf1a.js";import{R as c,b as F,r as a,h as U}from"./vendor-dd4ba10b.js";import{u as C}from"./react-hook-form-a6ecef1c.js";import{o as M}from"./yup-f7f8305f.js";import{c as G,a as m}from"./yup-79911193.js";import{M as $,A as B,G as H,t as q,s as K}from"./index-3efdd896.js";import"./react-quill-25360d36.js";import{M as u}from"./MkdInput-bb15886f.js";import{I as V}from"./InteractiveButton-6ddb3b9d.js";import{S as z}from"./index-3b0c955b.js";import"./@hookform/resolvers-61c06af0.js";import"./react-confirm-alert-5d5c0db6.js";import"./html2pdf.js-19c9759c.js";import"./@headlessui/react-7b0d4887.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-1762f471.js";import"./@fortawesome/react-fontawesome-0b111e8e.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";import"./@craftjs/core-3d3a3b40.js";import"./MoonLoader-795b8e10.js";let f=new $;const Ne=n=>{var y,I;const{dispatch:N}=c.useContext(B),w=G({stripe_id:m(),price_id:m(),user_id:m(),object:m(),status:m(),is_lifetime:m()}).required(),{dispatch:x}=c.useContext(H),[j,J]=c.useState({}),[g,h]=c.useState(!1),[E,_]=c.useState(!1),v=F(),[Q,P]=a.useState(""),[W,k]=a.useState(""),[X,A]=a.useState(0),[Y,R]=a.useState(""),[Z,T]=a.useState(""),[ee,L]=a.useState(""),{register:r,handleSubmit:D,setError:S,setValue:o,formState:{errors:s}}=C({resolver:M(w)}),l=U();a.useEffect(function(){(async function(){try{_(!0),f.setTable("stripe_subscription");const e=await f.callRestAPI({id:n.activeId?n.activeId:Number(l==null?void 0:l.id)},"GET");e.error||(o("stripe_id",e.model.stripe_id),o("price_id",e.model.price_id),o("user_id",e.model.user_id),o("object",e.model.object),o("status",e.model.status),o("is_lifetime",e.model.is_lifetime),P(e.model.stripe_id),k(e.model.price_id),A(e.model.user_id),R(e.model.object),T(e.model.status),L(e.model.is_lifetime),_(!1))}catch(e){_(!1),console.log("error",e),q(N,e.message)}})()},[]);const O=async e=>{h(!0);try{f.setTable("stripe_subscription");for(let p in j){let d=new FormData;d.append("file",j[p].file);let b=await f.uploadImage(d);e[p]=b.url}const i=await f.callRestAPI({id:n.activeId?n.activeId:Number(l==null?void 0:l.id),stripe_id:e.stripe_id,price_id:e.price_id,user_id:e.user_id,object:e.object,status:e.status,is_lifetime:e.is_lifetime},"PUT");if(!i.error)K(x,"Updated"),v("/admin/stripe_subscription"),x({type:"REFRESH_DATA",payload:{refreshData:!0}}),n.setSidebar(!1);else if(i.validation){const p=Object.keys(i.validation);for(let d=0;d<p.length;d++){const b=p[d];S(b,{type:"manual",message:i.validation[b]})}}h(!1)}catch(i){h(!1),console.log("Error",i),S("stripe_id",{type:"manual",message:i.message})}};return c.useEffect(()=>{x({type:"SETPATH",payload:{path:"stripe_subscription"}})},[]),t.jsxs("div",{className:" shadow-md rounded   mx-auto p-5",children:[t.jsx("h4",{className:"text-2xl font-medium",children:"Edit Stripe Subscription"}),E?t.jsx(z,{}):t.jsxs("form",{className:" w-full max-w-lg",onSubmit:D(O),children:[t.jsx(u,{type:"text",page:"edit",name:"stripe_id",errors:s,label:"Stripe Id",placeholder:"Stripe Id",register:r,className:""}),t.jsx(u,{type:"text",page:"edit",name:"price_id",errors:s,label:"Price Id",placeholder:"Price Id",register:r,className:""}),t.jsx(u,{type:"number",page:"edit",name:"user_id",errors:s,label:"User Id",placeholder:"User Id",register:r,className:""}),t.jsxs("div",{className:"mb-4  ",children:[t.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"object",children:"Object"}),t.jsx("textarea",{placeholder:"Object",...r("object"),className:`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(y=s.object)!=null&&y.message?"border-red-500":""}`,row:50}),t.jsx("p",{className:"text-red-500 text-xs italic",children:(I=s.object)==null?void 0:I.message})]}),t.jsx(u,{type:"text",page:"edit",name:"status",errors:s,label:"Status",placeholder:"Status",register:r,className:""}),t.jsx(u,{type:"number",page:"edit",name:"is_lifetime",errors:s,label:"Is Lifetime",placeholder:"Is Lifetime",register:r,className:""}),t.jsx(V,{type:"submit",className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",loading:g,disable:g,children:"Submit"})]})]})};export{Ne as default};

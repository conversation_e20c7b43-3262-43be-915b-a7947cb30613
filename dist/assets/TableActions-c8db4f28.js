import{j as p}from"./@react-google-maps/api-4794cf1a.js";import"./vendor-dd4ba10b.js";import{S as n}from"./index-3efdd896.js";import{A as u}from"./AddButton-bf2721f5.js";import"./react-confirm-alert-5d5c0db6.js";import"./html2pdf.js-19c9759c.js";import"./@headlessui/react-7b0d4887.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-1762f471.js";import"./@fortawesome/react-fontawesome-0b111e8e.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";const C=({actions:i,selectedItems:o})=>p.jsx("div",{className:"flex gap-2",children:Object.keys(i).map(r=>i[r].show).includes(!0)?p.jsx(p.Fragment,{children:Object.keys(i).map(r=>{var a,l;if(i[r].show&&!["select","add","export"].includes(r)){if(o&&(o==null?void 0:o.length)===1&&!((a=i[r])!=null&&a.multiple))return p.jsx(u,{showPlus:!1,className:`cursor-pointer px-2 py-2 text-lg  font-medium leading-loose tracking-wide ${r==="view"?"text-blue-500":r==="delete"?"text-red-500":"text-[#292829fd]"} hover:underline`,onClick:()=>{var t;(t=i[r])!=null&&t.action&&i[r].action(o)},children:n(r,{casetype:"capitalize",separator:" "})},r);if(o&&(o==null?void 0:o.length)>=1&&((l=i[r])!=null&&l.multiple))return p.jsx(u,{showPlus:!1,className:`cursor-pointer px-2 py-2 text-lg  font-medium leading-loose tracking-wide ${r==="view"?"text-blue-500":r==="delete"?"text-red-500":"text-[#292829fd]"} hover:underline`,onClick:()=>{var t;(t=i[r])!=null&&t.action&&i[r].action(o)},children:n(r,{casetype:"capitalize",separator:" "})},r)}}).filter(Boolean)}):null});export{C as default};

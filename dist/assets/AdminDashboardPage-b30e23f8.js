import{j as t}from"./@react-google-maps/api-4794cf1a.js";import{R as e}from"./vendor-dd4ba10b.js";import{A as r,G as a}from"./index-3efdd896.js";import"./react-confirm-alert-5d5c0db6.js";import"./html2pdf.js-19c9759c.js";import"./@headlessui/react-7b0d4887.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-1762f471.js";import"./@fortawesome/react-fontawesome-0b111e8e.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";const b=()=>{e.useContext(r);const{dispatch:o}=e.useContext(a);return e.useEffect(()=>{o({type:"SETPATH",payload:{path:"admin"}})},[]),t.jsx(t.Fragment,{children:t.jsx("div",{className:"w-full flex justify-center items-center text-7xl h-screen text-gray-700 ",children:t.jsx("h4",{className:"text-2xl font-medium",children:"Dashboarddd"})})})};export{b as default};

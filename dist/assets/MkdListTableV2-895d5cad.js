import{j as e}from"./@react-google-maps/api-4794cf1a.js";import{R as n}from"./vendor-dd4ba10b.js";import{u as Le}from"./react-hook-form-a6ecef1c.js";import{o as $e}from"./yup-f7f8305f.js";import{c as Me}from"./yup-79911193.js";import{M as ze,A as Be,G as Oe,L as C,T as qe,p as ee,t as $,g as Ge}from"./index-3efdd896.js";import{P as He}from"./index-fe50f63a.js";import{S as _e}from"./index-3b0c955b.js";import{a as Ke}from"./index.esm-c1b51ffb.js";import{A as Ue}from"./index.esm-8010d0dc.js";import{R as We}from"./index.esm-3f4ea327.js";import{A as Ye}from"./AddButton-bf2721f5.js";import Je from"./MkdListTableHead-39b57446.js";import Qe from"./MkdListTableRow-16125096.js";import{M as Xe}from"./index-01b5280f.js";import"./index-e6a343d0.js";import"./@hookform/resolvers-61c06af0.js";import"./react-confirm-alert-5d5c0db6.js";import"./html2pdf.js-19c9759c.js";import"./@headlessui/react-7b0d4887.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-1762f471.js";import"./@fortawesome/react-fontawesome-0b111e8e.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";import"./react-icons-0b96c072.js";const Ze=({onClick:o,className:a})=>e.jsx(e.Fragment,{children:e.jsxs("button",{onClick:o,className:`relative flex h-[2.125rem] w-fit min-w-fit  items-center justify-center overflow-hidden rounded-md border border-primaryBlue bg-indigo-600 px-[.6125rem]  py-[.5625rem] font-['Inter'] text-sm font-medium leading-none text-white shadow-md shadow-indigo-600 ${a}`,children:[e.jsx("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})}),e.jsx("span",{children:"Export"})]})});let m=new ze;const kt=({columns:o=[],actions:a={view:{show:!0,multiple:!0,action:null},edit:{show:!0,multiple:!0,action:null},delete:{show:!0,multiple:!0,action:null},select:{show:!0,multiple:!0,action:null},add:{show:!0,multiple:!0,action:null,showChildren:!0,children:"Add New"},export:{show:!0,multiple:!0,action:null}},actionPostion:E="ontable",actionId:M="id",table:h,tableRole:te,tableTitle:A="",hasFilter:se=!0,schemaFields:Ve=[],showPagination:et=!0,refreshRef:z=null})=>{var J,Q,X,Z;const{dispatch:T}=n.useContext(Be),[j,b]=n.useState([]),[d,B]=n.useState(10),[re,le]=n.useState(0),[D,ae]=n.useState(0),[ne,ie]=n.useState(!1),[oe,de]=n.useState(!1),[ce,v]=n.useState(!1),[ue,k]=n.useState(!1),[O,q]=n.useState(!1),[G,H]=n.useState(!1),[f,P]=n.useState([]),[me,R]=n.useState([]),[y,x]=n.useState([]),[he,fe]=n.useState("eq"),[_,F]=n.useState(!0),I=n.useRef(null),pe=Me({}),[K,U]=n.useState(null),[W,N]=n.useState(!1),[p,g]=n.useState([]),{state:xe,dispatch:ge}=n.useContext(Oe);function we(t){var l;const s=p;if((l=a==null?void 0:a.select)!=null&&l.multiple)if(s.includes(t)){const r=s.filter(i=>i!==t);g(()=>[...r]),x(r)}else{const r=[...s,t];g(()=>[...r]),x(r)}else if(s.includes(t)){const r=s.filter(i=>i!==t);g(()=>[...r]),x(r)}else{const r=[t];g(()=>[...r]),x(r)}}const je=()=>{if(N(t=>!t),W)g([]),x([]);else{const t=j.map(s=>s[M]);g(t),x(t)}},ye=async t=>{v(!0),U(t)};n.useEffect(()=>{p.length<=0&&N(!1),p.length===j.length&&N(!0),p.length<j.length&&p.length>0&&N(!1)},[p,j]);const{handleSubmit:be,reset:tt}=Le({resolver:$e(pe)});function ve(t){o[t].isSorted?o[t].isSortedDesc=!o[t].isSortedDesc:(o.map(s=>s.isSorted=!1),o.map(s=>s.isSortedDesc=!1),o[t].isSorted=!0),async function(){await u(0,d)}()}function Ne(){u(D-1,d)}function Se(){u(D+1,d)}const Ce=(t,s,l)=>{const r=s==="eq"&&isNaN(l)?`${l}`:l,i=`${t},${s},${r}`;R(c=>[...c.filter(w=>!w.includes(t)),i])},Ee=()=>{u(0,d,{},me)},Ae=t=>{u(0,d,{},t)};async function u(t,s,l={},r=[]){F(!0);try{m.setTable(h);const i=await m.callRestAPI({payload:{...l},page:t,limit:s,filter:r},"PAGINATE");i&&F(!1);const{list:c,total:V,limit:w,num_pages:S,page:L}=i;b(c),B(w),le(S),ae(L),ie(L>1),de(L+1<=S)}catch(i){F(!1),console.log("ERROR",i),$(T,i.message)}}async function Te(t,s,l={},r=[]){m.setTable(h);const i=await m.callRestAPI({payload:{...l},page:t,limit:s,filter:r},"PAGINATE"),{list:c}=i;b(c),ge({type:"REFRESH_DATA",payload:{refreshData:!1}})}const De=async t=>{async function s(l){try{k(!0),m.setTable(h);const r=await m.callRestAPI({id:l},"DELETE");r!=null&&r.error||(b(i=>i.filter(c=>Number(c.id)!==Number(l))),k(!1),v(!1))}catch(r){throw k(!1),v(!1),$(T,r==null?void 0:r.message),new Error(r)}}typeof t=="object"?t.forEach(async l=>{await s(l)}):typeof t=="number"&&await s(t)},ke=async t=>{try{m.setTable(h);const s=await m.exportCSV()}catch(s){throw new Error(s)}},Pe=t=>{const s=o.filter(l=>l.accessor).map(l=>{const r=Ge(t[l.accessor]);return r?`${l.accessor},cs,${r}`:null}).filter(Boolean);u(0,d,{},s)};async function Re(t,s,l){try{m.setTable(h);const r=await m.callRestAPI({id:t,[s]:l},"PUT")}catch(r){console.log("ERROR",r),$(T,r.message)}}const Fe=()=>{P([]),R([]),u(1,d)};async function Ie(t,s,l,r){let i;s=isNaN(Number.parseInt(s))?s:Number.parseInt(s);try{clearTimeout(i),i=setTimeout(async()=>{await Re(t,r,s)},200),b(c=>c.map((w,S)=>S===l?{...w,[r]:s}:w))}catch(c){console.error(c)}}n.useEffect(()=>{var t;(t=a==null?void 0:a.select)!=null&&t.action&&a.select.action()},[y.length]),n.useEffect(()=>{const s=setTimeout(async()=>{await u(1,d)},700);return()=>{clearTimeout(s)}},[]),n.useEffect(()=>{Te(1,d)},[xe.refreshData]);const Y=t=>{I.current&&!I.current.contains(t.target)&&q(!1)};return n.useEffect(()=>(document.addEventListener("mousedown",Y),()=>{document.removeEventListener("mousedown",Y)}),[]),e.jsxs("div",{className:"px-8",children:[z&&e.jsx("button",{ref:z,onClick:()=>u(1,d),className:"hidden"}),e.jsxs("div",{className:`flex gap-3 ${A?"flex-col":"h-fit items-center"}`,children:[se?e.jsx("div",{className:"flex w-auto items-center justify-between ",children:e.jsx("form",{className:"relative rounded bg-white",onSubmit:be(Pe),children:e.jsx("div",{className:"flex items-center gap-4 text-gray-700 text-nowrap",children:e.jsxs("div",{className:"relative",ref:I,children:[e.jsxs("div",{className:"flex cursor-pointer items-center justify-between gap-3 rounded-md border border-gray-200 px-3 py-1",onClick:()=>q(!O),children:[e.jsx(Ke,{}),e.jsx("span",{children:"Filters"}),f.length>0&&e.jsx("span",{className:"flex h-6 w-6 items-center justify-center rounded-full bg-gray-800 text-start text-white",children:f.length})]}),O&&e.jsx("div",{className:"absolute z-10 mt-4 w-[500px] min-w-[90%] top-fill left-0 filter-form-holder bg-white border border-gray-200 rounded-md shadow-lg",children:e.jsxs("div",{className:"p-4",children:[f==null?void 0:f.map((t,s)=>e.jsxs("div",{className:"mb-2 flex w-full items-center justify-between gap-3 text-gray-600",children:[e.jsx("div",{className:" mb-3 w-1/3 rounded-md border border-gray-300 px-3 py-2 leading-tight text-gray-700 outline-none",children:t}),e.jsxs("select",{className:"mb-3 w-[40%] rounded-md border px-3 py-2 leading-tight text-gray-700 outline-none",onChange:l=>{fe(l.target.value)},children:[e.jsx("option",{value:"eq",selected:!0,children:"equals"}),e.jsx("option",{value:"cs",children:"contains"}),e.jsx("option",{value:"sw",children:"start with"}),e.jsx("option",{value:"ew",children:"ends with"}),e.jsx("option",{value:"lt",children:"lower than"}),e.jsx("option",{value:"le",children:"lower or equal"}),e.jsx("option",{value:"ge",children:"greater or equal"}),e.jsx("option",{value:"gt",children:"greater than"}),e.jsx("option",{value:"bt",children:"between"}),e.jsx("option",{value:"in",children:"in"}),e.jsx("option",{value:"is",children:"is null"})]}),e.jsx("input",{type:"text",placeholder:"Enter value...",className:" mb-3 w-1/3 rounded-md border px-3 py-2 leading-tight text-gray-700 outline-none",onChange:l=>Ce(t,he,l.target.value),onKeyDown:l=>{l.key==="Enter"&&l.preventDefault()}}),e.jsx("div",{className:"w-1/12 mt-[-10px]",children:e.jsx(We,{className:" cursor-pointer text-xl",onClick:()=>{P(l=>l.filter(r=>r!==t)),R(l=>{const r=l.filter(i=>!i.includes(t));return Ae(r),r})}})})]},s)),e.jsxs("div",{className:"search-buttons relative flex items-center justify-between font-semibold",children:[e.jsxs("div",{className:"mr-2 flex w-auto cursor-pointer items-center gap-2 rounded bg-gray-100 px-4 py-2.5 font-medium leading-tight text-gray-600 transition duration-150 ease-in-out ",onClick:()=>{H(!G)},children:[e.jsx(Ue,{}),"Add filter"]}),G&&e.jsx("div",{className:"absolute top-11 z-10 bg-white px-5 py-3 text-gray-600 shadow-md",children:e.jsx("ul",{className:"flex flex-col gap-2 text-gray-500",children:o.slice(0,-1).map(t=>e.jsx("li",{className:`${f.includes(t.accessor)?"cursor-not-allowed text-gray-400":"cursor-pointer"}`,onClick:()=>{f.includes(t.accessor)||P(s=>[...s,t.accessor]),H(!1)},children:t.header},t.accessor))})}),f.length>0&&e.jsx("div",{onClick:Fe,className:"inline-block cursor-pointer  rounded px-6  py-2.5 font-medium leading-tight text-gray-600  transition duration-150 ease-in-out",children:"Clear all filter"})]}),e.jsx("button",{type:"button",onClick:Ee,className:"mt-4 inline-block cursor-pointer rounded bg-blue-500 px-6 py-2.5 font-medium leading-tight text-white transition duration-150 ease-in-out",children:"Apply Filters"})]})})]})})})}):null,e.jsxs("div",{className:"flex h-fit w-full justify-between text-center",children:[e.jsx("h4",{className:"text-2xl font-medium capitalize",children:A||""}),e.jsxs("div",{className:"flex h-full gap-2",children:[y!=null&&y.length&&E==="abovetable"?e.jsx(C,{children:e.jsx(qe,{actions:a,selectedItems:y})}):null,((J=a==null?void 0:a.export)==null?void 0:J.show)&&e.jsx(Ze,{showText:!1,onClick:ke,className:"mx-1"}),((Q=a==null?void 0:a.add)==null?void 0:Q.show)&&e.jsx(Ye,{onClick:()=>{var t,s;(t=a==null?void 0:a.add)!=null&&t.action&&((s=a==null?void 0:a.add)==null||s.action())},showChildren:(X=a==null?void 0:a.add)==null?void 0:X.showChildren,children:(Z=a==null?void 0:a.add)==null?void 0:Z.children})]})]})]}),e.jsx("div",{className:"overflow-x-auto  rounded bg-white p-5 px-0",children:e.jsxs(e.Fragment,{children:[e.jsx("div",{className:`${_?"":"overflow-x-auto"} border-b border-gray-200 shadow`,children:_?e.jsx("div",{className:"flex max-h-fit min-h-fit min-w-fit max-w-full items-center justify-center  py-5",children:e.jsx(_e,{})}):e.jsx(e.Fragment,{children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsx(C,{children:e.jsx(Je,{onSort:ve,columns:o,actions:a,actionPostion:E,areAllRowsSelected:W,handleSelectAll:je})})}),e.jsx("tbody",{className:"divide-y divide-gray-200 bg-white",children:j.map((t,s)=>e.jsx(C,{children:e.jsx(Qe,{i:s,row:t,columns:o,actions:a,actionPostion:E,actionId:M,handleTableCellChange:Ie,handleSelectRow:we,selectedIds:p,setDeleteId:ye,table:h,tableRole:te},s)},s))})]})})}),e.jsx(C,{children:e.jsx(Xe,{open:ce,actionHandler:()=>{De(K)},closeModalFunction:()=>{U(null),v(!1)},title:`Delete ${ee(h)} `,message:`You are about to delete ${ee(h)} ${K}, note that this action is irreversible`,acceptText:"DELETE",rejectText:"CANCEL",loading:ue})})]})}),e.jsx(He,{currentPage:D,pageCount:re,pageSize:d,canPreviousPage:ne,canNextPage:oe,updatePageSize:t=>{B(t),u(1,t)},previousPage:Ne,nextPage:Se})]})};export{kt as default};

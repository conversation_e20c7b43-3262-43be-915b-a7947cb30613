import{j as e}from"./@react-google-maps/api-4794cf1a.js";import{R as o,b as N}from"./vendor-dd4ba10b.js";import{u as A}from"./react-hook-form-a6ecef1c.js";import{o as v}from"./yup-f7f8305f.js";import{c as E,a as l}from"./yup-79911193.js";import{G as k,A as R,M as D,s as O,t as P}from"./index-3efdd896.js";import"./react-quill-25360d36.js";import{M as d}from"./MkdInput-bb15886f.js";import{I as T}from"./InteractiveButton-6ddb3b9d.js";import"./index-3b0c955b.js";import"./@hookform/resolvers-61c06af0.js";import"./react-confirm-alert-5d5c0db6.js";import"./html2pdf.js-19c9759c.js";import"./@headlessui/react-7b0d4887.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-1762f471.js";import"./@fortawesome/react-fontawesome-0b111e8e.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";import"./@craftjs/core-3d3a3b40.js";import"./MoonLoader-795b8e10.js";const oe=({setSidebar:j})=>{var h,g;const{dispatch:c}=o.useContext(k),S=E({stripe_id:l(),price_id:l(),user_id:l(),object:l(),status:l(),is_lifetime:l()}).required(),{dispatch:y}=o.useContext(R),[b,F]=o.useState({}),[f,n]=o.useState(!1),_=N(),{register:i,handleSubmit:w,setError:x,setValue:C,formState:{errors:s}}=A({resolver:v(S)});o.useState([]);const I=async a=>{let u=new D;n(!0);try{for(let m in b){let r=new FormData;r.append("file",b[m].file);let p=await u.uploadImage(r);a[m]=p.url}u.setTable("stripe_subscription");const t=await u.callRestAPI({stripe_id:a.stripe_id,price_id:a.price_id,user_id:a.user_id,object:a.object,status:a.status,is_lifetime:a.is_lifetime},"POST");if(!t.error)O(c,"Added"),_("/admin/stripe_subscription"),j(!1),c({type:"REFRESH_DATA",payload:{refreshData:!0}});else if(t.validation){const m=Object.keys(t.validation);for(let r=0;r<m.length;r++){const p=m[r];x(p,{type:"manual",message:t.validation[p]})}}n(!1)}catch(t){n(!1),console.log("Error",t),x("stripe_id",{type:"manual",message:t.message}),P(y,t.message)}};return o.useEffect(()=>{c({type:"SETPATH",payload:{path:"stripe_subscription"}})},[]),e.jsxs("div",{className:" shadow-md rounded  mx-auto p-5",children:[e.jsx("h4",{className:"text-2xl font-medium",children:"Add Stripe Subscription"}),e.jsxs("form",{className:" w-full max-w-lg",onSubmit:w(I),children:[e.jsx(d,{type:"text",page:"add",name:"stripe_id",errors:s,label:"Stripe Id",placeholder:"Stripe Id",register:i,className:""}),e.jsx(d,{type:"text",page:"add",name:"price_id",errors:s,label:"Price Id",placeholder:"Price Id",register:i,className:""}),e.jsx(d,{type:"number",page:"add",name:"user_id",errors:s,label:"User Id",placeholder:"User Id",register:i,className:""}),e.jsxs("div",{className:"mb-4  ",children:[e.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"object",children:"Object"}),e.jsx("textarea",{placeholder:"Object",...i("object"),className:`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(h=s.object)!=null&&h.message?"border-red-500":""}`,row:50}),e.jsx("p",{className:"text-red-500 text-xs italic",children:(g=s.object)==null?void 0:g.message})]}),e.jsx(d,{type:"text",page:"add",name:"status",errors:s,label:"Status",placeholder:"Status",register:i,className:""}),e.jsx(d,{type:"number",page:"add",name:"is_lifetime",errors:s,label:"Is Lifetime",placeholder:"Is Lifetime",register:i,className:""}),e.jsx(T,{type:"submit",loading:f,disabled:f,className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:"Submit"})]})]})};export{oe as default};

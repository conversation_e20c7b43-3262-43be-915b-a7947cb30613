import{j as s}from"./@react-google-maps/api-4794cf1a.js";import{R as q,r as B,b as D,L as I}from"./vendor-dd4ba10b.js";import{u as M}from"./react-hook-form-a6ecef1c.js";import{o as O}from"./yup-f7f8305f.js";import{c as T,a as n,b as _}from"./yup-79911193.js";import{A as K,M as U,s as Y,t as z}from"./index-3efdd896.js";import{I as G}from"./InteractiveButton-6ddb3b9d.js";import"./@hookform/resolvers-61c06af0.js";import"./react-confirm-alert-5d5c0db6.js";import"./html2pdf.js-19c9759c.js";import"./@headlessui/react-7b0d4887.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-1762f471.js";import"./@fortawesome/react-fontawesome-0b111e8e.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";import"./MoonLoader-795b8e10.js";const ue=()=>{var m,c,p,u,x,g;const{dispatch:i}=q.useContext(K),[l,a]=B.useState(!1),E=window.location.search,R=new URLSearchParams(E).get("token"),F=T({code:n().required(),password:n().required(),confirmPassword:n().oneOf([_("password"),null],"Passwords must match")}).required(),L=D(),{register:o,handleSubmit:$,setError:d,formState:{errors:t}}=M({resolver:O(F)}),A=async h=>{var f,b,w,y,j,N,k,v;let C=new U;try{a(!0);const e=await C.reset(R,h.code,h.password);if(!e.error)Y(i,"Password Reset"),setTimeout(()=>{L("/admin/login")},2e3);else if(e.validation){const P=Object.keys(e.validation);for(let r=0;r<P.length;r++){const S=P[r];d(S,{type:"manual",message:e.validation[S]})}}a(!1)}catch(e){a(!1),console.log("Error",e),d("code",{type:"manual",message:(b=(f=e==null?void 0:e.response)==null?void 0:f.data)!=null&&b.message?(y=(w=e==null?void 0:e.response)==null?void 0:w.data)==null?void 0:y.message:e==null?void 0:e.message}),z(i,(N=(j=e==null?void 0:e.response)==null?void 0:j.data)!=null&&N.message?(v=(k=e==null?void 0:e.response)==null?void 0:k.data)==null?void 0:v.message:e==null?void 0:e.message)}};return s.jsx(s.Fragment,{children:s.jsxs("div",{className:"w-full max-w-xs mx-auto",children:[s.jsxs("form",{onSubmit:$(A),className:"bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4 mt-8 ",children:[s.jsxs("div",{className:"mb-4",children:[s.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"code",children:"Code"}),s.jsx("input",{type:"text",placeholder:"Enter code sent to your email",...o("code"),className:`"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(m=t.code)!=null&&m.message?"border-red-500":""}`}),s.jsx("p",{className:"text-red-500 text-xs italic",children:(c=t.code)==null?void 0:c.message})]}),s.jsxs("div",{className:"mb-6",children:[s.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"password",children:"Password"}),s.jsx("input",{type:"password",placeholder:"******************",...o("password"),className:`shadow appearance-none border  rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline ${(p=t.password)!=null&&p.message?"border-red-500":""}`}),s.jsx("p",{className:"text-red-500 text-xs italic",children:(u=t.password)==null?void 0:u.message})]}),s.jsxs("div",{className:"mb-6",children:[s.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"confirmPassword",children:"Confirm Password"}),s.jsx("input",{type:"password",placeholder:"******************",...o("confirmPassword"),className:`shadow appearance-none border  rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline ${(x=t.confirmPassword)!=null&&x.message?"border-red-500":""}`}),s.jsx("p",{className:"text-red-500 text-xs italic",children:(g=t.confirmPassword)==null?void 0:g.message})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx(G,{className:"bg-primaryBlue disabled:cursor-not-allowed text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",type:"submit",loading:l,disabled:l,children:"Reset Password"}),s.jsx(I,{className:"inline-block align-baseline font-bold text-sm text-primaryBlue",to:"/admin/login",children:"Login?"})]})]}),s.jsxs("p",{className:"text-center text-gray-500 text-xs",children:["© ",new Date().getFullYear()," manaknightdigital inc. All rights reserved."]})]})})};export{ue as default};

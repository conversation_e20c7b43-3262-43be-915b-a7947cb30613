import{_ as t}from"./html2pdf.js-19c9759c.js";import"./vendor-dd4ba10b.js";function n(r,e){if(t(r)!="object"||!r)return r;var i=r[Symbol.toPrimitive];if(i!==void 0){var o=i.call(r,e||"default");if(t(o)!="object")return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(r)}function u(r){var e=n(r,"string");return t(e)=="symbol"?e:e+""}function m(r,e,i){return(e=u(e))in r?Object.defineProperty(r,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):r[e]=i,r}export{m as _};

import{j as t}from"./@react-google-maps/api-4794cf1a.js";import"./vendor-dd4ba10b.js";import{m as s}from"./framer-motion-1ebc0218.js";const o={start:{transition:{staggerChildren:.2}},end:{transition:{staggerChildren:.2}}},a={start:{y:"0%"},end:{y:"100%"}},i={duration:.4,yoyo:1/0,ease:"easeIn"};function p({dotsClasses:r,size:d,style:e}){const n="block w-[9px] h-[9px] bg-slate-900 rounded-md shrink-0 "+r;return t.jsxs(s.div,{variants:o,className:"flex justify-between items-center w-[40px] pb-[10px]",initial:"start",animate:"end",style:{...e},children:[t.jsx(s.span,{className:n,variants:a,transition:i}),t.jsx(s.span,{className:n,variants:a,transition:i}),t.jsx(s.span,{className:n,variants:a,transition:i})]})}export{p as default};

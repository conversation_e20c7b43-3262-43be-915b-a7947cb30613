import{j as a}from"./@react-google-maps/api-4794cf1a.js";import{R as t,b as K}from"./vendor-dd4ba10b.js";import{M as w,A as J,G as y,a as Q,t as U,s as V}from"./index-3efdd896.js";import{o as W}from"./yup-f7f8305f.js";import{c as X,a as m}from"./yup-79911193.js";import{u as Y}from"./react-hook-form-a6ecef1c.js";import{P as Z}from"./index-fe50f63a.js";import ee from"./AddAdminPhotoPage-69f2465a.js";import{A as te}from"./AddButton-bf2721f5.js";import{S as ae}from"./index-3b0c955b.js";import"./react-confirm-alert-5d5c0db6.js";import"./html2pdf.js-19c9759c.js";import"./@headlessui/react-7b0d4887.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-1762f471.js";import"./@fortawesome/react-fontawesome-0b111e8e.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";import"./@hookform/resolvers-61c06af0.js";import"./@uppy/xhr-upload-54a0071a.js";import"./@uppy/aws-s3-61b9ec5d.js";import"./@craftjs/core-3d3a3b40.js";import"./@uppy/aws-s3-multipart-d8008cbd.js";import"./@uppy/react-c6d54ea9.js";import"./@uppy/core-e491152c.js";import"./@uppy/compressor-dfa3f345.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/dashboard-7f6fae24.js";import"./@uppy/drag-drop-98694d6f.js";import"./@uppy/progress-bar-e3c7768f.js";import"./@uppy/file-input-a14c1f6c.js";let c=new w;const P=[{header:"Photos",accessor:"url"},{header:"Create at",accessor:"create_at"},{header:"Action",accessor:""}],Oe=()=>{const{dispatch:b}=t.useContext(J),{dispatch:u}=t.useContext(y);t.useState("");const[v,A]=t.useState([]),[d,h]=t.useState(3),[f,N]=t.useState(0),[se,C]=t.useState(0),[o,E]=t.useState(0),[k,D]=t.useState(!1),[R,T]=t.useState(!1),[ie,x]=t.useState(!1),[L,p]=t.useState(!1),[oe,F]=t.useState(!1);t.useState(!1),t.useState([]),t.useState([]),t.useState(""),t.useState("eq"),K(),t.useContext(y);const g=t.useRef(null),[M,_]=t.useState(!0),I=X({date:m(),id:m(),user_id:m()});Y({resolver:W(I)});function z(e){(async function(){h(e),await r(0,e)})()}function G(){(async function(){await r(o-1>0?o-1:0,d)})()}function O(){(async function(){await r(o+1<=f?o+1:0,d)})()}async function r(e,n,s){try{c.setTable("photo");const i=await c.callRestAPI({payload:{...s},page:e,limit:n},"PAGINATE"),{list:B,total:q,limit:H,num_pages:j,page:l}=i;A(B),h(H),N(j),E(l),C(q),D(l>1),T(l+1<=j),_(!1)}catch(i){console.log("ERROR",i),U(b,i.message)}}t.useEffect(()=>{u({type:"SETPATH",payload:{path:"photo"}}),async function(){x(!0),await r(0,50),x(!1)}()},[]);async function $(e){c.setTable("photo"),await c.callRestAPI({id:e},"DELETE"),V(u,"Deleted"),await r(0,50)}const S=e=>{g.current&&!g.current.contains(e.target)&&F(!1)};return t.useEffect(()=>(document.addEventListener("mousedown",S),()=>{document.removeEventListener("mousedown",S)}),[]),a.jsxs("div",{className:"px-8",children:[a.jsx("div",{className:"flex items-center justify-between py-3",children:a.jsx(te,{onClick:()=>p(!0)})}),a.jsx("div",{children:M?a.jsx(ae,{}):a.jsx("div",{className:"shadow overflow-x-auto border-b border-gray-200 ",children:a.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[a.jsx("thead",{className:"bg-gray-50",children:a.jsx("tr",{children:P.map((e,n)=>a.jsxs("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500",children:[e.header,a.jsx("span",{children:e.isSorted?e.isSortedDesc?" ▼":" ▲":""})]},n))})}),a.jsx("tbody",{className:"divide-y divide-gray-200 bg-white",children:v.map((e,n)=>a.jsx("tr",{children:P.map((s,i)=>s.accessor==""?a.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:a.jsxs("button",{className:"text-xs text-red-400",onClick:()=>{$(e.id)},children:[" ","Delete"]})},i):s.mapping?a.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:s.mapping[e[s.accessor]]},i):a.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:s.accessor=="url"?a.jsx("img",{width:200,height:200,src:e!=null&&e.url.includes("http")?e==null?void 0:e.url:`https://mkdlabs.com${e==null?void 0:e.url}`,alt:e!=null&&e.caption?e==null?void 0:e.caption:"broken image link"}):e[s.accessor]},i))},n))})]})})}),a.jsx(Z,{currentPage:o,pageCount:f,pageSize:d,canPreviousPage:k,canNextPage:R,updatePageSize:z,previousPage:G,nextPage:O}),a.jsx(Q,{isModalActive:L,closeModalFn:()=>p(!1),children:a.jsx(ee,{setSidebar:p})})]})};export{Oe as default};

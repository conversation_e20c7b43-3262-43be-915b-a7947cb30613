import{j as e}from"./@react-google-maps/api-4794cf1a.js";import{r as m,R as u,b as K}from"./vendor-dd4ba10b.js";import{u as Y}from"./react-hook-form-a6ecef1c.js";import{o as z}from"./yup-f7f8305f.js";import{c as J,a as l}from"./yup-79911193.js";import{A as Q,G as X,M as L,s as p,t as Z}from"./index-3efdd896.js";import"./@hookform/resolvers-61c06af0.js";import"./react-confirm-alert-5d5c0db6.js";import"./html2pdf.js-19c9759c.js";import"./@headlessui/react-7b0d4887.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-1762f471.js";import"./@fortawesome/react-fontawesome-0b111e8e.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";const fe=({setSidebar:i})=>{var v,j,w,N,k,_,S,C,A,P,T,E,F,$;const[R,I]=m.useState("one_time"),[M,U]=m.useState([]),[x,h]=m.useState(!1),D=J({product_id:l().required(),name:l().required(),amount:l().required(),type:l().required(),interval:l().when("type",{is:"recurring",then:t=>t.required(),otherwise:t=>t.notRequired()}),interval_count:l(),usage_type:l().when("type",{is:"recurring",then:t=>t.required(),otherwise:t=>t.notRequired()}),usage_limit:l(),trial_days:l()}).required(),{dispatch:g}=u.useContext(Q),{dispatch:n}=u.useContext(X),G=K(),{register:a,handleSubmit:y,setError:H,setValue:b,trigger:O,resetField:ee,getValues:te,formState:{errors:s}}=Y({resolver:z(D)}),V=[{key:0,value:"",display:"Nothing Selected"},{key:1,value:"one_time",display:"One Time"},{key:2,value:"recurring",display:"Recurring"}],W=[{key:0,value:"",display:"Nothing Selected"},{key:1,value:"licenced",display:"Upfront"},{key:2,value:"metered",display:"Metered"}],B=[{key:0,value:"",display:"Nothing Selected"},{key:1,value:"day",display:"Day"},{key:2,value:"week",display:"Week"},{key:3,value:"month",display:"Month"},{key:4,value:"year",display:"Year"},{key:5,value:"lifetime",display:"Lifetime"}],f=async t=>{let o=new L;console.log(t),h(!0);try{const r=await o.addStripePrice(t);if(!r.error)p(n,"Price Added"),G("/admin/stripe_price");else if(r.validation){const q=Object.keys(r.validation);for(let d=0;d<q.length;d++){const c=q[d];console.log(c),H(c,{type:"manual",message:r.validation[c]})}}}catch(r){console.log("Error",r),p(n,r.message),Z(g,r.message)}h(!1)};return u.useEffect(()=>{n({type:"SETPATH",payload:{path:"prices"}}),(async()=>{const o=await new L().callRawAPI("/v4/api/records/stripe_product?page=1&limit=100",[],"GET");o.error?p(g,"Something went wrong while fetching products list"):U(o.list)})()},[]),e.jsxs("div",{className:"mx-auto rounded ",children:[e.jsxs("div",{className:"flex items-center justify-between gap-4 border-b border-b-[#E0E0E0] p-3",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("svg",{onClick:()=>i(!1),xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:e.jsx("path",{d:"M14.3322 5.83203L19.8751 11.3749C20.2656 11.7654 20.2656 12.3986 19.8751 12.7891L14.3322 18.332M19.3322 12.082H3.83218",stroke:"#A8A8A8",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})}),e.jsx("span",{className:"text-lg font-semibold",children:"Add Price"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("button",{className:"flex items-center rounded-md border border-[#C6C6C6] px-3 py-2 shadow-sm hover:bg-[#f4f4f4]",onClick:()=>i(!1),children:"Cancel"}),e.jsx("button",{className:"flex items-center rounded-md bg-[#1f1d1a] px-3 py-2 text-white shadow-sm",onClick:async()=>{await y(f)(),i(!1)},disabled:x,children:x?"Saving...":"Save"})]})]}),e.jsxs("form",{className:"w-full max-w-lg p-4 text-left ",onSubmit:y(f),children:[e.jsxs("div",{className:"mb-4 ",children:[e.jsx("label",{className:"block mb-2 text-sm font-bold text-gray-700",htmlFor:"product_id",children:"Product"}),e.jsxs("select",{className:"focus:shadow-outline mb-3 w-full  appearance-none rounded border border-[#1f1d1a] px-3 py-2 leading-tight text-gray-700   shadow focus:outline-none",...a("product_id"),children:[e.jsx("option",{value:"",children:"Nothing selected"},"prod_default"),M.map(t=>e.jsx("option",{value:t.id,children:t.name},`prod_${t.id}`))]}),e.jsx("p",{className:"text-xs italic text-red-500",children:(v=s.product_id)==null?void 0:v.message})]}),e.jsxs("div",{className:"mb-4 ",children:[e.jsx("label",{className:"block mb-2 text-sm font-bold text-gray-700",htmlFor:"name",children:"Name"}),e.jsx("input",{type:"text",placeholder:"Name",...a("name"),className:`"shadow focus:shadow-outline w-full appearance-none rounded border border-[#1f1d1a] bg-transparent px-3 py-2 leading-tight text-[#1d1f1a] focus:outline-none ${(j=s.name)!=null&&j.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(w=s.name)==null?void 0:w.message})]}),e.jsxs("div",{className:"mb-5",children:[e.jsx("label",{className:"block mb-2 text-sm font-bold text-gray-700",htmlFor:"amount",children:"Amount"}),e.jsx("input",{type:"number",min:.1,step:"any",placeholder:"Amount",...a("amount"),className:`focus:shadow-outline mb-3 w-full  appearance-none rounded border border-[#1f1d1a] px-3 py-2 leading-tight text-gray-700   shadow focus:outline-none ${(N=s.amount)!=null&&N.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(k=s.amount)==null?void 0:k.message})]}),e.jsxs("div",{className:"mb-5",children:[e.jsx("label",{className:"block mb-2 text-sm font-bold text-gray-700",htmlFor:"type",children:"Type"}),e.jsx("select",{className:"focus:shadow-outline mb-3 w-full  appearance-none rounded border border-[#1f1d1a] px-3 py-2 leading-tight text-gray-700   shadow focus:outline-none",...a("type"),onChange:t=>{const o=t.target.value;I(o),b("type",o),O("type")},children:V.map(t=>e.jsx("option",{value:t.value.toLowerCase(),children:t.display},`interval_${t.key}`))}),e.jsx("p",{className:"text-xs italic text-red-500",children:(_=s.type)==null?void 0:_.message})]}),R==="recurring"?e.jsxs("div",{className:"ml-6",children:[e.jsxs("div",{className:"mb-5",children:[e.jsx("label",{className:"block mb-2 text-sm font-bold text-gray-700",htmlFor:"interval",children:"Interval"}),e.jsx("select",{className:"focus:shadow-outline mb-3 w-full  appearance-none rounded border border-[#1f1d1a] px-3 py-2 leading-tight text-gray-700   shadow focus:outline-none",...a("interval"),placeholder:"Select",children:B.map(t=>e.jsx("option",{value:t.value.toLowerCase(),children:t.display},`interval_${t.key}`))}),e.jsx("p",{className:"text-xs italic text-red-500",children:(S=s.interval)==null?void 0:S.message})]}),e.jsxs("div",{className:"mb-5",children:[e.jsx("label",{className:"block mb-2 text-sm font-bold text-gray-700",htmlFor:"interval_count",children:"Interval Count"}),e.jsx("input",{type:"number",step:"1",placeholder:"Interval Count",...a("interval_count"),...b("interval_count",1),className:`focus:shadow-outline mb-3 w-full  appearance-none rounded border border-[#1f1d1a] px-3 py-2 leading-tight text-gray-700   shadow focus:outline-none ${(C=s.interval_count)!=null&&C.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(A=s.interval_count)==null?void 0:A.message})]}),e.jsxs("div",{className:"mb-5",children:[e.jsx("label",{className:"block mb-2 text-sm font-bold text-gray-700",htmlFor:"usage_type",children:"Usage Type"}),e.jsx("select",{className:"focus:shadow-outline mb-3 w-full  appearance-none rounded border border-[#1f1d1a] px-3 py-2 leading-tight text-gray-700   shadow focus:outline-none",...a("usage_type"),placeholder:"Select",children:W.map(t=>e.jsx("option",{value:t.value.toLowerCase(),children:t.display},`interval_${t.key}`))}),e.jsx("p",{className:"text-xs italic text-red-500",children:(P=s.usage_type)==null?void 0:P.message})]}),e.jsxs("div",{className:"mb-5",children:[e.jsx("label",{className:"block mb-2 text-sm font-bold text-gray-700",htmlFor:"trial_days",children:"Trial Days"}),e.jsx("input",{type:"number",step:"1",placeholder:"0",...a("trial_days"),className:`focus:shadow-outline mb-3 w-full  appearance-none rounded border border-[#1f1d1a] px-3 py-2 leading-tight text-gray-700   shadow focus:outline-none ${(T=s.trial_days)!=null&&T.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(E=s.trial_days)==null?void 0:E.message})]}),e.jsxs("div",{className:"mb-5",children:[e.jsx("label",{className:"block mb-2 text-sm font-bold text-gray-700",htmlFor:"trial_days",children:"Usage Limit"}),e.jsx("input",{type:"number",step:"1",placeholder:"1000",...a("usage_limit"),className:`focus:shadow-outline mb-3 w-full  appearance-none rounded border border-[#1f1d1a] px-3 py-2 leading-tight text-gray-700   shadow focus:outline-none ${(F=s.usage_limit)!=null&&F.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:($=s.usage_limit)==null?void 0:$.message})]})]}):null]})]})};export{fe as default};

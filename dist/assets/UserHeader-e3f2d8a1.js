import{j as e}from"./@react-google-maps/api-4794cf1a.js";import{R as l,u,L as g,j as p}from"./vendor-dd4ba10b.js";import{M as f,G as v,A as w,t as j}from"./index-3efdd896.js";import{M as N}from"./index.esm-f1328d83.js";import{G as i}from"./react-icons-0b96c072.js";import{a as C}from"./index.esm-3f4ea327.js";import{L as b}from"./logo-a4db6296.js";import"./react-confirm-alert-5d5c0db6.js";import"./html2pdf.js-19c9759c.js";import"./@headlessui/react-7b0d4887.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-1762f471.js";import"./@fortawesome/react-fontawesome-0b111e8e.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";function A(a){return i({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M428 224H288a48 48 0 01-48-48V36a4 4 0 00-4-4h-92a64 64 0 00-64 64v320a64 64 0 0064 64h224a64 64 0 0064-64V228a4 4 0 00-4-4zm-92 160H176a16 16 0 010-32h160a16 16 0 010 32zm0-80H176a16 16 0 010-32h160a16 16 0 010 32z"}},{tag:"path",attr:{d:"M419.22 188.59L275.41 44.78a2 2 0 00-3.41 1.41V176a16 16 0 0016 16h129.81a2 2 0 001.41-3.41z"}}]})(a)}function y(a){return i({tag:"svg",attr:{viewBox:"0 0 320 512"},child:[{tag:"path",attr:{d:"M137.4 374.6c12.5 12.5 32.8 12.5 45.3 0l128-128c9.2-9.2 11.9-22.9 6.9-34.9s-16.6-19.8-29.6-19.8L32 192c-12.9 0-24.6 7.8-29.6 19.8s-2.2 25.7 6.9 34.9l128 128z"}}]})(a)}function E(a){return i({tag:"svg",attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M224 256A128 128 0 1 0 224 0a128 128 0 1 0 0 256zm-45.7 48C79.8 304 0 383.8 0 482.3C0 498.7 13.3 512 29.7 512H418.3c16.4 0 29.7-13.3 29.7-29.7C448 383.8 368.2 304 269.7 304H178.3z"}}]})(a)}let B=new f;const L=[{to:"/user/dashboard",text:"Dashboard",icon:e.jsx(N,{className:"text-[24px] text-[#373A4B]"}),value:"admin"},{to:"/user/onboarding",text:"Onboarding",icon:e.jsx(E,{className:"text-[24px] text-[#373A4B]"}),value:"onboarding"},{to:"/user/document-library",text:"Documents library",icon:e.jsx(A,{className:"text-[24px] text-[#373A4B]"}),value:"documents"},{to:"/user/get-coaching",text:"Coaching",icon:e.jsx(C,{className:"text-[24px] text-[#373A4B]"}),value:"coaching"}],K=()=>{const{state:{isOpen:a,path:c},dispatch:n}=l.useContext(v),{state:k,dispatch:x}=l.useContext(w);l.useState(!1),l.useState(!1);const[o,m]=l.useState(null),r=u();let d=t=>{n({type:"OPEN_SIDEBAR",payload:{isOpen:t}})};const h=t=>{m(o===t?null:t)};return l.useEffect(()=>{async function t(){try{const s=await B.getProfile();x({type:"UPDATE_PROFILE",payload:s})}catch(s){console.log("Error",s),j(x,s.response.data.message?s.response.data.message:s.message)}}t()},[]),l.useEffect(()=>{const t=()=>{n({type:"OPEN_SIDEBAR",payload:{isOpen:window.innerWidth>=768}})};return window.addEventListener("resize",t),()=>window.removeEventListener("resize",t)},[n]),e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:`z-50 flex max-h-screen flex-1 flex-col bg-white py-4 text-[#A8A8A8] transition-all ${a?"fixed lg:sticky lg:top-0 h-screen w-[318px] min-w-[318px] max-w-[318px] overflow-y-auto":"hidden"}`,children:[e.jsxs("div",{className:`text-[#393939] ${a?"flex w-full":"flex items-center justify-center"} `,children:[e.jsx("div",{}),a&&e.jsx("div",{className:"flex gap-2 w-full px-[2rem]",children:e.jsxs(g,{to:"/user/dashboard",className:"flex gap-2",children:[e.jsx("img",{src:b,alt:"Logo",className:"h-[29px] w-[22px]"}),e.jsx("p",{className:"text-[20px] font-semibold leading-[28px] text-[#020617]",children:"Rescue Career Academy"})]})})]}),e.jsx("div",{className:"h-fit w-auto flex-1",children:e.jsx("div",{className:"sidebar-list w-auto",children:e.jsx("ul",{className:"flex flex-wrap px-2 text-sm mt-[1rem]",children:L.map(t=>e.jsx("li",{className:"w-full",children:t.hasSubMenu?e.jsxs("div",{className:"block w-full list-none py-[0.75rem] ml-[0.6rem] px-[1rem]",children:[e.jsxs("div",{className:`flex items-center justify-between cursor-pointer w-full ${c===t.value?"active-nav":""}`,onClick:()=>h(t.value),children:[e.jsxs("div",{className:"flex items-center gap-3",children:[t.icon,a&&e.jsx("span",{className:"text-[16px] font-normal text-[#373A4B] leading-[24px]",children:t.text})]}),a&&e.jsx(y,{className:`text-[24px] text-[#373A4B] transition-transform ${o===t.value?"rotate-180":""}`})]}),a&&o===t.value&&e.jsx("ul",{className:"ml-8 mt-2",children:t.subItems.map(s=>e.jsx("li",{children:e.jsx(p,{to:s.to,className:`block py-2 text-[14px] text-[#373A4B] hover:text-[#000] ${c===s.value?"font-medium":""}`,children:e.jsxs("p",{className:"text-[16px] font-normal text-[#373A4B] leading-[24px]",children:[" ",s.text]})})},s.value))})]}):e.jsx("div",{className:`block w-full list-none py-[0.75rem] px-[1rem] ${r.pathname===t.to?"bg-[#054FB1]":""} hover:bg-[#054FB1] group transition-all duration-300 ease-in-out`,children:e.jsx(p,{to:t.to,style:{background:"transparent"},className:"block w-full",children:e.jsxs("div",{className:"flex items-center gap-3 bg-transparent",children:[l.cloneElement(t.icon,{className:`text-[24px] ${r.pathname===t.to?"text-white":"text-[#373A4B]"} group-hover:text-white transition-colors duration-300 ease-in-out`}),a&&e.jsx("span",{className:`text-[16px] font-normal leading-[24px] ${r.pathname===t.to?"text-white":"text-[#373A4B]"} group-hover:text-white transition-colors duration-300 ease-in-out`,children:t.text})]})})})},t.value))})})}),e.jsx("div",{className:"flex justify-end",children:e.jsx("div",{className:"mr-3 cursor-pointer rounded-lg border border-[#E0E0E0] bg-white p-2 text-2xl text-gray-400",children:e.jsx("span",{onClick:()=>d(!a),children:e.jsx("svg",{className:`transition-transform ${a?"":"rotate-180"}`,xmlns:"http:www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:e.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M2 12C2 17.5228 6.47715 22 12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12ZM10.4142 11L11.7071 9.70711C12.0976 9.31658 12.0976 8.68342 11.7071 8.29289C11.3166 7.90237 10.6834 7.90237 10.2929 8.29289L7.82322 10.7626C7.13981 11.446 7.13981 12.554 7.82322 13.2374L10.2929 15.7071C10.6834 16.0976 11.3166 16.0976 11.7071 15.7071C12.0976 15.3166 12.0976 14.6834 11.7071 14.2929L10.4142 13H16C16.5523 13 17 12.5523 17 12C17 11.4477 16.5523 11 16 11H10.4142Z",fill:"#A8A8A8"})})})})})]}),!a&&e.jsx("div",{className:"hidden lg:block fixed bottom-4 left-4",children:e.jsx("div",{className:"cursor-pointer rounded-lg border border-[#E0E0E0] bg-white p-2 text-2xl text-gray-400",children:e.jsx("span",{onClick:()=>d(!0),children:e.jsx("svg",{className:"rotate-180",xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:e.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M2 12C2 17.5228 6.47715 22 12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12ZM10.4142 11L11.7071 9.70711C12.0976 9.31658 12.0976 8.68342 11.7071 8.29289C11.3166 7.90237 10.6834 7.90237 10.2929 8.29289L7.82322 10.7626C7.13981 11.446 7.13981 12.554 7.82322 13.2374L10.2929 15.7071C10.6834 16.0976 11.3166 16.0976 11.7071 15.7071C12.0976 15.3166 12.0976 14.6834 11.7071 14.2929L10.4142 13H16C16.5523 13 17 12.5523 17 12C17 11.4477 16.5523 11 16 11H10.4142Z",fill:"#A8A8A8"})})})})}),a&&e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden",onClick:()=>n({type:"OPEN_SIDEBAR",payload:{isOpen:!1}})})]})};export{K as UserHeader,K as default};

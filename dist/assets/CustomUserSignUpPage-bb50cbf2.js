import{j as e}from"./@react-google-maps/api-4794cf1a.js";import{r as i,R,u as I,b as F,L as M}from"./vendor-dd4ba10b.js";import{u as O}from"./react-hook-form-a6ecef1c.js";import{o as T}from"./yup-f7f8305f.js";import{c as q,a as u}from"./yup-79911193.js";import{A as j,G as Z,M as G,s as c}from"./index-3efdd896.js";import{I as U}from"./InteractiveButton-6ddb3b9d.js";import{L as B}from"./logo-a4db6296.js";import{L as D}from"./AI image-fab52c2e.js";import{E as W,C as z}from"./@stripe/react-stripe-js-1762f471.js";import{l as K}from"./@stripe/stripe-js-6b714a86.js";import X from"./CardDetails-d4a40607.js";import"./@hookform/resolvers-61c06af0.js";import"./react-confirm-alert-5d5c0db6.js";import"./html2pdf.js-19c9759c.js";import"./@headlessui/react-7b0d4887.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";import"./@fortawesome/react-fontawesome-0b111e8e.js";import"./MoonLoader-795b8e10.js";const H=K("pk_test_51R3UZOBazfqAX4xwvEnCZFjXvlOqYkch3qznif64teEvrxi2IzKXj2XKc5LTHhunoQiUE266ZCokcSNHAvswJOt900QLGddBPx"),Ne=()=>{const[d,o]=i.useState(!1),{dispatch:b}=i.useContext(j),{dispatch:m}=i.useContext(Z);R.useContext(j);const y=I(),N=F(),n=new G,v=new URLSearchParams(y.search).get("redirect_uri"),[f,C]=i.useState(!1),[A,g]=i.useState(!1),[a,k]=i.useState(null),P=q({firstName:u().required(),lastName:u().required(),email:u().email().required(),password:u().required()}).required(),{register:x,handleSubmit:S,setError:$,formState:{errors:s}}=O({resolver:T(P)}),L=async p=>{try{o(!0);const t=await n.callRawAPI("/v3/api/custom/jordan/user/check-email",{email:p.email},"POST");if(t.error||t.exists){c(m,"This email is already registered. Please use a different email or login.",4e3,"error"),o(!1);return}k(p),g(!0)}catch(t){c(m,(t==null?void 0:t.message)||"An error occurred. Please try again.",4e3,"error")}finally{o(!1)}},_=async({stripe:p,elements:t})=>{try{o(!0);const l=await n.callRawAPI("/v3/api/custom/jordan/user/create-payment-intent",{price_id:7,email:a.email},"POST");if(l.error)c(m,"Payment intent creation failed",4e3,"error");else{const{stripe_payment_id:J,secret:E}=l.data,{paymentIntent:r,error:Y}=await p.confirmCardPayment(E,{payment_method:{card:t.getElement(z)}});if(console.log("paymentIntent",r),!(await n.callRawAPI("/v3/api/custom/jordan/user/confirm-registration-payment",{payment_intent_id:r==null?void 0:r.id,email:a.email},"POST")).error){const h=await n.callRawAPI("/v3/api/custom/jordan/user/register-subscribe",{paymentIntentId:r==null?void 0:r.id,email:a.email,role:a.role,password:a.password,first_name:a.firstName,last_name:a.lastName},"POST");if(console.log("respose2",h),!h.error){b({type:"LOGIN",payload:h});const w=h.user_id;console.log("user_id",w),n.verifyUser(w),c(m,"Successfully Registered",4e3,"success"),N(v??"/user/dashboard")}}}}catch(l){c(m,l==null?void 0:l.message,4e3,"error")}finally{o(!1)}};return e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"flex flex-col items-center justify-center min-h-screen bg-[#FAFAFA] md:p-4",children:e.jsxs("div",{className:"flex flex-row w-full md:w-[90%] h-screen md:h-[80vh] lg:h-[70vh] xl:h-[80vh]",children:[e.jsx("div",{className:"w-full lg:w-[50%] flex items-center justify-center bg-white h-screen md:h-[80vh] lg:h-[70vh] xl:h-[80vh] rounded-lg shadow px-[2rem] md:px-[7rem] lg:px-[4rem] xl:px-[7rem] py-[2rem] rounded-[24px]]",children:e.jsxs("div",{className:"w-full max-w-md lg:max-w-none",children:[e.jsx("div",{className:"flex justify-center mb-6",children:e.jsx("img",{src:B,alt:"Logo",className:"w-[55px] h-[73px]"})}),e.jsxs("form",{onSubmit:S(L),className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm mb-1",children:"First Name"}),e.jsx("input",{type:"text",placeholder:"First Name",...x("firstName"),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none"}),(s==null?void 0:s.firstName)&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:s.firstName.message})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm mb-1",children:"Last Name"}),e.jsx("input",{type:"text",placeholder:"Last Name",...x("lastName"),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none"}),(s==null?void 0:s.lastName)&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:s.lastName.message})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm mb-1",children:"Email"}),e.jsx("input",{type:"email",placeholder:"Email",...x("email"),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none"}),(s==null?void 0:s.email)&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:s.email.message})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm mb-1",children:"Password"}),e.jsxs("div",{className:"relative",children:[e.jsx("input",{type:f?"text":"password",placeholder:"Password",...x("password"),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none pr-10"}),e.jsx("button",{type:"button",onClick:()=>C(!f),className:"absolute right-3 top-1/2 -translate-y-1/2",children:f?e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",children:[e.jsx("path",{d:"M2.5 10C2.5 10 5 5 10 5C15 5 17.5 10 17.5 10C17.5 10 15 15 10 15C5 15 2.5 10 2.5 10Z",stroke:"#A8A8A8",strokeWidth:"1.5"}),e.jsx("path",{d:"M10 12.5C11.3807 12.5 12.5 11.3807 12.5 10C12.5 8.61929 11.3807 7.5 10 7.5C8.61929 7.5 7.5 8.61929 7.5 10C7.5 11.3807 8.61929 12.5 10 12.5Z",stroke:"#A8A8A8",strokeWidth:"1.5"})]}):e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",children:[e.jsx("path",{d:"M2.5 10C2.5 10 5 5 10 5C15 5 17.5 10 17.5 10C17.5 10 15 15 10 15C5 15 2.5 10 2.5 10Z",stroke:"#A8A8A8",strokeWidth:"1.5"}),e.jsx("path",{d:"M10 12.5C11.3807 12.5 12.5 11.3807 12.5 10C12.5 8.61929 11.3807 7.5 10 7.5C8.61929 7.5 7.5 8.61929 7.5 10C7.5 11.3807 8.61929 12.5 10 12.5Z",stroke:"#A8A8A8",strokeWidth:"1.5"}),e.jsx("path",{d:"M4 4L16 16",stroke:"#A8A8A8",strokeWidth:"1.5"})]})}),(s==null?void 0:s.password)&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:s.password.message})]})]}),e.jsx(U,{type:"submit",loading:d,disabled:d,className:"w-full py-2 px-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 mt-6",children:d?"CHECKING...":"CREATE ACCOUNT"}),e.jsx("div",{className:"text-center mt-4",children:e.jsx(M,{to:"/user/login",className:"text-blue-600 text-sm",children:"Already a member? Login"})})]})]})}),e.jsx("img",{src:D,alt:"login image",className:"w-[50%] hidden lg:flex"})]})}),A&&e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999]",children:e.jsx("div",{className:"relative bg-white rounded-lg w-full max-w-4xl",children:e.jsx(W,{stripe:H,children:e.jsx(X,{onClose:()=>g(!1),onSubmit:_,price:99,loading:d})})})})]})};export{Ne as default};

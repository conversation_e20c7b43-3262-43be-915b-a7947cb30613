import{j as e}from"./@react-google-maps/api-4794cf1a.js";import{R as g,b as H,r as s}from"./vendor-dd4ba10b.js";import{A as K,G as U,L as S,a as N,k as q,l as B,M as J}from"./index-3efdd896.js";import{C as O}from"./react-spinners-06b0cea5.js";import"./react-confirm-alert-5d5c0db6.js";import"./html2pdf.js-19c9759c.js";import"./@headlessui/react-7b0d4887.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-1762f471.js";import"./@fortawesome/react-fontawesome-0b111e8e.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";const p=[{header:"Id",accessor:"id"},{header:"Name",accessor:"name"},{header:"Features",accessor:"features"},{header:"Stripe ID",accessor:"stripe_id"},{header:"Status",accessor:"status",mappingExist:!0,mappings:{0:{text:"Inactive",bg:"#F6A13C",color:"black"},1:{text:"Active",bg:"#9DD321",color:"black"}}},{header:"Created At",accessor:"create_at"},{header:"Updated At",accessor:"update_at"}],ne=()=>{g.useContext(K);const{dispatch:w}=g.useContext(U);H();const[h,P]=s.useState([]),[d,v]=s.useState(10),[k,A]=s.useState(0),[Q,C]=s.useState(0),[i,E]=s.useState(1),[T,D]=s.useState(!1),[I,L]=s.useState(!1),[u,b]=s.useState(!1),[M,n]=s.useState(!1),[j,l]=s.useState(!1),[_,F]=s.useState(),o=async(a,c)=>{b(!0);try{const x=await new J().callRawAPI(`/v4/api/records/stripe_product?page=${a}&limit=${c}`,[],"GET"),{list:t,total:G,limit:$,num_pages:y,page:m}=x;P(t),v($),A(y),E(m),C(G),D(m>1),L(m+1<=y)}catch(r){console.log("Error",r)}finally{b(!1)}};function z(){o(i-1,d)}function R(){o(i+1,d)}const f=(a,c,r=[])=>{switch(a){case"add":n(c);break;case"edit":l(c),F(r[0]);break}};return g.useEffect(()=>{o(1,d),w({type:"SETPATH",payload:{path:"packages"}})},[]),e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"overflow-x-auto rounded bg-white p-5 shadow",children:[e.jsxs("div",{className:"flex justify-between mb-5",children:[e.jsx("h4",{className:"text-2xl font-medium",children:"Packages"}),e.jsx("button",{onClick:()=>f("add",!0),className:"bg-primary px-4 py-2 text-white rounded-md",children:"Add New"})]}),e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsxs("tr",{children:[p.map((a,c)=>e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:a.header},c)),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),!u&&h.length>0?e.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:h.map((a,c)=>e.jsxs("tr",{children:[p.map((r,x)=>{if(r.mappingExist){const t=r.mappings[a[r.accessor]];return e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("span",{className:"px-2 py-1 rounded",style:{backgroundColor:t==null?void 0:t.bg,color:t==null?void 0:t.color},children:t==null?void 0:t.text})},x)}return e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:a[r.accessor]},x)}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("button",{onClick:()=>f("edit",!0,[a.id]),className:"text-indigo-600 hover:text-indigo-900 mr-3",children:"Edit"})})]},c))}):u?e.jsx("tbody",{children:e.jsx("tr",{children:e.jsxs("td",{colSpan:p.length+1,className:"text-center py-4",children:[e.jsx(O,{color:"#000",size:20,className:"mr-3"}),"Loading..."]})})}):e.jsx("tbody",{children:e.jsx("tr",{children:e.jsx("td",{colSpan:p.length+1,className:"text-center py-4",children:"No data found"})})})]}),h.length>0&&!u&&e.jsxs("div",{className:"flex items-center justify-between mt-4",children:[e.jsx("button",{onClick:z,disabled:!T,className:"px-4 py-2 border rounded",children:"Previous"}),e.jsxs("span",{children:["Page ",i," of ",k]}),e.jsx("button",{onClick:R,disabled:!I,className:"px-4 py-2 border rounded",children:"Next"})]})]}),e.jsx(S,{children:e.jsx(N,{isModalActive:M,closeModalFn:()=>n(!1),children:e.jsx(q,{setSidebar:n,onSuccess:()=>{n(!1),o(i,d)}})})}),j&&e.jsx(S,{children:e.jsx(N,{isModalActive:j,closeModalFn:()=>l(!1),children:e.jsx(B,{activeId:_,setSidebar:l,onSuccess:()=>{l(!1),o(i,d)}})})})]})};export{ne as default};

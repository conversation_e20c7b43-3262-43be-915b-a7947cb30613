import{j as e}from"./@react-google-maps/api-4794cf1a.js";import"./vendor-dd4ba10b.js";const a=({isOpen:t,onClose:s,onConfirm:n,itemName:i="quiz"})=>t?e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:e.jsxs("div",{className:"bg-white rounded-lg p-6 w-[400px]",children:[e.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Confirm Delete"}),e.jsxs("p",{className:"text-gray-600 mb-6",children:["Are you sure you want to delete this ",i,"? This action cannot be undone."]}),e.jsxs("div",{className:"flex justify-end gap-4",children:[e.jsx("button",{onClick:s,className:"px-4 py-2 text-gray-600 hover:text-gray-800",children:"Cancel"}),e.jsx("button",{onClick:n,className:"px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700",children:"Delete"})]})]})}):null;export{a as D};

import{j as t}from"./@react-google-maps/api-4794cf1a.js";import{R as a,b as F}from"./vendor-dd4ba10b.js";import{u as R}from"./react-hook-form-a6ecef1c.js";import{o as V}from"./yup-f7f8305f.js";import{c as G,a as o}from"./yup-79911193.js";import{M as I,A as L,G as $,t as T,s as K}from"./index-3efdd896.js";import{D as M}from"./index-96030dce.js";import"./@hookform/resolvers-61c06af0.js";import"./react-confirm-alert-5d5c0db6.js";import"./html2pdf.js-19c9759c.js";import"./@headlessui/react-7b0d4887.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-1762f471.js";import"./@fortawesome/react-fontawesome-0b111e8e.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";let S=new I;const ie=({activeId:m,setSidebar:c})=>{var v,j,w;const P=G({page:o().required(),key:o().required(),type:o().required(),value:o()}).required(),d=[{key:"text",value:"Text"},{key:"image",value:"Image"},{key:"number",value:"Number"},{key:"kvp",value:"Key-Value Pair"},{key:"image-list",value:"Image List"},{key:"captioned-image-list",value:"Captioned Image List"},{key:"team-list",value:"Team List"}],{dispatch:p}=a.useContext(L),{dispatch:u}=a.useContext($),[A,U]=a.useState((v=d[0])==null?void 0:v.key),[x,y]=a.useState(""),[_,q]=a.useState(""),[g,r]=a.useState(!1),D=F(),{register:l,handleSubmit:f,setError:h,setValue:n,formState:{errors:b}}=R({resolver:V(P)}),k=async e=>{r(!0);let N=new I;r(!0);try{N.setTable("cms");const s=await N.cmsEdit(_,e.page,e.key,e.type,x);if(!s.error)D("/admin/cms"),K(u,"Updated");else if(s.validation){const C=Object.keys(s.validation);for(let i=0;i<C.length;i++){const E=C[i];h(E,{type:"manual",message:s.validation[E]})}}}catch(s){console.log("Error",s),h("page",{type:"manual",message:s.message}),T(p,s.message)}r(!1)};return a.useEffect(()=>{u({type:"SETPATH",payload:{path:"cms"}}),async function(){try{S.setTable("cms");const e=await S.callRestAPI({id:m},"GET");console.log("result: ",e),e.error||(q(e.model.id),n("page",e.model.page),n("type",e.model.content_type),n("key",e.model.content_key),y(e.model.content_value))}catch(e){console.log("Error",e),T(p,e.message)}}()},[m]),t.jsxs("div",{className:"mx-auto rounded",children:[t.jsxs("div",{className:"flex items-center justify-between gap-4 border-b border-b-[#E0E0E0] p-3",children:[t.jsx("div",{className:"flex items-center gap-3",children:t.jsx("span",{className:"text-lg font-semibold",children:"Edit User"})}),t.jsxs("div",{className:"flex items-center gap-4",children:[t.jsx("button",{className:"flex items-center rounded-md border border-[#C6C6C6] px-3 py-2 shadow-sm hover:bg-[#f4f4f4]",onClick:()=>c(!1),children:"Cancel"}),t.jsx("button",{className:"flex items-center rounded-md bg-[#4F46E5] px-3 py-2 text-white shadow-sm",onClick:async()=>{await f(k)(),c(!1)},disabled:g,children:g?"Saving...":"Save"})]})]}),t.jsxs("form",{className:"w-full p-4 text-left",onSubmit:f(k),children:[t.jsxs("div",{className:"mb-4",children:[t.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"page",children:"Page"}),t.jsx("input",{type:"text",placeholder:"Page",...l("page"),className:"shadow appearance-none border rounded w-full mb-3 py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline}"})]}),t.jsxs("div",{className:"mb-4",children:[t.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"key",children:"Content Identifier"}),t.jsx("input",{type:"text",placeholder:"Content Identifier",...l("key"),className:`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(j=b.key)!=null&&j.message?"border-red-500":""}`}),t.jsx("p",{className:"text-xs italic text-red-500",children:(w=b.key)==null?void 0:w.message})]}),t.jsxs("div",{className:"mb-4",children:[t.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",children:"Content Type"}),t.jsx("select",{name:"type",id:"type",className:"shadow border  rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline",...l("type",{onChange:e=>U(e.target.value)}),children:d.map(e=>t.jsx("option",{name:e.name,value:e.key,children:e.value},e.key))})]}),t.jsx(M,{contentValue:x,contentType:A,setContentValue:y})]})]})};export{ie as default};

import{j as e}from"./@react-google-maps/api-4794cf1a.js";import{r as a,u as k,b as L,L as g}from"./vendor-dd4ba10b.js";import{u as S}from"./react-hook-form-a6ecef1c.js";import{o as I}from"./yup-f7f8305f.js";import{c as M,a as w}from"./yup-79911193.js";import{A as F,G as E,M as G,s as f}from"./index-3efdd896.js";import{I as P}from"./InteractiveButton-6ddb3b9d.js";import{L as W}from"./logo-a4db6296.js";import{L as Z}from"./AI image-fab52c2e.js";import"./@hookform/resolvers-61c06af0.js";import"./react-confirm-alert-5d5c0db6.js";import"./html2pdf.js-19c9759c.js";import"./@headlessui/react-7b0d4887.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-1762f471.js";import"./@fortawesome/react-fontawesome-0b111e8e.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";import"./MoonLoader-795b8e10.js";const oe=()=>{const[o,b]=a.useState(!1),[l,r]=a.useState(!1),{dispatch:j}=a.useContext(F),{dispatch:i}=a.useContext(E);k();const v=L(),y=new G,N=M({email:w().email().required(),password:w().required()}).required(),{register:m,handleSubmit:C,setError:d,formState:{errors:t}}=S({resolver:I(N)}),A=async n=>{var c,x,p,h;try{r(!0);const s=await y.login(n.email,n.password,"user");s.error?(r(!1),s.validation&&Object.keys(s.validation).forEach(u=>{d(u,{type:"manual",message:s.validation[u]})})):(j({type:"LOGIN",payload:s}),f(i,"Successfully Logged In",4e3,"success"),v("/user/dashboard"))}catch(s){r(!1),f(i,((x=(c=s==null?void 0:s.response)==null?void 0:c.data)==null?void 0:x.message)||(s==null?void 0:s.message),4e3,"error"),d("email",{type:"manual",message:((h=(p=s==null?void 0:s.response)==null?void 0:p.data)==null?void 0:h.message)||(s==null?void 0:s.message)})}};return e.jsx("div",{className:"flex flex-col items-center justify-center min-h-screen bg-[#FAFAFA] md:p-4",children:e.jsxs("div",{className:"flex flex-row w-full md:w-[90%] h-screen md:h-[80vh]",children:[e.jsx("div",{className:"w-full lg:w-[50%] flex items-center justify-center bg-white h-screen md:h-[80vh] rounded-lg shadow px-[2rem] md:px-[7rem] py-[2rem] rounded-[24px]]",children:e.jsxs("div",{className:"w-full max-w-md lg:max-w-none",children:[e.jsx("div",{className:"flex justify-center mb-6",children:e.jsx("img",{src:W,alt:"Logo",className:"w-[55px] h-[73px]"})}),e.jsxs("form",{onSubmit:C(A),className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm mb-1",children:"Email"}),e.jsx("input",{type:"email",placeholder:"Placeholder",...m("email"),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none"}),(t==null?void 0:t.email)&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:t.email.message})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm mb-1",children:"Password"}),e.jsxs("div",{className:"relative",children:[e.jsx("input",{type:o?"text":"password",placeholder:"Password",...m("password"),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none pr-10"}),e.jsx("button",{type:"button",onClick:()=>b(!o),className:"absolute right-3 top-1/2 -translate-y-1/2",children:o?e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",children:[e.jsx("path",{d:"M2.5 10C2.5 10 5 5 10 5C15 5 17.5 10 17.5 10C17.5 10 15 15 10 15C5 15 2.5 10 2.5 10Z",stroke:"#A8A8A8",strokeWidth:"1.5"}),e.jsx("path",{d:"M10 12.5C11.3807 12.5 12.5 11.3807 12.5 10C12.5 8.61929 11.3807 7.5 10 7.5C8.61929 7.5 7.5 8.61929 7.5 10C7.5 11.3807 8.61929 12.5 10 12.5Z",stroke:"#A8A8A8",strokeWidth:"1.5"})]}):e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",children:[e.jsx("path",{d:"M2.5 10C2.5 10 5 5 10 5C15 5 17.5 10 17.5 10C17.5 10 15 15 10 15C5 15 2.5 10 2.5 10Z",stroke:"#A8A8A8",strokeWidth:"1.5"}),e.jsx("path",{d:"M10 12.5C11.3807 12.5 12.5 11.3807 12.5 10C12.5 8.61929 11.3807 7.5 10 7.5C8.61929 7.5 7.5 8.61929 7.5 10C7.5 11.3807 8.61929 12.5 10 12.5Z",stroke:"#A8A8A8",strokeWidth:"1.5"}),e.jsx("path",{d:"M4 4L16 16",stroke:"#A8A8A8",strokeWidth:"1.5"})]})}),(t==null?void 0:t.password)&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:t.password.message})]})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("input",{type:"checkbox",id:"remember",className:"w-4 h-4 rounded border-gray-300"}),e.jsx("label",{htmlFor:"remember",className:"ml-2 text-sm text-gray-600",children:"Remember me for 30 days"})]}),e.jsx(P,{type:"submit",loading:l,disabled:l,className:"w-full py-2 px-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 mt-6",children:"LOGIN"}),e.jsxs("div",{className:"text-center mt-4",children:[e.jsx(g,{to:"/user/forgot",className:"text-blue-600 text-sm",children:"Forgot password"}),e.jsx("div",{className:"mt-2",children:e.jsx(g,{to:"/user/signup",className:"text-blue-600 text-sm",children:"Don't have an account? Sign up"})})]})]})]})}),e.jsx("img",{src:Z,alt:"login image",className:"w-[50%] hidden lg:flex"})]})})};export{oe as default};

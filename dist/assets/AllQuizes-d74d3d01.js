import{j as e}from"./@react-google-maps/api-4794cf1a.js";import{R as h,r as i,L as N}from"./vendor-dd4ba10b.js";import{A as E,G as w,M as p,s as a,t as f}from"./index-3efdd896.js";import{B as D}from"./index.esm-c1b51ffb.js";import{D as v}from"./DeleteModal-a879d8f5.js";import"./react-confirm-alert-5d5c0db6.js";import"./html2pdf.js-19c9759c.js";import"./@headlessui/react-7b0d4887.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-1762f471.js";import"./@fortawesome/react-fontawesome-0b111e8e.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";import"./react-icons-0b96c072.js";const P=()=>{const{state:q,dispatch:o}=h.useContext(E),{dispatch:r}=h.useContext(w),[d,m]=i.useState([]),[g,x]=i.useState(!0),[b,n]=i.useState(!1),[u,c]=i.useState(null);i.useEffect(()=>{(async()=>{try{x(!0);const l=await new p().callRawAPI("/v3/api/custom/jordan/user/quizzes",{page:1,limit:10},"GET");console.log("Quizzes Response:",l),!l.error&&l.list?m(l.list):a(r,"Failed to fetch quizzes",4e3,"error")}catch(s){console.error("Error fetching quizzes:",s),a(r,s.message||"Failed to fetch quizzes",4e3,"error"),f(o,s.message)}finally{x(!1)}})()},[o,r]);const j=async t=>{c(t),n(!0)},z=async()=>{try{const t=new p;t.setTable("quiz");const s=await t.callRestAPI({id:u},"DELETE");if(!s||s.error){a(r,"Failed to delete quiz",4e3,"error");return}m(l=>l.filter(y=>y.id!==u)),a(r,"Quiz deleted successfully!",4e3,"success")}catch(t){console.error("Error deleting quiz:",t),a(r,t.message||"Failed to delete quiz",4e3,"error"),f(o,t.message)}finally{n(!1),c(null)}};return e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"w-full text-7xl min-h-screen h-full text-gray-700 bg-[#E2E6EB] p-[1rem] md:pl-[3rem]",children:e.jsxs("div",{className:"w-[95%] md:w-[90%] mx-auto md:mx-0",children:[e.jsx("h1",{className:"text-[20px] text-[#111928] font-semibold mb-6",children:"Document title: Fire fighter exams 2025"}),e.jsx("p",{className:"text-[#373A4B] text-[16px] font-bold text-right",children:"All Quizzes"}),e.jsx("div",{className:"bg-white border border-gray-200 rounded-lg overflow-hidden mt-[1rem] p-[2rem] pb-[4rem]",children:e.jsx("div",{className:"overflow-x-auto",style:{msOverflowStyle:"none",scrollbarWidth:"none",WebkitOverflowScrolling:"touch"},children:e.jsx("div",{style:{width:"100%",minWidth:"600px"},children:g?e.jsx("div",{className:"flex justify-center items-center py-8",children:e.jsx(D,{className:"animate-spin text-[#054FB1] text-4xl"})}):e.jsxs("table",{className:"w-full",children:[e.jsx("thead",{children:e.jsxs("tr",{className:"bg-[#E4E4E4]",children:[e.jsx("th",{className:"text-left py-3 px-4 text-sm font-semibold text-gray-700",children:"Quiz number"}),e.jsx("th",{className:"text-left py-3 px-4 text-sm font-semibold text-gray-700",children:"Date submitted"}),e.jsx("th",{className:"text-left py-3 px-4 text-sm font-semibold text-gray-700",children:"Actions"})]})}),e.jsx("tbody",{children:d.length>0?d.map((t,s)=>(JSON.parse(t.quiz||"[]"),e.jsxs("tr",{className:`cursor-pointer ${s%2===0?"bg-white hover:bg-[#efefefb8]":"bg-[#EFEFEF]"}`,children:[e.jsxs("td",{className:"py-3 px-4 text-sm text-gray-900",children:["Quiz ",s+1]}),e.jsx("td",{className:"py-3 px-4 text-sm text-gray-900",children:new Date(t.create_at).toLocaleDateString()}),e.jsx("td",{className:"py-3 px-4 text-sm",children:e.jsxs("div",{className:"flex gap-6",children:[e.jsx(N,{to:`/user/onboarding/singlequiz/${t.id}`,children:e.jsx("button",{className:"text-blue-600 hover:text-blue-800 underline",children:"View details"})}),e.jsx("button",{onClick:()=>j(t.id),className:"text-blue-600 hover:text-blue-800 underline",children:"Delete"})]})})]},t.id))):e.jsx("tr",{children:e.jsx("td",{colSpan:"3",className:"text-center py-4",children:"No quizzes found"})})})]})})})})]})}),e.jsx(v,{isOpen:b,onClose:()=>{n(!1),c(null)},onConfirm:z,itemName:"quiz"})]})};export{P as default};

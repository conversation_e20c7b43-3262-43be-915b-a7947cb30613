import{j as e}from"./@react-google-maps/api-4794cf1a.js";import{R as t,b as x,r as b}from"./vendor-dd4ba10b.js";import{M as w,A as S,G as g,L as o,b as j,a as c,m as A,n as v}from"./index-3efdd896.js";import"./react-confirm-alert-5d5c0db6.js";import"./html2pdf.js-19c9759c.js";import"./@headlessui/react-7b0d4887.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-1762f471.js";import"./@fortawesome/react-fontawesome-0b111e8e.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";new w;const M=[{header:"Header",accessor:"header",isSorted:!1,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Action",accessor:""}],z=()=>{t.useContext(S),t.useContext(g),x();const[m,s]=t.useState(!1),[d,a]=t.useState(!1),[u,h]=t.useState(),p=b.useRef(null),[E,f]=t.useState([]),l=(i,r,n=[])=>{switch(i){case"add":s(r);break;case"edit":a(r),f(n),h(n[0]);break}};return e.jsxs(e.Fragment,{children:[e.jsx(e.Fragment,{children:e.jsx("div",{className:"overflow-x-auto  rounded bg-white p-5 shadow",children:e.jsx(o,{children:e.jsx(j,{columns:M,tableRole:"admin",table:"pages",actionId:"id",actions:{view:{show:!0,action:null,multiple:!1},edit:{show:!0,multiple:!1,action:i=>l("edit",!0,i)},delete:{show:!0,action:null,multiple:!1},select:{show:!0,action:null,multiple:!1},add:{show:!0,action:()=>l("add",!0),multiple:!1,children:"Add New",showChildren:!0},export:{show:!1,action:null,multiple:!0}},actionPostion:"ontable",refreshRef:p})})})}),e.jsx(o,{children:e.jsx(c,{isModalActive:m,closeModalFn:()=>s(!1),children:e.jsx(A,{setSidebar:s})})}),d&&e.jsx(o,{children:e.jsx(c,{isModalActive:d,closeModalFn:()=>a(!1),children:e.jsx(v,{activeId:u,setSidebar:a})})})]})};export{z as default};

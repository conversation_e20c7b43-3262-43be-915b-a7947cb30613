import{j as t}from"./@react-google-maps/api-4794cf1a.js";import{R as d,b as K,r as s,h as z}from"./vendor-dd4ba10b.js";import{u as J}from"./react-hook-form-a6ecef1c.js";import{o as Q}from"./yup-f7f8305f.js";import{c as W,a as r}from"./yup-79911193.js";import{M as X,A as Y,G as Z,t as ee,s as te}from"./index-3efdd896.js";import"./react-quill-25360d36.js";import{M as i}from"./MkdInput-bb15886f.js";import{I as ae}from"./InteractiveButton-6ddb3b9d.js";import{S as se}from"./index-3b0c955b.js";import"./@hookform/resolvers-61c06af0.js";import"./react-confirm-alert-5d5c0db6.js";import"./html2pdf.js-19c9759c.js";import"./@headlessui/react-7b0d4887.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-1762f471.js";import"./@fortawesome/react-fontawesome-0b111e8e.js";import"./@fortawesome/fontawesome-svg-core-254ba2e2.js";import"./@fortawesome/free-solid-svg-icons-82da594a.js";import"./@fortawesome/free-regular-svg-icons-a38012c9.js";import"./@fortawesome/free-brands-svg-icons-2c021b6b.js";import"./@craftjs/core-3d3a3b40.js";import"./MoonLoader-795b8e10.js";let h=new X;const He=c=>{var S,N;const{dispatch:j}=d.useContext(Y),v=W({oauth:r(),role:r(),first_name:r(),last_name:r(),email:r(),password:r(),type:r(),verify:r(),packages_id:r(),phone:r(),photo:r(),refer:r(),stripe_uid:r(),paypal_uid:r(),two_factor_authentication:r(),status:r()}).required(),{dispatch:y}=d.useContext(Z),[b,oe]=d.useState({}),[_,x]=d.useState(!1),[P,g]=d.useState(!1),k=K(),[le,E]=s.useState(""),[re,I]=s.useState(""),[ie,R]=s.useState(""),[me,T]=s.useState(""),[ne,A]=s.useState(""),[pe,F]=s.useState(""),[de,U]=s.useState(0),[ce,L]=s.useState(0),[ue,D]=s.useState(0),[he,O]=s.useState(""),[fe,C]=s.useState(""),[ye,M]=s.useState(""),[xe,V]=s.useState(""),[ge,G]=s.useState(""),[be,$]=s.useState(0),[_e,B]=s.useState(0),{register:o,handleSubmit:H,setError:w,setValue:l,formState:{errors:a}}=J({resolver:Q(v)}),n=z();s.useEffect(function(){(async function(){try{g(!0),h.setTable("user");const e=await h.callRestAPI({id:c.activeId?c.activeId:Number(n==null?void 0:n.id)},"GET");e.error||(l("oauth",e.model.oauth),l("role",e.model.role),l("first_name",e.model.first_name),l("last_name",e.model.last_name),l("email",e.model.email),l("password",e.model.password),l("type",e.model.type),l("verify",e.model.verify),l("packages_id",e.model.packages_id),l("phone",e.model.phone),l("photo",e.model.photo),l("refer",e.model.refer),l("stripe_uid",e.model.stripe_uid),l("paypal_uid",e.model.paypal_uid),l("two_factor_authentication",e.model.two_factor_authentication),l("status",e.model.status),E(e.model.oauth),I(e.model.role),R(e.model.first_name),T(e.model.last_name),A(e.model.email),F(e.model.password),U(e.model.type),L(e.model.verify),D(e.model.packages_id),O(e.model.phone),C(e.model.photo),M(e.model.refer),V(e.model.stripe_uid),G(e.model.paypal_uid),$(e.model.two_factor_authentication),B(e.model.status),g(!1))}catch(e){g(!1),console.log("error",e),ee(j,e.message)}})()},[]);const q=async e=>{x(!0);try{h.setTable("user");for(let u in b){let p=new FormData;p.append("file",b[u].file);let f=await h.uploadImage(p);e[u]=f.url}const m=await h.callRestAPI({id:c.activeId?c.activeId:Number(n==null?void 0:n.id),oauth:e.oauth,role:e.role,first_name:e.first_name,last_name:e.last_name,email:e.email,password:e.password,type:e.type,verify:e.verify,packages_id:e.packages_id,phone:e.phone,photo:e.photo,refer:e.refer,stripe_uid:e.stripe_uid,paypal_uid:e.paypal_uid,two_factor_authentication:e.two_factor_authentication,status:e.status},"PUT");if(!m.error)te(y,"Updated"),k("/admin/user"),y({type:"REFRESH_DATA",payload:{refreshData:!0}}),c.setSidebar(!1);else if(m.validation){const u=Object.keys(m.validation);for(let p=0;p<u.length;p++){const f=u[p];w(f,{type:"manual",message:m.validation[f]})}}x(!1)}catch(m){x(!1),console.log("Error",m),w("oauth",{type:"manual",message:m.message})}};return d.useEffect(()=>{y({type:"SETPATH",payload:{path:"user"}})},[]),t.jsxs("div",{className:" shadow-md rounded   mx-auto p-5",children:[t.jsx("h4",{className:"text-2xl font-medium",children:"Edit User"}),P?t.jsx(se,{}):t.jsxs("form",{className:" w-full max-w-lg",onSubmit:H(q),children:[t.jsx(i,{type:"text",page:"edit",name:"oauth",errors:a,label:"Oauth",placeholder:"Oauth",register:o,className:""}),t.jsx(i,{type:"text",page:"edit",name:"role",errors:a,label:"Role",placeholder:"Role",register:o,className:""}),t.jsx(i,{type:"text",page:"edit",name:"first_name",errors:a,label:"First Name",placeholder:"First Name",register:o,className:""}),t.jsx(i,{type:"text",page:"edit",name:"last_name",errors:a,label:"Last Name",placeholder:"Last Name",register:o,className:""}),t.jsx(i,{type:"text",page:"edit",name:"email",errors:a,label:"Email",placeholder:"Email",register:o,className:""}),t.jsx(i,{type:"text",page:"edit",name:"password",errors:a,label:"Password",placeholder:"Password",register:o,className:""}),t.jsx(i,{type:"number",page:"edit",name:"type",errors:a,label:"Type",placeholder:"Type",register:o,className:""}),t.jsx(i,{type:"number",page:"edit",name:"verify",errors:a,label:"Verify",placeholder:"Verify",register:o,className:""}),t.jsx(i,{type:"number",page:"edit",name:"packages_id",errors:a,label:"Packages Id",placeholder:"Packages Id",register:o,className:""}),t.jsx(i,{type:"text",page:"edit",name:"phone",errors:a,label:"Phone",placeholder:"Phone",register:o,className:""}),t.jsxs("div",{className:"mb-4  ",children:[t.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"photo",children:"Photo"}),t.jsx("textarea",{placeholder:"Photo",...o("photo"),className:`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(S=a.photo)!=null&&S.message?"border-red-500":""}`,row:50}),t.jsx("p",{className:"text-red-500 text-xs italic",children:(N=a.photo)==null?void 0:N.message})]}),t.jsx(i,{type:"text",page:"edit",name:"refer",errors:a,label:"Refer",placeholder:"Refer",register:o,className:""}),t.jsx(i,{type:"text",page:"edit",name:"stripe_uid",errors:a,label:"Stripe Uid",placeholder:"Stripe Uid",register:o,className:""}),t.jsx(i,{type:"text",page:"edit",name:"paypal_uid",errors:a,label:"Paypal Uid",placeholder:"Paypal Uid",register:o,className:""}),t.jsx(i,{type:"number",page:"edit",name:"two_factor_authentication",errors:a,label:"Two Factor Authentication",placeholder:"Two Factor Authentication",register:o,className:""}),t.jsx(i,{type:"number",page:"edit",name:"status",errors:a,label:"Status",placeholder:"Status",register:o,className:""}),t.jsx(ae,{type:"submit",className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",loading:_,disable:_,children:"Submit"})]})]})};export{He as default};

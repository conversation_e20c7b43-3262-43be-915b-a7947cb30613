
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link
      rel="icon"
      type="image/svg+xml"
      href="/src/favicon.svg"
    />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0"
    />
    <title>jordan</title>
    <script type="module" crossorigin src="/assets/index-3efdd896.js"></script>
    <link rel="modulepreload" crossorigin href="/assets/vendor-dd4ba10b.js">
    <link rel="modulepreload" crossorigin href="/assets/@react-google-maps/api-4794cf1a.js">
    <link rel="modulepreload" crossorigin href="/assets/react-confirm-alert-5d5c0db6.js">
    <link rel="modulepreload" crossorigin href="/assets/html2pdf.js-19c9759c.js">
    <link rel="modulepreload" crossorigin href="/assets/@headlessui/react-7b0d4887.js">
    <link rel="modulepreload" crossorigin href="/assets/@stripe/stripe-js-6b714a86.js">
    <link rel="modulepreload" crossorigin href="/assets/@fortawesome/fontawesome-svg-core-254ba2e2.js">
    <link rel="modulepreload" crossorigin href="/assets/@fortawesome/react-fontawesome-0b111e8e.js">
    <link rel="modulepreload" crossorigin href="/assets/@stripe/react-stripe-js-1762f471.js">
    <link rel="modulepreload" crossorigin href="/assets/@fortawesome/free-solid-svg-icons-82da594a.js">
    <link rel="modulepreload" crossorigin href="/assets/@fortawesome/free-regular-svg-icons-a38012c9.js">
    <link rel="modulepreload" crossorigin href="/assets/@fortawesome/free-brands-svg-icons-2c021b6b.js">
    <link rel="stylesheet" href="/assets/index-5300e751.css">
  </head>
  <body>
    <div id="root"></div>
    <div id="portal"></div>
    
    
  </body>
</html>
